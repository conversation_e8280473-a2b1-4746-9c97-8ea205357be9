package com.yunqu.tariff.handler.export;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.utils.BusiUtil;
import com.yunqu.tariff.utils.excel.AsyncExcelExport;
import com.yunqu.tariff.utils.excel.ExcelExportDataHandle;
import com.yunqu.tariff.utils.excel.impl.ExcelExportDataHandleImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import java.io.File;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.*;

/**
 * TariffRecordExportHandler
 *
 * @ClassName TariffRecordExportHandler
 * <AUTHOR> Copy This Tag)
 * @Since create in 2024/12/10 11:02
 * @Version v1.0
 * @Copyright Copyright (c) 2024
 * @Company 广州云趣信息科技有限公司
 */
public class TariffRecordExportHandler extends BaseExportHandler {


    @Override
    protected String excuete(JSONObject taskObject) {
        logger.info("开始执行导出任务:{}", taskObject);
        JSONObject param = taskObject.getJSONObject("PARAMS");
        return actionForExport(param, taskObject.getString("FILE_NAME"));
    }

    @Override
    protected String getFileName(String userAcct) {
        if(StringUtils.isNotBlank(fileName)) return fileName;
        fileName = "资费列表_"+userAcct+"_"+ EasyDate.getCurrentDateString("yyyyMMdd")+".xlsx";
        return fileName;
    }

    public String actionForExport(JSONObject param, String fileName) {
        try {
            String fields = param.getString("fields");
            if (!isValidString(fields)) {
                return null;
            }

            // 准备Excel导出相关参数
            List<String> headers = new ArrayList<>();
            List<String> fields2 = new ArrayList<>();
            Map<String, String> dictMap = new HashMap<>();
            Map<String, JSONObject> treeMap = new HashMap<>();
            String[] fieldArray = fields.split(",");
            for (String fieldName : fieldArray) {
                String fieldZhName = BusiUtil.FieldZhMap.getString(fieldName);
                if("FIELD_CHECK_RESULT".equals(fieldName)){
                    headers.add("字段检查结果");
                    fields2.add("FIELD_CHECK_RESULT");
                }else if("FIELD_CHECK_TIME".equals(fieldName)){
                    fields2.add("FIELD_CHECK_TIME");
                    headers.add("检查时间");
                }else if("IS_PUBLIC".equals(fieldName)){
                    fields2.add("IS_PUBLIC");
                    headers.add("是否公示");
                }else if("PUBLIC_VERSIONS".equals(fieldName)){
                    fields2.add("PUBLIC_VERSIONS");
                    headers.add("公示版本");
                }else if("FEES".equals(fieldName)){
                    fields2.add("FEES_STR");
                    headers.add("资费标准");
                }else{
                    fields2.add(fieldName);
                    headers.add(fieldZhName);
                }
            }

            dictMap.put("TYPE1", "XTY_TARIFF_ONE_TYPE");
            dictMap.put("TYPE2", "XTY_TARIFF_TWO_TYPE");
            dictMap.put("STATUS", "XTY_TARIFF_STATUS");
            dictMap.put("TARIFF_ATTR", "XTY_TARIFF_ATTRIBUTES");
            dictMap.put("IS_TELECOM", "XTY_TARIFF_TELECOM");

            // 准备文件路径
            String currentDateString = EasyDate.getCurrentDateString("yyyyMMdd");
            String year = currentDateString.substring(0, 4);
            String month = currentDateString.substring(4, 6);
            String day = currentDateString.substring(6, 8);
            String filePath = Constants.FILE_BASE_PATH + File.separator + year + File.separator + month + File.separator + day;
            File folder = new File(filePath);
            if (!folder.exists()) {
                folder.mkdirs();
            }
            String fileAbsPath = filePath + File.separator + fileName;
            File file = new File(fileAbsPath);

            // 创建数据字段映射
            Map<String, String> dataFieldMapping = new HashMap<>();
            for (int i = 0; i < headers.size(); i++) {
                dataFieldMapping.put(headers.get(i), fields2.get(i));
            }

            // 使用分页查询导出ES数据
            // 创建Excel文件
            try (OutputStream os = Files.newOutputStream(file.toPath())) {
                ExcelExportDataHandle handle = new ExcelExportDataHandleImpl();
                handle.setEntId(Constants.getEntId());
                handle.setDictMap(dictMap);
                handle.setTreeMap(treeMap);

                // 修改：使用单一SQL查询所有ID，避免多次追加文件的问题
                int pageIndex = 1;
                List<JSONObject> resultList = new ArrayList<>();
                while (true) {
                    List<JSONObject> tmpList = queryEsDataByPage(param,pageIndex, fields2);
                    if(tmpList.isEmpty() || tmpList.size()<1){
                        break;
                    }
                    for (JSONObject json : tmpList) {
                        if(json.containsKey("OFFLINE_DAY") && Constants.OFFLINE_DAY_DEFAULT.equals(json.getString("OFFLINE_DAY"))){
                            json.put("OFFLINE_DAY","");
                        }
                    }
                    resultList.addAll(tmpList);
                    pageIndex++;
                }
                logger.info("资费备案库[导出]执行查询，总记录数: {}", resultList.size());

                logger.info(fields2.toString());
                logger.info(headers.toString());

                // 导出数据
                AsyncExcelExport exportExcel = new AsyncExcelExport()
                        .setJsonObjectList(resultList)
                        .setFields(fields2)
                        .setHeaderList(headers)
                        .setExcelExportDataHandle(handle);
                exportExcel.export(file.getName(), os, null);
            }

            return fileAbsPath;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 分页查询ES数据
     * @param queryParam 查询参数
     * @param pageIndex 页码（从1开始）
     * @param fields 需要获取的字段列表
     * @return 查询结果列表
     */
    private List<JSONObject> queryEsDataByPage(JSONObject queryParam, int pageIndex, List<String> fields) {
        try {
            // 构建ES查询参数
            JSONObject searchParams = buildEsQueryParams(queryParam);

            // 设置分页参数
            int pageSize = 10000;
            searchParams.put("from", (pageIndex - 1) * pageSize);
            searchParams.put("size", pageSize);

            // 设置要获取的字段（如果有指定）
            if (fields != null && !fields.isEmpty()) {
                JSONArray sourceArray = new JSONArray();
                for (String field : fields) {
                    sourceArray.add(field);
                }
                searchParams.put("_source", sourceArray);
            }

            // 执行ES查询
            String indexName = Constants.XTY_TARIFF_BAK_INFO_INDEX;
            JSONObject result = com.yunqu.xty.commonex.kit.ElasticsearchKit.search(indexName, searchParams);

            if (result == null || !result.containsKey("hits")) {
                logger.warn("[queryEsDataByPage] 查询ES数据失败或返回为空");
                return new ArrayList<>();
            }

            // 获取命中结果
            JSONObject hitsObj = result.getJSONObject("hits");
            JSONArray hits = hitsObj.getJSONArray("hits");

            if (hits == null || hits.isEmpty()) {
                return new ArrayList<>();
            }

            // 处理查询结果
            int batchSize = hits.size();
            List<JSONObject> resultList = new ArrayList<>(batchSize);
            for (int i = 0; i < batchSize; i++) {
                JSONObject hit = hits.getJSONObject(i);
                JSONObject source = hit.getJSONObject("_source");
                resultList.add(source);
            }

            logger.info("[queryEsDataByPage] 查询第{}页数据，获取到{}条记录", pageIndex, resultList.size());
            return resultList;
        } catch (Exception e) {
            logger.error("[queryEsDataByPage] 查询ES数据失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建ES查询参数
     */
    private JSONObject buildEsQueryParams(JSONObject param) {
        // 这里我们完善一下已有方法的返回值
        return param.containsKey("_esQuery") ? param.getJSONObject("_esQuery") : buildSimpleEsQueryParams(param);
    }

    /**
     * 构建简单的ES查询参数
     *
     * 这个方法主要用于在原有的buildEsQueryParams方法之外提供一个更简单的封装
     */
    private JSONObject buildSimpleEsQueryParams(JSONObject param) {
        // 构建ES查询JSON
        JSONObject queryParams = new JSONObject();
        JSONObject queryObj = new JSONObject();
        JSONObject boolObj = new JSONObject();
        JSONArray mustArray = new JSONArray();

        // 资费类型
        String type = param.getString("type");
        if (Constants.TARIFF_ATTR_01.equals(type)) { // 全国
            JSONObject typeMatch = new JSONObject();
            typeMatch.put("term", new JSONObject().fluentPut("TARIFF_ATTR", Constants.TARIFF_ATTR_01));
            mustArray.add(typeMatch);
        } else if (Constants.TARIFF_ATTR_02.equals(type)) {  // 省内
            JSONObject typeQuery = new JSONObject();
            JSONArray typeOrArray = new JSONArray();

            JSONObject type2Match = new JSONObject();
            type2Match.put("term", new JSONObject().fluentPut("TARIFF_ATTR", Constants.TARIFF_ATTR_02));
            typeOrArray.add(type2Match);

            JSONObject type3Match = new JSONObject();
            type3Match.put("term", new JSONObject().fluentPut("TARIFF_ATTR", Constants.TARIFF_ATTR_03));
            typeOrArray.add(type3Match);

            typeQuery.put("bool", new JSONObject().fluentPut("should", typeOrArray).fluentPut("minimum_should_match", 1));
            mustArray.add(typeQuery);
        }
        // 添加其他查询条件
        addEsSearchConditions(param,mustArray);

        // 将所有查询条件组合起来
        if (!mustArray.isEmpty()) {
            boolObj.put("must", mustArray);
            queryObj.put("bool", boolObj);
            queryParams.put("query", queryObj);
        }

        JSONArray sortArray = new JSONArray();

        // 第一排序字段：date_id 降序
        JSONObject dateIdSort = new JSONObject();
        dateIdSort.put("CREATE_TIME.keyword", new JSONObject().fluentPut("order", "desc"));
        sortArray.add(dateIdSort);

        // 第二排序字段：_id 降序
        JSONObject idSort = new JSONObject();
        idSort.put("_id", new JSONObject().fluentPut("order", "desc"));
        sortArray.add(idSort);

        queryParams.put("sort", sortArray);

        return queryParams;
    }




    /**
     * 添加其他通用查询条件
     */
    private void addEsSearchConditions(JSONObject param,JSONArray mustArray) {

        // 是否公示
        String isPublic = param.getString("isPublic");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(isPublic)) {
            JSONObject entMatch = new JSONObject();
            entMatch.put("term", new JSONObject().fluentPut("IS_PUBLIC.keyword", isPublic));
            mustArray.add(entMatch);
        }

        //公示版本
        //公示版本
        String version = param.getString("publicVersions");
        if (StringUtils.isNotBlank(version)) {
            JSONObject query = new JSONObject();
            query.put("terms", new JSONObject().fluentPut("PUBLIC_VERSIONS.keyword", new JSONArray().fluentAdd(version)));
            mustArray.add(query);
        }

        // 企业
        String ent = param.getString("ent");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(ent)) {
            JSONObject entMatch = new JSONObject();
            if (org.apache.commons.lang3.StringUtils.contains(ent, ",")) {
                entMatch.put("terms", new JSONObject().fluentPut("ENT", StringUtils.split(ent, ",")));
            } else {
                entMatch.put("term", new JSONObject().fluentPut("ENT", ent));
            }
            mustArray.add(entMatch);
        }

        // 备案方类型
        String reporterType = param.getString("reporterType");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(reporterType)) {
            JSONObject reporterTypeMatch = new JSONObject();
            reporterTypeMatch.put("term", new JSONObject().fluentPut("REPORTER_TYPE", reporterType));
            mustArray.add(reporterTypeMatch);
        }

        // 资费状态
        String status = param.getString("status");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(status)) {
            JSONObject statusQuery = new JSONObject();
            JSONArray statusOrArray = new JSONArray();

            for (String statusCode : status.split(",")) {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(statusCode)) {
                    JSONObject statusMatch = new JSONObject();
                    statusMatch.put("term", new JSONObject().fluentPut("STATUS", statusCode));
                    statusOrArray.add(statusMatch);
                }
            }

            statusQuery.put("bool", new JSONObject().fluentPut("should", statusOrArray).fluentPut("minimum_should_match", 1));
            mustArray.add(statusQuery);
        }

        // 适用省份和地区过滤
        String provinceCode = param.getString("provinceCode");
        String areaCode = param.getString("areaCode");

        // 处理省份查询
        if (org.apache.commons.lang3.StringUtils.isNotBlank(provinceCode)) {
            JSONObject provinceQuery = new JSONObject();
            JSONArray provinceOrArray = new JSONArray();

            // 从AREA_LIST中提取PROVINCE_CODE进行查询
            String[] provinceCodes = provinceCode.split(",");
            JSONArray provinceTermsArray = new JSONArray();
            for (String pCode : provinceCodes) {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(pCode)) {
                    provinceTermsArray.add(pCode);
                }
            }

            if (provinceTermsArray.size() > 0) {
                // 使用PROVINCE_CODES数组字段进行查询
                JSONObject provinceCodesTerms = new JSONObject();
                provinceCodesTerms.put("terms", new JSONObject().fluentPut("PROVINCE_CODES", provinceTermsArray));
                provinceOrArray.add(provinceCodesTerms);
            }

            // 添加通用编码
            JSONObject allProvinceCodesQuery = new JSONObject();
            allProvinceCodesQuery.put("term", new JSONObject().fluentPut("PROVINCE_CODES", Constants.AREA_CODE_ALL));
            provinceOrArray.add(allProvinceCodesQuery);

            provinceQuery.put("bool", new JSONObject().fluentPut("should", provinceOrArray).fluentPut("minimum_should_match", 1));
            mustArray.add(provinceQuery);
        }

        // 处理地区查询
        if (org.apache.commons.lang3.StringUtils.isNotBlank(areaCode)) {
            JSONObject areaQuery = new JSONObject();
            JSONArray areaOrArray = new JSONArray();

            // 将地区代码拆分为数组
            String[] areaCodes = areaCode.split(",");
            JSONArray areaCodesArray = new JSONArray();
            for (String aCode : areaCodes) {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(aCode)) {
                    areaCodesArray.add(aCode);
                }
            }

            if (areaCodesArray.size() > 0) {
                // 使用AREA_CODES数组字段进行查询
                JSONObject areaCodesTerms = new JSONObject();
                areaCodesTerms.put("terms", new JSONObject().fluentPut("AREA_CODES", areaCodesArray));
                areaOrArray.add(areaCodesTerms);
            }

            // 添加通用编码
            JSONObject allAreaCodesQuery = new JSONObject();
            allAreaCodesQuery.put("term", new JSONObject().fluentPut("AREA_CODES", Constants.AREA_CODE_ALL));
            areaOrArray.add(allAreaCodesQuery);

            areaQuery.put("bool", new JSONObject().fluentPut("should", areaOrArray).fluentPut("minimum_should_match", 1));
            mustArray.add(areaQuery);
        }

        // 名称
        String name = param.getString("name");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(name)) {
            JSONObject nameMatch = new JSONObject();
            nameMatch.put("wildcard", new JSONObject().fluentPut("NAME", "*" + name + "*"));
            mustArray.add(nameMatch);
        }

        // 序列号
        String seqNo = param.getString("seq_no");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(seqNo)) {
            JSONObject seqNoMatch = new JSONObject();
            seqNoMatch.put("wildcard", new JSONObject().fluentPut("SEQ_NO.keyword", "*" + seqNo + "*"));
            mustArray.add(seqNoMatch);
        }

        // 资费属性
        String tariff_attr = param.getString("tariff_attr");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(tariff_attr)) {
            JSONObject tariffAttrQuery = new JSONObject();
            JSONArray tariffAttrOrArray = new JSONArray();

            for (String attrCode : tariff_attr.split(",")) {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(attrCode)) {
                    JSONObject attrMatch = new JSONObject();
                    attrMatch.put("term", new JSONObject().fluentPut("TARIFF_ATTR", attrCode));
                    tariffAttrOrArray.add(attrMatch);
                }
            }

            tariffAttrQuery.put("bool", new JSONObject().fluentPut("should", tariffAttrOrArray).fluentPut("minimum_should_match", 1));
            mustArray.add(tariffAttrQuery);
        }

        // 备案号
        String reportNo = param.getString("reportNo");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(reportNo)) {
            JSONObject reportNoMatch = new JSONObject();
            reportNoMatch.put("wildcard", new JSONObject().fluentPut("REPORT_NO", "*" + reportNo + "*"));
            mustArray.add(reportNoMatch);
        }

        // 备案主体
        String reporter = param.getString("reporter");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(reporter)) {
            if ("1".compareTo(reporter) <= 0 && "5".compareTo(reporter) >= 0) {
                JSONObject entMatch = new JSONObject();
                entMatch.put("term", new JSONObject().fluentPut("ENT", reporter));
                mustArray.add(entMatch);
            } else {
                JSONObject reporterMatch = new JSONObject();
                reporterMatch.put("wildcard", new JSONObject().fluentPut("REPORTER.keyword", "*" + reporter + "*"));
                mustArray.add(reporterMatch);
            }
        }

        // 备案者
        String reportObj = param.getString("reportObj");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(reportObj)) {
            JSONObject reportObjMatch = new JSONObject();
            reportObjMatch.put("wildcard", new JSONObject().fluentPut("REPORT_OBJ.keyword", "*" + reportObj + "*"));
            mustArray.add(reportObjMatch);
        }

        // 资费标准范围 - 优化FEES字段查询
        String feesBegin = param.getString("feesBegin");
        String feesEnd = param.getString("feesEnd");

        logger.info("[tariffRecordListNew] 查询资费范围: feesBegin={}, feesEnd={}", feesBegin, feesEnd);

        if (org.apache.commons.lang3.StringUtils.isNotBlank(feesBegin) || org.apache.commons.lang3.StringUtils.isNotBlank(feesEnd)) {
            // 创建bool查询用于组合多个条件
            JSONObject boolQuery = new JSONObject();
            JSONArray shouldArray = new JSONArray();

            // 使用优化后的FEES查询构建方法
            JSONObject feesQuery = buildFeesRegexQuery(feesBegin, feesEnd);
            if (feesQuery != null) {
                shouldArray.add(feesQuery);
                logger.info("[tariffRecordListNew] 主要FEES查询条件: {}", feesQuery.toJSONString());
            } else {
                // 如果buildFeesRegexQuery返回null，回退到基本查询
                // 1. 尝试数值范围查询
                try {
                    JSONObject numericRangeQuery = new JSONObject();
                    JSONObject numericRangeObj = new JSONObject();
                    boolean hasNumericRange = false;

                    if (org.apache.commons.lang3.StringUtils.isNotBlank(feesBegin)) {
                        double beginValue = Double.parseDouble(feesBegin);
                        numericRangeObj.put("gte", beginValue);
                        hasNumericRange = true;
                    }

                    if (org.apache.commons.lang3.StringUtils.isNotBlank(feesEnd)) {
                        double endValue = Double.parseDouble(feesEnd);
                        numericRangeObj.put("lte", endValue);
                        hasNumericRange = true;
                    }

                    if (hasNumericRange) {
                        numericRangeQuery.put("range", new JSONObject().fluentPut("FEES", numericRangeObj));
                        shouldArray.add(numericRangeQuery);
                    }
                } catch (NumberFormatException e) {
                    logger.warn("[tariffRecordListNew] 资费值解析为数值失败: {}", e.getMessage());
                }

                // 2. 对于精确查询，添加特殊处理
                if (org.apache.commons.lang3.StringUtils.isNotBlank(feesBegin) && org.apache.commons.lang3.StringUtils.isNotBlank(feesEnd) && feesBegin.equals(feesEnd)) {
                    // 精确匹配，既尝试原值也尝试带小数点的形式
                    JSONObject termsQuery = new JSONObject();
                    JSONArray termValues = new JSONArray();
                    termValues.add(feesBegin);

                    // 如果没有小数点，添加.0形式
                    if (!feesBegin.contains(".")) {
                        termValues.add(feesBegin + ".0");
                    }

                    termsQuery.put("terms", new JSONObject().fluentPut("FEES.str", termValues));
                    shouldArray.add(termsQuery);
                }

                // 3. 使用字符串匹配作为备选
                JSONObject strRangeQuery = new JSONObject();
                JSONObject strRangeObj = new JSONObject();

                if (org.apache.commons.lang3.StringUtils.isNotBlank(feesBegin)) {
                    strRangeObj.put("gte", feesBegin);
                }

                if (org.apache.commons.lang3.StringUtils.isNotBlank(feesEnd)) {
                    strRangeObj.put("lte", feesEnd);
                }

                strRangeQuery.put("range", new JSONObject().fluentPut("FEES.str", strRangeObj));
                shouldArray.add(strRangeQuery);
            }

            // 如果有足够的查询条件，添加bool查询
            if (shouldArray.size() > 0) {
                boolQuery.put("bool", new JSONObject()
                        .fluentPut("should", shouldArray)
                        .fluentPut("minimum_should_match", 1));

                mustArray.add(boolQuery);

                logger.info("[tariffRecordListNew] 最终费用范围查询条件: {}", boolQuery.toJSONString());
            }
        }

        // 适用范围
        String applicablePeople = param.getString("applicablePeople");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(applicablePeople)) {
            JSONObject applicablePeopleMatch = new JSONObject();
            applicablePeopleMatch.put("wildcard", new JSONObject().fluentPut("APPLICABLE_PEOPLE.keyword", "*" + applicablePeople + "*"));
            mustArray.add(applicablePeopleMatch);
        }

        // 退订方式
        String unsubscribe = param.getString("unsubscribe");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(unsubscribe)) {
            JSONObject unsubscribeMatch = new JSONObject();
            unsubscribeMatch.put("wildcard", new JSONObject().fluentPut("UNSUBSCRIBE.keyword", "*" + unsubscribe + "*"));
            mustArray.add(unsubscribeMatch);
        }

        // 违约责任
        String responsibility = param.getString("responsibility");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(responsibility)) {
            JSONObject responsibilityMatch = new JSONObject();
            responsibilityMatch.put("wildcard", new JSONObject().fluentPut("RESPONSIBILITY.keyword", "*" + responsibility + "*"));
            mustArray.add(responsibilityMatch);
        }

        // 其他服务内容
        String otherContent = param.getString("otherContent");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(otherContent)) {
            JSONObject otherContentMatch = new JSONObject();
            otherContentMatch.put("wildcard", new JSONObject().fluentPut("OTHER_CONTENT.keyword", "*" + otherContent + "*"));
            mustArray.add(otherContentMatch);
        }

        // 其他事项
        String others = param.getString("others");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(others)) {
            JSONObject othersMatch = new JSONObject();
            othersMatch.put("wildcard", new JSONObject().fluentPut("OTHERS.keyword", "*" + others + "*"));
            mustArray.add(othersMatch);
        }

        // 销售渠道
        String channel = param.getString("channel");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(channel)) {
            JSONObject channelMatch = new JSONObject();
            channelMatch.put("wildcard", new JSONObject().fluentPut("CHANNEL.keyword", "*" + channel + "*"));
            mustArray.add(channelMatch);
        }

        // 是否通信类
        JSONArray is_telecom = param.getJSONArray("is_telecom");
        if (is_telecom != null && !is_telecom.isEmpty() && org.apache.commons.lang3.StringUtils.isNotBlank(is_telecom.getString(0))) {
            JSONObject isTelecomMatch = new JSONObject();
            isTelecomMatch.put("terms", new JSONObject().fluentPut("IS_TELECOM", is_telecom));
            mustArray.add(isTelecomMatch);
        }

        // 超出资费
        String extra_fees = param.getString("extra_fees");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(extra_fees)) {
            JSONObject extraFeesMatch = new JSONObject();
            extraFeesMatch.put("wildcard", new JSONObject().fluentPut("EXTRA_FEES.keyword", "*" + extra_fees + "*"));
            mustArray.add(extraFeesMatch);
        }

        // 其他费用
        String other_fees = param.getString("other_fees");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(other_fees)) {
            JSONObject otherFeesMatch = new JSONObject();
            otherFeesMatch.put("wildcard", new JSONObject().fluentPut("OTHER_FEES.keyword", "*" + other_fees + "*"));
            mustArray.add(otherFeesMatch);
        }

        // 一级分类
        String type1 = param.getString("type1");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(type1)) {
            JSONObject type1Query = new JSONObject();
            JSONArray type1OrArray = new JSONArray();

            for (String code : type1.split(",")) {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(code)) {
                    JSONObject typeMatch = new JSONObject();
                    typeMatch.put("term", new JSONObject().fluentPut("TYPE1", code));
                    type1OrArray.add(typeMatch);
                }
            }

            type1Query.put("bool", new JSONObject().fluentPut("should", type1OrArray).fluentPut("minimum_should_match", 1));
            mustArray.add(type1Query);
        }

        // 二级分类
        String type2 = param.getString("type2");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(type2)) {
            JSONObject type2Query = new JSONObject();
            JSONArray type2OrArray = new JSONArray();

            for (String code : type2.split(",")) {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(code)) {
                    JSONObject typeMatch = new JSONObject();
                    typeMatch.put("term", new JSONObject().fluentPut("TYPE2", code));
                    type2OrArray.add(typeMatch);
                }
            }

            type2Query.put("bool", new JSONObject().fluentPut("should", type2OrArray).fluentPut("minimum_should_match", 1));
            mustArray.add(type2Query);
        }

        // 资费单位
        String fees_unit = param.getString("fees_unit");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(fees_unit)) {
            JSONObject feesUnitMatch = new JSONObject();
            feesUnitMatch.put("wildcard", new JSONObject().fluentPut("FEES_UNIT.keyword", "*" + fees_unit + "*"));
            mustArray.add(feesUnitMatch);
        }

        // 有效期限
        String valid_period = param.getString("valid_period");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(valid_period)) {
            JSONObject validPeriodMatch = new JSONObject();
            validPeriodMatch.put("wildcard", new JSONObject().fluentPut("VALID_PERIOD.keyword", "*" + valid_period + "*"));
            mustArray.add(validPeriodMatch);
        }

        // 在网要求
        String duration = param.getString("duration");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(duration)) {
            JSONObject durationMatch = new JSONObject();
            durationMatch.put("wildcard", new JSONObject().fluentPut("DURATION.keyword", "*" + duration + "*"));
            mustArray.add(durationMatch);
        }

        // 别名
        String tariff_another_name = param.getString("tariff_another_name");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(tariff_another_name)) {
            JSONObject anotherNameMatch = new JSONObject();
            anotherNameMatch.put("wildcard", new JSONObject().fluentPut("TARIFF_ANOTHER_NAME.keyword", "*" + tariff_another_name + "*"));
            mustArray.add(anotherNameMatch);
        }

        // 版本号
        String versionNum = param.getString("versionNum");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(versionNum)) {
            if ("0".equals(versionNum)) {
                JSONObject versionNumMatch = new JSONObject();
                versionNumMatch.put("term", new JSONObject().fluentPut("VERSION_NUM.keyword", "V0"));
                mustArray.add(versionNumMatch);
            } else {
                // 不等于V0的版本号
                JSONObject versionNumNotMatch = new JSONObject();
                versionNumNotMatch.put("bool", new JSONObject().fluentPut("must_not",
                        new JSONArray().fluentAdd(new JSONObject().fluentPut("term",
                                new JSONObject().fluentPut("VERSION_NUM.keyword", "V0")))));
                mustArray.add(versionNumNotMatch);
            }
        }

        // 上线日期
        String onlineDayStart = param.getString("onlineDayStart");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(onlineDayStart)) {
            JSONObject onlineDayStartMatch = new JSONObject();
            onlineDayStartMatch.put("range", new JSONObject().fluentPut("ONLINE_DAY",
                    new JSONObject().fluentPut("gte", onlineDayStart.replaceAll("-", ""))));
            mustArray.add(onlineDayStartMatch);
        }

        String onlineDayEnd = param.getString("onlineDayEnd");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(onlineDayEnd)) {
            JSONObject onlineDayEndMatch = new JSONObject();
            onlineDayEndMatch.put("range", new JSONObject().fluentPut("ONLINE_DAY",
                    new JSONObject().fluentPut("lte", onlineDayEnd.replaceAll("-", ""))));
            mustArray.add(onlineDayEndMatch);
        }

        // 下线日期
        String offlineDayStart = param.getString("offlineDayStart");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(offlineDayStart)) {
            JSONObject offlineDayStartMatch = new JSONObject();
            offlineDayStartMatch.put("range", new JSONObject().fluentPut("OFFLINE_DAY",
                    new JSONObject().fluentPut("gte", offlineDayStart.replaceAll("-", ""))));
            mustArray.add(offlineDayStartMatch);
        }

        String offlineDayEnd = param.getString("offlineDayEnd");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(offlineDayEnd)) {
            JSONObject offlineDayEndMatch = new JSONObject();
            offlineDayEndMatch.put("range", new JSONObject().fluentPut("OFFLINE_DAY",
                    new JSONObject().fluentPut("lte", offlineDayEnd.replaceAll("-", ""))));
            mustArray.add(offlineDayEndMatch);
        }

        // 创建时间
        String createTime = param.getString("createTime");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(createTime) && createTime.contains("~")) {
            String[] createTimeArray = createTime.split("~");
            if (createTimeArray.length == 2) {
                JSONObject createTimeStartMatch = new JSONObject();
                createTimeStartMatch.put("range", new JSONObject().fluentPut("CREATE_TIME.keyword",
                        new JSONObject().fluentPut("gte", createTimeArray[0])));
                mustArray.add(createTimeStartMatch);

                JSONObject createTimeEndMatch = new JSONObject();
                createTimeEndMatch.put("range", new JSONObject().fluentPut("CREATE_TIME.keyword",
                        new JSONObject().fluentPut("lte", createTimeArray[1])));
                mustArray.add(createTimeEndMatch);
            }
        }

        // 修改时间
        String updateTime = param.getString("updateTime");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(updateTime) && updateTime.contains("~")) {
            String[] updateTimeArray = updateTime.split("~");
            if (updateTimeArray.length == 2) {
                JSONObject updateTimeStartMatch = new JSONObject();
                updateTimeStartMatch.put("range", new JSONObject().fluentPut("UPDATE_TIME.keyword",
                        new JSONObject().fluentPut("gte", updateTimeArray[0])));
                mustArray.add(updateTimeStartMatch);

                JSONObject updateTimeEndMatch = new JSONObject();
                updateTimeEndMatch.put("range", new JSONObject().fluentPut("UPDATE_TIME.keyword",
                        new JSONObject().fluentPut("lte", updateTimeArray[1])));
                mustArray.add(updateTimeEndMatch);
            }
        }

        // 资费规则核查时间
        String fieldCheckTime = param.getString("fieldCheckTime");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(fieldCheckTime) && fieldCheckTime.contains("~")) {
            String[] fieldCheckTimeArray = fieldCheckTime.split("~");
            if (fieldCheckTimeArray.length == 2) {
                JSONObject fieldCheckTimeStartMatch = new JSONObject();
                fieldCheckTimeStartMatch.put("range", new JSONObject().fluentPut("FIELD_CHECK_TIME.keyword",
                        new JSONObject().fluentPut("gte", fieldCheckTimeArray[0])));
                mustArray.add(fieldCheckTimeStartMatch);

                JSONObject fieldCheckTimeEndMatch = new JSONObject();
                fieldCheckTimeEndMatch.put("range", new JSONObject().fluentPut("FIELD_CHECK_TIME.keyword",
                        new JSONObject().fluentPut("lte", fieldCheckTimeArray[1])));
                mustArray.add(fieldCheckTimeEndMatch);
            }
        }

        // 资费字段编号
        String fieldCheckNo = param.getString("fieldCheckNo");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(fieldCheckNo)) {
            JSONArray fileCheckNoArray = JSONArray.parseArray("[" + fieldCheckNo + "]");
            mustArray.add(new JSONObject()
                    .fluentPut("terms", new JSONObject().fluentPut("FIELD_CHECK_NOS.keyword", fileCheckNoArray)));
        }
    }

    private JSONObject buildFeesRegexQuery(String feesBegin, String feesEnd) {
        try {
            if (org.apache.commons.lang3.StringUtils.isBlank(feesBegin) && org.apache.commons.lang3.StringUtils.isBlank(feesEnd)) {
                return null;
            }

            // 精确匹配场景（范围首尾相同）
            if (org.apache.commons.lang3.StringUtils.isNotBlank(feesBegin) && org.apache.commons.lang3.StringUtils.isNotBlank(feesEnd) && feesBegin.equals(feesEnd)) {
                // 对于精确匹配，我们有两种情况需要处理：
                // 1. 精确数值匹配 (如 "20")
                // 2. 带小数点的匹配 (如 "20.0")

                // 如果输入没有小数点，尝试匹配原始输入和带.0的形式
                if (!feesBegin.contains(".")) {
                    JSONObject exactTermQuery = new JSONObject();
                    exactTermQuery.put("terms", new JSONObject()
                            .fluentPut("FEES.str", new JSONArray()
                                    .fluentAdd(feesBegin)
                                    .fluentAdd(feesBegin + ".0")));
                    return exactTermQuery;
                } else {
                    // 有小数点的情况，精确匹配
                    JSONObject termQuery = new JSONObject();
                    JSONObject termFields = new JSONObject();
                    termFields.put("FEES.str", feesBegin);
                    termQuery.put("term", termFields);
                    return termQuery;
                }
            }

            // 范围查询，优先使用数值范围查询
            try {
                JSONObject rangeQuery = new JSONObject();
                JSONObject rangeFields = new JSONObject();

                if (org.apache.commons.lang3.StringUtils.isNotBlank(feesBegin)) {
                    rangeFields.put("gte", Double.parseDouble(feesBegin));
                }

                if (org.apache.commons.lang3.StringUtils.isNotBlank(feesEnd)) {
                    rangeFields.put("lte", Double.parseDouble(feesEnd));
                }

                rangeQuery.put("range", new JSONObject().fluentPut("FEES", rangeFields));
                return rangeQuery;
            } catch (NumberFormatException nfe) {
                // 如果数值解析失败，使用字符串范围查询
                JSONObject strRangeQuery = new JSONObject();
                JSONObject strRangeObj = new JSONObject();

                if (org.apache.commons.lang3.StringUtils.isNotBlank(feesBegin)) {
                    strRangeObj.put("gte", feesBegin);
                }

                if (org.apache.commons.lang3.StringUtils.isNotBlank(feesEnd)) {
                    strRangeObj.put("lte", feesEnd);
                }

                strRangeQuery.put("range", new JSONObject().fluentPut("FEES.str", strRangeObj));
                return strRangeQuery;
            }
        } catch (Exception e) {
            logger.error("[buildFeesRegexQuery] 构建FEES查询条件时出错: {}", e.getMessage());
            return null;
        }
    }


    /**
     * 从ES获取数据 - 作为备用方法
     *
     * 此方法保留仅供参考和紧急情况下使用，但不再是主要导出逻辑的一部分
     * 主要导出逻辑已改为直接流式处理
     */
    private List<JSONObject> queryDataFromEs(JSONObject param) {
        try {
            // 构建ES查询参数的基础部分（不包含分页）
            JSONObject baseQueryParams = buildEsQueryParams(param);

            // 存储所有查询结果
            List<JSONObject> allDataList = new ArrayList<>();

            // 设置初始查询参数
            int pageSize = 1000; // 每页大小
            int batchCount = 0;  // 批次计数
            long totalCount = 0; // 总记录数
            long maxBatches = 500; // 最大批次限制，防止无限循环
            Object[] searchAfterValues = null; // 用于search_after分页
            boolean hasMore = true;

            logger.info("备用方法：开始使用search_after查询ES数据，每批大小: {}", pageSize);

            // 使用ElasticsearchKit
            String indexName = Constants.XTY_TARIFF_BAK_INFO_INDEX;

            while (hasMore && batchCount < maxBatches) {
                // 设置查询参数
                JSONObject searchParams = new JSONObject(baseQueryParams);
                searchParams.put("size", pageSize);

                // 添加排序条件（必须有排序才能使用search_after）
                JSONArray sortArray = new JSONArray();
                // 使用_id字段排序以确保记录唯一性
                sortArray.add(new JSONObject().fluentPut("_id", new JSONObject().fluentPut("order", "asc")));
                searchParams.put("sort", sortArray);

                // 从第二批开始使用search_after
                if (searchAfterValues != null) {
                    searchParams.put("search_after", searchAfterValues);
                }

                logger.info("查询第 {} 批，size={}, search_after={}",
                        (batchCount + 1), pageSize,
                        searchAfterValues != null ? Arrays.toString(searchAfterValues) : "null");

                // 执行查询
                JSONObject result = com.yunqu.xty.commonex.kit.ElasticsearchKit.search(indexName, searchParams);

                if (result == null || !result.containsKey("hits")) {
                    logger.warn("第 {} 批查询失败或返回为空", (batchCount + 1));
                    break;
                }

                // 获取命中结果
                JSONObject hitsObj = result.getJSONObject("hits");
                JSONArray hits = hitsObj.getJSONArray("hits");

                // 第一批时获取总记录数
                if (batchCount == 0 && hitsObj.containsKey("total")) {
                    JSONObject totalObj = hitsObj.getJSONObject("total");
                    if (totalObj != null && totalObj.containsKey("value")) {
                        totalCount = totalObj.getLongValue("value");
                        // 计算实际预计批次数
                        maxBatches = Math.min(maxBatches, (long) Math.ceil((double) totalCount / pageSize));
                        logger.info("查询到符合条件的总记录数: {}, 预计需要 {} 批", totalCount, maxBatches);
                    }
                }

                // 没有更多结果时退出循环
                if (hits == null || hits.isEmpty()) {
                    logger.info("第 {} 批无数据，结束查询", (batchCount + 1));
                    hasMore = false;
                    break;
                }

                // 处理本批结果
                int batchSize = hits.size();

                // 获取最后一条记录的sort值，用于下一批的search_after
                if (batchSize > 0) {
                    JSONObject lastHit = hits.getJSONObject(batchSize - 1);
                    JSONArray sortValues = lastHit.getJSONArray("sort");
                    if (sortValues != null && !sortValues.isEmpty()) {
                        searchAfterValues = new Object[sortValues.size()];
                        for (int i = 0; i < sortValues.size(); i++) {
                            searchAfterValues[i] = sortValues.get(i);
                        }
                    } else {
                        logger.warn("未找到sort值，无法继续分页");
                        hasMore = false;
                    }
                }

                // 转换ES结果为数据列表并添加到总结果中
                for (int i = 0; i < batchSize; i++) {
                    JSONObject hit = hits.getJSONObject(i);
                    JSONObject source = hit.getJSONObject("_source");
                    allDataList.add(source);
                }

                logger.info("第 {} 批查询完成，获取到 {} 条记录，当前总记录数: {}, 完成率: {}%",
                        (batchCount + 1), batchSize, allDataList.size(),
                        totalCount > 0 ? String.format("%.2f", (allDataList.size() * 100.0 / totalCount)) : "未知");

                // 如果本批结果少于页大小，说明已经没有更多结果
                if (batchSize < pageSize) {
                    logger.info("数据已全部获取，结束查询");
                    hasMore = false;
                }

                // 准备获取下一批
                batchCount++;

                // 避免过快请求导致服务器压力过大
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    // 忽略
                }

                // 定期执行GC以防止内存溢出
                if (batchCount % 10 == 0) {
                    logger.info("执行内存回收...");
                    System.gc();
                }
            }

            // 检查是否因为最大批次限制而停止
            if (batchCount >= maxBatches && hasMore) {
                logger.warn("达到最大批次限制 ({})，可能还有更多数据未获取", maxBatches);
            }

            logger.info("ES数据查询完成，共获取{}条记录，查询了{}批", allDataList.size(), batchCount);
            return allDataList;
        } catch (Exception e) {
            logger.error("从ES获取数据失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 判断字符串是否只包含英文字母、下划线和逗号
     *
     * @param input 要验证的字符串
     * @return 如果字符串只包含英文字母、下划线和逗号，则返回true；否则返回false
     */
    public static boolean isValidString(String input) {
        if (input == null || input.isEmpty()) {
            return false;
        }
        // 修复正则表达式，匹配英文字母、数字、下划线和逗号
        String regex = "^[a-zA-Z0-9_,]+$";
        return input.matches(regex);
    }

}
