package com.yunqu.handle.base;

import org.easitline.common.core.context.AppContext;

/**
 * <p>
 *    常量类
 * </p>
 *
 * @ClassName Constants
 * <AUTHOR> Copy This Tag)
 * @Description 常量类
 * @Since create in 2024/5/14 15:18
 * @Version v1.0
 * @Copyright Copyright (c) 2024
 * @Company 广州云趣信息科技有限公司
 */
public class Constants {

    public static final String APP_NAME = "cx-mix-handle";

    public final static String DS_WIRTE_NAME_ONE = "yc-wirte-ds-1";     //默认数据源名称(写)

    public final static String DS_WIRTE_NAME_TWO = "yc-wirte-ds-2";     //默认数据源名称(写)

    public final static String DS_READ_NAME= "yc-read-ds";     //默认数据源名称(读)

    public static AppContext context = AppContext.getContext("cx-mix-handle");

    /*
     * 附件未同步
     */
    public static final String ATTACHMENT_IS_NOT_SYSN = "01";
    /*
     * 附件已同步
     */
    public static final String ATTACHMENT_IS_SYSN = "02";


    /**
     * 获取系统密钥
     * @return 系统密钥
     */
    public static String getSysSecretKey() {
        try {
            return context.getProperty("OUT_APP_SECRET", "default-secret-key-123456");
        } catch (Exception e) {
            return "default-secret-key-123456";
        }
    }
    
    /**
     * 获取应用ID
     * @return 应用ID
     */
    public static String getAppId() {
        try {
            return context.getProperty("OUT_APP_ID", "appeal");
        } catch (Exception e) {
            return "handle-system";
        }
    }

    public static String getOutInterface() {
        try {
            return context.getProperty("OUT_APP_URL", "http://localhost:8080/web-public");
        } catch (Exception e) {
            return "http://localhost:8080/web-public";
        }
    }
    
    /**
     * 获取配置属性
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static String getProperty(String key, String defaultValue) {
        try {
            return context.getProperty(key, defaultValue);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    
    public static String getSysSchema() {
        return context.getProperty("SYS_SCHEMA","ycbusi_ekf");
    }

    public static String getEntId() {
        return context.getProperty("ENT_ID","1000");
    }

    public static String getBusiOrderId() {
        return context.getProperty("BUSI_ORDER_ID","");
    }

    // WARN_PHONE
    public static String getWarnPhone() {
        return context.getProperty("WARN_PHONE","");
    }

    //IS_SYNC
    public static boolean getIsSync() {
        return "01".equals(context.getProperty("IS_SYNC","01"));
    }




}
