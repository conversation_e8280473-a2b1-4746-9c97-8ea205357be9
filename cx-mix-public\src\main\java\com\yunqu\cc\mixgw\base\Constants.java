package com.yunqu.cc.mixgw.base;

import java.io.File;

import org.easitline.common.core.context.AppContext;

/**
 * 常量
 * <AUTHOR>
 *
 */
public class Constants {

	public final static String DS_WIRTE_NAME_ONE = "yc-wirte-ds-1";     //默认数据源名称(写)

	public final static String DS_WIRTE_NAME_TWO = "yc-wirte-ds-2";     //默认数据源名称(写)

	public final static String DS_READ_NAME= "yc-read-ds";     //默认数据源名称(读)

	public final static String APP_NAME = "cx-mix-public";     //应用

	/** 企业呼叫中心订购ID */
	public final static String BUSI_ID_007 = "007";

    private static AppContext context = AppContext.getContext("yc-api");

	private static AppContext cxContext = AppContext.getContext(APP_NAME);



	public static String ROOT_PATH= "/home"+File.separator ;

	public static String BASE_PATH= "cx-public" + File.separator;

	public static String BASE_COPY_PATH= "cx-publicCopy" + File.separator;

	/**申诉来源-网络*/
	public static String APPEAL_SOURCE_03="03";

	public static String XTY_ORDER_FILE="XTY_ORDER_FILE_";

	public static String CLIENT_TITLE="电信用户申诉受理中心";

	/**默认文件上传类型*/
	public static final String DEFAULT_FILE_TYPE="jpg;jpeg;xls;txt;word;mp4;png;xlsx;docx;zip;pdf;AVI;FLV;mov;";

	/**错误返回500*/
	public static final String ERROR_MSG_500="500";
	/**成功返回200*/
	public static final String SUCC_CODE_200="200";
	/**状态码成功返回1*/
	public static final String SUCC_STATE_1="1";
	/**状态码失败返回0*/
	public static final String ERROR_STATE_0="0";

	/**状态码操作频繁*/
	public static final String WARN_STATE_99="99";

	/**
	 * 统计库名称
	 */
	public static String getStatSchema(){
		return context.getProperty("STAT_DB","stat");
	}
	/**
	public static String getPassword(){
		return baseContext.getProperty("SERVICE_INTERFACE_PWD", "yq_85521717");
	}**/

	/**心跳缓存名称*/
	public final static String HEART_BEAT_CACHE_NAME = "HEART_BEAT_CACHE";

	/**查询类缓存名称*/
	public final static String CACHE_NAME_SELECT = "CACHE_NAME_SELECT";

	/**操作类缓存名称*/
	public final static String CACHE_NAME_OPTION = "CACHE_NAME_OPTION";

	/**公众端配置缓存名称*/
	public final static String CLIENT_CONFIG_CACHE_KEY = "CLIENT_CONFIG_CACHE_KEY";

	/**只是新增key**/
	public final static String ADD_NAME="ADD";

	/**只是查询key**/
	public final static String SELECT_NAME="SELECT";

	/**只是保存验证码key**/
	public final static String CODE_NAME="CODE";

	/**
	 * 部门组类型：99-部门
	 */
	public static final String GROUP_TYPE_DEPT = "99";

	/**
	 * 部门组类型：990101-工信部信管局
	 */
	public static final String GROUP_TYPE_B_XGJ = "990101";

	/**
	 * 部门组类型：990102-工信部申诉中心
	 */
	public static final String GROUP_TYPE_B_SSC = "990102";

	/**
	 * 部门组类型：990103-信通院
	 */
	public static final String GROUP_TYPE_B_XTY = "990103";

	/**
	 * 部门组类型：99010401-集团-电信
	 */
	public static final String GROUP_TYPE_GE_TELECOM = "99010401";

	/**
	 * 部门组类型：99010402-集团-移动
	 */
	public static final String GROUP_TYPE_GE_MOBILE = "99010402";

	/**
	 * 部门组类型：99010403-集团-联通
	 */
	public static final String GROUP_TYPE_GE_UNICOM = "99010403";

	/**
	 * 部门组类型：99010404-集团-广电
	 */
	public static final String GROUP_TYPE_GE_BROAD = "99010404";

	/**
	 * 部门组类型：990201-省管局
	 */
	public static final String GROUP_TYPE_P_SGJ = "990201";

	/**
	 * 部门组类型：990202-省申诉中心
	 */
	public static final String GROUP_TYPE_P_SCC = "990202";

	/**
	 * 部门组类型：990203-省企业
	 */
	public static final String GROUP_TYPE_PE = "990203";
	/**
	 * 部门组类型：99020301-省企业-电信
	 */
	public static final String GROUP_TYPE_PE_TELECOM= "99020301";

	/**
	 * 部门组类型：99020302-省企业-移动
	 */
	public static final String GROUP_TYPE_PE_MOBILE = "99020302";

	/**
	 * 部门组类型：99020303-	省企业-联通
	 */
	public static final String GROUP_TYPE_PE_UNICOM = "99020303";

	/**
	 * 部门组类型：99020304-	省企业-广电
	 */
	public static final String GROUP_TYPE_PE_BROAD = "99020304";

	/**
	 * 部门组类型：99020305-	省企业-本地
	 */
	public static final String GROUP_TYPE_PE_LOCAL = "99020305";

	/**
	 * 企业，电信001、移动002、联通003、广电004
	 */
	public static final String ENT_TYPE_001 = "1";
	public static final String ENT_TYPE_002 = "2";
	public static final String ENT_TYPE_003 = "3";
	public static final String ENT_TYPE_004 = "5";

	/**
	 * 资费状态，1-正常 2-已删除
	 */
	public static final String TARIFF_STATUS_1 = "1";
	public static final String TARIFF_STATUS_2 = "2";

	//请求的消息队列名称
	public static String getCustServiceBroker(){
		return cxContext.getProperty("CUST_SERVICE_BROKER","");
	}


	/**三要素验证接口地址*/
	public static String aAuthenticationAddress() {
		return cxContext.getProperty("AUTHENTICATION_ADDRESS", "");
	}

	/**归属地验证接口地址*/
	public static String placeAuthenticationAddress() {
		return cxContext.getProperty("PLACE_AUTHENTICATION_ADDRESS", "");
	}

	/**三要素归属地等验证接口用户id*/
	public static String aAuthenticationUserId() {
		return cxContext.getProperty("AUTHENTICATION_USERID", "");
	}

	/**三要素归属地SecretKey*/
	public static String aAuthenticationSecretKey() {
		return cxContext.getProperty("AUTHENTICATION_SECRET_KEY", "");
	}

	public static String getSmsServiceBroker(){
		return cxContext.getProperty("SMS_SERVICE_BROKER","");
	}

	public static String getTranServiceBroker(){
		return cxContext.getProperty("TRAN_SERVICE_BROKER","");
	}

	public static String getTraiffAuditLoadBroker(){
		return cxContext.getProperty("TRAIFF_AUDIT_LOAD_BROKER","");
	}

	public static String getTraiffBakBroker(){
		return cxContext.getProperty("TRAIFF_BAK_BROKER","");
	}

	public static String getTraiffReqBakBroker(){
		return cxContext.getProperty("TRAIFF_REQ_BAK_BROKER","");
	}

	public static String getTraiffOrderQueryBroker(){
		return cxContext.getProperty("TRAIFF_ORDER_QUERY_BROKER","");
	}
	public static String getTraiffDelMsgBroker(){
		return cxContext.getProperty("TRAIFF_DEL_MSG_BROKER","");
	}

	public static String getTraiffExeMessageBroker(){
		return cxContext.getProperty("TRAIFF_EXE_MESSAGE_BROKER","");
	}

	public static String getTraiffExeAuditMessageBroker(){
		return cxContext.getProperty("TRAIFF_EXE_AUDIT_MESSAGE_BROKER","");
	}

	public static String getTraiffOrderQueryUrl(){
		return cxContext.getProperty("TRAIFF_ORDER_QUERY_URL","");
	}

	public static String getTraiffOrderStatFileEncoding(){
		return cxContext.getProperty("TARIFF_STAT_FILE_ENCODING","UTF-8");
	}

	/**
	 * 携转系统平台解密KEY
	 */
	public static String getTariffOrderQuerySys(){
		return cxContext.getProperty("TRAIFF_ORDER_QUERY_KEY","");
	}

	//定时任务保存的服务名称
	public static String getTaskBroker(){
		return cxContext.getProperty("TASK_BROKER","");
	}

	//http转发服务名称
	public static String getReqForwardBroker(){
		return cxContext.getProperty("REQ_FORWARD_BROKER","");
	}

	public static String getPhoneVerifyBroker() {
		return cxContext.getProperty("PHONE_VERIFY_BROKER", "");
	}





	/**结果队列名称
	public static String getCustServerResultBroker() {
		return cxContext.getProperty("CUST_SERVER_RESULT_BROKER", "");
	}**/


	public static String getEntId(){
		return cxContext.getProperty("ENT_ID","");
	}

	public static String getBusiOrderId(){
		return cxContext.getProperty("BUSI_ORDER_ID","");
	}

	/**
	 * 携转系统地址
	 */
	public static String getTranSystemUrl(){
		return cxContext.getProperty("TRAN_SYS_URL","http://192.168.190.10:8083/applyflow/applyFlowQuery");
	}

	/**
	 * 携转系统平台名称
	 */
	public static String getTranSysPlatform(){
		return cxContext.getProperty("TRAN_SYS_PLATFORM","TPlatform");
	}

	/**
	 * 携转系统平台签名
	 */
	public static String getTranSysSign(){
		return cxContext.getProperty("TRAN_SYS_SIGN","%urQuBEmM4=F");
	}

	/**
	 * 携转系统平台解密KEY
	 */
	public static String getTranSysDe(){
		return cxContext.getProperty("TRAN_SYS_DE","D5A99CD51E0C0DC6D8C42966BB261D9A");
	}

	/**
	 * 附件上传格式
	 */
	public static String getAttachmentSuffix(){
		return cxContext.getProperty("ATTACHMENT_SUFFIX","");
	}


	/**
	 * 获取SFTP_IP SFTP服务器IP地址
	 * @return
	 */
	public static String getSftpIp() {
		return cxContext.getProperty("SFTP_IP", "");
	}
	/**
	 * 获取SFTP_PORT SFTP服务器端口
	 * @return
	 */
	public static String getSftpPort() {
		return cxContext.getProperty("SFTP_PORT", "");
	}
	/**
	 * 获取SFTP_USERNAME SFTP服务器登录用户名
	 * @return
	 */
	public static String getSftpUsername() {
		return cxContext.getProperty("SFTP_USERNAME", "");
	}
	/**
	 * 获取SFTP_PASSWORD SFTP服务器登录密码
	 * @return
	 */
	public static String getSftpPassword() {
		return cxContext.getProperty("SFTP_PASSWORD", "");
	}
	/**
	 * 获取SFTP_BASE_PATH SFTP服务器文件根目录
	 * @return
	 */
	public static String getSftpBasePath() {
		return cxContext.getProperty("SFTP_BASE_PATH", "");
	}
	/**
	 * 获取SFTP_BASE_PATH SFTP本地文件存放路径
	 * @return
	 */
	public static String getSftpLocalBasePath() {
		return cxContext.getProperty("SFTP_LOCAL_BASE_PATH", "");
	}

	/**
	 * 获取短信过期时间 XTY_SMS_MSG_OT
	 */
	public static int getXtySmsMsgOt() {
		return Integer.parseInt(cxContext.getProperty("XTY_SMS_MSG_OT", "2"));
	}

	/**
	 * 资费解析任务broker
	 */
	public static String TARIFF_INFO_ANALYSIS_BROKER = "TARIFF_INFO_ANALYSIS_BROKER";

	/**
	 * 资费语义分析文件存放路径
	 */
	public static String getTariffAnalysisBaseFolder() {
		return cxContext.getProperty("TARIFF_NLP_FILE_BASE_PATH", "");
	}

	public static final String ALARM_EMAIL_QUEUE = "ALARM_EMAIL_QUEUE";

    /**
	 * 携转任务文件上传至SFTP服务器的远程目录
	 */
    public static final String AUDIT_TASK_SFTP_REMOTE_BASE_FOLDER = AppContext.getContext(APP_NAME).getProperty("AUDIT_TASK_SFTP_REMOTE_BASE_FOLDER", "");

	public static final String REPORT_SFTP_REMOTE_BASE_FOLDER = AppContext.getContext(APP_NAME).getProperty("REPORT_SFTP_REMOTE_BASE_FOLDER", "");
}
