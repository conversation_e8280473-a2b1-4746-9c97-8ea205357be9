package com.yunqu.xty.inf;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.IBaseService;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.util.*;
import com.yunqu.xty.base.CommonLogger;
import com.yunqu.xty.base.Constants;
import com.yunqu.xty.base.QueryFactory;
import com.yunqu.xty.excuotr.EventDispatcher;
import com.yunqu.xty.service.DutyOrderExService;
import com.yunqu.xty.service.OrderProvinceCfgService;
import com.yunqu.xty.service.OrderTimeService;
import com.yunqu.xty.strategy.allocation.AllocationEnum;
import com.yunqu.xty.strategy.allocation.AllocationStrategy;
import com.yunqu.xty.utils.PhoneCryptor;
import com.yunqu.xty.utils.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

public class FixDutyService extends IBaseService{

	private Logger logger = LoggerFactory.getLogger(CommonLogger.getLogger("duty-service").getName());

	private Logger repairLogger =  LoggerFactory.getLogger(CommonLogger.getLogger("extend-repiar").getName());

	@Override
	public String getServiceId() {
		return "FIX_DUTY_SERVICE";
	}

	@Override
	public String getName() {
		return "定责服务类";
	}

	@Override
	public JSONObject invokeMethod(JSONObject json) throws ServiceException {
		String command = json.getString("command");
		if(StringUtils.equals(command, "addOrder")) {
			return addOrder(json);
		} else if(StringUtils.equals(command, "execProvinceFixSample")) {
			return execProvinceFixSample(json);
		} else if(StringUtils.equals(command, "execSpecializedFixSample")) {
			return execSpecializedFixSample(json,true);
		} else if(StringUtils.equals(command, "extractJudge")) {
			return extractJudge(json);
		}
		return null;
	}

	/**
	 * 添加定责工单
	 */
	private JSONObject addOrder(JSONObject json) {
		try {
			// 1. 校验并获取工单信息
			DutyOrderContext context = validateAndGetOrderInfo(json);
			if(!context.isValid()) {
				return getFailJson(context.getErrorMsg());
			}

			// 2. 创建或更新定责工单
			String dutyOrderId = createOrUpdateDutyOrder(context);
			
			// 3. 创建任务
			boolean success = createDutyTask(context, dutyOrderId);
			if(!success) {
				return getFailJson("创建任务失败");
			}

			// 4. 处理自动核验
			handleAutoVerify(context, dutyOrderId);
			
			return getSuccJson();
		} catch (Exception e) {
			logger.error("定责任务创建异常:" + e.getMessage(), e);
			return getFailJson(e.getMessage());
		}
	}

	/**
	 * 校验并获取工单信息
	 */
	private DutyOrderContext validateAndGetOrderInfo(JSONObject json) throws Exception {
		DutyOrderContext context = new DutyOrderContext();
		String orderId = json.getString("orderId");
		String schema = json.getString("schema");
		
		// 检查工单是否已存在
		synchronized (orderId.intern()) {
			JSONObject duty = this.getQuery().queryForRow(
				"select * from " + schema + ".C_BOX_DUTY_ORDER where P_ID = ?", 
				new String[]{orderId}, 
				new JSONMapperImpl()
			);
			
			if(Objects.nonNull(duty)) {
				String fixStatus = duty.getString("FIX_STATUS");
				if (Constants.AFFIRM_STATUS_10.equals(fixStatus)) {
					context.setInsert(false);
					context.setDutyOrder(duty);
				} else {
					context.setValid(false);
					context.setErrorMsg("已生成定责工单，无法重复生成");
					return context;
				}
			} else {
				context.setInsert(true);
			}
		}
		
		// 获取工单基本信息
		JSONObject orderInfo = getOrderBaseInfo(schema, orderId);
		if(orderInfo == null) {
			context.setValid(false);
			context.setErrorMsg("未找到工单信息");
			return context;
		}
		
		// 设置上下文信息
		context.setOrderInfo(orderInfo);
		context.setSchema(schema);
		context.setOrderId(orderId);
		context.setEntId(json.getString("entId"));
		context.setBusiOrderId(json.getString("busiOrderId")); 
		context.setCurrentTime(DateUtil.getCurrentDateStr());

		String entType = orderInfo.getString("ENT_TYPE");
		String groupType = Constants.getGroupTypeByEntType(entType);
		if (StringUtils.isNotBlank(groupType)) {
			JSONObject entConfig = DutyUtil.getEntConfig(groupType, schema);
			if (entConfig != null && entConfig.containsKey("CONFIG_JSON")) {
				JSONObject configJson = entConfig.getJSONObject("CONFIG_JSON");
				if (Constants.MAGIC_01.equals(configJson.getString("USE_NEW_PROCESS"))) {
					context.setNewProcess("1");
				}
			}
		}
		
		// 校验申诉时间
		String appealTime = orderInfo.getString("APPEAL_TIME");
		if (DateUtil.compareDate(Constants.getStartDutyDate(), appealTime, DateUtil.TIME_FORMAT) > 0) {
			context.setValid(false);
			context.setErrorMsg("当前日期不生成定责");
			return context;
		}
		
		context.setValid(true);
		return context;
	}

	/**
	 * 获取工单基本信息
	 */
	private JSONObject getOrderBaseInfo(String schema, String orderId) throws Exception {
		EasySQL sql = new EasySQL("select t1.ORDER_NO,t2.PROVINCE_CODE,t2.CITY_CODE,t2.ENT_TYPE,t2.ENT_DEPT_CODE," +
				"t2.ENT_DEPT_NAME,t2.CLASS_CODE1,t2.CLASS_CODE2,t2.CLASS_CODE3,t2.ACCEPT_TIME,t2.APPEAL_TIME," +
				"t2.COMPLETE_TIME,t2.APPEAL_PHONE,t2.APPEAL_NAME ");
		sql.append("from " + schema + ".C_BO_BASE_ORDER t1");
		sql.append("left join " + schema + ".C_BOX_APPEAL_ORDER t2 on t1.ID = t2.M_ID");
		sql.append(orderId, "where t1.ID = ?");
		
		return this.getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
	}

	/**
	 * 创建或更新定责工单
	 */
	private String createOrUpdateDutyOrder(DutyOrderContext context) throws Exception {
		String dutyOrderId;
		EasyRecord record = new EasyRecord(context.getSchema() + ".C_BOX_DUTY_ORDER", "ID");
		JSONObject nodeInfo = getDutyNodeInfo(context);
		if (context.isInsert()) {
			dutyOrderId = createNewDutyOrder(context, record, nodeInfo);
			// 插入拓展表数据
			DutyOrderExService.addDutyRunEx(this.getQuery(), context.getSchema(), dutyOrderId);
		} else {
			dutyOrderId = updateExistingDutyOrder(context, record, nodeInfo);
		}
		
		return dutyOrderId;
	}

	/**
	 * 创建新定责工单
	 */
	private String createNewDutyOrder(DutyOrderContext context, EasyRecord record,JSONObject nodeJson) throws Exception {
		String dutyOrderId = RandomKit.randomStr();
		JSONObject orderInfo = context.getOrderInfo();
		record.put("ID", dutyOrderId);
		record.put("P_ID", context.getOrderId());
		record.put("P_ORDER_NO", orderInfo.getString("ORDER_NO"));
		record.put("ENT_ID", context.getEntId());
		record.put("BUSI_ORDER_ID", context.getBusiOrderId());
		record.put("ENT_TYPE", orderInfo.getString("ENT_TYPE"));
		record.put("ENT_DEPT_CODE", orderInfo.getString("ENT_DEPT_CODE"));
		record.put("ENT_DEPT_NAME", orderInfo.getString("ENT_DEPT_NAME"));
		record.put("CITY_CODE", orderInfo.getString("CITY_CODE"));
		record.put("PROVINCE_CODE", orderInfo.getString("PROVINCE_CODE"));
		record.put("CREATE_TIME", context.getCurrentTime());
		record.put("NEW_PROCESS", context.getNewProcess());
		record.put("FIX_STATUS", nodeJson != null && Constants.DUTY_PROCESS_NODE_JUDGEMENT.equals(nodeJson.getString("NODE_ID")) ? Constants.AFFIRM_STATUS_2: Constants.AFFIRM_STATUS_0);
		
		String appealTime = orderInfo.getString("APPEAL_TIME");
		if(StringUtils.isNotBlank(appealTime)) {
			String belongMonthId = getBelongMonthId(appealTime);
			record.put("MONTH_ID", belongMonthId);
		}
		record.put("APPEAL_TIME", appealTime);
		record.put("CLASS_CODE1", orderInfo.getString("CLASS_CODE1"));
		record.put("CLASS_CODE2", orderInfo.getString("CLASS_CODE2")); 
		record.put("CLASS_CODE3", orderInfo.getString("CLASS_CODE3"));
		
		this.getQuery().save(record);
		logger.info("创建定责工单成功，工单id：" + dutyOrderId);
		
		return dutyOrderId;
	}

	/**
	 * 更新已有定责工单
	 */
	private String updateExistingDutyOrder(DutyOrderContext context, EasyRecord record, JSONObject nodeJson ) throws Exception {
		String dutyOrderId = context.getDutyOrder().getString("ID");
		record.put("ID", dutyOrderId);
		record.put("FIX_STATUS", nodeJson != null && Constants.DUTY_PROCESS_NODE_JUDGEMENT.equals(nodeJson.getString("NODE_ID")) ? Constants.AFFIRM_STATUS_2: Constants.AFFIRM_STATUS_0);
		record.put("NEW_PROCESS", context.getNewProcess());
		logger.info("更新定责信息：" + JSONObject.toJSONString(record));
		this.getQuery().update(record);
		
		return dutyOrderId;
	}

	/**
	 * 创建定责任务
	 */
	private boolean createDutyTask(DutyOrderContext context, String dutyOrderId) throws Exception {
		// 获取配置信息
		JSONObject provinceCfg = getProvinceCfg(context);
		JSONObject nodeInfo = getDutyNodeInfo(context);
		if(nodeInfo == null) {
			logger.error("工单环节配置不全");
			return false;
		}
		
		// 分配处理人
		String taskId = RandomKit.randomStr();
		JSONObject assignUser = assignDutyUser(context, dutyOrderId, taskId, nodeInfo);
		if(assignUser == null) {
			logger.error("未找到分配人员");
			return false;
		}
		
		// 创建任务记录
		createTaskRecord(context, dutyOrderId, taskId, nodeInfo, assignUser, provinceCfg);
		
		return true;
	}

	/**
	 * 获取省份配置
	 */
	private JSONObject getProvinceCfg(DutyOrderContext context) throws Exception {
		String provinceCode = context.getOrderInfo().getString("PROVINCE_CODE");
		return ClassTools.getClass(OrderProvinceCfgService.class)
			.getProvinceCfg(context.getSchema(), provinceCode, "0");
	}

	/**
	 * 获取定责节点配置
	 */
	private JSONObject getDutyNodeInfo(DutyOrderContext context) throws Exception {
		String processId = Constants.DUTY_FLOW_KEY;
		String nodeId = Constants.DUTY_PROCESS_NODE_APPEND;
		JSONObject orderInfo = context.getOrderInfo();
		
		JSONObject nodeJson = OrderNodeUtil.getInstance().getNodeInfo(
			processId, 
			nodeId,
			context.getEntId(),
			orderInfo.getString("PROVINCE_CODE"),
			context.getSchema()
		);
		
		if(nodeJson != null) {
			DutyUtil.getFullNodeInfo(nodeJson, orderInfo, context.getSchema(), nodeId);

			if (Constants.MAGIC_02.equals(nodeJson.getString("STATUS"))) {
				nodeId = Constants.DUTY_PROCESS_NODE_JUDGEMENT;
				nodeJson = OrderNodeUtil.getInstance().getNodeInfo(
					processId, 
					nodeId,
					context.getEntId(),
					orderInfo.getString("PROVINCE_CODE"),
					context.getSchema()
				);
				DutyUtil.getFullNodeInfo(nodeJson, orderInfo, context.getSchema(), nodeId);
			}
			nodeJson.put("PROCESS_ID", processId);
		}
		
		return nodeJson;
	}

	/**
	 * 分配处理人
	 */
	private JSONObject assignDutyUser(DutyOrderContext context, String dutyOrderId, 
		String taskId, JSONObject nodeJson) throws Exception {
		
		return getAssignUser(
			context.getEntId(),
			context.getBusiOrderId(), 
			dutyOrderId,
			taskId,
			nodeJson.getString("PROCESS_ID"),
			nodeJson
		);
	}

	/**
	 * 创建任务记录
	 */
	private void createTaskRecord(DutyOrderContext context, String dutyOrderId,
		String taskId, JSONObject nodeJson, JSONObject assignUser, 
		JSONObject provinceCfg) throws Exception {
		
		JSONObject orderInfo = context.getOrderInfo();
		String timeStyleId = provinceCfg.getString("TIME_STYLE_ID");
		
		// 计算处理时间
		String planDealTime = calculatePlanDealTime(context, nodeJson, timeStyleId);
		
		// 创建任务记录
		EasyRecord record = new EasyRecord(context.getSchema() + ".XTY_RU_TASK", "ID");
		record.put("ID", taskId);
		record.put("TASK_ID", RandomKit.randomStr());
		record.put("M_ID", dutyOrderId);
		record.put("NODE_ID", nodeJson.getString("NODE_ID"));
		record.put("NODE_NAME", nodeJson.getString("NODE_NAME"));
		record.put("PROVINCE", orderInfo.getString("PROVINCE_CODE"));
		record.put("CITY", orderInfo.getString("CITY_CODE"));
		record.put("DEPT_TYPE", nodeJson.getString("DEAL_DEPT_TYPE"));
		record.put("DEPT_CODE", assignUser.getString("userDeptCode"));
		record.put("DEPT_NAME", assignUser.getString("userDeptName"));
		record.put("ASSIGN_TYPE", nodeJson.getString("ALLOCATION_TYPE"));
		record.put("STATUS", StringUtils.isNotBlank(assignUser.getString("userAcc")) ? "02" : "01");
		record.put("CREATE_TIME", context.getCurrentTime());
		record.put("OPTION_TIME", context.getCurrentTime());
		record.put("FLOW_KEY", nodeJson.getString("PROCESS_ID"));
		record.put("HANDLE_ACC", assignUser.getString("userAcc"));
		record.put("HANDLE_NAME", assignUser.getString("userName"));
		record.put("PLAN_TIME", planDealTime);
		
		// 设置工作日/自然日
		setWorkDays(context, record, nodeJson, planDealTime, timeStyleId);
		logger.info("创建定责任务记录：" + JSONObject.toJSONString(record));
		this.getQuery().save(record);
		
		// 更新扩展信息
		record.put("userGroupName", assignUser.getString("userGroupName"));
		logger.info("更新定责任务扩展信息：" + JSONObject.toJSONString(record));
		DutyOrderExService.updateDutyRunEx(getQuery(), context.getSchema(), record, false, false);
	}

	/**
	 * 计算计划处理时间
	 */
	private String calculatePlanDealTime(DutyOrderContext context, JSONObject nodeJson, 
	    String timeStyleId) throws Exception {

	    JSONObject orderInfo = context.getOrderInfo();
	    String acceptTime = orderInfo.getString("ACCEPT_TIME");
	    String appealTime = orderInfo.getString("APPEAL_TIME");

	    // 获取初始计划处理时间
	    String planDealTime = getPlanDealTime(context, nodeJson, timeStyleId,
	        StringUtils.isNotBlank(acceptTime) ? acceptTime : context.getCurrentTime());
		logger.info("初始计划处理时间：" + planDealTime);
	    if (Constants.DUTY_PROCESS_NODE_APPEND.equals(nodeJson.getString("NODE_ID"))) {
			logger.info("当前节点为提交环节，需要判断初判节点计划处理时间");
	        String dutyProcessNodeJudgement = Constants.DUTY_PROCESS_NODE_JUDGEMENT;
	        JSONObject checkNodeJson = OrderNodeUtil.getInstance().getNodeInfo(Constants.DUTY_FLOW_KEY, dutyProcessNodeJudgement, context.getEntId(), context.getOrderInfo().getString("PROVINCE_CODE"), context.getSchema());
	        if (checkNodeJson == null) {
	            return planDealTime;
	        }
	        DutyUtil.getFullNodeInfo(checkNodeJson, context.getOrderInfo(), context.getSchema(), dutyProcessNodeJudgement);

	        // 获取初判节点计划处理时间
	        String checkTime = getPlanDealTime(context, checkNodeJson, timeStyleId, appealTime);

	        // 取较小值，并记录详细日志
	        if (DateUtil.compareDate(planDealTime, checkTime, DateUtil.TIME_FORMAT) > 0) {
	            logger.info("初始计划处理时间【" + planDealTime + "】大于复核节点计划处理时间【" + checkTime + "】，采用复核节点时间 " + checkTime);
	            planDealTime = checkTime;
	        }
	    }
	    return planDealTime;
	}

	/**
	 * 获取计划处理时间
	 */
	private String getPlanDealTime(DutyOrderContext context, JSONObject nodeJson, String timeStyleId, String baseTime) throws Exception {
	    return ClassTools.getClass(OrderTimeService.class).getPlanDealTime(
	        context.getEntId(),
	        context.getBusiOrderId(),
	        context.getOrderId(),
	        timeStyleId,
	        baseTime,
	        nodeJson.getString("DEAL_DAY_TYPE"),
	        CommonUtil.parseInt(nodeJson.getString("DEAL_DAYS"))
	    );
	}



	/**
	 * 设置工作日/自然日
	 */
	private void setWorkDays(DutyOrderContext context, EasyRecord record,
		JSONObject nodeJson, String planDealTime, String timeStyleId) throws Exception {
		
		String dealDayType = nodeJson.getString("DEAL_DAY_TYPE");
		String dateStr = context.getCurrentTime();
		record.put("DAY_TYPES",dealDayType);
		if ("2".equals(dealDayType)) {
			int workDays = WorkTimeUtils.getWorkDays(
				context.getEntId(), 
				context.getSchema(),
				timeStyleId,
				dateStr,
				planDealTime
			);
			record.put("WORKING_DAYS", workDays);
		} else {
			int natureDays = WorkTimeUtils.getNatureDays(dateStr, planDealTime);
			record.put("NATURAL_DAYS", natureDays);
		}
	}

	/**
	 * 处理自动核验
	 */
	private void handleAutoVerify(DutyOrderContext context, String dutyOrderId) {
		if (!context.isInsert()) {
			return;
		}
		
		JSONObject orderInfo = context.getOrderInfo();
		boolean needVerify = isCheck(
			context.getSchema(),
			orderInfo.getString("CLASS_CODE1"),
			orderInfo.getString("CLASS_CODE2"),
			orderInfo.getString("CLASS_CODE3")
		);
		
		logger.info("是否发起自动核验：" + needVerify);
		
		if (needVerify) {
			try {
				doAutoVerify(context, dutyOrderId, orderInfo);
			} catch (Exception e) {
				logger.error("自动核验异常:" + e.getMessage(), e);
			}
		}
	}

	/**
	 * 执行自动核验
	 */
	private void doAutoVerify(DutyOrderContext context, String dutyOrderId,
		JSONObject orderInfo) throws Exception {
		
		JSONObject param = new JSONObject();
		param.put("ORDER_ID", dutyOrderId);
		param.put("PROVINCE_CODE", orderInfo.getString("PROVINCE_CODE"));
		param.put("APPEAL_PHONE", PhoneCryptor.getInstance().decrypt(orderInfo.getString("APPEAL_PHONE")));
		param.put("ENT_DEPT_CODE", orderInfo.getString("ENT_DEPT_CODE"));
		param.put("ENT_DEPT_NAME", PhoneCryptor.getInstance().decrypt(orderInfo.getString("ENT_DEPT_NAME")));
		param.put("APPLY_NAME", "系统");
		param.put("VERIFY_TIME", orderInfo.getString("APPEAL_TIME"));
		param.put("APPLY_DEPT_NAME", "系统");
		param.put("ENT_ID", context.getEntId());
		param.put("TYPE", 1);
		
		logger.info("请求自动核验：" + JSONObject.toJSONString(param));
		JSONObject result = ServiceUtil.invoke2("ORDER_PHONE_VERIFY_SERVICE", param);
		logger.info("请求自动核验结果：" + JSONObject.toJSONString(result));
	}

	/**
	 * 判断是否核验
	 * @param schema
	 * @param classCodes
	 * @return
	 */
	private boolean isCheck(String schema, String... classCodes) {
	    String keyPrefix = "XTY_CLASS_IS_CEHCK_";
	    for (String classCode : classCodes) {
	        String cacheKey = keyPrefix + classCode;
	        String cachedValue = CacheUtil.get(cacheKey);
	        if (StringUtils.isBlank(cachedValue)) {
				cachedValue = this.getIsCheck(schema, classCode);
	            if (StringUtils.isNotBlank(cachedValue)) {
					CacheUtil.put(cacheKey, cachedValue);
	            }
	        }
			logger.info("当前分类【"+classCode+"】是否需要自动核验："+cachedValue);
	        if (Constants.MAGIC_01.equals(CacheUtil.get(cacheKey))) {
	            return true;
	        }
	    }
	    return false;
	}


	private String getIsCheck (String schema,String classCode) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" select * from " + schema + ".C_CF_COMMON_TREE ");
			sql.append(classCode," where ID = ? ",false);
			JSONObject row = QueryFactory.getWriteQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if (Objects.nonNull(row)) {
				return row.getString("EX2");
			}
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return null;
	}

	private String getBelongMonthId(String appealTime) throws SQLException {
		try {
			EasyCalendar easyCalendar = EasyCalendar.newInstance(appealTime, "yyyy-MM-dd HH:mm:ss");
			String dateId = String.valueOf(easyCalendar.getDateInt());
			JSONObject config = getConfig(dateId);
			String year = config.getString("P_YEAR");
			int month = config.getIntValue("P_MONTH");
			return year + String.format("%02d", month);
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return null;
	}


	private JSONObject getConfig(String dateId) throws SQLException {
		EasyCache cache = CacheManager.getCache();
		JSONObject result = cache.get("xty_dim_date:"+dateId);
		if(result != null) return result;
		String sql = "select * from "+Constants.getStatSchema()+".xty_dim_date where date_id=?";
		result = QueryFactory.getReadQuery().queryForRow(sql, new Object[]{dateId}, new JSONMapperImpl());
		if(result!=null) cache.put("xty_dim_date:"+dateId, result);
		return result;
	}


	/**
	 * 执行专班复核抽样
	 * @param json
	 * @return
	 */
	public JSONObject execSpecializedFixSample(JSONObject json,boolean next) {
		String key = "EXTRACT_RECHECK_";
		try {
			String entId = Constants.getEntId();
			String busiOrderId = SchemaService.getCcBusiOrderIdByEntId(entId);
			String schema = SchemaService.findSchemaByEntId(entId);
			String processId = Constants.DUTY_FLOW_KEY;
			String nodeId = Constants.DUTY_PROCESS_NODE_REVIEW;
			String fixStatus = Constants.AFFIRM_STATUS_5;
			String currentDate = DateUtil.getCurrentDateStr("yyyy-MM-dd");
			String dealDateStr = json.getString("dealDateStr");
			String ents = json.getString("entType");
			String provinces = json.getString("provinces");
			String dealDate = null;
			String o = CacheUtil.get(key + currentDate);
			if (StringUtils.isNotBlank(o)) {
				logger.error("当前复核任务正在处理中请稍后再试！" );
				return getFailJson();
			}
			// 遍历各省数据
			List<String> provinceList = StringUtils.isNotBlank(provinces) ? Arrays.asList(provinces.split(","))
					: this.getQuery().queryForList("select PROVINCE_CODE from CC_PROVINCE").stream().map(t -> t.getColumnValue("PROVINCE_CODE")).collect(Collectors.toList());
			for(String provinceCode : provinceList) {
				// 获取省份参数配置
				JSONObject configJson = ClassTools.getClass(OrderProvinceCfgService.class).getProvinceCfg(schema, provinceCode, "0");
				if(configJson == null) {
					continue;
				}
				// 获取工作时间样式ID
				String timeStyleId = configJson.getString("TIME_STYLE_ID");
				// 获取定责节点配置
				JSONObject nodeJson = OrderNodeUtil.getInstance().getNodeInfo(processId, nodeId, entId, provinceCode, schema);
				if(nodeJson == null) {
					logger.error("省份[" + provinceCode + "]配置信息不全，无法分配");
					continue;
				}
				nodeJson.put("PROCESS_ID", processId);

				JSONObject fixDutyJson = configJson.getJSONObject("FIX_DUTY_CONFIG");
				if(fixDutyJson != null) {
					// 计算当前省份需要认定的工单数据
					String dealTimes = fixDutyJson.getString("SPECIALIZED_CLASS_DEAL_TIMES");
					String dealTimeType = fixDutyJson.getString("SPECIALIZED_CLASS_DEAL_TIME_TYPE");
					String dealTimes2 = fixDutyJson.getString("SPECIALIZED_CLASS_DEAL_TIMES2");
					String dealTimeType2 = fixDutyJson.getString("SPECIALIZED_CLASS_DEAL_TIME_TYPE2");
					float sampleRatio = fixDutyJson.getFloatValue("SPECIALIZED_CLASS_SAMPLE_RATIO");
					logger.info("省份[" + provinceCode + "]配置信息dealTimes[" + dealTimes + "] dealTimeType[" + dealTimeType + "] sampleRatio[" + sampleRatio + "]");

					EasySQL sql = null;
					if(StringUtils.isBlank(dealDateStr)) {
						if("1".equals(dealTimeType)) {
							dealDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD,currentDate, -Integer.parseInt(dealTimes));
						} else {
							dealDate = WorkTimeUtils.getWorkStartDate2(entId, schema, timeStyleId, currentDate,currentDate,Integer.parseInt(dealTimes));
						}
						logger.info("时间段1：" + dealDate);
						if(StringUtils.isNotBlank(dealTimes2)) {
							if("1".equals(dealTimeType2)) {
								dealDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD,dealDate, -Integer.parseInt(dealTimes2));
							} else {
								dealDate = WorkTimeUtils.getWorkStartDate2(entId, schema, timeStyleId, dealDate,dealDate,Integer.parseInt(dealTimes2));
							}
							logger.info("时间段2：" + dealDate);
						}
					} else {
						dealDate = dealDateStr;
					}
					sql = createSql(entId, schema, provinceCode, fixStatus, dealDate, processId, nodeId,null);
					logger.info("省份[" + provinceCode + "]开始查询数据日期[" + dealDate + "]");
					List<JSONObject> orderList = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

					// 开始抽样 按地市平均抽取
					if(sampleRatio == 100) {
						// 全量抽取
						Map<String, List<JSONObject>> entMap = orderList.stream().collect(Collectors.groupingBy(t ->  t.getString("ENT_TYPE")));
						for(String entType : entMap.keySet()) {
							if (StringUtils.isNotBlank(ents)) {
								if (!Arrays.stream(ents.split(","))
										.anyMatch(ent -> ent.equals(entType))) {
									logger.error("当前企业不做处理！");
									// 假设这段代码在一个循环中，使用continue跳过当前循环的剩余部分
									continue;
								}
							}
							List<JSONObject> entList = entMap.get(entType);
							for (JSONObject orderJson : entList) {
								setUserByOrder(entId, busiOrderId, schema,  nodeJson, orderJson, fixStatus,true);
								String pId = orderJson.getString("ID");
								EventDispatcher.appendAddDocEvent(pId, schema, "FixDutyService.execSpecializedFixSample");
							}
						}
					} else {
						JSONObject dateJson = this.getReadQuery().queryForRow("select max(t1.DATE_VALUE) END_DATE,min(t1.DATE_VALUE) START_DATE from " + Constants.getStatSchema() + ".XTY_DIM_DATE t1," + Constants.getStatSchema() + ".XTY_DIM_DATE t2 where t1.P_YEAR = T2.P_YEAR and t1.P_MONTH = t2.P_MONTH and t2.DATE_VALUE = ?", new Object[]{dealDate}, new JSONMapperImpl());
						String startDate = dateJson.getString("START_DATE");
						String endDate = dateJson.getString("END_DATE");
						if (StringUtils.isBlank(startDate)) {
							startDate = dealDateStr;
						}
						List<JSONObject> entCounts = getExtractCount2(schema, startDate,endDate, dealDate, fixStatus, provinceCode,Constants.DUTY_PROCESS_NODE_REVIEW);
						Map<String,JSONObject> entCountMap = new HashMap<>();
						if (CollectionUtils.isNotEmpty(entCounts)) {
							entCountMap = entCounts.stream().collect(Collectors.toMap(item -> item.getString("ENT_TYPE"),item -> item,(o1,o2) -> o1));
						}
						Map<String, List<JSONObject>> entMap = orderList.stream().collect(Collectors.groupingBy(t -> t.getString("ENT_TYPE")));
						for(String entType : entMap.keySet()) {
							if (StringUtils.isNotBlank(ents)) {
								if (!Arrays.stream(ents.split(","))
										.anyMatch(ent -> ent.equals(entType))) {
									logger.error("当前企业不做处理！");
									// 假设这段代码在一个循环中，使用continue跳过当前循环的剩余部分
									continue;
								}
							}
							List<JSONObject> entList = entMap.get(entType);
							int count = entList.size();
							JSONObject object = entCountMap.get(entType);
							int dutyOrder = object.getIntValue("DUTY_ORDER");
							int extractOrder = object.getIntValue("EXTRACT_ORDER");
							int sampleCount = Math.round(dutyOrder * sampleRatio / 100) - extractOrder;
							logger.info("执行日期[" + currentDate + "]抽样日期[" + dealDate + "]抽样集团[" + entType + "]定责工单总量[" + dutyOrder + "]当天定责工单量[" + count + "] 抽取比例[" + sampleRatio + "] 抽取数量[" + sampleCount + "]");
							if (sampleCount <= 0) {
								updateOrderStatus(entId, schema, nodeJson, entList,Constants.AFFIRM_STATUS_9);
								continue;
							}
							// 打乱顺序
							Collections.shuffle(entList);
							// 开始抽取
							for(JSONObject orderJson : entList.subList(0, sampleCount)) {
								setUserByOrder(entId, busiOrderId, schema,  nodeJson, orderJson, fixStatus,true);
							}
							updateOrderStatus(entId, schema, nodeJson, entList.subList(sampleCount, entList.size()),Constants.AFFIRM_STATUS_9);


							for (JSONObject jsonObject : entList) {
								String pId = jsonObject.getString("ID");
								EventDispatcher.appendAddDocEvent(pId, schema, "FixDutyService.execSpecializedFixSample");
							}
						}
					}
					this.updateTaskCancel(schema,Constants.DUTY_PROCESS_NODE_AFFIRM_CHECK,dealDate,provinceCode,null);
					if (next && "2".equals(dealTimeType)) {
						this.extractNextDate(entId,schema,timeStyleId, WorkTimeUtils.getWorkStartDate2(entId, schema, timeStyleId, currentDate,currentDate,Integer.parseInt(dealTimes)),provinceCode,fixDutyJson);
					}
				} else {
					logger.error("省份[" + provinceCode + "]配置信息不全，无法分配");
				}

			}
			return getSuccJson();
		} catch (Exception e) {
			logger.error("定责任务分配异常:"+e.getMessage(), e);
		} finally {
			CacheUtil.delete(key + DateUtil.getCurrentDateStr("yyyy-MM-dd"));
			//上报运行状态
            XtyModuleRuntimeDataCollector.report(Constants.APP_NAME, "FixDutyService", "定责复核抽取", 25 * 60 * 60);
		}
		return getFailJson();
	}

	private void extractNextDate(String entId,String schema,String timeStyleId,String dateStr,String provinceCode,JSONObject fixDutyJson) {
		logger.info("extractNextDate ... start");
		String dealTimes2 = fixDutyJson.getString("SPECIALIZED_CLASS_DEAL_TIMES2");
		String dealTimeType2 = fixDutyJson.getString("SPECIALIZED_CLASS_DEAL_TIME_TYPE2");
		if (StringUtils.isNotBlank(dateStr)) {
			dateStr = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD,dateStr, -1);
			String str = dateStr;
			boolean check = WorkTimeUtils.isWTimeCheck(entId, schema, timeStyleId, dateStr, String.valueOf(DateUtil.getWeek(dateStr)));
			logger.info("date: " + str + "check: " + check);
			if (!check) {
				if(StringUtils.isNotBlank(dealTimes2)) {
					if("1".equals(dealTimeType2)) {
						dateStr = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD,dateStr, -Integer.parseInt(dealTimes2));
					} else {
						dateStr = WorkTimeUtils.getWorkStartDate2(entId, schema, timeStyleId, dateStr,dateStr,Integer.parseInt(dealTimes2));
					}
				}
				JSONObject json = new JSONObject();
				json.put("dealDateStr",dateStr);
				json.put("provinces",provinceCode);
				this.execSpecializedFixSample(json,false);
				this.extractNextDate(entId,schema,timeStyleId,str,provinceCode,fixDutyJson);
			}
		}
	}

	/**
	 * 执行企业抽样
	 * @param json
	 * @return
	 */
	public JSONObject extractJudge(JSONObject json) {
		String key = "EXTRACT_JUDGE_";
	    try {
			logger.info("开始执行企业抽样");
	        String entId = Constants.getEntId();
	        String busiOrderId = SchemaService.getCcBusiOrderIdByEntId(entId);
	        String schema = SchemaService.findSchemaByEntId(entId);
	        String processId = Constants.DUTY_FLOW_KEY;
	        String nodeId = Constants.DUTY_PROCESS_NODE_JUDGEMENT_CHECK;
	        String fixStatus = Constants.AFFIRM_STATUS_21;
	        String currentDate = DateUtil.getCurrentDateStr("yyyy-MM-dd");
	        String dealDateStr = json.getString("dealDateStr");
			String entType = json.getString("entType");
			String o = CacheUtil.get(key + currentDate);
			if (StringUtils.isNotBlank(o)) {
				logger.error("当前任务正在处理中请稍后再试！" );
				return getFailJson();
			}
			CacheUtil.put(key + currentDate,"Y",5*60);
			List<String> entGroups = Arrays.asList(
	            Constants.GROUP_TYPE_GE_TELECOM,
	            Constants.GROUP_TYPE_GE_BROAD,
	            Constants.GROUP_TYPE_GE_MOBILE,
	            Constants.GROUP_TYPE_GE_UNICOM
	        ); // 企业类型

	        for (String entGroup : entGroups) {
				if (StringUtils.isNotBlank(entType)) {
					// 使用流API来简化字符串处理和检查逻辑
					if (!Arrays.stream(entType.split(","))
							.map(ent -> "9901040" + ent)
							.anyMatch(ent -> ent.equals(entGroup))) {
						logger.error("当前企业不做处理！");
						// 假设这段代码在一个循环中，使用continue跳过当前循环的剩余部分
						continue;
					}
				}
				logger.info("企业类型[{}]开始抽样", entGroup);
	            JSONObject entConfig = DutyUtil.getEntConfig(entGroup, schema);
	            if (entConfig == null) {
	                throw new RuntimeException("企业类型配置不存在");
	            }
				//从entConfig中获取省份配置信息$.CONFIG_JSON.PROVINCE_CONFIG
				JSONObject object = entConfig.getJSONObject("CONFIG_JSON");
				String unsampledProcessType = object.getString("UNSAMPLED_PROCESS_TYPE");
				String provinceConfigStr = object.getString("PROVINCE_CONFIG");
				logger.info("企业类型[{}]配置信息[{}]", entGroup, provinceConfigStr);
	            if (StringUtils.isNotBlank(provinceConfigStr)) {
	                List<JSONObject> list = JSONObject.parseArray(provinceConfigStr, JSONObject.class);
	                for (JSONObject provinceConfig : list) {
	                    String provinceCode = provinceConfig.getString("PROVINCE_CODE");
	                    JSONObject nodeJson = OrderNodeUtil.getInstance().getNodeInfo(processId, Constants.DUTY_PROCESS_NODE_JUDGEMENT, entId, provinceCode, schema);
						JSONObject orderInfo = new JSONObject() {{
							put("ENT_TYPE", Constants.getEntTypeByGroupType(entGroup));
							put("ENT_DEPT_CODE", provinceConfig.getString("DEPT_CODE"));
						}};
						DutyUtil.getFullNodeInfo(nodeJson,
								orderInfo,schema,Constants.DUTY_PROCESS_NODE_JUDGEMENT);
	                    if (nodeJson == null) {
	                        logger.error("省份[{}]配置信息不全，无法分配", provinceCode);
	                        continue;
	                    }
						nodeJson.put("PROCESS_ID", processId);
	                    JSONObject configJson = ClassTools.getClass(OrderProvinceCfgService.class).getProvinceCfg(schema, provinceCode, "0");
	                    if (configJson == null) {
							logger.info("省份[{}]配置信息不存在", provinceCode);
	                        continue;
	                    }

	                    String timeStyleId = configJson.getString("TIME_STYLE_ID");
	                    String dealDayType = nodeJson.getString("DEAL_DAY_TYPE");
	                    String dealDays = nodeJson.getString("DEAL_DAYS");
	                    float sampleRatio = provinceConfig.getFloatValue("EXTRACTION_RATIO");

	                    logger.info("省份[{}]配置信息dealTimes[{}] dealTimeType[{}] sampleRatio[{}]", provinceCode, dealDays, dealDayType, sampleRatio);

	                    String dealDate = calculateDealDate(entId,schema,dealDateStr, dealDays, dealDayType, timeStyleId, currentDate);
	                    EasySQL sql = createSql(entId, schema, provinceCode, fixStatus, dealDate, processId, nodeId, entGroup);

	                    logger.info("省份[{}]开始查询数据日期[{}]", provinceCode, dealDate);
	                    List<JSONObject> orderList = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

						nodeJson = OrderNodeUtil.getInstance().getNodeInfo(processId, nodeId, entId, provinceCode, schema);
						DutyUtil.getFullNodeInfo(nodeJson, orderInfo,schema,nodeId);
						nodeJson.put("PROCESS_ID", processId);
						if (nodeJson == null) {
							logger.error("省份[{}]配置信息不全，无法分配", provinceCode);
							continue;
						}
	                    if (sampleRatio == 100) {
	                        for (JSONObject orderJson : orderList) {
	                            setUserByOrder(entId, busiOrderId, schema, nodeJson, orderJson, fixStatus,true);
	                            EventDispatcher.appendAddDocEvent(orderJson.getString("ID"), schema, "FixDutyService.extractJudge");
	                        }
	                    } else {
	                        JSONObject dateRange = getDateRange(schema, dealDate);
	                        String startDate = dateRange.getString("START_DATE");
	                        String endDate = dateRange.getString("END_DATE");
	                        if (StringUtils.isBlank(startDate)) {
	                            startDate = dealDateStr;
	                        }

	                        JSONObject entCounts = getExtractCount3(schema, startDate, endDate, dealDate, fixStatus, provinceCode, nodeId, entGroup);
	                        int dutyOrder = entCounts.getIntValue("DUTY_ORDER");
	                        int extractOrder = entCounts.getIntValue("EXTRACT_ORDER");
	                        int sampleCount = Math.round(dutyOrder * sampleRatio / 100) - extractOrder;
							sampleCount = Math.min(sampleCount, orderList.size());
							logger.info("初判审核任务抽取：省份[{}]执行日期[{}]抽样日期[{}]抽样集团[{}]定责工单总量[{}]定责已抽取工单量[{}] 抽取比例[{}] 抽取数量[{}]", provinceCode,currentDate, dealDate, entGroup, dutyOrder, extractOrder, sampleRatio, sampleCount);
	                        if (sampleCount <= 0) {
								if (Constants.MAGIC_02.equals(unsampledProcessType)) {
									nodeJson = DutyUtil.getEntNodeJson(provinceConfig.getString("DEPT_CODE"),schema,nodeId);
									nodeJson.put("PROCESS_ID", processId);
									for (JSONObject orderJson : orderList) {
										setUserByOrder(entId, busiOrderId, schema, nodeJson, orderJson, fixStatus,false);
									}
								} else {
									updateOrderExtractStatus(entId, schema, nodeJson, orderList,Constants.AFFIRM_STATUS_3);
								}
	                            continue;
	                        }

	                        Collections.shuffle(orderList);
	                        for (JSONObject orderJson : orderList.subList(0, sampleCount)) {
	                            setUserByOrder(entId, busiOrderId, schema, nodeJson, orderJson, fixStatus,true);
	                        }
							List<JSONObject> subList = orderList.subList(sampleCount, orderList.size());
							if (Constants.MAGIC_02.equals(unsampledProcessType)) {
								nodeJson = DutyUtil.getEntNodeJson(provinceConfig.getString("DEPT_CODE"),schema,nodeId);
								nodeJson.put("PROCESS_ID", processId);
								for (JSONObject orderJson : subList) {
									setUserByOrder(entId, busiOrderId, schema, nodeJson, orderJson, fixStatus,false);
								}
							} else {
								updateOrderExtractStatus(entId, schema, nodeJson, subList,Constants.AFFIRM_STATUS_3);
							}
	                        for (JSONObject jsonObject : orderList) {
	                            EventDispatcher.appendAddDocEvent(jsonObject.getString("ID"), schema, "FixDutyService.extractJudge");
	                        }
	                    }
						// this.updateTaskCancel(schema,Constants.DUTY_PROCESS_NODE_JUDGEMENT,dealDate,provinceCode,entGroup);
	                }

	            }
	        }
	    } catch (Exception e) {
	        logger.error("定责任务分配异常: " + e.getMessage(), e);
			return getFailJson();
	    } finally {
			CacheUtil.delete(key + DateUtil.getCurrentDateStr("yyyy-MM-dd"));
			//上报运行状态
            XtyModuleRuntimeDataCollector.report(Constants.APP_NAME, "FixDutyService", "定责初判抽取", 25 * 60 * 60);
		}
	    return getSuccJson();
	}

	private String calculateDealDate(String entId,String schema,String dealDateStr, String dealDays, String dealDayType, String timeStyleId, String currentDate) {
	    if (StringUtils.isBlank(dealDateStr)) {
	        if (StringUtils.isNotBlank(dealDays)) {
	            return "1".equals(dealDayType)
	                ? DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, currentDate, -Integer.parseInt(dealDays))
	                : WorkTimeUtils.getWorkStartDate2(entId, schema, timeStyleId, currentDate, currentDate, Integer.parseInt(dealDays));
	        }
	    }
	    return dealDateStr;
	}

	private JSONObject getDateRange(String schema, String dealDate) throws SQLException {
	    return this.getReadQuery().queryForRow(
	        "select max(t1.DATE_VALUE) END_DATE, min(t1.DATE_VALUE) START_DATE " +
	        "from " + Constants.getStatSchema() + ".XTY_DIM_DATE t1, " + Constants.getStatSchema() + ".XTY_DIM_DATE t2 " +
	        "where t1.P_YEAR = t2.P_YEAR and t1.P_MONTH = t2.P_MONTH and t2.DATE_VALUE = ?",
	        new Object[]{dealDate},
	        new JSONMapperImpl()
	    );
	}


	/**
	 * 执行省份认定抽样
	 * @param json
	 * @return
	 */
	public JSONObject execProvinceFixSample(JSONObject json) {
		String key = "EXTRACT_AFFIRM_";
		try {
			String entId = Constants.getEntId();
			String busiOrderId = SchemaService.getCcBusiOrderIdByEntId(entId);
			String schema = SchemaService.findSchemaByEntId(entId);
			String processId = Constants.DUTY_FLOW_KEY;
			String nodeId = Constants.DUTY_PROCESS_NODE_AFFIRM;
			String fixStatus = Constants.AFFIRM_STATUS_3;
			String currentDate = DateUtil.getCurrentDateStr("yyyy-MM-dd");
			String dealDateStr = json.getString("dealDateStr");
			String ents = json.getString("entType");
			String o = CacheUtil.get(key + currentDate);
			if (StringUtils.isNotBlank(o)) {
				logger.error("当前认定抽取任务正在处理中请稍后再试！" );
				return getFailJson();
			}
			CacheUtil.put(key + currentDate,"Y",5*60);
			// 遍历各省数据
			List<String> provinceList = this.getQuery().queryForList("select PROVINCE_CODE from CC_PROVINCE").stream().map(t -> t.getColumnValue("PROVINCE_CODE")).collect(Collectors.toList());
			for(String provinceCode : provinceList) {
				// 获取省份参数配置
				JSONObject configJson = ClassTools.getClass(OrderProvinceCfgService.class).getProvinceCfg(schema, provinceCode, "0");
				if(configJson == null) {
					continue;
				}
				// 获取工作时间样式ID
				String timeStyleId = configJson.getString("TIME_STYLE_ID");
				// 获取定责节点配置
				JSONObject nodeJson = OrderNodeUtil.getInstance().getNodeInfo(processId, nodeId, entId, provinceCode, schema);
				if(nodeJson == null) {
					logger.error("省份[" + provinceCode + "]配置信息不全，无法分配");
					continue;
				}
				nodeJson.put("PROCESS_ID", processId);
				logger.error("省份[" + provinceCode + "]配置信息：" + JSONObject.toJSONString(nodeJson));
				JSONObject fixDutyJson = configJson.getJSONObject("FIX_DUTY_CONFIG");
				if(fixDutyJson != null) {
					// 计算当前省份需要认定的工单数据
					String dealTimes = fixDutyJson.getString("PROVINCE_MANAGER_DEAL_TIMES");
					String dealTimeType = fixDutyJson.getString("PROVINCE_MANAGER_DEAL_TIME_TYPE");
					String dealTimes2 = fixDutyJson.getString("PROVINCE_MANAGER_DEAL_TIMES2");
					String dealTimeType2 = fixDutyJson.getString("PROVINCE_MANAGER_DEAL_TIME_TYPE2");
					float sampleRatio = fixDutyJson.getFloatValue("PROVINCE_MANAGER_SAMPLE_RATIO");
					logger.info("省份[" + provinceCode + "]配置信息dealTimes[" + dealTimes + "] dealTimeType[" + dealTimeType + "] sampleRatio[" + sampleRatio + "]");
					String dealDate = null;
					EasySQL sql = null;
					if(StringUtils.isBlank(dealDateStr)) {
						if("1".equals(dealTimeType)) {
							dealDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD,currentDate, -Integer.parseInt(dealTimes));
						} else {
							dealDate = WorkTimeUtils.getWorkStartDate2(entId, schema, timeStyleId, currentDate,currentDate,Integer.parseInt(dealTimes));
						}
						if(StringUtils.isNotBlank(dealTimes2)) {
							if("1".equals(dealTimeType2)) {
								dealDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD,dealDate, -Integer.parseInt(dealTimes2));
							} else {
								dealDate = WorkTimeUtils.getWorkStartDate2(entId, schema, timeStyleId, dealDate,dealDate,Integer.parseInt(dealTimes2));
							}
						}
						sql = createSql(entId, schema, provinceCode, fixStatus, dealDate, processId, nodeId,null);
					} else {
						dealDate = dealDateStr;
						// 后门代码，测试完成之后需要删除
						sql = createSql(entId, schema, provinceCode, fixStatus, dealDateStr, processId, nodeId,null);
					}
					logger.info("省份[" + provinceCode + "]开始查询数据日期[" + dealDate + "]");
					List<JSONObject> orderList = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

					// 开始抽样 按地市平均抽取
					if(sampleRatio == 100) {
						// 全量抽取
						Map<String, List<JSONObject>> entMap = orderList.stream().collect(Collectors.groupingBy(t ->  t.getString("ENT_TYPE")));
						for(String entType : entMap.keySet()) {
							if (StringUtils.isNotBlank(ents)) {
								if (!Arrays.stream(ents.split(","))
										.anyMatch(ent -> ent.equals(entType))) {
									logger.error("当前企业不做处理！");
									// 假设这段代码在一个循环中，使用continue跳过当前循环的剩余部分
									continue;
								}
							}
							List<JSONObject> entList = entMap.get(entType);
							for (JSONObject orderJson : entList) {
								setUserByOrder(entId, busiOrderId, schema,  nodeJson, orderJson, fixStatus,true);
								String pId = orderJson.getString("ID");
								EventDispatcher.appendAddDocEvent(pId, schema, "FixDutyService.execProvinceFixSample");
							}
						}
						
					} else {
						JSONObject dateJson = this.getReadQuery().queryForRow("select max(t1.DATE_VALUE) END_DATE,min(t1.DATE_VALUE) START_DATE from " + Constants.getStatSchema() + ".XTY_DIM_DATE t1," + Constants.getStatSchema() + ".XTY_DIM_DATE t2 where t1.P_YEAR = T2.P_YEAR and t1.P_MONTH = t2.P_MONTH and t2.DATE_VALUE = ?", new Object[]{dealDate}, new JSONMapperImpl());
						String startDate = dateJson.getString("START_DATE");
						String endDate = dateJson.getString("END_DATE");
						if (StringUtils.isBlank(startDate)) {
							startDate = dealDateStr;
						}
						List<JSONObject> entCounts = getExtractCount2(schema, startDate,endDate, dealDate, fixStatus, provinceCode,Constants.DUTY_PROCESS_NODE_AFFIRM);
						Map<String,JSONObject> entCountMap = new HashMap<>();
						if (CollectionUtils.isNotEmpty(entCounts)) {
							entCountMap = entCounts.stream().collect(Collectors.toMap(item -> item.getString("ENT_TYPE"),item -> item,(o1,o2) -> o1));
						}
						Map<String, List<JSONObject>> entMap = orderList.stream().collect(Collectors.groupingBy(t ->  t.getString("ENT_TYPE")));
						for(String entType : entMap.keySet()) {
							if (StringUtils.isNotBlank(ents)) {
								if (!Arrays.stream(ents.split(","))
										.anyMatch(ent -> ent.equals(entType))) {
									logger.error("当前企业不做处理！");
									// 假设这段代码在一个循环中，使用continue跳过当前循环的剩余部分
									continue;
								}
							}
							List<JSONObject> entList = entMap.get(entType);
							int count = entList.size();
							JSONObject object = entCountMap.get(entType);
							int dutyOrder = object.getIntValue("DUTY_ORDER");
							int extractOrder = object.getIntValue("EXTRACT_ORDER");
							int sampleCount = Math.round(dutyOrder * sampleRatio / 100) - extractOrder;
							logger.info("执行日期[" + currentDate + "]抽样日期[" + dealDate + "]抽样集团[" + entType + "]定责工单总量[" + dutyOrder + "]当天定责工单量[" + count + "] 抽取比例[" + sampleRatio + "] 抽取数量[" + sampleCount + "]");
							if (sampleCount <= 0) {
								updateOrderStatus(entId, schema, nodeJson, entList,Constants.AFFIRM_STATUS_9);
								continue;
							}
							// 打乱顺序
							Collections.shuffle(entList);
							// 开始抽取
							for(JSONObject orderJson : entList.subList(0, sampleCount)) {
								setUserByOrder(entId, busiOrderId, schema,  nodeJson, orderJson, fixStatus,true);
							}
							updateOrderStatus(entId, schema, nodeJson, entList.subList(sampleCount, entList.size()),Constants.AFFIRM_STATUS_9);

							for (JSONObject jsonObject : entList) {
								String pId = jsonObject.getString("ID");
								EventDispatcher.appendAddDocEvent(pId, schema, "FixDutyService.execProvinceFixSample");
							}

						}
					}

					this.updateTaskCancel(schema,Constants.DUTY_PROCESS_NODE_JUDGEMENT_CHECK,dealDate,provinceCode,null);
				} else {
					logger.error("省份[" + provinceCode + "]配置信息不全，无法分配");
				}
			}
			return getSuccJson();
		} catch (Exception e) {
			logger.error("定责任务分配异常:"+e.getMessage(), e);
		} finally {
			CacheUtil.delete(key + DateUtil.getCurrentDateStr("yyyy-MM-dd"));
			//上报运行状态
            XtyModuleRuntimeDataCollector.report(Constants.APP_NAME, "FixDutyService", "定责认定抽取", 25 * 60 * 60);
		}
		return getFailJson();
	}

	private void updateTaskCancel (String schema,String nodeId,String dealDate,String province,String groupType) {
		try{
			EasySQL sql = new EasySQL();
			sql.append(" update " + schema + ".xty_ru_task_his t1 ");
			sql.append(" left join " + schema + ".c_box_duty_order t2 on t2.ID = t1.M_ID ");
			sql.append("02"," set t1.IS_CANCEL = ? ");
			sql.append(nodeId," where t1.NODE_ID = ? ",false);
			sql.append(Constants.AFFIRM_STATUS_9," and t2.FIX_STATUS = ? ");
			sql.append(dealDate + " 00:00:00", "and t2.APPEAL_TIME >= ?");
			sql.append(dealDate + " 23:59:59", "and t2.APPEAL_TIME <= ?");
			if (Constants.DUTY_PROCESS_NODE_JUDGEMENT.equals(nodeId)) {
				sql.append(Constants.MAGIC_02," and t2.JUDGEMENT_RESULT = ? ");
			} else if (Constants.DUTY_PROCESS_NODE_JUDGEMENT_CHECK.equals(nodeId)) {
				sql.append(Constants.MAGIC_01," and t2.JUDGEMENT_ADUIT_RESULT = ? ");
			} else if (Constants.DUTY_PROCESS_NODE_AFFIRM_CHECK.equals(nodeId)) {
				sql.append(Constants.MAGIC_01," and t2.AFFIRM_FINAL_RESULT = ? ");
			} else {
				sql.append(" and 1!= 1 ");
			}
			sql.append(province," and t2.PROVINCE_CODE = ?");
			if (StringUtils.isNotBlank(groupType)) {
				sql.append(Constants.getEntTypeByGroupType(groupType)," and t2.ENT_TYPE = ?");
			}
			logger.info("update task status:" + sql.getFullSq());
			QueryFactory.getWriteQuery().execute(sql.getSQL(),sql.getParams());
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}

	}


	/**
	 * 查询定责抽样工单sql
	 * @param entId
	 * @param schema
	 * @param provinceCode
	 * @param dealDate
	 * @return
	 */
	private EasySQL createSql(String entId, String schema, String provinceCode, String fixStatus, String dealDate, String processId, String nodeId,String groupType) {
		EasySQL sql = new EasySQL("select t1.ID,t1.PROVINCE_CODE,t1.CITY_CODE,t1.ENT_TYPE,t1.P_ID,t1.APPEAL_TIME ");
		sql.append("from " + schema + ".C_BOX_DUTY_ORDER t1");
		sql.append("left join " + schema + ".XTY_RU_TASK t2 on t1.ID = t2.M_ID").append(processId, "and FLOW_KEY = ?").append(nodeId, "and NODE_ID = ?");
		sql.append("where 1=1");
		sql.append(entId, "and t1.ENT_ID = ?");
		sql.append(fixStatus, "and t1.FIX_STATUS = ?");
		if(StringUtils.equals(Constants.AFFIRM_STATUS_3, fixStatus)) {
			sql.append("02", "and t1.JUDGEMENT_ADUIT_RESULT = ?");
		}
		if(StringUtils.equals(Constants.AFFIRM_STATUS_21, fixStatus)) {
			sql.append("02", "and t1.JUDGEMENT_RESULT = ?");
		}
		sql.append(provinceCode, "and t1.PROVINCE_CODE = ?");
		sql.append(dealDate + " 00:00:00", "and t1.APPEAL_TIME >= ?");
		sql.append(dealDate + " 23:59:59", "and t1.APPEAL_TIME <= ?");
		sql.append("and t2.ID is null");
		if (StringUtils.isNotBlank(groupType)) {
			sql.append(Constants.getEntTypeByGroupType(groupType), "and t1.ENT_TYPE = ?");
		}
		logger.info("抽取SQL：" + sql.getFullSq());
		return sql;
	}

	public List<JSONObject> getExtractCount(String schema, String startDate, String endDate, String status, String provinceCode,String nodeId) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" select t1.ENT_TYPE,sum( ");
			sql.append(nodeId," case when t2.NODE_ID  = ? ");
			if(StringUtils.equals(Constants.AFFIRM_STATUS_3, status)) {
				sql.append(status," or (t1.FIX_STATUS = ? ");
				sql.append("02", "and t1.JUDGEMENT_RESULT = ?)");
			} else {
				sql.append(status," or t1.FIX_STATUS = ? ");
			}
			sql.append(" then 1 else 0 end) DUTY_ORDER,");
			sql.append(" sum(case when t2.ASSIGN_RESULT = '01' then 1 else 0 end) EXTRACT_ORDER");
			sql.append(" from " + schema + ".c_box_duty_order t1");
			sql.append(nodeId," left join "+schema+".xty_task_allocation_record t2 on t2.DUTY_ORDER_ID = t1.ID and t2.NODE_ID =? ");
			sql.append(" where 1=1 ");
			sql.append(startDate + " 00:00:00", "and t1.APPEAL_TIME >= ?");
			sql.append(endDate + " 23:59:59", "and t1.APPEAL_TIME <= ?");
			sql.append(provinceCode, "and t1.PROVINCE_CODE = ?");
			sql.append(" group by t1.ENT_TYPE");
			logger.info("获取当前周期的抽取信息：" + sql.getFullSq());
			return QueryFactory.getWriteQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return null;
	}

	public List<JSONObject> getExtractCount2(String schema, String startDate, String endDate, String currDate,String status, String provinceCode,String nodeId) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" select ENT_TYPE,sum(DUTY_ORDER) DUTY_ORDER ,sum(EXTRACT_ORDER) EXTRACT_ORDER from (");
			sql.append(" select t2.ENT_TYPE,count(distinct t1.ID) DUTY_ORDER,sum(case when t1.ASSIGN_RESULT = '01' then 1 else 0 end) EXTRACT_ORDER ");
			sql.append(" from " + schema + ".xty_task_allocation_record t1");
			sql.append(nodeId," right join " + schema + ".c_box_duty_order t2 on t1.DUTY_ORDER_ID = t2.ID and t1.NODE_ID = ? ");
			sql.append(" where 1=1 ");
			sql.append(startDate + " 00:00:00"," and t2.APPEAL_TIME >= ? ");
			sql.append(endDate + " 23:59:59"," and t2.APPEAL_TIME <= ? ");
			sql.append(provinceCode," and t2.PROVINCE_CODE = ? ");
			sql.append(" group by t2.ENT_TYPE ");
			sql.append(" union all ");
			sql.append(" select t1.ENT_TYPE,count(distinct t1.ID) DUTY_ORDER,0 EXTRACT_ORDER ");
			sql.append(" from " + schema + ".c_box_duty_order t1 ");
			sql.append(nodeId," left join " + schema + ".xty_task_allocation_record t2 on t2.DUTY_ORDER_ID = t1.ID and t2.NODE_ID = ? ");
			sql.append(" where 1=1 ");
			sql.append(" and t2.ID is null ");
			sql.append(provinceCode," and t1.PROVINCE_CODE = ? ");
			sql.append(status," and t1.FIX_STATUS = ? ");
			sql.append(currDate + " 00:00:00", "and t1.APPEAL_TIME >= ?");
			sql.append(currDate + " 23:59:59", "and t1.APPEAL_TIME <= ?");
			if(StringUtils.equals(Constants.AFFIRM_STATUS_3, status)) {
				sql.append("02", "and t1.JUDGEMENT_ADUIT_RESULT = ? ");
			}
			sql.append(" group by t1.ENT_TYPE ");
			sql.append(") temp group by ENT_TYPE");
			logger.info("获取当前周期的抽取信息：" + sql.getFullSq());
			return QueryFactory.getWriteQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return null;
	}

	public JSONObject getExtractCount3(String schema, String startDate, String endDate, String currDate,String status, String provinceCode,String nodeId,String groupType) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" select sum(DUTY_ORDER) DUTY_ORDER ,sum(EXTRACT_ORDER) EXTRACT_ORDER from (");
			sql.append(" select count(distinct t1.ID) DUTY_ORDER,sum(case when t1.ASSIGN_RESULT = '01' then 1 else 0 end) EXTRACT_ORDER ");
			sql.append(" from " + schema + ".xty_task_allocation_record t1");
			sql.append(nodeId," right join " + schema + ".c_box_duty_order t2 on t1.DUTY_ORDER_ID = t2.ID and t1.NODE_ID = ? ");
			sql.append(" where 1=1 ");
			sql.append(startDate + " 00:00:00"," and t2.APPEAL_TIME >= ? ");
			sql.append(endDate + " 23:59:59"," and t2.APPEAL_TIME <= ? ");
			sql.append(provinceCode," and t2.PROVINCE_CODE = ? ");
			sql.append(Constants.getEntTypeByGroupType(groupType)," and t2.ENT_TYPE = ? ");
			sql.append(" union all ");
			sql.append(" select count(distinct t1.ID) DUTY_ORDER,0 EXTRACT_ORDER ");
			sql.append(" from " + schema + ".c_box_duty_order t1 ");
			sql.append(nodeId," left join " + schema + ".xty_task_allocation_record t2 on t2.DUTY_ORDER_ID = t1.ID and t2.NODE_ID = ? ");
			sql.append(" where 1=1 ");
			sql.append(" and t2.ID is null ");
			sql.append(provinceCode," and t1.PROVINCE_CODE = ? ");
			sql.append(status," and t1.FIX_STATUS = ? ");
			sql.append(currDate + " 00:00:00", "and t1.APPEAL_TIME >= ?");
			sql.append(currDate + " 23:59:59", "and t1.APPEAL_TIME <= ?");
			if(StringUtils.equals(Constants.AFFIRM_STATUS_3, status)) {
				sql.append("02", "and t1.JUDGEMENT_ADUIT_RESULT = ? ");
			}
			sql.append(Constants.getEntTypeByGroupType(groupType)," and t1.ENT_TYPE = ? ");
			sql.append(") temp ");
			logger.info("获取当前周期的抽取信息：" + sql.getFullSq());
			return QueryFactory.getWriteQuery().queryForRow(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return null;
	}



	private EasySQL getRepairOrderList(String entId, String schema, String provinceCode, String startDate, String endDate, String processId, String nodeId) {
		EasySQL sql = new EasySQL("select t1.ID,t1.PROVINCE_CODE,t1.CITY_CODE,t1.ENT_TYPE,t1.P_ID,t1.APPEAL_TIME ");
		sql.append("from " + schema + ".C_BOX_DUTY_ORDER t1");
		sql.append("left join " + schema + ".XTY_RU_TASK t2 on t1.ID = t2.M_ID").append(processId, "and FLOW_KEY = ?").append(nodeId, "and NODE_ID = ?");
		sql.append("left join " + schema + ".XTY_TASK_ALLOCATION_RECORD t3 on t1.ID = t3.DUTY_ORDER_ID");
		sql.append("where 1=1");
		sql.append(entId, "and t1.ENT_ID = ?");
		sql.append(Constants.AFFIRM_STATUS_9, "and t1.FIX_STATUS = ?");
		sql.append(provinceCode, "and t1.PROVINCE_CODE = ?");
		sql.append(startDate + " 00:00:00", "and t1.APPEAL_TIME >= ?");
		sql.append(endDate + " 23:59:59", "and t1.APPEAL_TIME <= ?");
		// 无需抽取的定责工单
		sql.append(Constants.ASSIGN_RESULT_2, "and t3.ASSIGN_RESULT = ?");
		sql.append("and t2.ID is null");
		logger.info("补抽SQL：" + sql.getFullSq());
		return sql;
	}

	private EasySQL getDutyOrderCount(String schema, String provinceCode, String startDate, String endDate, String professorId, String nodeId) {
		EasySQL sql = new EasySQL("select count(1) ORDER_COUNT,");
		sql.append(Constants.ASSIGN_RESULT_1, "sum(case when ASSIGN_RESULT = ? then 1 else 0 end) ASSIGN_COUNT,");
		sql.append(Constants.ASSIGN_RESULT_2, "sum(case when ASSIGN_RESULT = ? then 1 else 0 end) UN_ASSIGN_COUNT");
		sql.append("from " + schema + ".XTY_TASK_ALLOCATION_RECORD t1");
		sql.append("right join " + schema + ".C_BOX_DUTY_ORDER t2 on t1.DUTY_ORDER_ID = t2.ID");
		sql.append("where 1=1");
		sql.append(professorId, "and PROFESSOR_ID = ?");
		sql.append(nodeId, "and NODE_ID = ?");
		sql.append(provinceCode, "and t2.PROVINCE_CODE = ?");
		sql.append(startDate + " 00:00:00", "and t2.APPEAL_TIME >= ?");
		sql.append(endDate + " 23:59:59", "and t2.APPEAL_TIME <= ?");
		return sql;
	}

	/**
	 * 修改未分配的工单为已完成
	 * @param schema
	 * @param orderList
	 * @throws SQLException
	 */
	private void updateOrderStatus(String entId, String schema, JSONObject nodeJson, List<JSONObject> orderList,String status) throws SQLException {
		if(CommonUtil.listIsNotNull(orderList)) {
			List<Object[]> paramList = new ArrayList<Object[]>();
			for (JSONObject row : orderList) {
				String orderId = row.getString("ID");
				paramList.add(new Object[] { status, orderId });
				// 记录分配日志
				saveRecordLog(entId, schema, nodeJson.getString("PROCESS_ID"), null, null, orderId, nodeJson.getString("NODE_ID"), Constants.ASSIGN_RESULT_2);
				this.updateTaskCancelStatus(QueryFactory.getWriteQuery(),schema,orderId);
			}
			this.getQuery().executeBatch("update " + schema + ".C_BOX_DUTY_ORDER set FIX_STATUS = ? where ID = ?", paramList);
		}
	}

	private void updateOrderExtractStatus(String entId, String schema, JSONObject nodeJson, List<JSONObject> orderList,String status) throws SQLException {
		if(CommonUtil.listIsNotNull(orderList)) {
			List<Object[]> paramList = new ArrayList<Object[]>();
			for (JSONObject row : orderList) {
				String orderId = row.getString("ID");
				paramList.add(new Object[] { status,Constants.MAGIC_02, orderId });
				// 记录分配日志
				saveRecordLog(entId, schema, nodeJson.getString("PROCESS_ID"), null, null, orderId, nodeJson.getString("NODE_ID"), Constants.ASSIGN_RESULT_2);
				this.updateTaskCancelStatus(QueryFactory.getWriteQuery(),schema,orderId);
			}
			this.getQuery().executeBatch("update " + schema + ".C_BOX_DUTY_ORDER set FIX_STATUS = ?,JUDGE_IS_EXTRACT=? where ID = ?", paramList);
		}
	}


	/**
	 * 设置处理人员
	 * @param entId
	 * @param busiOrderId
	 * @param schema
	 * @param nodeJson 节点信息
	 * @param orderJson 工单信息
	 * @throws Exception
	 */
	private void setUserByOrder(String entId ,String busiOrderId , String schema,  JSONObject nodeJson, JSONObject orderJson, String fixStatus,boolean isExtract) throws Exception {
		String orderId = orderJson.getString("ID");
		String handleAcc = null;
		String handleName = null;
		String handleDeptCode = "";
		String handleDeptName = "";
		String userGroupName = "";
		String id = RandomKit.randomStr();
		String dateStr = DateUtil.getCurrentDateStr();
		if(StringUtils.isBlank(handleAcc)) {
			JSONObject assignUser = getAssignUser(entId, busiOrderId, orderId, id, Constants.DUTY_FLOW_KEY, nodeJson);
			if(assignUser == null) {
				throw new Exception("未找到分配人员");
			}
			handleName = assignUser.getString("userName");
			handleAcc = assignUser.getString("userAcc");
			handleDeptCode = assignUser.getString("userDeptCode");
			handleDeptName = assignUser.getString("userDeptName");
			userGroupName = assignUser.getString("userGroupName");
		}

		logger.info("定责工单[" + orderId + "]分配处理人员[" + handleAcc + "]处理部门[" + handleDeptCode + "]处理部门名称[" + handleDeptName + "]");
		EasyRecord record = new EasyRecord(schema + ".XTY_RU_TASK", "ID");

		record.put("ID", id);
		record.put("TASK_ID", IDGenerator.getDefaultNUMID());
		record.put("M_ID", orderId);
		record.put("NODE_ID", nodeJson.getString("NODE_ID"));
		record.put("NODE_NAME", nodeJson.getString("NODE_NAME"));
		record.put("PROVINCE", orderJson.getString("PROVINCE_CODE"));
		record.put("CITY", orderJson.getString("CITY_CODE"));
		record.put("DEPT_TYPE", nodeJson.getString("DEAL_DEPT_TYPE"));
		record.put("DEPT_CODE", handleDeptCode);
		record.put("DEPT_NAME", handleDeptName);
		record.put("ASSIGN_TYPE", nodeJson.getString("ALLOCATION_TYPE"));
		record.put("STATUS", "01");
		record.put("CREATE_TIME", dateStr);
		record.put("OPTION_TIME", dateStr);
		record.put("FLOW_KEY", nodeJson.getString("PROCESS_ID"));
		record.put("HANDLE_ACC", handleAcc);
		record.put("HANDLE_NAME", handleName);
		JSONObject provinceCfg = ClassTools.getClass(OrderProvinceCfgService.class).getProvinceCfg(schema, orderJson.getString("PROVINCE_CODE"), "0");
		String timeStyleId = provinceCfg.getString("TIME_STYLE_ID");
		String dealDayType = nodeJson.getString("DEAL_DAY_TYPE");
		String dealDays = nodeJson.getString("DEAL_DAYS");
		String appealTime = orderJson.getString("APPEAL_TIME");
		String dealDayType2 = nodeJson.getString("DEAL_DAY_TYPE2");
		String dealDays2 = nodeJson.getString("DEAL_DAYS2");
		String planDealTime = ClassTools.getClass(OrderTimeService.class).getPlanDealTime(entId, busiOrderId, orderId, timeStyleId, appealTime, dealDayType, CommonUtil.parseInt(dealDays));
		if (StringUtils.isNotBlank(dealDays2) && Integer.parseInt(dealDays2) > 0) {
			planDealTime = DateUtil.addDay(DateUtil.TIME_FORMAT,planDealTime,1);
			planDealTime = ClassTools.getClass(OrderTimeService.class).getPlanDealTime(entId, busiOrderId, orderId, timeStyleId, planDealTime, dealDayType2, CommonUtil.parseInt(dealDays2));
		}
		record.put("PLAN_TIME", planDealTime);
		String calType = StringUtils.isNotBlank(dealDayType2) ? dealDayType2 : dealDayType;
		record.put("DAY_TYPES", calType);
		if ("2".equals(calType)) {
			int workDays = WorkTimeUtils.getWorkDays(entId, schema, timeStyleId, dateStr, planDealTime);
			record.put("WORKING_DAYS", workDays);
		} else {
			int natureDays = WorkTimeUtils.getNatureDays(dateStr, planDealTime);
			record.put("NATURAL_DAYS", natureDays);
		}
		this.getQuery().save(record);

		if(StringUtils.equalsAny(fixStatus, Constants.AFFIRM_STATUS_3, Constants.AFFIRM_STATUS_9)) {
			this.getQuery().execute("update " + schema + ".C_BOX_DUTY_ORDER set FIX_STATUS = ? where ID = ?", Constants.AFFIRM_STATUS_4, orderId);
		} else if(StringUtils.equals(fixStatus, Constants.AFFIRM_STATUS_5)){
			this.getQuery().execute("update " + schema + ".C_BOX_DUTY_ORDER set FIX_STATUS = ? where ID = ?", Constants.AFFIRM_STATUS_6, orderId);
		} else if(StringUtils.equals(fixStatus, Constants.AFFIRM_STATUS_21)){
			this.getQuery().execute("update " + schema + ".C_BOX_DUTY_ORDER set FIX_STATUS = ?,JUDGE_IS_EXTRACT=? where ID = ?", Constants.AFFIRM_STATUS_14,isExtract?Constants.MAGIC_01:Constants.MAGIC_02, orderId);
		}

		record.put("userGroupName", userGroupName);
		DutyOrderExService.updateDutyRunEx(getQuery(), schema, record, false, false);

		saveRecordLog(entId, schema, nodeJson.getString("PROCESS_ID"), id, null, orderId, nodeJson.getString("NODE_ID"), isExtract?Constants.ASSIGN_RESULT_1:Constants.ASSIGN_RESULT_2);

		this.updateTaskCancelStatus(QueryFactory.getWriteQuery(),schema,orderId);
	}

	private void updateTaskCancelStatus(EasyQuery query, String schema, String mId) {
		try {
			EasyRecord record = new EasyRecord(schema + ".xty_ru_task_his", "M_ID");
			record.put("M_ID", mId);
			record.put("IS_CANCEL", "02");
			query.update(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}


	/**
	 * 根据工单的分配方式，找出要分配的人员，返回人员信息
	 * @param entId
	 * @param busiOrderId
	 * @param orderId         工单id
	 * @param nodeConfig  工单对应的省份的分配方式
	 * @return  json中包含userAcc、userDeptCode
	 */
	public JSONObject getAssignUser(String entId,String busiOrderId,String orderId,String taskId,String flowKey,JSONObject nodeConfig) {
		String allocationType = nodeConfig.getString("ALLOCATION_TYPE");
		AllocationEnum item = AllocationEnum.getItem(allocationType);
		if (Objects.isNull(item)) {
			JSONObject object = new JSONObject();
			object.put("userDeptCode","");
			object.put("userDeptName", "");
			object.put("userAcc", "");
			object.put("userName", "");
			return object;
		}
		AllocationStrategy strategy = item.getStrategy();
		JSONObject allocation = strategy.allocation(entId, busiOrderId, orderId,taskId, flowKey, nodeConfig,null);
		allocation.put("userDeptCode",StringUtils.isNotBlank(allocation.getString("userDeptCode")) ? allocation.getString("userDeptCode") : "");
		allocation.put("userDeptName", StringUtils.isNotBlank(allocation.getString("userDeptName")) ? allocation.getString("userDeptName") : "");
		allocation.put("userAcc",StringUtils.isNotBlank(allocation.getString("userAcc")) ? allocation.getString("userAcc") : "");
		allocation.put("userName",StringUtils.isNotBlank(allocation.getString("userName")) ? allocation.getString("userName") : "");

		// 新增分配工作组
		JSONObject rs = allocation.getJSONObject("rs");
		if (rs != null && "2".equals(rs.getString("rsType"))) {
			allocation.put("userGroupName", rs.getString("rsName"));
		}
		logger.info("获取分配结果：" + JSONObject.toJSONString(allocation));
		return allocation;
	}


	private void saveRecordLog(String entId, String schema, String processId, String taskId, String appealOrderId, String dutyOrderId, String nodeId, String assginResult) {
		try {
			String logId = this.getQuery().queryForString("select ID from " + schema + ".XTY_TASK_ALLOCATION_RECORD where DUTY_ORDER_ID = ? and PROFESSOR_ID = ? and NODE_ID = ?", dutyOrderId, processId, nodeId);

			EasyRecord record = new EasyRecord(schema + ".XTY_TASK_ALLOCATION_RECORD", "ID");
			record.put("PROFESSOR_ID", processId);
			record.put("TASK_ID", taskId);
			record.put("APPEAL_ORDER_ID", appealOrderId);
			record.put("DUTY_ORDER_ID", dutyOrderId);
			record.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			record.put("NODE_ID", nodeId);
			record.put("ASSIGN_RESULT", assginResult);
			if(StringUtils.isNotBlank(logId)) {
				record.put("ID", logId);
				this.getQuery().update(record);
			} else {
				record.put("ID", RandomKit.randomStr());
				this.getQuery().save(record);
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}

	private EasyQuery getQuery() {
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_ONE);
	}

	private EasyQuery getReadQuery() {
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_READ_NAME);
	}

	/**
	 * 响应json
	 * @return
	 */
	private JSONObject getSuccJson() {
		JSONObject result = new JSONObject();
		result.put("state", "1");
		result.put("msg", "操作成功");
		return result;
	}

	/**
	 * 响应json
	 * @return
	 */
	private JSONObject getFailJson() {
		JSONObject result = new JSONObject();
		result.put("state", "500");
		result.put("msg", "操作失败");
		return result;
	}

	/**
	 * 响应json
	 * @return
	 */
	public JSONObject getFailJson(String msg) {
		JSONObject result = new JSONObject();
		result.put("state", "500");
		result.put("msg", msg);
		return result;
	}

	

}
