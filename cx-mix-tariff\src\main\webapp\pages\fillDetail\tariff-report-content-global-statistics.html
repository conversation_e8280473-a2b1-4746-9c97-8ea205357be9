<!DOCTYPE html>
<html>

<head>
  <title>报送资费内容情况整体统计</title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
  <!-- 基础的 css js 资源 -->
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.4">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.3">
  <link rel="stylesheet" href="./common.css?v=20241127">
  <!-- 表头样式覆盖 -->
  <link rel="stylesheet" href="/cx-mix-report/static/css/tableModify.css?v=1.0.0">
  <link rel="stylesheet" href="/cx-mix-tariff/static/css/searchForm.css">
  <style>
    #tariff-report-content-global-statistics .search-form.grid-5 {
      gap: 16px 4px !important;
    }
  </style>
</head>

<body class="yq-page-full vue-box">
  <div id="tariff-report-content-global-statistics" class="flex yq-table-page" v-loading="loading"
    element-loading-text="加载中..." v-cloak v-auth:[permissions]="'cx-xty-tariff-report-whole-export'">
    <div class="yq-card">
      <div class="card-header">
        <div class="head-title">报送资费内容情况整体统计</div>
        <div class="yq-table-control">
          <el-button type="primary" plain size="small"
            @click="handleExportFront($refs.table.$el, {title: '报送资费内容情况整体统计'})"
            v-if="permissions['cx-xty-tariff-report-whole-export']">
            <i class="el-icon-download"></i>导出
          </el-button>
        </div>
      </div>
      <div class="card-content">
        <div class="search-box">
          <senior-search :show.sync="moreSearch">
            <el-form class="search-form grid-5" :model="searchForm" :rules="rules" ref="searchForm" size="small"
              label-width="90px">
              <!-- 默认搜索条件 -->
              <el-form-item label="适用地区" prop="provinceCode">
                <el-cascader v-model="tree" @change="handleCasader" :options="options" :props="{multiple: true}"
                  clearable collapse-tags placeholder=" 请选择">
                </el-cascader>
              </el-form-item>
              <el-form-item label="运营商" prop="entCode">
                <el-select v-model="searchForm.entCode" placeholder="请选择" multiple collapse-tags filterable clearable>
                  <el-option v-for="(label, value) in companys" :key="value" :label="label" :value="value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="一级分类" prop="type1">
                <el-select v-model="searchForm.type1" placeholder="请选择" multiple collapse-tags clearable>
                  <el-option v-for="(label, value) in XTY_TARIFF_ONE_TYPE" :key="value" :label="label"
                    :value="value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="资费状态" prop="status">
                <el-select v-model="searchForm.status" placeholder="请选择" multiple collapse-tags clearable>
                  <el-option v-for="(label, value) in XTY_TARIFF_STATUS" :key="value" :label="label"
                    :value="value"></el-option>
                </el-select>
              </el-form-item>
              <!-- 高级搜索条件 -->
              <template v-if="moreSearch">
                <el-form-item label="是否公示" prop="isPublic">
                  <el-select v-model="searchForm.isPublic" placeholder="请选择" clearable>
                    <el-option v-for="(label, value) in is_public_list" :key="value" :label="label"
                      :value="value"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="是否通信类" prop="isTelecom">
                  <el-select v-model="searchForm.isTelecom" placeholder="请选择" multiple collapse-tags clearable>
                    <el-option v-for="(label, value) in XTY_TARIFF_TELECOM" :key="value" :label="label"
                      :value="value"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="报送主体" prop="reporter">
                  <div class="baseflex">
                    <el-cascader v-model="searchForm.label_1" :options="provinceAndGroup" :props="provinceCascaderProps"
                      :show-all-levels="false" placeholder="请选择" clearable style="margin-right: 2px"></el-cascader>
                    <el-select v-model="searchForm.label_2" placeholder="请选择" filterable clearable>
                      <el-option v-for="(label, value) in XTY_REPORTER_ENT" :key="value" :label="label"
                        :value="value"></el-option>
                    </el-select>
                  </div>
                </el-form-item>
                <el-form-item label="公示版本" prop="version">
                  <el-select v-model="searchForm.version" placeholder="请选择" clearable>
                    <el-option v-for="(item, index) in versionOptions" :key="index" :label="item.VERSION_NO"
                      :value="item.VERSION_NO"></el-option>
                  </el-select>
                </el-form-item>
              </template>
              <el-form-item class="btns" label-width="16px">
                <el-button type="primary" plain size="small" icon="el-icon-refresh"
                  @click="handleReset">{{getI18nValue('重置')}}</el-button>
                <el-button type="primary" size="small" icon="el-icon-search"
                  @click="getList">{{getI18nValue('搜索')}}</el-button>
                <el-button type="primary" plain size="small" @click.stop="moreSearch = !moreSearch">
                  <img src="/easitline-cdn/vue-yq/static/imgs/filter.png" alt="">高级搜索
                </el-button>
              </el-form-item>
            </el-form>
          </senior-search>
        </div>

        <div class="yq-table">
          <el-table ref="table" :data="tableData" height="100%" stripe border fit style="width: 100%"
            :span-method="spanMethod" @sort-change="handleSortChange">
            <el-table-column label="省份" prop="provinceName" min-width="100"></el-table-column>
            <el-table-column label="运营商" prop="entName" min-width="120">
              <template slot-scope="scope">
                {{ getReporterName(scope.row) }}
              </template>
            </el-table-column>
            <el-table-column label="资费总数" prop="total" min-width="120">
              <template slot-scope="scope">
                <el-link type="primary" :underline="false" @click="openTab(scope.row)">
                  {{ scope.row.total }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column v-for="(rule, ruleCode) in ruleDict" :key="ruleCode" :prop="ruleCode" :label="rule"
              min-width="120">
              <template slot-scope="scope">
                <el-link type="primary" :underline="false" @click="openTab(scope.row, ruleCode)">
                  {{ scope.row[ruleCode] }}
                </el-link>
              </template>
            </el-table-column>
            <el-empty slot="empty" description="暂无信息"></el-empty>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</body>
<script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
<script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
<script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
<script src="/cc-base/static/js/my_i18n.js?v=202111"></script>
<script src="/cc-base/static/js/i18n.js?v=1"></script>
<script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.1"></script>
<script src="/cx-mix-tariff/pages/fillDetail/mixins/statisticMixins.js?v=20241224"></script>
<script type="text/javascript" src="/cx-mix-tariff/static/js/time.js"></script>
<script>
  var appPage = new Vue({
    el: '#tariff-report-content-global-statistics',
    mixins: [statisticMixins],
    data: function () {
      return {
        moreSearch: false,
        searchForm: {
          provinceCode: '',     // 省份代码
          areaCode: '',
          entCode: [],          // 运营商
          type1: [],            // 一级分类
          status: [],           // 资费状态
          isPublic: '',         // 是否公示
          isTelecom: [],        // 是否通信类
          version: '',       // 版本号
          label_1: '',          // 报送主体
          label_2: '',          // 报送主体
        },
        tree: [],
        rules: null,
        options: [],
        versionOptions: [],
        ruleDict: {},
        provinceAndGroup: [],
        provinceCascaderProps: {
          value: "id",
          label: "name",
          children: "children",
          emitPath: false,
        },
        XTY_TARIFF_ONE_TYPE: {},
        XTY_TARIFF_TWO_TYPE: {},
        XTY_TARIFF_TELECOM: {},
        XTY_REPORTER_ENT: {},
        XTY_TARIFF_STATUS: {},
        is_public_list: {
          Y: "公示",
          N: "未公示",
        },
      }
    },
    created() {
      this.getVersions()
      this.getProvinceAndGroup()
      this.getRuleDict()
      this.getDict()
      this.handleGetProvinces()
      this.getXTY_REPORTER_ENT()
    },
    methods: {
      handleReset() {
        this.$refs['searchForm'].resetFields();
        this.searchForm.label_1 = ''
        this.searchForm.label_2 = ''
        this.tree = []
        this.handleCasader()
        this.getList()
      },
      getList() {
        this.$refs.searchForm?.validate?.(valid => {
          if (!valid) {
            return false
          }

          this.loading = true
          const payload = {
            ...this.searchForm,
            entCode: this.searchForm.entCode.join(),
            type1: this.searchForm.type1.join(),
            isTelecom: this.searchForm.isTelecom.join(),
            status: this.searchForm.status.join(),
          }

          let label_1 = this.findProvince(this.searchForm.label_1, 'id')
          payload.reporter = label_1 ? (label_1.tariffProvinceCode + this.searchForm.label_2) : this.searchForm.label_2

          yq.remoteCall('/cx-mix-tariff/webcall?action=tariffPublicLibStatDao.reportTariffWholeStat', payload)
            .then(res => {
              if (res.data) {
                this.tableData = (res.data.statistics || [])
                  .sort((a, b) => a.provinceCode - b.provinceCode)
                  .sort((a, b) => a.provinceCode === b.provinceCode ? (a.entCode - b.entCode) : 0)
              }
            })
            .finally(() => this.loading = false)
        })
      },
      getReporterName(row) {
        if (row.provinceCode === '999999') {
          return row.entName
        } else {
          return row.provinceName + row.entName
        }
      },
      handleCasader() {
        const label1 = []
        const label2 = []
        this.tree.forEach(item => {
          label1.push(item[0])
          label2.push(item[1])
        })
        this.searchForm.provinceCode = [...new Set(label1)].join(',');
        this.searchForm.areaCode = [...new Set(label2)].join(',')
      },
      openTab(row, ruleCode) {
        const data = {
          sourceChannel: 'tariff-report-content-global-statistics',
          provinceCode: row.provinceCode === '999999' ? '' : row.provinceCode,
          ent: row.entCode === '999999' ? this.searchForm.entCode.join() : row.entCode,
          isPublic: this.searchForm.isPublic,
          is_telecom: this.searchForm.isTelecom.join(),
          label_1: this.searchForm.label_1,
          label_2: this.searchForm.label_2,
          TYPE1: this.searchForm.type1.join(),
          publicVersions: this.searchForm.version,
          fieldCheckNo: ruleCode,
          status: this.searchForm.status.join(),
        }
        let tree = row.provinceCode === '999999' ? this.tree : this.tree.filter(item => item[0] === row.provinceCode)
        tree.length && localStorage.setItem("areaTree", JSON.stringify(tree));
        top.popup.openTab({
          url: '/cx-mix-tariff/pages/fillDetail/china-all-ent-check.html',
          title: '报送字段检查（全行业）',
          data,
          id: 'cx-xty-tariff-list-qhyjc',
        });
      },
      // 获取字典数据
      getVersions() {
        return yq.remoteCall(
          "/cx-mix-tariff/webcall?action=crawlerTariffData.getVersionList"
        ).then((res) => {
          this.versionOptions = res.data || [];
        });
      },
      getCommonDict() {
        let data = {
          "controls": [
            "common.getDict(XTY_TARIFF_ENT)"
          ],
          "params": {}
        }
        return yq.remoteCall('/cx-mix-report/webcall', data).then(res => {
          this.companys = res["common.getDict(XTY_TARIFF_ENT)"].data || {}
          // this.companys[999999] = '全行业'
        })
      },
      // 获取报送主体的省份
      getProvinceAndGroup() {
        return yq
          .remoteCall(
            "/cx-mix-tariff/webcall?action=common.queryTariffProvinceTree"
          )
          .then((res) => {
            if (res.state == 1) {
              this.provinceAndGroup = res.data || [];
            }
          });
      },
      // 获取检查规则字典
      getRuleDict() {
        return yq
          .remoteCall(
            "/cx-mix-tariff/webcall?action=common.tariffRuleDict"
          )
          .then((res) => {
            this.ruleDict = res.data || {};
          });
      },
      // 获取报送主体的运营商code
      getXTY_REPORTER_ENT() {
        yq.remoteCall(
          "/cx-mix-tariff/webcall?action=common.ents",
          {},
          (res) => {
            this.XTY_REPORTER_ENT = res.data
          }
        );
      },
      // 获取字典数据
      getDict() {
        yq.daoCall(
          {
            controls: [
              "common.getDict(XTY_TARIFF_ONE_TYPE)",
              "common.getDict(XTY_TARIFF_TWO_TYPE)",
              "common.getDict(XTY_TARIFF_TELECOM)",
              "common.getDict(XTY_TARIFF_STATUS)"
            ],
            params: {},
          },
          (data) => {
            this.XTY_TARIFF_ONE_TYPE =
              data["common.getDict(XTY_TARIFF_ONE_TYPE)"].data;
            this.XTY_TARIFF_TWO_TYPE =
              data["common.getDict(XTY_TARIFF_TWO_TYPE)"].data;
            this.XTY_TARIFF_TELECOM =
              data["common.getDict(XTY_TARIFF_TELECOM)"].data;
            this.XTY_TARIFF_STATUS =
              data["common.getDict(XTY_TARIFF_STATUS)"].data;
          },
          { contextPath: "cx-mix-tariff" }
        );
      },
      //获取省份树结构
      handleGetProvinces() {
        yq.remoteCall("/cx-mix-tariff/webcall?action=common.queryAreaTree", {}, (res) => {
          this.options = res.data || []
        })
      },
    }
  });
</script>

</html>