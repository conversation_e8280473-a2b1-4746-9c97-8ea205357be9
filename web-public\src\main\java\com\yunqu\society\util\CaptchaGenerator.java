package com.yunqu.society.util;

import com.yunqu.society.base.CommonLogger;
import org.apache.log4j.Logger;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.util.Base64;
import java.util.Random;

/**
 * 验证码工具类
 * 手写验证码生成功能，模拟前端Canvas效果
 *
 * <AUTHOR> Assistant
 * @date 2025-08-07
 */
public class CaptchaGenerator {

    private static final Logger logger = CommonLogger.getLogger();

    /** 验证码图片宽度 */
    private static final int WIDTH = 110;

    /** 验证码图片高度 */
    private static final int HEIGHT = 36;

    /** 验证码字符数 */
    private static final int CODE_COUNT = 4;

    /** 验证码字符集 */
    private static final String CHARS = "ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789";

    /** 字体大小范围 */
    private static final int FONT_SIZE_MIN = 20;
    private static final int FONT_SIZE_MAX = 28;

    /** 背景颜色范围 */
    private static final int BG_COLOR_MIN = 180;
    private static final int BG_COLOR_MAX = 240;

    /** 字体颜色范围 */
    private static final int FONT_COLOR_MIN = 50;
    private static final int FONT_COLOR_MAX = 160;

    /** 干扰线颜色范围 */
    private static final int LINE_COLOR_MIN = 40;
    private static final int LINE_COLOR_MAX = 180;

    private static final Random random = new Random();

    /**
     * 生成验证码图片
     *
     * @return CaptchaResult 包含验证码文本和Base64图片数据
     */
    public static CaptchaResult generateCaptcha() {
        try {
            // 生成验证码文本
            String code = generateCode();

            // 创建图片
            BufferedImage image = new BufferedImage(WIDTH, HEIGHT, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = image.createGraphics();

            // 设置抗锯齿
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            // 绘制背景
            drawBackground(g2d);

            // 绘制验证码文字
            drawText(g2d, code);

            // 绘制干扰线
            drawLines(g2d);

            // 绘制干扰点
            drawDots(g2d);

            g2d.dispose();

            // 转换为Base64
            String imageBase64 = imageToBase64(image);

            logger.info("生成验证码成功，验证码：" + code);

            return new CaptchaResult(code, imageBase64);

        } catch (Exception e) {
            logger.error("生成验证码失败", e);
            throw new RuntimeException("生成验证码失败", e);
        }
    }

    /**
     * 生成随机验证码文本
     */
    private static String generateCode() {
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < CODE_COUNT; i++) {
            code.append(CHARS.charAt(random.nextInt(CHARS.length())));
        }
        return code.toString();
    }

    /**
     * 绘制背景
     */
    private static void drawBackground(Graphics2D g2d) {
        Color bgColor = randomColor(BG_COLOR_MIN, BG_COLOR_MAX);
        g2d.setColor(bgColor);
        g2d.fillRect(0, 0, WIDTH, HEIGHT);
    }

    /**
     * 绘制验证码文字
     */
    private static void drawText(Graphics2D g2d, String code) {
        for (int i = 0; i < code.length(); i++) {
            // 随机字体大小
            int fontSize = randomNum(FONT_SIZE_MIN, FONT_SIZE_MAX);
            Font font = new Font("SimHei", Font.BOLD, fontSize);
            g2d.setFont(font);

            // 随机字体颜色
            Color fontColor = randomColor(FONT_COLOR_MIN, FONT_COLOR_MAX);
            g2d.setColor(fontColor);

            // 计算字符位置
            int x = (i + 1) * (WIDTH / (code.length() + 1));
            int y = randomNum(fontSize, HEIGHT - 5);

            // 随机旋转角度
            double angle = Math.toRadians(randomNum(-30, 30));

            // 保存当前变换
            Graphics2D g2dCopy = (Graphics2D) g2d.create();

            // 旋转并绘制字符
            g2dCopy.translate(x, y);
            g2dCopy.rotate(angle);
            g2dCopy.drawString(String.valueOf(code.charAt(i)), 0, 0);

            g2dCopy.dispose();
        }
    }

    /**
     * 绘制干扰线
     */
    private static void drawLines(Graphics2D g2d) {
        for (int i = 0; i < 2; i++) {
            Color lineColor = randomColor(LINE_COLOR_MIN, LINE_COLOR_MAX);
            g2d.setColor(lineColor);

            int x1 = randomNum(0, WIDTH);
            int y1 = randomNum(0, HEIGHT);
            int x2 = randomNum(0, WIDTH);
            int y2 = randomNum(0, HEIGHT);

            g2d.drawLine(x1, y1, x2, y2);
        }
    }

    /**
     * 绘制干扰点
     */
    private static void drawDots(Graphics2D g2d) {
        for (int i = 0; i < 20; i++) {
            Color dotColor = randomColor(0, 255);
            g2d.setColor(dotColor);

            int x = randomNum(0, WIDTH);
            int y = randomNum(0, HEIGHT);
            int radius = Math.max(1, 1); // 固定半径为1

            g2d.fillOval(x, y, radius * 2, radius * 2);
        }
    }

    /**
     * 生成随机数
     */
    private static int randomNum(int min, int max) {
        return random.nextInt(max - min) + min;
    }

    /**
     * 生成随机颜色
     */
    private static Color randomColor(int min, int max) {
        int r = randomNum(min, max);
        int g = randomNum(min, max);
        int b = randomNum(min, max);
        return new Color(r, g, b);
    }

    /**
     * 将图片转换为Base64
     */
    private static String imageToBase64(BufferedImage image) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "png", baos);
        byte[] imageBytes = baos.toByteArray();
        String base64 = Base64.getEncoder().encodeToString(imageBytes);
        return "data:image/png;base64," + base64;
    }

    /**
     * 验证码结果类
     */
    public static class CaptchaResult {
        /** 验证码文本 */
        private String code;

        /** Base64编码的图片数据 */
        private String imageBase64;

        public CaptchaResult(String code, String imageBase64) {
            this.code = code;
            this.imageBase64 = imageBase64;
        }

        public String getCode() {
            return code;
        }

        public String getImageBase64() {
            return imageBase64;
        }
    }
}
