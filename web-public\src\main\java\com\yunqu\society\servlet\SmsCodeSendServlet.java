package com.yunqu.society.servlet;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CacheUtil;
import com.yunqu.society.base.AppBaseServlet;
import com.yunqu.society.base.CommonLogger;
import com.yunqu.society.base.Constants;
import com.yunqu.society.service.SmsCodeSendService;
import com.yunqu.society.util.AesUtils;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.web.EasyResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;

@WebServlet("/interface/smsCode")
public class SmsCodeSendServlet extends AppBaseServlet {

    private static Logger logger = LoggerFactory.getLogger(CommonLogger.getLogger("sms").getName());

    private static final SmsCodeSendService smsCodeSendService = new SmsCodeSendService();
    
    public JSONObject actionForSendCode() {
        JSONObject param = this.getJSONObject();
        String phone = param.getString("phone");
        String inputCaptcha = param.getString("captcha");
        String type = param.getString("type");//1-提交工单 2-查询工单
        // 参数校验
        if (StringUtils.isBlank(phone)) {
            return EasyResult.error(500,"手机号不能为空");
        }
        if (StringUtils.isBlank(inputCaptcha)){
            return EasyResult.error(500,"验证码不能为空");
        }
        if (!isValidPhoneNumber(phone)) {
            return EasyResult.error(500,"手机号格式错误");
        }
        HttpServletRequest request = this.getRequest();
        if (!refererHost(request)){
            return EasyResult.fail("校验失败");
        }
        String random = param.getString("random");
        String captchaKey = "appeal:captcha:" + getClientOneIP(request) + ":" + random;
        logger.info("图形验证码key:{}", captchaKey);
        //验证 验证码
        String captcha = CacheUtil.get(captchaKey);

        if (StringUtils.isBlank(captcha)) {
            return EasyResult.fail("图形验证码已失效,请重新获取");
        }


        String phoneRandomKey = "appeal:phone:random:" + getClientOneIP(request) + ":" + random;
        String randomPhone = CacheUtil.get(phoneRandomKey);
        CacheUtil.delete(phoneRandomKey);
        if(StringUtils.isBlank(randomPhone) || !StringUtils.equalsAny(randomPhone, phone)) {
            logger.error("校验前后手机号不一致 randomPhone:" + randomPhone + ",phone:" + phone);
            return EasyResult.fail("校验前后手机号不一致");
        }

        try {
            //对验证码进行解密
            String  decryptKey= AesUtils.aesDecrypt(inputCaptcha);
            // 验证码比对忽略大小写
            if (!captcha.equalsIgnoreCase(decryptKey)) {
                return EasyResult.fail("图形验证码错误");
            }
            // 验证通过后清除Redis中的验证码
            CacheUtil.delete(captchaKey);
            // 调用service发送验证码
            smsCodeSendService.sendCode(phone);
            return EasyResult.ok();
        } catch (Exception e) {
            logger.error("发送验证码失败:", e);
            return EasyResult.error(500,e.getMessage());
        }
    }

    public JSONObject actionForVerifyCode() {
        JSONObject param = this.getJSONObject();
        String phone = param.getString("phone");
        String code = param.getString("code");
        // 参数校验
        if (StringUtils.isBlank(phone) || StringUtils.isBlank(code)) {
            return EasyResult.error(1000,"手机号或验证码不能为空");
        }
        try {
            // 调用service验证验证码
            String token = smsCodeSendService.verifyCode(phone, code);
            if (StringUtils.isNotBlank(token)) {
                return EasyResult.ok(token);
            } else {
                return EasyResult.error(1000,"短信验证码错误");
            }
        } catch (Exception e) {
            logger.error("验证验证码失败:", e);
            return EasyResult.error(500,"短信验证验证码验证失败,请稍后重试");
        }
    }

    /**
     * 校验手机号码格式
     * @param phone 手机号码
     * @return 是否为有效的手机号码
     */
    private boolean isValidPhoneNumber(String phone) {
        if (StringUtils.isBlank(phone)) {
            return false;
        }
        // 支持11位手机号码（1开头）
        if (phone.length() == 11 && phone.startsWith("1")) {
            return phone.matches("^1[3-9]\\d{9}$");
        }
        // 支持12位手机号码（01开头）
        if (phone.length() == 12 && phone.startsWith(Constants.PHONE_PREFIX_CHINA_TELECOM)) {
            return phone.matches("^01[3-9]\\d{9}$");
        }
        return false;
    }


    private  boolean refererHost(HttpServletRequest request){
        //referer检测，需要在cc-base里设置referer白名单才生效
        String refererHost = Constants.getServerWhiteHost();
        if(StringUtils.isNotBlank(refererHost)){
            boolean refererCheckResult = false;
            String referer = request.getHeader("referer");
            if(StringUtils.isNotBlank(referer)){
                String[] hosts = refererHost.split(";");
                for(String host : hosts){
                    if(referer.startsWith(host)){
                        refererCheckResult = true;
                        break;
                    }
                }
                if(!refererCheckResult){
                    logger.error("Referer 检查不合法, referer="+referer+",允许路径:"+refererHost);
                    return false;
                }
            }
        }
        return true;
    }

    private String getClientOneIP(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if (StringUtils.isNotBlank(ip) ) {
            ip = ip.split(",")[0];
        }
        return ip;
    }

}
