/**
 * 版本调度配置管理页面脚本
 */

// 配置类型枚举
const CONFIG_TYPE = {
  SYSTEM: 'SYSTEM',
  CUSTOM: 'CUSTOM'
}

// 启用状态枚举
const ENABLED_STATUS = {
  YES: 'Y',
  NO: 'N'
}

// 工具函数
const ConfigUtils = {
  // 格式化配置类型
  formatConfigType: function(type) {
    return type === CONFIG_TYPE.SYSTEM ? '系统配置' : '自定义配置'
  },
  
  // 获取配置类型标签样式
  getConfigTypeTagType: function(type) {
    return type === CONFIG_TYPE.SYSTEM ? 'warning' : 'primary'
  },
  
  // 格式化启用状态
  formatEnabledStatus: function(status) {
    return status === ENABLED_STATUS.YES ? '已启用' : '已禁用'
  },
  
  // 获取启用状态标签样式
  getEnabledStatusTagType: function(status) {
    return status === ENABLED_STATUS.YES ? 'success' : 'danger'
  },
  
  // 格式化执行日期
  formatRuleValue: function(value) {
    return `每月${value}日`
  },
  
  // 格式化有效期
  formatEffectivePeriod: function(startDate, endDate) {
    if (!startDate && !endDate) {
      return '永久有效'
    }
    return `${startDate || '不限'} ~ ${endDate || '不限'}`
  },
  
  // 验证执行日期
  validateRuleValue: function(value) {
    const num = parseInt(value)
    if (isNaN(num) || num < 1 || num > 31) {
      return { valid: false, message: '执行日期必须在1-31之间' }
    }
    return { valid: true }
  }
}

// API调用封装
const ConfigAPI = {
  // 获取配置列表
  getConfigList: function(params, callback) {
    yq.daoCall({
      controls: ["tariffCrawlerVersionConfig.getConfigList"],
      params: params
    }, callback, {
      contextPath: '/cx-mix-tariff'
    })
  },
  
  // 获取配置详情
  getConfigDetail: function(configId, callback) {
    yq.daoCall({
      controls: ["tariffCrawlerVersionConfig.getConfigDetail"],
      params: { ID: configId }
    }, callback, {
      contextPath: '/cx-mix-tariff'
    })
  },
  
  // 新增配置
  addConfig: function(configData, callback) {
    yq.remoteCall("/cx-mix-tariff/servlet/crawlerVersionConfig/add", configData, callback)
  },
  
  // 修改配置
  updateConfig: function(configData, callback) {
    yq.remoteCall("/cx-mix-tariff/servlet/crawlerVersionConfig/update", configData, callback)
  },
  
  // 删除配置
  deleteConfig: function(configId, callback) {
    yq.remoteCall("/cx-mix-tariff/servlet/crawlerVersionConfig/delete", {
      ID: configId
    }, callback)
  },
  
  // 获取执行历史
  getExecuteHistory: function(configId, callback) {
    yq.daoCall({
      controls: ["tariffCrawlerVersionConfig.getExecuteHistory"],
      params: { configId: configId }
    }, callback, {
      contextPath: '/cx-mix-tariff'
    })
  },
  
  // 检查今日版本生成任务
  checkTodayVersionGenerate: function(callback) {
    yq.daoCall({
      controls: ["tariffCrawlerVersionConfig.checkTodayVersionGenerate"],
      params: {}
    }, callback, {
      contextPath: '/cx-mix-tariff'
    })
  },
  
  // 检查日期冲突
  checkDateConflict: function(ruleValue, excludeConfigId, callback) {
    yq.daoCall({
      controls: ["tariffCrawlerVersionConfig.checkDateConflict"],
      params: { 
        ruleValue: ruleValue,
        excludeConfigId: excludeConfigId 
      }
    }, callback, {
      contextPath: '/cx-mix-tariff'
    })
  }
}

// 页面事件处理
const ConfigEvents = {
  // 初始化页面
  init: function() {
    console.log('版本调度配置页面初始化')
  },
  
  // 处理搜索
  handleSearch: function() {
    console.log('执行配置搜索')
  },
  
  // 处理重置
  handleReset: function() {
    console.log('重置搜索条件')
  },
  
  // 新增配置
  addConfig: function() {
    console.log('新增配置')
  },
  
  // 编辑配置
  editConfig: function(configId) {
    console.log('编辑配置:', configId)
  },
  
  // 删除配置
  deleteConfig: function(configId, configName) {
    console.log('删除配置:', configId, configName)
  },
  
  // 查看执行历史
  viewHistory: function(configId) {
    console.log('查看执行历史:', configId)
  },
  
  // 检查今日任务
  checkTodayTask: function() {
    console.log('检查今日任务')
  }
}

// 表单验证
const ConfigValidation = {
  // 验证配置表单
  validateConfigForm: function(form) {
    const errors = []
    
    // 配置名称验证
    if (!form.configName || form.configName.trim() === '') {
      errors.push('配置名称不能为空')
    }
    
    // 执行日期验证
    if (!form.ruleValue) {
      errors.push('执行日期不能为空')
    } else {
      const ruleValidation = ConfigUtils.validateRuleValue(form.ruleValue)
      if (!ruleValidation.valid) {
        errors.push(ruleValidation.message)
      }
    }
    
    // 优先级验证
    if (form.priorityOrder && isNaN(parseInt(form.priorityOrder))) {
      errors.push('优先级必须是数字')
    }
    
    // 有效期验证
    if (form.effectiveStartDate && form.effectiveEndDate) {
      if (new Date(form.effectiveStartDate) > new Date(form.effectiveEndDate)) {
        errors.push('生效开始日期不能晚于结束日期')
      }
    }
    
    return {
      valid: errors.length === 0,
      errors: errors
    }
  },
  
  // 验证搜索表单
  validateSearchForm: function(form) {
    // 执行日期范围验证
    if (form.ruleValue) {
      const ruleValidation = ConfigUtils.validateRuleValue(form.ruleValue)
      if (!ruleValidation.valid) {
        return { valid: false, message: ruleValidation.message }
      }
    }
    
    return { valid: true }
  }
}

// 数据处理
const ConfigDataProcessor = {
  // 处理配置列表数据
  processConfigListData: function(data) {
    if (!data || !Array.isArray(data)) {
      return []
    }
    
    return data.map(item => {
      return {
        ...item,
        CONFIG_TYPE_TEXT: ConfigUtils.formatConfigType(item.CONFIG_TYPE),
        IS_ENABLED_TEXT: ConfigUtils.formatEnabledStatus(item.IS_ENABLED),
        RULE_VALUE_TEXT: ConfigUtils.formatRuleValue(item.RULE_VALUE),
        EFFECTIVE_PERIOD_TEXT: ConfigUtils.formatEffectivePeriod(
          item.EFFECTIVE_START_DATE, 
          item.EFFECTIVE_END_DATE
        )
      }
    })
  },
  
  // 处理今日任务检查结果
  processTodayTaskResult: function(data) {
    if (!data) return null
    
    return {
      ...data,
      needGenerateText: data.needGenerate ? '是' : '否',
      matchedConfigsText: data.matchedConfigs ? 
        data.matchedConfigs.map(config => config.CONFIG_NAME).join('、') : 
        '无'
    }
  },
  
  // 处理执行历史数据
  processExecuteHistoryData: function(data) {
    if (!data || !Array.isArray(data)) {
      return []
    }
    
    return data.map(item => {
      return {
        ...item,
        EXECUTE_RESULT_TEXT: item.EXECUTE_RESULT === 'SUCCESS' ? '成功' : '失败'
      }
    })
  }
}

// 导出给全局使用
window.ConfigUtils = ConfigUtils
window.ConfigAPI = ConfigAPI
window.ConfigEvents = ConfigEvents
window.ConfigValidation = ConfigValidation
window.ConfigDataProcessor = ConfigDataProcessor
window.CONFIG_TYPE = CONFIG_TYPE
window.ENABLED_STATUS = ENABLED_STATUS