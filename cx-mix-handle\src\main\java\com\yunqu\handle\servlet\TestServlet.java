package com.yunqu.handle.servlet;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.ServiceUtil;
import com.yunqu.handle.base.AppBaseServlet;
import com.yunqu.handle.base.CommonLogger;
import com.yunqu.handle.base.Constants;
import com.yunqu.handle.base.QueryFactory;
import com.yunqu.handle.base.mapper.HumpMapper;
import com.yunqu.handle.service.AppealOrderCreateService;
import com.yunqu.handle.service.OrderFetchService;
import com.yunqu.handle.service.StatusSyncService;
import com.yunqu.handle.service.strategy.CancelAppealStrategy;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.InputStream;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 公告和企业信息服务类
 * <AUTHOR>
 * @date 2024-05-15 10:00:00
 */
@WebServlet("/servlet/test")
public class TestServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;

	private static Logger logger = LoggerFactory.getLogger(CommonLogger.getLogger("test").getName());

	private boolean flag = true;

    public JSONObject actionForComplain () {
        String startTime = this.getPara("startTime");
        String endTime = this.getPara("endTime");
        AppealOrderCreateService service = new AppealOrderCreateService();
            
            // 分页查询未处理的申诉记录
            boolean hasMoreData = true;
            
            while (hasMoreData) {
                logger.info("查询第{}页未处理投诉记录，每页{}条", 1, 50);
                
                // 调用服务查询数据
                JSONObject result = service.queryUnprocessedComplains(0, 50,startTime,endTime);
                
                if (result != null && result.getBooleanValue("success")) {
                    List<JSONObject> dataList = result.getJSONArray("data").toJavaList(JSONObject.class);
                    
                    if (dataList != null && !dataList.isEmpty()) {
                        logger.info("本页获取到{}条未处理投诉记录", dataList.size());
                        
                        // 处理申诉记录，调用OrderSubmitInfService发起工单
                        service.processComplains(dataList);
                        
                        // 如果返回的数据少于pageSize，说明没有更多数据了
                        if (dataList.size() < 50) {
                            hasMoreData = false;
                            logger.info("没有更多未处理投诉记录，结束处理");
                        } 
                    } else {
                        // 没有数据，结束循环
                        hasMoreData = false;
                        logger.info("没有未处理投诉记录");
                    }
                } else {
                    // 查询失败，结束循环
                    hasMoreData = false;
                    String errorMsg = result != null ? result.getString("message") : "查询失败";
                    logger.error("查询未处理投诉记录失败: {}", errorMsg);
                }
            }
            
            logger.info("投诉工单创建定时任务执行完成");
            return EasyResult.ok();
    }


	public EasyResult  actionForCancel() {
		// 获取resources/cancel.json
		Map<String, JSONObject> map = null;
		try {
			// 从resources目录读取cancel.json文件
			InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("cancel3.json");
			if (inputStream == null) {
				logger.error("未找到resources/cancel3.json文件");
				return EasyResult.error(500,"未找到cancel3.json配置文件");
			}
			
			// 读取文件内容
			StringBuilder content = new StringBuilder();
			byte[] buffer = new byte[1024];
			int length;
			while ((length = inputStream.read(buffer)) != -1) {
				content.append(new String(buffer, 0, length, "UTF-8"));
			}
			inputStream.close();
			
			// 解析JSON数组，提取orderId
			List<JSONObject> jsonArray = JSONArray.parseArray(content.toString(),JSONObject.class);
			map = jsonArray.stream().collect(Collectors.toMap(jsonObject -> jsonObject.getString("orderId"), jsonObject -> jsonObject,(o1,o2) -> o1));
            logger.info("从cancel.json读取到{}个订单ID", map.values().size());
			map.values().forEach(jsonObject -> {
				try {
                    execute(new JSONObject(){{put("id",jsonObject.getString("orderId"));put("createTime", jsonObject.getString("createTime"));}},getRequest(),getResponse());
                } catch (Exception e) {
                    logger.info("取消订单{}失败",jsonObject.getString("orderId"));
                }
			});
		} catch (Exception e) {
			logger.error("读取cancel.json文件失败: " + e.getMessage(), e);
			return EasyResult.error(500,"读取配置文件失败: " + e.getMessage());
		}
		
		return EasyResult.ok("申诉取消操作完成");			
	}


	public JSONObject execute(JSONObject params, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
                String id = params.getString("id");
                String createTime = params.getString("createTime");
                /* if (!id.equals("82469111059582832152166")) { 
                    return createResult(false,"不是测试订单",null);
                } */
                
                String procInstId = getProcInstId(id);
                boolean isCancel = true;
                try {
                    if (StringUtils.isBlank(id)) {
                        return createResult(false,"id不能为空",null);
                    }
                    JSONObject appeal = getAppeal(id);
                    if (appeal == null) {
                        return createResult(false,"申诉单不存在",null);
                    }
					long startTime = System.currentTimeMillis();
                    JSONObject param = new JSONObject();
                    param.put("procInstId", procInstId);
                    param.put("orderId", id);
                    param.put("orderNo", appeal.getString("ORDER_NO"));
                    param.put("entId", Constants.getEntId());
                    param.put("busiOrderId", Constants.getBusiOrderId());
                    param.put("schema", Constants.getSysSchema());
                    param.put("command", "endProcess");
                    param.put("title", "用户撤诉");
                    param.put("content", "用户撤诉");
                    param.put("userAcc","system");
                    param.put("userName","系统");
                    param.put("reason", "用户自行撤诉，工单关闭。");
                    param.put("flowVar", new JSONObject() {{put("COMPLETED", "01");}});
                    logger.info("-->[关闭工单请求] param:"+param.toJSONString());
                    JSONObject result = new JSONObject();
                    try {
                        result = ServiceUtil.invoke2("CC_EORDER_OPERATION_HANDLE", param);
                        logger.info("<--[关闭工单返回内容] result:"+result.toJSONString());
                        if (result != null && !result.getString("state").equals("1")) {
                            isCancel = false;
                        }
                    } catch (Exception e) {
                        isCancel = false;
                        logger.error("关闭工单失败: " + e.getMessage(), e);
                    }
                    JSONObject resultData = result.getJSONObject("data");
                    
                    JSONObject data = new JSONObject();
                    data.put("flowKey", "xty_ss");
                    data.put("flowInstanceId", procInstId);
                    data.put("orderId", id);
                    data.put("lastTaskId", resultData != null ?resultData.getString("taskId"):"");
                    data.put("entId", Constants.getEntId());
                    data.put("busiOrderId", Constants.getBusiOrderId());
                    data.put("assignee", "");
                    data.put("schema", Constants.getSysSchema());
                    logger.info("-->[关闭工单请求EXTEND] data:"+data.toJSONString());
                    JSONObject extend = ServiceUtil.invoke2("ORDER_EXTEND_TODO_SERVICE", data);
                    logger.info("<--[关闭工单请求EXTEND返回内容] result:"+extend.toJSONString());

                    logger.info("修改申诉单业务状态 orderId:" + id + ",serviceStatus=06");
                    // 修改申诉单业务状态，若为办结操作，不在此更新，后续一并更新
                    EasySQL sql = new EasySQL();
                    sql.append("update " + Constants.getSysSchema() + ".C_BOX_APPEAL_ORDER ");
                    sql.append("06", " set SERVICE_STATUS = ? ", false);
                    /* sql.append("01", " ,IS_COMPLETED = ? ", false);
                    sql.append(createTime, " ,COMPLETE_TIME = ? ", false); */
                    sql.append("04", " ,APPEAL_RESULT = ? ");
                    sql.append(id, " where M_ID = ? ", false);
                    getQuery().execute(sql.getSQL(), sql.getParams());
                    long endTime = System.currentTimeMillis();
                    logger.info("工单编号：" + appeal.getString("ORDER_NO") + ",耗时:" + (endTime - startTime));
                    if (!isCancel && !existEndProcess(id, Constants.getSysSchema())) {
                        addFlow(Constants.getSysSchema(), id, "6", "用户撤诉", "用户自行撤诉，工单关闭。",createTime);
                    }
                    
                    //ServiceUtil.invoke2("XTY_ORDER_SYN_ES_SERVICE", new JSONObject() {{put("orderId", id);put("schema", Constants.getSysSchema());}});
                    syncStaus("xty_ss", id, "06", "04");
                    return createResult(true, "申诉撤销成功", null);
                } catch (Exception e) {
                    String message = e.getMessage();
                    logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + message, e);
                    // execution  doesn't exist
                    if (StringUtils.isNotBlank(message) && message.contains("execution  doesn't exist")) {
                        logger.info("工单{}流程已不存在！！！！",id);
                        return createResult(true, "申诉撤销成功", null);

                    }
                }
                return createResult(false, "申诉撤销失败", null);
    }

	 /**
     * 统一返回结果方法
     * @param success 是否成功
     * @param message 消息
     * @param data 数据
     * @return 统一格式的返回结果
     */
    private JSONObject createResult(boolean success, String message, Object data) {
        JSONObject result = new JSONObject();
        result.put("success", success);
        result.put("message", message);
        if (data != null) {
            result.put("data", data);
        }
        return result;
    }

    private boolean existEndProcess (String id,String schema) {
       try {
            EasySQL sql = new EasySQL();
            sql.append(" select count(1)  from " + schema + ".c_bo_order_follow ");
            sql.append(id," where ORDER_ID = ? ");
            sql.append("用户撤诉" ," and TASK_NAME =? ");
            return QueryFactory.getQuery().queryForExist(sql.getSQL(), sql.getParams());
       } catch (Exception e) {
        logger.error("查询工单是否已办结失败: " + e.getMessage(), e);
       }
       return false;
    }



   

    /**
	 * 获取流程实例ID
	 * @return
	 * @throws SQLException
	 */
	public String getProcInstId(String id) throws SQLException {
		return QueryFactory.getWriteQuery().queryForString("select PROC_INST_ID from " + Constants.getSysSchema() + ".C_BO_BASE_ORDER where ID = ?", id);
	}

    private JSONObject getAppeal (String id) throws SQLException {
        EasySQL sql = new EasySQL();
        sql.append("select t1.*,t2.ORDER_NO from " + getTableName("c_box_appeal_order t1"));
        sql.append(" left join " + getTableName("c_bo_base_order t2"));
        sql.append(" on t1.M_ID = t2.ID");
        sql.append(" where 1=1 ");
        sql.append(id," and M_ID =? ",false);
        return getQuery().queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
    }
    
     /**
     * 添加跟进记录
     *
     * @param schema
     * @param orderId
     * @param type    跟进类型 0-创建工单 1-办理工单 2-转派工单 3-撤回工单 4-退回工单 5-签收工单 6-其他 7-暂存 8-取消签收工单 9-挂起 10-取消挂起 11-异常暂存 12-到达 13-分派 14-自动回访 15-工单归档 16-用户催办 17-用户补充信息 18-用户撤单 19-用户评价 20-自动分派 21-自动回收
     * @param content
     * @throws ServiceException
	 * -- ycbusi_ekf.c_bo_order_follow definition
	 */
    private void addFlow(String schema, String orderId, String type, String taskName, String content,String createTime) {
        try {
            EasyRecord record = new EasyRecord(getTableName("c_bo_order_follow"),"ID");
            record.set("ORDER_ID", orderId);
            record.set("TASK_NAME", taskName);
            record.set("TYPE", type);
            record.set("CONTENT", content);
            record.set("CREATE_TIME", createTime);
            record.set("CREATE_NAME", "系统");
            record.set("CREATE_ACC", "system");
            record.set("CREATE_DEPT_CODE", "system");
            record.set("EP_CODE", getEntId());
            record.set("BUSI_ORDER_ID", getBusiOrderId());
            record.set("CREATE_TIMESTAMP", System.currentTimeMillis());
			record.set("ID", IDGenerator.getDefaultNUMID());
			QueryFactory.getWriteQuery().save(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }
    

	/**
	 * 新增公告
	 * @return
	 */
	public EasyResult actionForSyncStatus() {
		try {
			int pageIndex = 0;
			int pageSize = 1000;
			while (flag) {
				EasySQL sql = new EasySQL();
				sql.append(" select t1.SERVICE_STATUS STATUS,t1.APPEAL_RESULT RESULT,t1.M_ID ID from ycbusi_ekf.c_box_appeal_order t1 left join ycbusi_ekf.c_bo_base_order t2 on t2.ID= t1.M_ID  where  SERVICE_STATUS in ('06','12') ");
				sql.append("2025-07-10 00:00:00"," and t1.create_time >= ? ");
                sql.append(DateUtil.getCurrentDateStr()," and t2.create_time<=?");
				sql.append(" limit " + pageIndex * pageSize + "," + pageSize);

				logger.info("sql:{}",sql.getFullSq());
				List<JSONObject> queryForRow = QueryFactory.getQuery().queryForList(sql.getSQL(), sql.getParams(),new HumpMapper());
				if (queryForRow == null || queryForRow.isEmpty()) {
					break;
				}
				StatusSyncService.getInstance().batchAdd(queryForRow, "xty_ss");
				pageIndex++;
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
		return EasyResult.ok();
	}

	public JSONObject actionForSyncOrderOrder () {
        try {
            String startTime = getPara("startTime");
            String endTime = getPara("endTime");;
			OrderFetchService service = new OrderFetchService();
            // 分页拉取工单数据
            boolean hasMoreData = true;
            int pageIndex = 0;
            int pageSize = 50;
            
            while (hasMoreData) {
                logger.info("拉取第{}页工单数据，每页{}条",  pageIndex, pageSize);
                // 调用服务拉取数据 DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD)
                JSONObject result = service.fetchOrders(pageIndex, pageSize,true,  startTime,endTime);

                if (result != null && result.getBooleanValue("success")) {
                    List<JSONObject> dataList = JSONArray.parseArray(result.getString("data"),JSONObject.class);
                    if (dataList != null && !dataList.isEmpty()) {
                        logger.info("本页获取到{}条工单数据", dataList.size());

                        try {
                            List<JSONObject> unSyncList = this.getUnSyncIds(dataList);
                            if (unSyncList != null && !unSyncList.isEmpty()) {
                                logger.info("本页获取到{}条未同步工单数据", unSyncList.size());

                                logger.info("开始自动同步未同步数据：{}" ,JSONObject.toJSONString(unSyncList));
                                service.saveOrdersToDatabase(unSyncList);
                                logger.info("结束自动同步未同步数据");
                            }
                        } catch (SQLException e) {
                            logger.info("查询未同步工单ID异常: " + e.getMessage(), e);
                        };
                        // 如果返回的数据少于pageSize，说明没有更多数据了
                        if (dataList.size() < pageSize) {
                            hasMoreData = false;
                        }  else {
                            pageIndex++;
                        }
                    } else {
                        logger.info("本页没有获取到工单数据，结束拉取");
                        hasMoreData = false;
                    }
                } else {
                    logger.error("拉取工单数据失败: {}", result != null ? result.getString("message") : "未知错误");
                    hasMoreData = false;
                }
            }
            return null;
		} catch(Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
		return EasyResult.ok();
    }


	 /**
     * 获取未同步的ID列表
     * 从输入列表中筛选出在数据库中不存在的ID
     * 
     * @param list 包含id字段的JSONObject列表
     * @return 未同步的ID列表，如果没有则返回null
     * @throws SQLException 数据库查询异常
     */
    private List<JSONObject> getUnSyncIds(List<JSONObject> list) throws SQLException {
        // 提取所有ID
        List<String> idList = list.stream()
                .map(item -> item.getString("id"))
                .collect(Collectors.toList());
        
        if (idList.isEmpty()) {
            return null;
        }
        
        // 查询数据库中已存在的ID
        EasySQL sql = new EasySQL();
        sql.append(" select ID from " + Constants.getSysSchema() + ".c_box_appeal");
        sql.append(" where 1=1 ");
        sql.appendIn(idList.toArray(new String[]{}), " and id  ");
        
        List<JSONObject> appealIds = QueryFactory.getQuery().queryForList(
                sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        
        if (CollectionUtils.isEmpty(appealIds)) {
            // 如果数据库中没有任何匹配的ID，说明所有ID都是未同步的
            return list;
        }
        
        // 使用HashSet提高查找效率，时间复杂度从O(n²)降低到O(n)
        Set<String> existingIds = appealIds.stream()
                .map(item -> item.getString("ID"))
                .collect(Collectors.toSet());
        
        // 筛选出未同步的ID
        return list.stream()
                .filter(item -> !existingIds.contains(item.getString("id")))
                .collect(Collectors.toList());
    }

	public EasyResult actionForStop() {
		flag = false;
		return EasyResult.ok();
	}

	public void syncStaus (String flowKey,String orderId,String status,String result) {
        try {
            logger.info("同步状态开始：" + flowKey + "，工单ID：" + orderId + "，状态：" + status + "，结果：" + result);
            JSONObject invoke2 = ServiceUtil.invoke2("CX-HANDLE-DATA-SYNC", new JSONObject(){{
                            put("command", "statusSync");
                            put("flowKey", flowKey);
                            put("id", orderId);
                            put("status", status);
                            put("result", result);
                            put("updateTime", DateUtil.getCurrentDateStr());
                        }});
            logger.info("同步状态结束：" + invoke2);
        } catch (ServiceException e) {
            logger.error("数据同步失败", e);
        }
    }

	public JSONObject actionForReloadClass () {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" select M_ID from " + Constants.getSysSchema() + ".c_box_appeal_order ");
			sql.append(" where 1=1");
			sql.append(" and create_time >='2025-07-16 00:00:00'");
			sql.append(" and class_code1 is null ");
			logger.info("sql:{}",sql.getFullSq());
			List<JSONObject> rows = QueryFactory.getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			for(JSONObject jsonObject : rows) {
				String mId = jsonObject.getString("M_ID");
				try {
					ServiceUtil.invoke2("XTY_INVOKE_AI_SERVICE", new JSONObject() {{put("orderId",mId);put("schema",Constants.getSysSchema());put("entId",Constants.getEntId());put("busiOrderId",Constants.getBusiOrderId());}});
				} catch (ServiceException e) {
					logger.error("数据同步失败", e);
				}
			}
		} catch (Exception e) {
			logger.info("数据同步失败");

		}
		return EasyResult.ok();

	}

	public JSONObject actionForRepairFile () {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" select M_ID from " + Constants.getSysSchema() + ".c_box_appeal_order ");
			sql.append(" where 1=1");
			sql.append(" and create_time is null");
			sql.append(" and class_code1 is null ");
			logger.info("sql:{}",sql.getFullSq());
			List<JSONObject> rows = QueryFactory.getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			for(JSONObject jsonObject : rows) {
				String mId = jsonObject.getString("M_ID");
				try {

				}catch (Exception e) {
					logger.error("数据同步失败", e);
				}
			}
		}catch (Exception e) {
			logger.error("数据同步失败", e);
		}
		return null;
	}


	

}