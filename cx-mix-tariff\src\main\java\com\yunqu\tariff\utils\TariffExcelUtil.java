package com.yunqu.tariff.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.base.CommonLogger;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import java.io.File;
import java.util.*;

/**
 * 稽核任务Excel文件处理工具类
 *
 * <AUTHOR>
 * @date 2024-12-21
 */
public class TariffExcelUtil {

    private static final Logger logger = CommonLogger.getLogger("excel-util");

    /**
     * 解析Excel文件，提取产品数据
     */
    public static List<JSONObject> parseExcelFile(String filePath, String productIdColumn,
                                                  String productNameColumn, String productCodeColumn) {
        logger.info("开始解析Excel文件 filePath: {} , productIdColumn: {}, productNameColumn: {} , productCodeColumn: {}", filePath, productIdColumn, productNameColumn, productCodeColumn);
        List<JSONObject> productList = new ArrayList<>();

        // 检查Web应用状态
        if (isWebApplicationStopped()) {
            logger.error("Web应用已停止，无法解析Excel文件: {}", filePath);
            return productList;
        }

        if (StringUtils.isBlank(filePath) || StringUtils.isBlank(productIdColumn) ||
                StringUtils.isBlank(productNameColumn) || StringUtils.isBlank(productCodeColumn)) {
            logger.error("参数不能为空");
            return productList;
        }

        File file = new File(filePath);
        if (!file.exists() || !TariffZipUtil.isExcelFile(file.getName())) {
            logger.error("Excel文件不存在或格式不正确: {}", filePath);
            return productList;
        }

        try {
            // 再次检查Web应用状态
            if (isWebApplicationStopped()) {
                logger.error("Web应用已停止，中止Excel文件解析: {}", filePath);
                return productList;
            }

            // 使用EasyExcel读取文件
            EasyExcel.read(filePath, new ExcelDataListener(productList, productIdColumn,
                    productNameColumn, productCodeColumn)).sheet().doRead();

            logger.info("Excel文件解析完成，共解析 {} 条产品数据", productList.size());

        } catch (IllegalStateException e) {
            if (e.getMessage() != null && e.getMessage().contains("web application instance has been stopped")) {
                logger.error("Web应用已停止，Excel解析被中断: {}", filePath, e);
            } else {
                logger.error("Excel解析状态异常: {}", filePath, e);
            }
            productList.clear();
        } catch (Exception e) {
            logger.error("解析Excel文件失败: " + filePath, e);
            productList.clear();
        }

        return productList;
    }

    /**
     * 检查Web应用是否已停止
     */
    private static boolean isWebApplicationStopped() {
        try {
            // 尝试访问类加载器资源来检测Web应用状态
            ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
            if (classLoader == null) {
                return true;
            }

            // 尝试加载一个系统资源
            classLoader.getResources("META-INF/services/javax.xml.parsers.SAXParserFactory");
            return false;
        } catch (IllegalStateException e) {
            if (e.getMessage() != null && e.getMessage().contains("web application instance has been stopped")) {
                return true;
            }
            return false;
        } catch (Exception e) {
            // 其他异常不认为是Web应用停止
            return false;
        }
    }

    /**
     * 验证Excel文件结构
     */
    public static boolean validateExcelStructure(String filePath, List<String> requiredColumns) {
        if (StringUtils.isBlank(filePath) || requiredColumns == null || requiredColumns.isEmpty()) {
            logger.error("参数不能为空");
            return false;
        }

        // 检查Web应用状态
        if (isWebApplicationStopped()) {
            logger.error("Web应用已停止，无法验证Excel文件: {}", filePath);
            return false;
        }

        File file = new File(filePath);
        if (!file.exists() || !TariffZipUtil.isExcelFile(file.getName())) {
            logger.error("Excel文件不存在或格式不正确: {}", filePath);
            return false;
        }

        try {
            // 读取表头验证列名
            List<Map<Integer, String>> headerList = new ArrayList<>();
            EasyExcel.read(filePath, new AnalysisEventListener<Map<Integer, String>>() {
                @Override
                public void invoke(Map<Integer, String> data, AnalysisContext context) {
                    // 只读取第一行表头
                    if (context.readRowHolder().getRowIndex() == 0) {
                        headerList.add(data);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    // 读取完成
                }
            }).sheet().doRead();

            if (headerList.isEmpty()) {
                logger.error("Excel文件为空或无法读取表头");
                return false;
            }

            Map<Integer, String> header = headerList.get(0);
            List<String> columnNames = new ArrayList<>(header.values());

            // 检查必需的列是否存在
            for (String requiredColumn : requiredColumns) {
                boolean found = false;
                for (String columnName : columnNames) {
                    if (requiredColumn.equalsIgnoreCase(columnName)) {
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    logger.error("Excel文件缺少必需的列: {}", requiredColumn);
                    return false;
                }
            }

            logger.info("Excel文件结构验证通过");
            return true;

        } catch (Exception e) {
            logger.error("验证Excel文件结构失败: " + filePath, e);
            return false;
        }
    }

    /**
     * 导出数据到Excel文件
     */
    public static boolean exportToExcel(List<JSONObject> data, String filePath, String sheetName) {
        if (data == null || data.isEmpty()) {
            logger.warn("导出数据为空");
            return false;
        }

        if (StringUtils.isBlank(filePath) || StringUtils.isBlank(sheetName)) {
            logger.error("文件路径或Sheet名称不能为空");
            return false;
        }

        try {
            // 创建目录
            File file = new File(filePath);
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            // 准备表头和数据
            List<List<String>> head = new ArrayList<>();
            List<List<Object>> dataList = new ArrayList<>();

            if (!data.isEmpty()) {
                JSONObject firstRow = data.get(0);

                // 构建表头
                head.add(createHeader("序号"));
                head.add(createHeader("产品ID"));
                head.add(createHeader("产品名称"));
                head.add(createHeader("产品编号"));
                /*head.add(createHeader("省份"));
                head.add(createHeader("运营商"));
                head.add(createHeader("备注"));*/

                // 构建数据行
                for (int i = 0; i < data.size(); i++) {
                    JSONObject row = data.get(i);
                    List<Object> rowData = new ArrayList<>();
                    rowData.add(i + 1); // 序号
                    rowData.add(row.getString("productId"));
                    rowData.add(row.getString("productName"));
                    rowData.add(row.getString("productCode"));
                    /*rowData.add(row.getString("provinceName"));
                    rowData.add(row.getString("entName"));
                    rowData.add(row.getString("remark"));*/
                    dataList.add(rowData);
                }
            }

            // 写入Excel文件
            EasyExcel.write(filePath)
                    .head(head)
                    .sheet(sheetName)
                    .doWrite(dataList);

            logger.info("Excel文件导出成功: {}, 共 {} 条数据", filePath, data.size());
            return true;

        } catch (Exception e) {
            logger.error("导出Excel文件失败: " + filePath, e);
            return false;
        }
    }

    /**
     * 创建表头
     */
    private static List<String> createHeader(String columnName) {
        List<String> header = new ArrayList<>();
        header.add(columnName);
        return header;
    }

    /**
     * 获取Excel文件的列名映射
     */
    public static Map<String, Integer> getColumnMapping(String filePath) {
        Map<String, Integer> columnMapping = new HashMap<>();

        if (StringUtils.isBlank(filePath)) {
            return columnMapping;
        }

        try {
            EasyExcel.read(filePath, new AnalysisEventListener<Map<Integer, String>>() {
                @Override
                public void invoke(Map<Integer, String> data, AnalysisContext context) {
                    // 只读取第一行表头
                    if (context.readRowHolder().getRowIndex() == 0) {
                        for (Map.Entry<Integer, String> entry : data.entrySet()) {
                            String columnName = entry.getValue();
                            if (StringUtils.isNotBlank(columnName)) {
                                columnMapping.put(columnName.trim(), entry.getKey());
                            }
                        }
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    // 读取完成
                }
            }).sheet().doRead();

        } catch (Exception e) {
            logger.error("获取Excel列名映射失败: " + filePath, e);
        }

        return columnMapping;
    }

    /**
     * Excel数据监听器
     */
    public static class ExcelDataListener extends AnalysisEventListener<Map<Integer, String>> {

        private final List<JSONObject> productList;
        private final String productIdColumn;
        private final String productNameColumn;
        private final String productCodeColumn;
        private Map<String, Integer> columnMapping;
        private boolean isFirstRow = true;

        public ExcelDataListener(List<JSONObject> productList, String productIdColumn,
                                 String productNameColumn, String productCodeColumn) {
            this.productList = productList;
            this.productIdColumn = productIdColumn;
            this.productNameColumn = productNameColumn;
            this.productCodeColumn = productCodeColumn;
        }


        @Override
        @SuppressWarnings("rawtypes")
        public void invokeHead(Map<Integer, CellData> headMap, AnalysisContext context) {
            if (headMap != null) {
                columnMapping = new HashMap<>();
                Set<Map.Entry<Integer, CellData>> entries = headMap.entrySet();
                for (Map.Entry<Integer, CellData> entry : entries) {
                    CellData cellData = entry.getValue();
                    String columnName = cellData.getStringValue();
                    columnMapping.put(columnName.trim(), entry.getKey());
                }
            }
        }

        @Override
        public void invoke(Map<Integer, String> data, AnalysisContext context) {
            /*if (isFirstRow) {
                // 第一行是表头，建立列名映射
                columnMapping = new HashMap<>();
                for (Map.Entry<Integer, String> entry : data.entrySet()) {
                    String columnName = entry.getValue();
                    if (StringUtils.isNotBlank(columnName)) {
                        columnMapping.put(columnName.trim(), entry.getKey());
                    }
                }
                isFirstRow = false;
                return;
            }*/

            // 数据行处理
            if (columnMapping != null) {
                JSONObject product = new JSONObject();

                // 提取产品ID
                Integer productIdIndex = columnMapping.get(productIdColumn);
                if (productIdIndex != null) {
                    product.put("productId", data.get(productIdIndex));
                }

                // 提取产品名称
                Integer productNameIndex = columnMapping.get(productNameColumn);
                if (productNameIndex != null) {
                    product.put("productName", data.get(productNameIndex));
                }

                // 提取产品编号
                Integer productCodeIndex = columnMapping.get(productCodeColumn);
                if (productCodeIndex != null) {
                    product.put("productCode", data.get(productCodeIndex));
                }

                // 验证数据完整性
                if (StringUtils.isNotBlank(product.getString("productId")) ||
                        StringUtils.isNotBlank(product.getString("productName")) ||
                        StringUtils.isNotBlank(product.getString("productCode"))) {

                    // 添加行号信息
                    product.put("rowIndex", context.readRowHolder().getRowIndex() + 1);
                    productList.add(product);
                }
            }
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            //logger.debug("Excel数据解析完成，共解析 {} 条记录", productList.size());
        }
    }
}
