package com.yunqu.tariff.base;

import com.yq.busi.common.util.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.AppContext;

import java.util.Arrays;
import java.util.List;

/**
 * 常量
 * <AUTHOR>
 *
 */
public class Constants {
    public final static String APP_NAME 	= "cx-mix-tariff";    	//应用

    public final static String DS_WIRTE_NAME_ONE = "yc-wirte-ds-1";     //默认数据源名�?(�?)

    public final static String DS_WIRTE_NAME_TWO = "yc-wirte-ds-2";     //默认数据源名�?(�?)

    public final static String DS_READ_NAME	= "yc-read-ds";  	//默认数据源名�?(�?)

    public final static String DS_TARIFF_NAME	= "yc-tariff-ds";  	//资费数据源


    public static AppContext context = AppContext.getContext(Constants.APP_NAME);

    public static String IS_START_TASK = AppContext.getContext(APP_NAME).getProperty("IS_START_TASK", "N");//是否开启任务抽取服务


    public static String getTraiffAuditLoadBroker(){
        return context.getProperty("TRAIFF_AUDIT_LOAD_BROKER","");
    }

    public static String getTraiffBakBroker(){
        return context.getProperty("TRAIFF_BAK_BROKER","");
    }

    public static String getTraiffReqBakBroker(){
        return context.getProperty("TRAIFF_REQ_BAK_BROKER","");
    }


    public static String getTraiffOrderQueryBroker(){
        return context.getProperty("TRAIFF_ORDER_QUERY_BROKER","");
    }

    public static String getTraiffDelMsgBroker(){
        return context.getProperty("TRAIFF_DEL_MSG_BROKER","");
    }

    public static String getTraiffExeMessageBroker(){
        return context.getProperty("TRAIFF_EXE_MESSAGE_BROKER","");
    }

    public static String getTraiffExeAuditMessageBroker(){
        return context.getProperty("TRAIFF_EXE_AUDIT_MESSAGE_BROKER","");
    }

    public static String emailType() {
        return context.getProperty("EMAIL_TYPE", "126");
    }
    public static String fromEmail() {
        return context.getProperty("EMAIL_FROM", "");
    }

    public static String fromPassword() {
        return context.getProperty("EMAIL_PASSWORD", "");
    }

    public static List<String> toEmails() {
        String toEmails = context.getProperty("EMAIL_TO", "");
        return Arrays.asList(toEmails.split(","));
    }

    public static List<String> ccEmails() {
        String ccEmails = context.getProperty("EMAIL_CC", "");
        return Arrays.asList(ccEmails.split(","));
    }

    public static int THRESHOLD = Integer.parseInt(context.getProperty("THRESHOLD", "10"));

    public static final String ALARM_EMAIL_QUEUE = "ALARM_EMAIL_QUEUE";

    /**
     * 全国通用的地区编号
     */
    public static final String AREA_CODE_ALL = "000";

    /**
     * 下架日期默认值
     */
    public static final String OFFLINE_DAY_DEFAULT = "49991208";


    public static final String JT_PROVINCE_CODE = "999998";


    /**
     * 部门组类型：99-部门
     */
    public static final String GROUP_TYPE_DEPT = "99";

    /**
     * 部门组类型：990101-工信部信管局
     */
    public static final String GROUP_TYPE_B_XGJ = "990101";

    /**
     * 部门组类型：990102-工信部申诉中心
     */
    public static final String GROUP_TYPE_B_SSC = "990102";

    /**
     * 部门组类型：990103-信通院
     */
    public static final String GROUP_TYPE_B_XTY = "990103";

    /**
     * 部门组类型：990104-集团
     */
    public static final String GROUP_TYPE_GE = "990104";

    /**
     * 部门组类型：99010401-集团-电信
     */
    public static final String GROUP_TYPE_GE_TELECOM = "99010401";

    /**
     * 部门组类型：99010402-集团-移动
     */
    public static final String GROUP_TYPE_GE_MOBILE = "99010402";

    /**
     * 部门组类型：99010403-集团-联通
     */
    public static final String GROUP_TYPE_GE_UNICOM = "99010403";

    /**
     * 部门组类型：99010404-集团-广电
     */
    public static final String GROUP_TYPE_GE_BROAD = "99010404";

    /**
     * 部门组类型：990201-省管局
     */
    public static final String GROUP_TYPE_P_SGJ = "990201";

    /**
     * 部门组类型：990202-省申诉中心
     */
    public static final String GROUP_TYPE_P_SCC = "990202";

    /**
     * 部门组类型：990203-省企业
     */
    public static final String GROUP_TYPE_PE = "990203";
    /**
     * 部门组类型：99020301-省企业-电信
     */
    public static final String GROUP_TYPE_PE_TELECOM= "99020301";

    /**
     * 部门组类型：99020302-省企业-移动
     */
    public static final String GROUP_TYPE_PE_MOBILE = "99020302";

    /**
     * 部门组类型：99020303-	省企业-联通
     */
    public static final String GROUP_TYPE_PE_UNICOM = "99020303";

    /**
     * 部门组类型：99020304-	省企业-广电
     */
    public static final String GROUP_TYPE_PE_BROAD = "99020304";

    /**
     * 部门组类型：99020305-	省企业-本地
     */
    public static final String GROUP_TYPE_PE_LOCAL = "99020305";

    /**
     * 企业，电信001、移动002、联通003、广电004
     */
    public static final String ENT_TYPE_001 = "1"; //001
    public static final String ENT_TYPE_002 = "2"; //002
    public static final String ENT_TYPE_003 = "3"; //003
    public static final String ENT_TYPE_004 = "5"; //004
    public static final String ENT_TYPE_005 = "5"; //005

    /**
     * 资费操作类型
     */
    public static final String ACTION_TYPE_ADD = "A";
    public static final String ACTION_TYPE_EDIT = "M";
    public static final String ACTION_TYPE_DEL = "D";
    public static final String ACTION_TYPE_VERIFY = "AC";
    public static final String ACTION_TYPE_EDIT_VERIFY = "MC";

    /**
     * 资费权限类型
     */
    public static final String AUTH_ADD = "add";
    public static final String AUTH_EDIT_PART = "edit_part";
    public static final String AUTH_EDIT_ALL = "edit_all";
    public static final String AUTH_DEL = "del";

    /**
     * 创建方式，1-人工填报 2-接口填报
     */
    public static final String CREATE_TYPE_1 = "1";
    public static final String CREATE_TYPE_2 = "2";

    /**
     * 历史资费状态，1-正常 2-已删除
     */
    public static final String TARIFF_HIS_STATUS_1 = "1";
    public static final String TARIFF_HIS_STATUS_2 = "2";

    /**
     * 资费状态，1-在售 2-失效 3-下架 4-未售
     */
    public static final String TARIFF_STATUS_1 = "1";
    public static final String TARIFF_STATUS_2 = "2";
    public static final String TARIFF_STATUS_3 = "3";
    public static final String TARIFF_STATUS_4 = "4";
    public static final String TARIFF_STATUS_5 = "5";

    /**
     * 地市编码状态，1-在售 2-待下架 3-下架
     */
    public static final String TARIFF_AREA_TYPE_1 = "1";
    public static final String TARIFF_AREA_TYPE_2 = "2";
    public static final String TARIFF_AREA_TYPE_3 = "3";


    public static final String CRAWLER_VERSION_STATUS_1 = "ACTIVE";
    public static final String CRAWLER_VERSION_STATUS_0 = "INVALID";



    /** 鍛ㄦ湡绫诲瀷 1-澶� */
    public static final String CYCLE_TYPE_01 = "day";
    /** 鍛ㄦ湡绫诲瀷 2-鍛� */
    public static final String CYCLE_TYPE_02 = "week";
    /** 鍛ㄦ湡绫诲瀷 3-鏈� */
    public static final String CYCLE_TYPE_03 = "month";
    /** 鍛ㄦ湡绫诲瀷 4-瀛ｅ害 */
    public static final String CYCLE_TYPE_04 = "quarter";
    /** 鍛ㄦ湡绫诲瀷 5-骞翠唤 */
    public static final String CYCLE_TYPE_05 = "year";
    /** 鍛ㄦ湡绫诲瀷 6-闃舵 */
    public static final String CYCLE_TYPE_06 = "stage";
    /** 鍛ㄦ湡绫诲瀷 7-绱 */
    public static final String CYCLE_TYPE_07 = "all";

    /** 资费属性： 1-全国 */
    public static final String TARIFF_ATTR_01 = "1";
    /** 资费属性： 2-省内 */
    public static final String TARIFF_ATTR_02 = "2";
    /** 资费属性： 3-本地 */
    public static final String TARIFF_ATTR_03 = "3";

    public static String getStatSchema(){
        return AppContext.getContext("yc-api").getProperty("STAT_DB","stat");
    }
    /**
     * 获取资费业务库名称
     */
    public static String getBusiSchema() {
        return context.getProperty("busi_schema", "ycbusi_ekf");
    }
    /**
     * 获取企业ID
     */
    public static String getEntId() {
        return context.getProperty("XTY_ENT_ID", "1000");
    }

    // ==================== 稽核任务相关常量 ====================

    /**
     * 稽核任务表名
     */
    public static final String AUDIT_TASK_TABLE = "XTY_TARIFF_AUDIT_TASK";

    /**
     * 稽核任务日志表名
     */
    public static final String AUDIT_TASK_LOG_TABLE = "XTY_TARIFF_AUDIT_LOG";

    /**
     * 获取稽核任务临时目录
     */
    public static String getAuditTempDir() {
        return context.getProperty("AUDIT_TASK_TEMP_DIR", "/home/<USER>/audit/temp");
    }

    /**
     * 获取稽核任务导出目录
     */
    public static String getAuditExportDir() {
        return context.getProperty("AUDIT_TASK_EXPORT_DIR", "/home/<USER>/audit/export");
    }

    /**
     * 获取稽核任务解压目录
     */
    public static String getAuditUnzipDir() {
        return context.getProperty("AUDIT_TASK_UNZIP_DIR", "/home/<USER>/audit/unzip");
    }

    /**
     * 获取稽核任务最大文件大小（字节）
     */
    public static long getAuditMaxFileSize() {
        return Long.parseLong(context.getProperty("AUDIT_TASK_MAX_FILE_SIZE", "52428800")); // 50MB
    }

    /**
     * 获取稽核任务线程池大小
     */
    public static int getAuditThreadPoolSize() {
        return Integer.parseInt(context.getProperty("AUDIT_TASK_THREAD_POOL_SIZE", "5"));
    }

    /**
     * 获取SFTP稽核目录
     */
    public static String getSftpAuditDir() {
        return context.getProperty("SFTP_AUDIT_DIR", "/check");
    }

    /**
     * 获取SFTP_IP SFTP服务器IP地址
     */
    public static String getSftpIp() {
        return context.getProperty("SFTP_IP", "");
    }
    /**
     * 获取SFTP_PORT SFTP服务器端口
     */
    public static String getSftpPort() {
        return context.getProperty("SFTP_PORT", "");
    }
    /**
     * 获取SFTP_USERNAME SFTP服务器登录用户名
     */
    public static String getSftpUsername() {
        return context.getProperty("SFTP_USERNAME", "");
    }
    /**
     * 获取SFTP_PASSWORD SFTP服务器登录密码
     */
    public static String getSftpPassword() {
        return context.getProperty("SFTP_PASSWORD", "");
    }
    /**
     * 获取SFTP_BASE_PATH SFTP服务器文件根目录
     */
    public static String getSftpBasePath() {
        return context.getProperty("SFTP_BASE_PATH", "");
    }
    /**
     * 获取SFTP_BASE_PATH SFTP本地文件存放路径
     */
    public static String getSftpLocalBasePath() {
        return context.getProperty("SFTP_LOCAL_BASE_PATH", "");
    }
    /**
     * 是否启动测试数据接口
     */
    public static String getEnableTestdataInf() {
        return context.getProperty("ENABLE_TESTDATA_INF", "N");
    }
    /**
     * 资费导出数量限制
     */
    public static int getTariffExportSize() {
        return CommonUtil.parseInt(context.getProperty("TARIFF_EXPORT_SIZE", "10000"));
    }

    /**
     * 获取备案年份限制
     */
    public static String getBakTariffYear() {
        return context.getProperty("BAK_TARIFF_YEAR", "");
    }


    /**
     * 根据用户的部门组织类型，获取用户的账号类型；
     * 0-管理员 1-全国账号 2-集团账号 3-省账号 4-省企业账号
     */
    public static String getUserAccType(String deptGroupType){
        if(StringUtils.isBlank(deptGroupType) || "99".equals(deptGroupType)){
            return "99";
        }
        if(StringUtils.equalsAny(deptGroupType, GROUP_TYPE_B_XGJ,GROUP_TYPE_B_SSC,GROUP_TYPE_B_XTY)){
            return USER_TYPE_B;
        }
        if(StringUtils.equalsAny(deptGroupType, GROUP_TYPE_GE_TELECOM,GROUP_TYPE_GE_MOBILE,GROUP_TYPE_GE_UNICOM,GROUP_TYPE_GE_BROAD)){
            return USER_TYPE_GE;
        }
        if(StringUtils.equalsAny(deptGroupType, GROUP_TYPE_P_SGJ,GROUP_TYPE_P_SCC)){
            return USER_TYPE_P;
        }
        if(StringUtils.equalsAny(deptGroupType, USER_TYPE_PE_ARR)){
            return USER_TYPE_PE;
        }
        return USER_TYPE_ERROR;
    }



    /**
     * 人员账号类型：0-管理员
     */
    public static final String USER_TYPE_ADMIN = "1";
    /**
     * 人员账号类型：1-全国账号
     */
    public static final String USER_TYPE_B = "2";
    /**
     * 人员账号类型：2-集团账号
     */
    public static final String USER_TYPE_GE = "3";
    /**
     * 人员账号类型：3-省账号
     */
    public static final String USER_TYPE_P = "4";
    /**
     * 人员账号类型：4-省企业账号
     */
    public static final String USER_TYPE_PE = "5";
    /**
     * 人员账号类型：99-非法账号
     */
    public static final String USER_TYPE_ERROR = "99";


    /**
     * 省企业类型
     */
    public static final String[] USER_TYPE_PE_ARR = new String[]{
            GROUP_TYPE_PE_TELECOM,
            GROUP_TYPE_PE_MOBILE,
            GROUP_TYPE_PE_UNICOM,
            GROUP_TYPE_PE_BROAD,
            GROUP_TYPE_PE_LOCAL
    };

    /**
     * 资费信息ES索引
     */
    public static final String XTY_TARIFF_INFO_INDEX = "xty_tariff_info_index";

    /**
     * 资费上报ES索引
     */
    public static final String XTY_TARIFF_AUDIT_INFO_INDEX = "xty_tariff_audit_info";

    /**
     * 资费备份信息ES索引
     */
    public static final String XTY_TARIFF_BAK_INFO_INDEX = "xty_tariff_bak_info";

    /**
     * 报表资费信息
     */
    public static final String TARIFF_REPORT_INF_INDEX = "tariff_report_inf_index";

    /**
     * ES操作类型:新增文档

     */
    public static final String ES_OPERATE_CREATE_DOC = "createDoc";

    /**
     * ES操作类型:更新文档
     */
    public static final String ES_OPERATE_UPDATE_DOC = "updateDoc";


    public static final String ES_TARIFF_REPORT_INF_INDEX = "tariff_report_inf_index";


    //ES操作命令:根据条件删除文档
    public static final String ES_OPERATE_COMMAND_DELETE_BY_QUERY_DOC = "deleteByQueryDoc";



    public static final String TARIFF_AUDIT_THREAD_POOL_NAME = "TariffAuditThreadPool";


    /**
     * 资费审核消息队列名称
     */
    public static final String TARIFF_REAUDIT_MSG_BROKER = "TARIFF_REAUDIT_MSG_BROKER";

    /**
     * 资费审核消息队列名称
     */
    public static final String TARIFF_REDIFF_MSG_BROKER = "TARIFF_REDIFF_MSG_BROKER";


    /**
     * 资费报送消息队列名称
     */
    public static final String TARIFF_REREPORT_MSG_BROKER = "TARIFF_REREPORT_MSG_BROKER";


    /**
     * 资费报送消息队列名称
     */
    public static final String TARIFF_REORDERSTAT_MSG_BROKER = "TARIFF_REORDERSTAT_MSG_BROKER";

    /**
     * 订购删除
     */
    public static final String TARIFF_NOTIFY_REQINTERFACE_BROKER =  "TARIFF_NOTIFY_REQINTERFACE_BROKER";

    /**
     * 订购重跑
     */
    public static final String TARIFF_NOTIFY_REBUILD_OLDORDER_BROKER =  "TARIFF_NOTIFY_REBUILD_OLDORDER_BROKER";

    /**
     * 稽核冲泡
     */
    public static final String TARIFF_NOTIFY_REBUILD_AUDIT_BROKER  =  "TARIFF_NOTIFY_REBUILD_AUDIT_BROKER";

    /**
     * 执行数据导出
     */
    public static final String TARIFF_NOTIFY_EXPORT_BROKER  =  "TARIFF_NOTIFY_EXPORT_BROKER";



    /**
     * 获取业务库表名称
     */
    public static String getBusiTable(String tableName) {
        if(StringUtils.isBlank(tableName)) throw new RuntimeException("tableName 为空,请检查");
        String busiSchema = getBusiSchema();
        if(StringUtils.isBlank(busiSchema)) return tableName;
        return busiSchema+"."+tableName;
    }

    /**
     * 资费稽核重跑开关
     */
    public static final String TARIFF_REAUDIT_FLAG = AppContext.getContext(APP_NAME).getProperty("TARIFF_REAUDIT_FLAG", "N");

    /**
     *
     */
    public static final String FILE_BASE_PATH = AppContext.getContext(APP_NAME).getProperty("FILE_BASE_PATH", "/home/<USER>/tariff");

    public static final String ES_COMMAND_BATCH_ADD = "ES_COMMAND_BATCH_ADD";

    public static final String ES_COMMAND_BATCH_UPT = "ES_COMMAND_BATCH_UPT";

    public static final String TASK_BTN = AppContext.getContext(APP_NAME).getProperty("TASK_BTN", "false");

    /**
     * 资费解析任务broker
     */
    public static String TARIFF_INFO_ANALYSIS_BROKER = "TARIFF_INFO_ANALYSIS_BROKER";

    /**
     * 资费解析结果broker
     */
    public static String TARIFF_INFO_ANALYSIS_RESULT_BROKER = "TARIFF_INFO_ANALYSIS_RESULT_BROKER";

    /**
     * 资费信息语义分析API地址
     */
    public static String TARIFF_INFO_ANALYSIS_API_URL = AppContext.getContext(APP_NAME).getProperty("TARIFF_INFO_ANALYSIS_API_URL", "");

    // ES索引名称
    public static final String XTY_TARIFF_PUBLIC_LIB_INDEX = "xty_tariff_public_lib_index";

    // 报送资费es统计索引
    public static final String XTY_TARIFF_BAK_INFO_STAT_INDEX = "xty_tariff_bak_info_stat";

    // public模块地址
    public static final String XTY_PUBLIC_HOST = AppContext.getContext(APP_NAME).getProperty("XTY_PUBLIC_HOST", "");
    // 本地服务器地址
    public static final String LOCAL_SERVER_HOST = AppContext.getContext(APP_NAME).getProperty("LOCAL_SERVER_HOST", "");

    /**
     * 是否强制更新报送索引
     */
    public static boolean isForceReportIndexUpdate() {
        return "Y".equalsIgnoreCase(context.getProperty("FORCE_REPORT_INDEX_UPDATE", "N"));
    }

}
