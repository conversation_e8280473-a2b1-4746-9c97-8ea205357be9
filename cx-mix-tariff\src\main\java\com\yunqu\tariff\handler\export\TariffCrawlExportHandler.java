package com.yunqu.tariff.handler.export;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.utils.excel.AsyncExcelExport;
import com.yunqu.tariff.utils.excel.ExcelExportDataHandle;
import com.yunqu.tariff.utils.excel.impl.ExcelExportDataHandleImpl;
import com.yunqu.xty.commonex.kit.ElasticsearchKit;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.utils.calendar.EasyDate;

import java.io.File;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公示库资费导出处理器
 *
 * @ClassName TariffPublicLibExportHandler
 * @Since create in 2024
 * @Version v1.0
 * @Company 广州云趣信息科技有限公司
 */
public class TariffCrawlExportHandler extends BaseExportHandler {
    // 字段映射：驼峰格式 -> 下划线格式
    private static final Map<String, String> FIELD_MAPPING = new HashMap<>();
    // 字段映射：下划线格式 -> 驼峰格式
    private static final Map<String, String> REVERSE_FIELD_MAPPING = new HashMap<>();

    private static final Map<String, String> fieldMap = new HashMap<>();


    static {
        // 初始化字段映射
        initFieldMapping();
    }

    /**
     * 初始化字段映射
     */
    private static void initFieldMapping() {
        // 常用字段映射
        FIELD_MAPPING.put("tariffNo", "tariff_no");
        FIELD_MAPPING.put("name", "name");
        FIELD_MAPPING.put("entName", "ent_code");
        FIELD_MAPPING.put("provinceName", "province_name");
        FIELD_MAPPING.put("tariffAttrType", "tariff_attr_type");
        FIELD_MAPPING.put("tariffType", "tariff_type");
        FIELD_MAPPING.put("fees", "fees");
        FIELD_MAPPING.put("feesUnit", "fees_unit");
        FIELD_MAPPING.put("exceedFees", "exceed_fees");    // 新增：超出资费
        FIELD_MAPPING.put("otherFees", "other_fees");      // 新增：其他费用
        FIELD_MAPPING.put("call", "call");
        FIELD_MAPPING.put("data", "data");
        FIELD_MAPPING.put("dataUnit", "data_unit");        // 新增：通用流量单位
        FIELD_MAPPING.put("sms", "sms");
        FIELD_MAPPING.put("orientTraffic", "orient_traffic");
        FIELD_MAPPING.put("orientTrafficUnit", "orient_traffic_unit"); // 新增：定向流量单位
        FIELD_MAPPING.put("iptv", "iptv");
        FIELD_MAPPING.put("bandwidth", "bandwidth");
        FIELD_MAPPING.put("rights", "rights");
        FIELD_MAPPING.put("otherContent", "other_content");
        FIELD_MAPPING.put("applicablePeople", "applicable_people");
        FIELD_MAPPING.put("validPeriod", "valid_period");
        FIELD_MAPPING.put("channel", "channel");
        FIELD_MAPPING.put("duration", "duration");
        FIELD_MAPPING.put("unsubscribe", "unsubscribe");
        FIELD_MAPPING.put("others", "others");
        FIELD_MAPPING.put("onlineDay", "online_day");
        FIELD_MAPPING.put("offlineDay", "offline_day");
        FIELD_MAPPING.put("versionNo", "version_no");
        FIELD_MAPPING.put("versionNos", "version_nos");     // 新增：版本号数组
        FIELD_MAPPING.put("dateIds", "date_ids");           // 新增：日期ID数组
        FIELD_MAPPING.put("appearMonths", "appear_months"); // 新增：出现月份
        FIELD_MAPPING.put("createTime", "create_time");
        FIELD_MAPPING.put("classicTypeOne", "classic_type_one");
        FIELD_MAPPING.put("classicTypeTwo", "classic_type_two");
        FIELD_MAPPING.put("isTelecom", "is_telecom");
        FIELD_MAPPING.put("reported", "reported");
        FIELD_MAPPING.put("fieldCheckResult", "field_check_result");
        FIELD_MAPPING.put("fieldCheckTime", "field_check_time");
        FIELD_MAPPING.put("fieldCheckNo", "field_check_no");

        FIELD_MAPPING.put("tariffState", "tariff_state");
        FIELD_MAPPING.put("tariffReporterName", "tariff_reporter_name");

        // 创建反向映射
        for (Map.Entry<String, String> entry : FIELD_MAPPING.entrySet()) {
            REVERSE_FIELD_MAPPING.put(entry.getValue(), entry.getKey());
        }

        // 公示资费字段映射 - 使用驼峰命名格式
        fieldMap.put("tariffNo", "方案编号");
        fieldMap.put("name", "资费名称");
        fieldMap.put("entName", "运营商");
        fieldMap.put("provinceName", "省份");
        fieldMap.put("tariffAttrType", "资费来源"); // 省内、全国
        fieldMap.put("tariffType", "资费分类");
        fieldMap.put("fees", "资费标准");
        fieldMap.put("feesUnit", "资费单位");
        fieldMap.put("exceedFees", "超出资费");     // 新增：超出资费
        fieldMap.put("otherFees", "其他费用");     // 新增：其他费用
        fieldMap.put("call", "语音/分钟");
        fieldMap.put("data", "通用流量");
        fieldMap.put("dataUnit", "流量单位");      // 新增：通用流量单位
        fieldMap.put("sms", "短信/条");
        fieldMap.put("orientTraffic", "定向流量");
        fieldMap.put("orientTrafficUnit", "定向流量单位");  // 新增：定向流量单位
        fieldMap.put("iptv", "IPTV");
        fieldMap.put("bandwidth", "带宽");
        fieldMap.put("rights", "权益");
        fieldMap.put("otherContent", "其他服务内容");
        fieldMap.put("applicablePeople", "适用范围");
        fieldMap.put("validPeriod", "有效期限");
        fieldMap.put("channel", "销售渠道");
        fieldMap.put("duration", "在网要求");
        fieldMap.put("unsubscribe", "退订方式");
        fieldMap.put("others", "其他事项");
        fieldMap.put("onlineDay", "上线日期");
        fieldMap.put("offlineDay", "下线日期");
        fieldMap.put("versionNo", "版本号");
        fieldMap.put("versionNos", "版本号列表");      // 新增：版本号数组
        fieldMap.put("dateIds", "日期列表");        // 新增：日期ID数组
        fieldMap.put("appearMonths", "出现月份");  // 新增：出现月份
        fieldMap.put("createTime", "入库时间");

        // 添加其他重要字段
        fieldMap.put("classicTypeOne", "一级分类");
        fieldMap.put("classicTypeTwo", "二级分类");
        fieldMap.put("isTelecom", "是否通信类");
        fieldMap.put("reported", "是否报送");
        fieldMap.put("fieldCheckResult", "字段核查结果");
        fieldMap.put("fieldCheckTime", "字段核查时间");
        fieldMap.put("fieldCheckNo", "核查编号");

        fieldMap.put("tariffState", "资费状态");
        fieldMap.put("tariffReporterName", "报送主体");
    }

    @Override
    protected String excuete(JSONObject taskObject) {
        logger.info("开始执行公示库导出任务:{}", taskObject);
        JSONObject param = taskObject.getJSONObject("PARAMS");
        return actionForExport(param, taskObject.getString("FILE_NAME"));
    }

    @Override
    protected String getFileName(String userAcct) {
        if (StringUtils.isNotBlank(fileName)) return fileName;
        fileName = "公示库资费列表_" + userAcct + "_" + EasyDate.getCurrentDateString("yyyyMMdd") + ".xlsx";
        return fileName;
    }

    public String actionForExport(JSONObject param, String fileName) {
        try {
            String fields = param.getString("fields");
            if (!isValidString(fields)) {
                return null;
            }

            // 准备Excel导出相关参数
            List<String> headers = new ArrayList<>();
            List<String> fields2 = new ArrayList<>();
            Map<String, String> dictMap = new HashMap<>();
            Map<String, JSONObject> treeMap = new HashMap<>();
            String[] fieldArray = fields.split(",");
            for (String fieldName : fieldArray) {
                String fieldZhName = fieldMap.get(fieldName);
                // 检查字段是否有对应的中文名称，如果没有则使用字段名本身作为表头
                if (fieldZhName == null) {
                    logger.warn("字段[{}]没有对应的中文名称，将使用字段名作为表头", fieldName);
                    fieldZhName = fieldName;
                }
                headers.add(fieldZhName);
                fields2.add(fieldName);
            }

            // 设置字典映射
            dictMap.put("classicTypeOne", "XTY_TARIFF_ONE_TYPE");
            dictMap.put("classicTypeTwo", "XTY_TARIFF_TWO_TYPE");
            dictMap.put("tariffAttrType", "XTY_TARIFF_CRAWL_TYPE");
            dictMap.put("isTelecom", "XTY_TARIFF_TELECOM");
            dictMap.put("reported", "XTY_TARIFF_CRAWL_REPORT");
//            dictMap.put("fieldCheckResult", "XTY_TARIFF_CHECK_RESULT");


            dictMap.put("entName", "XTY_TARIFF_ENT");

            dictMap.put("tariffState", "XTY_TARIFF_STATUS");


            // 准备文件路径
            String currentDateString = EasyDate.getCurrentDateString("yyyyMMdd");
            String year = currentDateString.substring(0, 4);
            String month = currentDateString.substring(4, 6);
            String day = currentDateString.substring(6, 8);
            String filePath = Constants.FILE_BASE_PATH + File.separator + year + File.separator + month + File.separator + day;
            File folder = new File(filePath);
            if (!folder.exists()) {
                folder.mkdirs();
            }
            String fileAbsPath = filePath + File.separator + fileName;
            File file = new File(fileAbsPath);

            // 使用分页查询导出ES数据
            // 创建Excel文件
            try (OutputStream os = Files.newOutputStream(file.toPath())) {
                ExcelExportDataHandle handle = new ExcelExportDataHandleImpl();
                handle.setEntId(Constants.getEntId());
                handle.setDictMap(dictMap);
                handle.setTreeMap(treeMap);

                // 查询所有数据
                int pageIndex = 1;
                List<JSONObject> resultList = new ArrayList<>();
                while (true) {
                    List<JSONObject> tmpList = queryEsDataByPage(param, pageIndex, fields2);
                    if (tmpList.isEmpty()) {
                        break;
                    }

                    // 对数据进行特殊处理
                    for (JSONObject json : tmpList) {
                        // 处理缺失的字段，确保每条记录都包含所有需要的字段
                        for (String field : fields2) {
                            if (!json.containsKey(field)) {
                                json.put(field, "");
                            }
                        }

                        // 特殊处理下线日期
                        if (json.containsKey("offlineDay") && Constants.OFFLINE_DAY_DEFAULT.equals(json.getString("offlineDay"))) {
                            json.put("offlineDay", "");
                        }

                        // 处理数组字段的格式，确保它们以可读形式显示
                        procesArrayFields(json, "versionNos");
                        procesArrayFields(json, "dateIds");
                        procesArrayFields(json, "appearMonths");
                    }

                    resultList.addAll(tmpList);
                    pageIndex++;

                    // 避免过快请求导致服务器压力过大
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        // 忽略
                    }

                    // 定期执行GC以防止内存溢出
                    if (pageIndex % 10 == 0) {
                        logger.info("执行内存回收...");
                        System.gc();
                    }
                }
                logger.info("公示库资费[导出]执行查询，总记录数: {}", resultList.size());

//                // 导出数据
//                for(JSONObject jsonObject:resultList){
//                    logger.info(jsonObject.toJSONString());
//
//                }
//
                logger.info("导出字段: {}", fields2);
                logger.info("表头: {}", headers);

                AsyncExcelExport exportExcel = new AsyncExcelExport()
                        .setJsonObjectList(resultList)
                        .setFields(fields2) // 使用驼峰格式字段
                        .setHeaderList(headers)
                        .setExcelExportDataHandle(handle);
                exportExcel.export(file.getName(), os, null);
            }

            return fileAbsPath;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 处理数组字段，将其转换为可读的字符串格式
     * @param json JSON对象
     * @param fieldName 字段名
     */
    private void procesArrayFields(JSONObject json, String fieldName) {
        if (json.containsKey(fieldName)) {
            Object value = json.get(fieldName);
            if (value instanceof JSONArray) {
                JSONArray array = (JSONArray) value;
                if (array.isEmpty()) {
                    json.put(fieldName, "");
                } else {
                    // 将数组转换为逗号分隔的字符串
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < array.size(); i++) {
                        if (i > 0) {
                            sb.append(", ");
                        }
                        sb.append(array.get(i).toString());
                    }
                    json.put(fieldName, sb.toString());
                }
            } else if (value == null || "null".equals(value.toString()) || "".equals(value.toString())) {
                json.put(fieldName, "");
            }
        }
    }

    /**
     * 分页查询ES数据
     * @param param 查询参数
     * @param pageIndex 页码（从1开始）
     * @param fields 需要获取的字段列表（驼峰格式）
     * @return 查询结果列表（驼峰格式）
     */
    private List<JSONObject> queryEsDataByPage(JSONObject param, int pageIndex, List<String> fields) {
        try {
            // 构建ES查询参数
            JSONObject queryParams = buildEsQueryParams(param);

            // 设置分页参数
            int pageSize = 10000;
            queryParams.put("from", (pageIndex - 1) * pageSize);
            queryParams.put("size", pageSize);

            // 设置要获取的字段（如果有指定）
            if (fields != null && !fields.isEmpty()) {
                JSONObject sourceFilter = new JSONObject();
                JSONArray includeFields = new JSONArray();

                // 将驼峰字段名转换为下划线格式，用于ES查询
                for (String field : fields) {
                    String underscoreField = FIELD_MAPPING.getOrDefault(field, StrUtil.toUnderlineCase(field));
                    includeFields.add(underscoreField);
                }
                sourceFilter.put("includes", includeFields);
                queryParams.put("_source", sourceFilter);
            }

            // 执行ES查询
            String indexName = Constants.XTY_TARIFF_PUBLIC_LIB_INDEX;
            logger.info("公示库资费分页导出查询参数: {}", queryParams.toJSONString());
            JSONObject result = ElasticsearchKit.search(indexName, queryParams);

            if (result == null || !result.containsKey("hits")) {
                logger.warn("[queryEsDataByPage] 查询ES数据失败或返回为空");
                return new ArrayList<>();
            }

            // 获取命中结果
            JSONObject hitsObj = result.getJSONObject("hits");
            JSONArray hits = hitsObj.getJSONArray("hits");

            if (hits == null || hits.isEmpty()) {
                return new ArrayList<>();
            }

            // 处理查询结果
            int batchSize = hits.size();
            List<JSONObject> resultList = new ArrayList<>(batchSize);
            for (int i = 0; i < batchSize; i++) {
                JSONObject hit = hits.getJSONObject(i);
                JSONObject source = hit.getJSONObject("_source");

                // 将ES中的下划线格式转换为驼峰格式
                JSONObject row = new JSONObject();
                for (String key : source.keySet()) {
                    // 使用映射表转换字段名
                    String camelKey = REVERSE_FIELD_MAPPING.getOrDefault(key, toCamelCase(key));

                    // 特殊处理某些字段值，如日期格式转换等
                    Object value = source.get(key);

                    // 处理下线日期显示，避免显示"99991231"
                    if ("offline_day".equals(key) && Constants.OFFLINE_DAY_DEFAULT.equals(String.valueOf(value))) {
                        value = "";
                    }

                    row.put(camelKey, value);
                }
                resultList.add(row);
            }

            logger.info("[queryEsDataByPage] 查询第{}页数据，获取到{}条记录", pageIndex, resultList.size());
            return resultList;
        } catch (Exception e) {
            logger.error("[queryEsDataByPage] 查询ES数据失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建ES查询参数
     * 与TariffCrawlRecordDao.publicLibRecordList方法保持一致
     */
    private JSONObject buildEsQueryParams(JSONObject param) {
        try {
            // 获取查询参数 - 驼峰命名参数
            JSONObject queryParams = new JSONObject();
            JSONObject boolQuery = new JSONObject();
            JSONArray mustArray = new JSONArray();

            // 处理基本查询参数
            processBasicQueryParams(param, mustArray);

            // 处理日期范围查询和规则核查相关参数
            processdateRangeParams(param, mustArray);

            // 构建排序
            JSONArray sortArray = new JSONArray();

            // 第一排序字段：date_id 降序
            JSONObject dateIdSort = new JSONObject();
            dateIdSort.put("date_id.keyword", new JSONObject().fluentPut("order", "desc"));
            sortArray.add(dateIdSort);

            // 第二排序字段：_id 降序
            JSONObject idSort = new JSONObject();
            idSort.put("_id", new JSONObject().fluentPut("order", "desc"));
            sortArray.add(idSort);

            queryParams.put("sort", sortArray);

            // 设置查询条件
            if (!mustArray.isEmpty()) {
                boolQuery.put("must", mustArray);
                queryParams.put("query", new JSONObject().fluentPut("bool", boolQuery));
            } else {
                queryParams.put("query", new JSONObject().fluentPut("match_all", new JSONObject()));
            }

            return queryParams;
        } catch (Exception e) {
            logger.error("构建ES查询参数失败: {}", e.getMessage(), e);
            return new JSONObject().fluentPut("query", new JSONObject().fluentPut("match_all", new JSONObject()));
        }
    }

    /**
     * 处理基本查询参数
     *
     * @param param 前端传入参数（驼峰格式）
     * @param mustArray 查询条件数组
     */
    private void processBasicQueryParams(JSONObject param, JSONArray mustArray) {
        try {

            String reporter = param.getString("tariffReporter");

            if (StringUtils.isNotBlank(reporter)) {
                if ("1".compareTo(reporter) <= 0 && "5".compareTo(reporter) >= 0) {
                    JSONObject entMatch = new JSONObject();
                    entMatch.put("term", new JSONObject().fluentPut("ent_code.keyword", reporter));
                    mustArray.add(entMatch);
                } else {
                    JSONObject reporterMatch = new JSONObject();
                    reporterMatch.put("wildcard", new JSONObject().fluentPut("tariff_reporter.keyword", "*" + reporter + "*"));
                    mustArray.add(reporterMatch);
                }
            }

            // 处理基本字段查询
            Map<String, String> exactMatchParams = new HashMap<>();
            exactMatchParams.put("id", "id.keyword");
            exactMatchParams.put("tariffRecordId", "tariff_record_id.keyword");
            exactMatchParams.put("taskId", "task_id");
            exactMatchParams.put("tariffNo", "tariff_no.keyword");
            exactMatchParams.put("tariffAttr", "tariff_attr_type.keyword");
//            exactMatchParams.put("entName", "ent_code.keyword");
            exactMatchParams.put("provinceName", "province_code.keyword");

            exactMatchParams.put("dateId", "date_id");
            exactMatchParams.put("monthId", "month_id");
            exactMatchParams.put("crawlId", "crawl_id.keyword");
//            exactMatchParams.put("isTelecom", "is_telecom");
            exactMatchParams.put("reported", "reported");


//            exactMatchParams.put("tariffState", "tariff_state");
            exactMatchParams.put("tariffReporter", "tariff_reporter");

            exactMatchParams.put("versionNo", "version_no.keyword");

            // 处理精确匹配字段
            for (Map.Entry<String, String> entry : exactMatchParams.entrySet()) {
                String value = param.getString(entry.getKey());
                if (StringUtils.isNotBlank(value)) {
                    addTermQuery(mustArray, entry.getValue(), value);
                }
            }

            // 运营商
            JSONArray entCodes = param.getJSONArray("entName");
            if (entCodes != null && !entCodes.isEmpty() && StringUtils.isNotBlank(entCodes.getString(0))) {
                JSONObject entCodeMatch = new JSONObject();
                entCodeMatch.put("terms", new JSONObject().fluentPut("ent_code.keyword", entCodes));
                mustArray.add(entCodeMatch);
            }

            // 资费状态
            JSONArray tariffState = param.getJSONArray("tariffState");
            if (tariffState != null && !tariffState.isEmpty() && StringUtils.isNotBlank(tariffState.getString(0))) {
                JSONObject tariffStateMatch = new JSONObject();
                tariffStateMatch.put("terms", new JSONObject().fluentPut("tariff_state", tariffState));
                mustArray.add(tariffStateMatch);
            }

            // 是否通信类
            JSONArray isTelecom = param.getJSONArray("isTelecom");
            if (isTelecom != null && !isTelecom.isEmpty() && StringUtils.isNotBlank(isTelecom.getString(0))) {
                JSONObject isTelecomMatch = new JSONObject();
                isTelecomMatch.put("terms", new JSONObject().fluentPut("is_telecom", isTelecom));
                mustArray.add(isTelecomMatch);
            }

            // 处理模糊匹配字段
            Map<String, String> fuzzyMatchParams = new HashMap<>();
            fuzzyMatchParams.put("name", "name");
            fuzzyMatchParams.put("fees", "fees");
            fuzzyMatchParams.put("feesUnit", "fees_unit");
            fuzzyMatchParams.put("exceedFees", "exceed_fees");   // 新增：超出资费
            fuzzyMatchParams.put("otherFees", "other_fees");     // 新增：其他费用
            fuzzyMatchParams.put("otherContent", "other_content");
            fuzzyMatchParams.put("validPeriod", "valid_period");
            fuzzyMatchParams.put("channel", "channel");
            fuzzyMatchParams.put("duration", "duration");
            fuzzyMatchParams.put("unsubscribe", "unsubscribe");
            fuzzyMatchParams.put("others", "others");
            fuzzyMatchParams.put("tariffType", "tariff_type");

            fuzzyMatchParams.put("fieldCheckResult", "field_check_result");

            // 处理模糊匹配字段
            for (Map.Entry<String, String> entry : fuzzyMatchParams.entrySet()) {
                String value = param.getString(entry.getKey());
                if (StringUtils.isNotBlank(value)) {
                    addMatchPhraseQuery(mustArray, entry.getValue(), value);
                }
            }
            String fieldCheckTime = param.getString("fieldCheckTime");

            // 资费规则核查时间
            if (StringUtils.isNotBlank(fieldCheckTime) && fieldCheckTime.contains("~")) {
                String[] fieldCheckTimeArray = fieldCheckTime.split("~");
                if (fieldCheckTimeArray.length == 2) {
                    JSONObject fieldCheckTimeStartMatch = new JSONObject();
                    fieldCheckTimeStartMatch.put("range", new JSONObject().fluentPut("field_check_time",
                            new JSONObject().fluentPut("gte", fieldCheckTimeArray[0])));
                    mustArray.add(fieldCheckTimeStartMatch);

                    JSONObject fieldCheckTimeEndMatch = new JSONObject();
                    fieldCheckTimeEndMatch.put("range", new JSONObject().fluentPut("field_check_time",
                            new JSONObject().fluentPut("lte", fieldCheckTimeArray[1])));
                    mustArray.add(fieldCheckTimeEndMatch);
                }
            }

            // 资费字段编号
            String fieldCheckNo = param.getString("fieldCheckNo");

            if (StringUtils.isNotBlank(fieldCheckNo)) {
                fieldCheckNo = fieldCheckNo.replace("[", "").replace("]", "")
                        .replaceAll("\"","");
                String[] fieldCheckNoArray = fieldCheckNo.split(",");
                if (fieldCheckNoArray.length > 0) {
                    JSONObject fieldCheckNoQuery = new JSONObject();
                    JSONArray fieldCheckNoOrArray = new JSONArray();

                    for (String no : fieldCheckNoArray) {
                        if (StringUtils.isNotBlank(no)) {
                            // 使用正则方式在 script 中模拟 FIND_IN_SET
                            JSONObject scriptQuery = new JSONObject();
                            JSONObject scriptObj = new JSONObject();

                            scriptObj.put("script", new JSONObject()
                                    .fluentPut("source", "if (doc['field_check_no.keyword'].size() > 0) { return /(^|,)" + no + "(,|$)/.matcher(doc['field_check_no.keyword'].value).find(); } return false;")
                                    .fluentPut("lang", "painless"));

                            scriptQuery.put("script", scriptObj);
                            fieldCheckNoOrArray.add(scriptQuery);
                        }
                    }

                    if (fieldCheckNoOrArray.size() > 0) {
                        fieldCheckNoQuery.put("bool", new JSONObject()
                                .fluentPut("should", fieldCheckNoOrArray)
                                .fluentPut("minimum_should_match", 1));
                        mustArray.add(fieldCheckNoQuery);
                    }
                }
            }

            // 处理时间范围查询
            String updateTimeBegin = param.getString("updateTimeBegin");
            String updateTimeEnd = param.getString("updateTimeEnd");
            if (StringUtils.isNotBlank(updateTimeBegin) || StringUtils.isNotBlank(updateTimeEnd)) {
                addRangeQuery(mustArray, "update_time", updateTimeBegin, updateTimeEnd);
            }

            String versionNos = param.getString("versionNos");
            String dateIds = param.getString("dateIds");
            String appearMonths = param.getString("appearMonths");

            // 添加入库月份过滤（appear_months是数组字段）
            if (StringUtils.isNotBlank(appearMonths)) {
                JSONObject termsQuery = new JSONObject();
                JSONObject termsFilter = new JSONObject();
                List<String> monthList = new ArrayList<>();
                monthList.add(appearMonths);
                termsFilter.put("appear_months", monthList);
                termsQuery.put("terms", termsFilter);
                mustArray.add(termsQuery);
                logger.info("添加入库月份过滤条件，月份：{}", appearMonths);
            }


            // 添加版本号数组查询
            if (StringUtils.isNotBlank(versionNos)) {
                try {
                    JSONObject termsQuery = new JSONObject();
                    JSONObject termsFilter = new JSONObject();
                    List<String> versionList = new ArrayList<>();
                    versionList.add(versionNos);
                    termsFilter.put("version_nos.keyword", versionList);
                    termsQuery.put("terms", termsFilter);
                    mustArray.add(termsQuery);
                    logger.info("添加版本号数组过滤条件，版本号：{}", versionNos);
                } catch (Exception e) {
                    logger.error("解析版本号数组参数失败: {}", versionNos, e);
                }
            }

            // 添加日期ID数组查询
            if (StringUtils.isNotBlank(dateIds)) {
                try {
                    JSONObject termsQuery = new JSONObject();
                    JSONObject termsFilter = new JSONObject();
                    JSONArray dateIdsArray = JSON.parseArray(dateIds);
                    termsFilter.put("date_id", dateIdsArray);
                    termsQuery.put("terms", termsFilter);
                    mustArray.add(termsQuery);
                    logger.info("添加日期ID数组过滤条件，日期ID：{}", dateIds);
                } catch (Exception e) {
                    logger.error("解析日期ID数组参数失败: {}", dateIds, e);
                }
            }

            // 处理费用范围查询
            String feesBegin = param.getString("feesBegin");
            String feesEnd = param.getString("feesEnd");
            if (StringUtils.isNotBlank(feesBegin) || StringUtils.isNotBlank(feesEnd)) {
                // 创建bool查询用于组合多个条件
                JSONObject boolQuery = new JSONObject();
                JSONArray shouldArray = new JSONArray();

                // 1. 尝试数值范围查询
                try {
                    JSONObject numericRangeQuery = new JSONObject();
                    JSONObject numericRangeObj = new JSONObject();
                    boolean hasNumericRange = false;

                    if (StringUtils.isNotBlank(feesBegin)) {
                        double beginValue = Double.parseDouble(feesBegin);
                        numericRangeObj.put("gte", beginValue);
                        hasNumericRange = true;
                    }

                    if (StringUtils.isNotBlank(feesEnd)) {
                        double endValue = Double.parseDouble(feesEnd);
                        numericRangeObj.put("lte", endValue);
                        hasNumericRange = true;
                    }

                    if (hasNumericRange) {
                        numericRangeQuery.put("range", new JSONObject().fluentPut("fees", numericRangeObj));
                        shouldArray.add(numericRangeQuery);
                    }
                } catch (NumberFormatException e) {
                    logger.warn("资费值解析为数值失败: {}", e.getMessage());
                }

                // 2. 使用字符串匹配作为备选
                JSONObject strRangeQuery = new JSONObject();
                JSONObject strRangeObj = new JSONObject();

                if (StringUtils.isNotBlank(feesBegin)) {
                    strRangeObj.put("gte", feesBegin);
                }

                if (StringUtils.isNotBlank(feesEnd)) {
                    strRangeObj.put("lte", feesEnd);
                }

                strRangeQuery.put("range", new JSONObject().fluentPut("fees.keyword", strRangeObj));
                shouldArray.add(strRangeQuery);

                // 如果有足够的查询条件，添加bool查询
                if (shouldArray.size() > 0) {
                    boolQuery.put("bool", new JSONObject()
                            .fluentPut("should", shouldArray)
                            .fluentPut("minimum_should_match", 1));
                    mustArray.add(boolQuery);
                }
            }
        } catch (Exception e) {
            logger.error("处理基本查询参数失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理日期范围和特殊查询参数
     *
     * @param param 前端传入参数
     * @param mustArray 查询条件数组
     */
    private void processdateRangeParams(JSONObject param, JSONArray mustArray) {
        try {
            // 处理上线时间和下线时间范围查询
            JSONArray onlineDayRange = param.getJSONArray("onlineDay");
            String onlineDayBegin = null;
            String onlineDayEnd = null;
            if (onlineDayRange != null && !onlineDayRange.isEmpty()) {
                String beginDate = onlineDayRange.getString(0);
                if (beginDate != null && beginDate.length() >= 10) {
                    onlineDayBegin = beginDate.substring(0, 10).replace("-", "");
                }
                if (onlineDayRange.size() > 1) {
                    String endDate = onlineDayRange.getString(1);
                    if (endDate != null && endDate.length() >= 10) {
                        onlineDayEnd = endDate.substring(0, 10).replace("-", "");
                    }
                }
            }

            JSONArray offlineDayRange = param.getJSONArray("offlineDay");
            String offlineDayBegin = null;
            String offlineDayEnd = null;
            if (offlineDayRange != null && !offlineDayRange.isEmpty()) {
                String beginDate = offlineDayRange.getString(0);
                if (beginDate != null && beginDate.length() >= 10) {
                    offlineDayBegin = beginDate.substring(0, 10).replace("-", "");
                }
                if (offlineDayRange.size() > 1) {
                    String endDate = offlineDayRange.getString(1);
                    if (endDate != null && endDate.length() >= 10) {
                        offlineDayEnd = endDate.substring(0, 10).replace("-", "");
                    }
                }
            }

            // 添加上下线时间范围查询
            if (StringUtils.isNotBlank(onlineDayBegin) || StringUtils.isNotBlank(onlineDayEnd)) {
                addRangeQuery(mustArray, "online_day", onlineDayBegin, onlineDayEnd);
            }

            if (StringUtils.isNotBlank(offlineDayBegin) || StringUtils.isNotBlank(offlineDayEnd)) {
                addRangeQuery(mustArray, "offline_day", offlineDayBegin, offlineDayEnd);
            }

            // 处理创建时间的月份范围
            JSONArray createTimeRange = param.getJSONArray("createTime");
            List<String> createTimeMonths = new ArrayList<>();
            if (createTimeRange != null && !createTimeRange.isEmpty()) {
                String beginMonth = createTimeRange.getString(0);
                String endMonth = createTimeRange.size() > 1 ? createTimeRange.getString(1) : beginMonth;

                if (StringUtils.isNotBlank(beginMonth) && StringUtils.isNotBlank(endMonth)) {
                    try {
                        // 解析开始和结束月份
                        int beginYear = Integer.parseInt(beginMonth.substring(0, 4));
                        int beginMonthVal = Integer.parseInt(beginMonth.substring(4));

                        int endYear = Integer.parseInt(endMonth.substring(0, 4));
                        int endMonthVal = Integer.parseInt(endMonth.substring(4));

                        // 生成所有在范围内的月份
                        for (int year = beginYear; year <= endYear; year++) {
                            int startM = (year == beginYear) ? beginMonthVal : 1;
                            int endM = (year == endYear) ? endMonthVal : 12;

                            for (int month = startM; month <= endM; month++) {
                                String monthStr = String.format("%04d%02d", year, month);
                                createTimeMonths.add(monthStr);
                            }
                        }
                    } catch (Exception e) {
                        logger.error("解析创建时间月份范围失败", e);
                    }
                }
            }

            // 添加创建时间月份过滤
            if (!createTimeMonths.isEmpty()) {
                JSONObject termsQuery = new JSONObject();
                JSONObject termsFilter = new JSONObject();
                termsFilter.put("appear_months", createTimeMonths);
                termsQuery.put("terms", termsFilter);
                mustArray.add(termsQuery);
            }

            // 资费规则核查时间
            String fieldCheckTime = param.getString("fieldCheckTime");
            if (StringUtils.isNotBlank(fieldCheckTime) && fieldCheckTime.contains("~")) {
                String[] fieldCheckTimeArray = fieldCheckTime.split("~");
                if (fieldCheckTimeArray.length == 2) {
                    JSONObject fieldCheckTimeStartMatch = new JSONObject();
                    fieldCheckTimeStartMatch.put("range", new JSONObject().fluentPut("field_check_time",
                            new JSONObject().fluentPut("gte", fieldCheckTimeArray[0])));
                    mustArray.add(fieldCheckTimeStartMatch);

                    JSONObject fieldCheckTimeEndMatch = new JSONObject();
                    fieldCheckTimeEndMatch.put("range", new JSONObject().fluentPut("field_check_time",
                            new JSONObject().fluentPut("lte", fieldCheckTimeArray[1])));
                    mustArray.add(fieldCheckTimeEndMatch);
                }
            }



                // 一级分类
                String type1 = param.getString("classicTypeOne");
                if (StringUtils.isNotBlank(type1)) {
                    type1 = type1.replace("[", "").replace("]", "")
                            .replaceAll("\"","");
                    if (StringUtils.isNotBlank(type1)) {
                        JSONObject type1Query = new JSONObject();
                        JSONArray type1OrArray = new JSONArray();
                        for (String code : type1.split(",")) {
                            if (StringUtils.isNotBlank(code)) {
                                JSONObject typeMatch = new JSONObject();
                                typeMatch.put("term", new JSONObject().fluentPut("classic_type_one", code));
                                type1OrArray.add(typeMatch);
                            }
                        }
                        type1Query.put("bool", new JSONObject().fluentPut("should", type1OrArray).fluentPut("minimum_should_match", 1));
                        mustArray.add(type1Query);
                    }
                }

                // 二级分类
                String type2 = param.getString("classicTypeTwo");
                if (StringUtils.isNotBlank(type2)) {

                    type2 = type2.replace("[", "").replace("]", "")
                            .replaceAll("\"","");

                    if (StringUtils.isNotBlank(type2)) {
                        JSONObject type2Query = new JSONObject();
                        JSONArray type2OrArray = new JSONArray();

                        for (String code : type2.split(",")) {
                            if (StringUtils.isNotBlank(code)) {
                                JSONObject typeMatch = new JSONObject();
                                typeMatch.put("term", new JSONObject().fluentPut("classic_type_two", code));
                                type2OrArray.add(typeMatch);
                            }
                        }

                        type2Query.put("bool", new JSONObject().fluentPut("should", type2OrArray).fluentPut("minimum_should_match", 1));
                        mustArray.add(type2Query);
                    }

            }
        } catch (Exception e) {
            logger.error("处理日期范围和规则核查参数失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 根据部门组类型获取运营商编码
     * @param deptGroupType 部门组类型
     * @return 运营商编码
     */
    private String getEntCodeByGroupType(String deptGroupType) {
        if (StringUtils.startsWith(deptGroupType, "g_dx") || StringUtils.startsWith(deptGroupType, "p_dx")) {
            return "1"; // 电信
        } else if (StringUtils.startsWith(deptGroupType, "g_yd") || StringUtils.startsWith(deptGroupType, "p_yd")) {
            return "2"; // 移动
        } else if (StringUtils.startsWith(deptGroupType, "g_lt") || StringUtils.startsWith(deptGroupType, "p_lt")) {
            return "3"; // 联通
        } else if (StringUtils.startsWith(deptGroupType, "g_gd") || StringUtils.startsWith(deptGroupType, "p_gd")) {
            return "5"; // 广电
        }
        return null;
    }

    /**
     * 添加精确匹配查询条件
     */
    private void addTermQuery(JSONArray mustArray, String field, String value) {
        if (StringUtils.isNotBlank(value)) {
            JSONObject termQuery = new JSONObject();
            termQuery.put("term", new JSONObject().fluentPut(field, value));
            mustArray.add(termQuery);
        }
    }

    /**
     * 添加短语匹配查询条件（模糊查询）
     */
    private void addMatchPhraseQuery(JSONArray mustArray, String field, String value) {
        if (StringUtils.isNotBlank(value)) {
            JSONObject matchQuery = new JSONObject();
            matchQuery.put("match_phrase", new JSONObject().fluentPut(field, value));
            mustArray.add(matchQuery);
        }
    }

    /**
     * 添加范围查询条件
     */
    private void addRangeQuery(JSONArray mustArray, String field, String beginValue, String endValue) {
        if (StringUtils.isNotBlank(beginValue) || StringUtils.isNotBlank(endValue)) {
            JSONObject rangeQuery = new JSONObject();
            JSONObject rangeCondition = new JSONObject();

            if (StringUtils.isNotBlank(beginValue)) {
                rangeCondition.put("gte", beginValue);
            }

            if (StringUtils.isNotBlank(endValue)) {
                rangeCondition.put("lte", endValue);
            }

            rangeQuery.put("range", new JSONObject().fluentPut(field, rangeCondition));
            mustArray.add(rangeQuery);
        }
    }

    /**
     * 将下划线命名转换为驼峰命名
     */
    private String toCamelCase(String name) {
        if (StringUtils.isBlank(name)) {
            return name;
        }

        StringBuilder sb = new StringBuilder();
        boolean upperCase = false;

        for (int i = 0; i < name.length(); i++) {
            char c = name.charAt(i);

            if (c == '_') {
                upperCase = true;
            } else if (upperCase) {
                sb.append(Character.toUpperCase(c));
                upperCase = false;
            } else {
                sb.append(c);
            }
        }

        return sb.toString();
    }

    /**
     * 判断字符串是否只包含英文字母、下划线和逗号
     */
    public static boolean isValidString(String input) {
        if (input == null || input.isEmpty()) {
            return false;
        }
        String regex = "^[a-zA-Z0-9_,]+$";
        return input.matches(regex);
    }
}
