package com.yunqu.tariff.utils;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.mq.MQBrokerUtil;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.xty.commonex.kit.ElasticsearchKit;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.slf4j.Logger;

import java.sql.SQLException;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class TariffPublicUtil {

    private static Logger logger = CommonLogger.getLogger();

    /**
     * 判断资费是否已公示
     * @param tariffNo 资费编号
     * @param auditDate 稽核日期
     * @return 是否已公示
     */
    public static boolean checkTariffIsPublic(String tariffNo, String auditDate) {
        try {
            if(StringUtils.isBlank(tariffNo)){
                return false;
            }

            if(StringUtils.isBlank(auditDate)){
                auditDate = EasyDate.getCurrentDateStr();
            }
            // 根据稽核日期确定应该使用的公示库版本
            String dateVersion = determinePublicDateId(auditDate);
            logger.info("稽核日期 {} 对应的公示库日期版本为: {}", auditDate, dateVersion);

            // 构建ES查询，从公示库中查询特定资费编号和日期的资费
            JSONObject queryParams = new JSONObject();
            queryParams.put("size", 10); // 只需要确认是否存在，获取少量数据即可

            // 构建查询条件
            JSONObject query = new JSONObject();
            JSONObject bool = new JSONObject();
            JSONArray must = new JSONArray();

            // 添加日期条件
            JSONObject dateTerms = new JSONObject();
            JSONObject dateFilter = new JSONObject();
            dateFilter.put("version_nos.keyword", new JSONArray().fluentAdd(dateVersion));
            dateTerms.put("terms", dateFilter);
            must.add(dateTerms);

            // 添加资费编号条件
            JSONObject tariffTerm = new JSONObject();
            JSONObject tariffFilter = new JSONObject();
            tariffFilter.put("tariff_no.keyword", tariffNo);
            tariffTerm.put("term", tariffFilter);
            must.add(tariffTerm);

            bool.put("must", must);
            query.put("bool", bool);
            queryParams.put("query", query);

            // 执行ES查询
            logger.info("查询公示库资费数据，参数: {}", queryParams.toJSONString());
            JSONObject esResult = ElasticsearchKit.search(Constants.XTY_TARIFF_PUBLIC_LIB_INDEX, queryParams);

            // 解析ES查询结果
            if (esResult != null && esResult.containsKey("hits")) {
                JSONObject hits = esResult.getJSONObject("hits");
                if (hits.containsKey("total") && hits.getJSONObject("total")!=null && hits.getJSONObject("total").getIntValue("value") > 0) {
                    // 存在匹配的资费记录
                    logger.info("资费 {} 在公示库中已存在", tariffNo);
                    return true;
                }
            }
            logger.info("资费 {} 在公示库中不存在", tariffNo);
            return false;
        } catch (Exception e) {
            logger.error("查询资费是否公示失败：reportNo:{}, 日期={}", tariffNo, auditDate, e);
            return false;
        }
    }


    /**
     * 根据稽核日期确定应该使用的公示库版本日期
     * 规则：1-10号使用当月1号版本，11-20号使用当月11号版本，21号及以后使用当月21号版本
     *
     * @param auditDate 稽核日期，格式为"yyyyMMdd"
     * @return 对应的公示库日期，格式为"yyyyMMdd"
     */
    public static String determinePublicDateId(String auditDate) {
        if (StringUtils.isBlank(auditDate)) {
            logger.error("稽核日期为空，无法确定公示库版本");
            return auditDate;
        }
        try {
            // 确保auditDate格式为yyyyMMdd
            if (auditDate.contains("-")) {
                auditDate = auditDate.replace("-", "");
            }

            EasySQL sql = new EasySQL("select version_no from " + Constants.getBusiSchema() + ".xty_crawler_version");
            sql.append("where 1=1");
            sql.append(auditDate, "and belong_date_id <= ?", false);
            sql.append(Constants.CRAWLER_VERSION_STATUS_1,"and version_status = ?");
            sql.append("order by belong_date_id desc limit 1");
            return QueryFactory.getTariffQuery().queryForString(sql.getSQL(), sql.getParams());
        } catch (Exception e) {
            logger.error("解析稽核日期失败: {}", auditDate, e);
            return auditDate;
        }
    }

    /**
     * 判断资费是否已报送
     * @param tariffNo 资费编号
     * @return 是否已报送
     */
    public static boolean checkTariffIsReport(String tariffNo) {
        try {
            logger.debug("检查报送库是否存在 REPORT_NO = {}", tariffNo);

            // 构建ES查询参数
            JSONObject queryParams = new JSONObject();
            queryParams.put("size", 1); // 只查一条
            queryParams.put("track_total_hits", true);

            // 构建查询条件
            JSONObject boolQuery = new JSONObject();
            JSONArray mustArray = new JSONArray();

            // 添加 REPORT_NO 条件
            JSONObject termQuery = new JSONObject();
            JSONObject termFilter = new JSONObject();
            termFilter.put("REPORT_NO", tariffNo); // 使用 keyword 精确匹配
            termQuery.put("term", termFilter);
            mustArray.add(termQuery);

            // 组装最终的查询
            boolQuery.put("must", mustArray);
            queryParams.put("query", new JSONObject().fluentPut("bool", boolQuery));

            logger.debug("报送库检查参数: {}", queryParams.toJSONString());

            // 执行ES查询
            JSONObject esResult = ElasticsearchKit.search(Constants.XTY_TARIFF_BAK_INFO_INDEX, queryParams);

            if (esResult != null && esResult.containsKey("hits")) {
                JSONObject hits = esResult.getJSONObject("hits");
                JSONArray hitsArray = hits.getJSONArray("hits");
                boolean exists = hitsArray != null && !hitsArray.isEmpty();
                return exists;
            }

            return false;
        } catch (Exception e) {
            logger.error("检查 ES 中是否存在 REPORT_NO={} 的记录失败: {}", tariffNo, e.getMessage(), e);
            return false;
        }
    }


    public static void addAuditTask(String[] auditDates) {
        if (auditDates == null || auditDates.length == 0) {
            logger.error("auditDates为空！");
            return;
        }
        logger.info("开始添加稽核任务...auditDates:{}",auditDates);
        for(String date: auditDates){
            auditInfo(date);
        }
    }

    public static void auditInfo(String dateId) {
        try {
        JSONObject param = new JSONObject();
        param.put("dateId", dateId);
        String snowflakeNextId = IdUtil.getSnowflakeNextIdStr();
        EasyRecord easyRecord = new EasyRecord(Constants.getBusiSchema()+".XTY_TARIFF_REAUDIT_RECORD", "ID")
                .setPrimaryValues(snowflakeNextId);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("ID", snowflakeNextId);
        jsonObject.put("PARAMS", param.toString());
        jsonObject.put("AUDIT_DATE", dateId);
        jsonObject.put("CREATE_TIME", EasyDate.getCurrentTimeStampString());
        jsonObject.put("CREATE_BY", "SYSTEM");
        jsonObject.put("SERVER_NODE", ServerContext.getNodeName());
        jsonObject.put("AUDIT_TYPE", "2");
        jsonObject.put("STATUS", "1");
        easyRecord.setColumns(jsonObject);
        EasyQuery query = QueryFactory.getWriteQuery();
        query.save(easyRecord);

        JSONObject params = new JSONObject();
        params.put("serialId", snowflakeNextId);
        params.put("dateId", dateId);
        params.put("command", "getAuditResult");
        params.put("data", jsonObject);
        params.put("triggerType", "1");
        logger.info("开始手工请求稽核发送消息");
        MQBrokerUtil.sendMsg(Constants.TARIFF_REAUDIT_MSG_BROKER, params.toString());


//            //执行完重新稽核之后执行资费名称不一致
        String snowflakeNextId2 = IdUtil.getSnowflakeNextIdStr();
        EasyRecord easyRecord2 = new EasyRecord(Constants.getBusiSchema()+".XTY_TARIFF_REAUDIT_RECORD", "ID")
                .setPrimaryValues(snowflakeNextId2);
        JSONObject jsonObject2 = new JSONObject();
        jsonObject2.put("ID", snowflakeNextId2);
        jsonObject2.put("PARAMS", param.toString());
        jsonObject2.put("AUDIT_DATE", dateId);
        jsonObject2.put("CREATE_TIME", EasyDate.getCurrentTimeStampString());
        jsonObject2.put("CREATE_BY", "SYSTEM");
        jsonObject2.put("SERVER_NODE", ServerContext.getNodeName());
        jsonObject2.put("AUDIT_TYPE", "7");
        jsonObject2.put("STATUS", "1");
        easyRecord2.setColumns(jsonObject2);
        query.save(easyRecord2);

        JSONObject params2 = new JSONObject();
        params2.put("serialId", snowflakeNextId2);
        params2.put("dateId", dateId);
        params2.put("command", "getTariffDiff");
        params2.put("data", jsonObject);
        MQBrokerUtil.sendMsg(Constants.TARIFF_REDIFF_MSG_BROKER, params2.toString());
        } catch (Exception e) {
            logger.error("执行手工请求稽核发送消息失败: {}", e);
        }
    }



    /**
     * <AUTHOR>
     * @Description 方法描述
     * @Param: [versionNo]
     * @Return: void
     * @Since create in 2025/7/29 10:49
     * @Company 广州云趣信息科技有限公司
     */
    public static void delPublicVersionTariff(String versionNo, String dateId) {
        try {
            // 1. 删除version_nos数组只包含待删除版本号的文档
            deleteDocumentsWithOnlyTargetVersion(versionNo);

            // 延迟3s 等待索引更新完成
            Thread.sleep(3000);

            // 2. 更新包含多个版本号的文档，移除指定版本号并更新相关字段
            updateDocumentAfterRemovingVersion(versionNo, dateId);

        } catch (Exception e) {
            logger.error("删除公式版本库 versionNo={}", versionNo, e);
        }
    }

    /**
     * 删除version_nos数组只包含待删除版本号的文档
     * @param versionNo 要删除的版本号
     */
    private static void deleteDocumentsWithOnlyTargetVersion(String versionNo) {
        try {
            // 构建查询条件：查找version_nos数组中包含指定版本号且数组长度为1的文档
            JSONObject boolQuery = new JSONObject()
                    .fluentPut("bool", new JSONObject()
                            .fluentPut("filter", new JSONArray()
                                    .fluentAdd(new JSONObject()
                                            .fluentPut("script", new JSONObject()
                                                    .fluentPut("script", new JSONObject()
                                                            .fluentPut("source", "return params.value.length == doc['version_nos.keyword'].length && doc['version_nos.keyword'].containsAll(params.value)")
                                                            .fluentPut("lang", "painless")
                                                            .fluentPut("params", new JSONObject().fluentPut("value", new JSONArray().fluentAdd(versionNo)))
                                                    )))));

            JSONObject deleteRequest = new JSONObject()
                    .fluentPut("query", boolQuery);
            logger.info("删除version_nos数组只包含目标版本号的文档 ({}): {}", versionNo, deleteRequest);

            // 执行删除操作
            JSONObject response = ElasticsearchKit.deleteByQuery(Constants.XTY_TARIFF_PUBLIC_LIB_INDEX, deleteRequest);

            int deleted = response.getIntValue("deleted");
            logger.info("删除version_nos数组只包含目标版本号的文档完成，删除了 {} 条记录", deleted);

        } catch (Exception e) {
            logger.error("删除version_nos数组只包含目标版本号的文档失败，版本号={}", versionNo, e);
        }
    }
    private static void updateDocumentAfterRemovingVersion(String versionToRemove, String dateToRemove) {
        try {
            // painless 脚本：移除目标version和date，更新version_no和date_id为最大
            String scriptSource = ""
                    + "if (ctx._source.version_nos != null && ctx._source.date_ids != null) {"
                    + "  ctx._source.version_nos.removeIf(v -> v == params.versionToRemove);"
                    + "  ctx._source.date_ids.removeIf(d -> d == params.dateToRemove);"
                    + "  "
                    + "  if (ctx._source.version_nos.size() > 0 && ctx._source.date_ids.size() > 0) {"
                    + "    ctx._source.version_no = ctx._source.version_nos.stream().max(String::compareTo).get();"
                    + "    ctx._source.date_id = ctx._source.date_ids.stream().max(String::compareTo).get();"
                    + "  }"
                    + "}";

            JSONObject script = new JSONObject()
                    .fluentPut("source", scriptSource)
                    .fluentPut("lang", "painless")
                    .fluentPut("params", new JSONObject()
                            .fluentPut("versionToRemove", versionToRemove)
                            .fluentPut("dateToRemove", dateToRemove)
                    );

            // 查询包含目标 version_no 的文档
            JSONObject query = new JSONObject()
                    .fluentPut("term", new JSONObject()
                            .fluentPut("version_nos.keyword", versionToRemove)
                    );

            JSONObject request = new JSONObject()
                    .fluentPut("script", script)
                    .fluentPut("query", query);
//                    .fluentPut("track_total_hits", true);

            logger.info("更新文档：移除 version={}, date={}，更新最新版本和日期: {}", versionToRemove, dateToRemove, request);
            while (true){
                try {
                    JSONObject response = ElasticsearchKit.updateByQuery(Constants.XTY_TARIFF_PUBLIC_LIB_INDEX, request);
                    int updated = StringUtils.isBlank(response.getString("updated"))?0:response.getIntValue("updated");
                    logger.info("更新完成，共更新 {} 条记录", updated);
                    if(updated == 0){
                        break;
                    }
                }catch(Exception e) {
                    logger.error("更新文档失败，version_no={}, date_id={}", versionToRemove, dateToRemove, e);
                    break;
                }
            }
        } catch (Exception e) {
            logger.error("更新文档失败，version_no={}, date_id={}", versionToRemove, dateToRemove, e);
        }
    }


    /**
     * 从 Elasticsearch 查询公示库记录
     * @return 公示库记录列表
     */
    public static List<JSONObject> queryPublicLibRecordFromESByNo(String tariffNo) {

        try {
            logger.info("从ES查询公示库记录，reportId: {}", tariffNo);

            // 构建ES查询参数
            JSONObject queryParams = new JSONObject();
            queryParams.put("size", 500);
            queryParams.put("track_total_hits", true);

            // 构建排序
            JSONArray sortArray = new JSONArray();
            // 按照date_id降序排序
            JSONObject dateIdSort = new JSONObject();
            dateIdSort.put("date_id.keyword", new JSONObject().fluentPut("order", "desc"));
            sortArray.add(dateIdSort);
            // 按照id降序排序
            JSONObject idSort = new JSONObject();
            idSort.put("_id", new JSONObject().fluentPut("order", "desc"));
            sortArray.add(idSort);
            queryParams.put("sort", sortArray);

            // 构建查询条件
            JSONObject boolQuery = new JSONObject();
            JSONArray mustArray = new JSONArray();

            // 添加ID列表条件
            JSONObject termsQuery = new JSONObject();
            JSONObject termsFilter = new JSONObject();
            termsFilter.put("tariff_no", tariffNo);
            termsQuery.put("term", termsFilter);
            mustArray.add(termsQuery);

            // 设置查询条件
            boolQuery.put("must", mustArray);
            queryParams.put("query", new JSONObject().fluentPut("bool", boolQuery));

            // 执行ES查询
            logger.debug("公示库查询参数: {}", queryParams.toJSONString());
            JSONObject esResult = ElasticsearchKit.search(Constants.XTY_TARIFF_PUBLIC_LIB_INDEX, queryParams);

            // 处理查询结果
            List<JSONObject> resultList = new ArrayList<>();
            if (esResult != null && esResult.containsKey("hits")) {
                JSONObject hits = esResult.getJSONObject("hits");
                if (hits.containsKey("hits")) {
                    JSONArray hitsArray = hits.getJSONArray("hits");
                    for (int i = 0; i < hitsArray.size(); i++) {
                        JSONObject hit = hitsArray.getJSONObject(i);
                        if (hit.containsKey("_source")) {
                            JSONObject source = hit.getJSONObject("_source");

                            // 将ES字段名转换为与数据库字段名一致（小写转大写）
                            JSONObject convertedRecord = new JSONObject();
                            for (Map.Entry<String, Object> entry : source.entrySet()) {
                                String upperCaseKey = entry.getKey().toUpperCase();
                                convertedRecord.put(upperCaseKey, entry.getValue());
                            }

                            resultList.add(convertedRecord);
                        }
                    }
                }
            }

            logger.info("从ES查询到{}条公示库记录", resultList.size());
            return resultList;
        } catch (Exception e) {
            logger.error("从ES查询公示库记录失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}
