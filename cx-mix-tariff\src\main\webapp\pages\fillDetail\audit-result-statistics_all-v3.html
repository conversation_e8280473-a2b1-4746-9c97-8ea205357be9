<!DOCTYPE html>
<html>

<head>
  <title>各省未报送资费数量统计</title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
  <!-- 基础的 css js 资源 -->
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.4">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.3">
  <link rel="stylesheet" href="./common.css?v=20241127">
  <!-- 表头样式覆盖 -->
  <link rel="stylesheet" href="/cx-mix-report/static/css/tableModify.css?v=1.0.0">
  <link rel="stylesheet" href="/cx-mix-tariff/static/css/searchForm.css">
  <style>
    #auditResultStatisticsOrders_global .search-form.grid-4 {
      grid-template-columns: repeat(4, 1fr) !important;
      gap: 16px 4px !important;
    }

    .el-tabs__nav-wrap::after {
      background-color: #ffffff;
    }
  </style>
</head>

<body class="yq-page-full vue-box">
  <div id="auditResultStatisticsOrders_global" class="flex yq-table-page" v-loading="loading"
    element-loading-text="加载中..." v-cloak
    v-auth:[permissions]="'cx-xty-tariff-restartComputer;cx-xty-tariff-restartMark;cx-xty-tariff-reStatistics;cx-xty-tariff-audit-export'">
    <div class="yq-card">
      <div class="card-header">
        <div class="head-title">{{ getI18nValue('各省未报送资费数量统计') }}【{{tableName_province === 'day' ? '日报' : '月报'}}】</div>
        <el-popover placement="bottom" title="温馨提示" trigger="hover">
          <p class="tip-item"><span class="tip-item_name">未报送资费数：</span>指有用户订购但未报送的资费方案数量。</p>
          <p class="tip-item"><span class="tip-item_name">资费报送率：</span>指活跃在售和续订资费方案数占用户订购的资费方案总数比例。</p>
          <div slot="reference" class="el-icon-info" style="margin-left: 8px; font-size: 16px;"></div>
        </el-popover>
        <div class="yq-table-control">
          <el-radio-group v-model="tableName_province" @change="handleTabChange">
            <el-radio-button label="day">日报</el-radio-button>
            <el-radio-button label="month">月报</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="card-content">
        <el-form class="search-form grid-4" :model="searchForm" :rules="rules" ref="searchForm" size="small"
          label-width="100px">
          <el-form-item label="出现日期" prop="appearTime">
            <el-date-picker v-model="searchForm.appearTime" :type="tableName_province === 'day' ? 'date' : 'month'"
              :value-format="tableName_province === 'day' ? 'yyyy-MM-dd' : 'yyyy-MM'" placeholder="请选择" size="small"
              style="width: 100%">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="订购省份" prop="provinceCode">
            <div style="display: flex;">
              <el-select v-model="searchForm.provinceCode" @change="handleChange($event, 'provinceCode')"
                placeholder="请选择" :disabled="roleInfo.userAccType === '4' || roleInfo.userAccType === '5'" filterable
                clearable multiple collapse-tags>
                <el-option v-for="item in provinces" :key="item.CODE" :label="item.NAME" :value="item.CODE"></el-option>
              </el-select>
              <el-form-item label="全选" label-width="50px">
                <el-checkbox v-model="provinceCodeCheckAll"
                  @change="handleChangeCheckAll($event, 'provinceCode')"></el-checkbox>
              </el-form-item>
            </div>
          </el-form-item>
          <el-form-item label="运营商" prop="entType">
            <el-select v-model="searchForm.entType" @change="handleChange($event, 'entType')" placeholder="请选择"
              :disabled="roleInfo.userAccType === '3' || roleInfo.userAccType === '5'" filterable clearable multiple
              collapse-tags>
              <!-- <el-checkbox v-slot="prefix" v-model="entTypeCheckAll"
                @change="handleChangeCheckAll($event, 'entType')"></el-checkbox> -->
              <el-option v-for="(label, value) in companys" :key="value" :label="label" :value="value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label-width="0px" style="justify-self: flex-end; grid-column: 4;">
            <el-button type="primary" icon="el-icon-search" @click="getList(1)">搜索</el-button>
            <el-button type="primary" plain icon="el-icon-refresh" @click="handleReset"
              style="margin-left: 4px;">重置</el-button>
            <el-button v-if="permissions['cx-xty-tariff-audit-export']" type="primary" plain size="small"
              @click="handleExportFront($refs.table.$el, {title:'各省未报送资费数量统计'})"
              style="margin-left: 4px;">
              <i class="el-icon-download"></i>导出
            </el-button>
            <el-dropdown>
              <el-button type="primary" plain size="small" style="margin-left: 4px;">
                更多<i class="el-icon-arrow-down"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  @click.native="$refs.orderControls && $refs.orderControls.open('order-delete')">订购删除-通知</el-dropdown-item>
                <el-dropdown-item
                  @click.native="$refs.orderControls && $refs.orderControls.open('order-restart')">订购重跑-通知</el-dropdown-item>
                <el-dropdown-item
                  @click.native="$refs.orderControls && $refs.orderControls.open('check-restart')">稽核重跑-通知</el-dropdown-item>
                <el-dropdown-item v-if="permissions['cx-xty-tariff-restartComputer']"
                  @click.native="handleDialogVisible(1)">{{getI18nValue('重新稽核')}}</el-dropdown-item>
                <el-dropdown-item v-if="permissions['cx-xty-tariff-reStatistics']"
                  @click.native="handleDialogVisible(3)">{{getI18nValue('重新统计')}}</el-dropdown-item>
                <el-dropdown-item v-if="permissions['cx-xty-tariff-restartMark']"
                  @click.native="handleDialogVisible(2)">重新报送</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </el-form-item>
        </el-form>
        <div class="yq-table">
          <el-table ref="table" :data="tableData" :span-method="spanMethod" height="100%" stripe border fit
            style="width: 100%">
            <el-table-column label="订购省份" prop="provinceName" min-width="120">
            </el-table-column>
            <el-table-column label="运营商" prop="entName" min-width="120">
              <template slot-scope="scope">
                {{ getReporterName(scope.row) }}
              </template>
            </el-table-column>
            <el-table-column label="未报送资费数" prop="unreportCount" min-width="120">
              <template slot-scope="scope">
                <el-link :underline="false" @click="handleToPlan(scope.row, 'unreport')">
                  {{scope.row.unreportCount}}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column label="资费报送率" prop="reportRate" min-width="120">
            </el-table-column>
            <el-table-column label="未公示资费数" prop="unpublicCount" min-width="120">
              <template slot-scope="scope">
                <el-link v-if="tableName_province === 'day'" :underline="false"
                  @click="handleToPlan(scope.row, 'unpublic')">
                  {{scope.row.unpublicCount}}
                </el-link>
                <span v-else>{{scope.row.unpublicCount}}</span>
              </template>
            </el-table-column>
            <el-table-column label="资费公示率" prop="publicRate" min-width="120">
            </el-table-column>
            <el-empty slot="empty" description="暂无信息"></el-empty>
          </el-table>
        </div>
      </div>
    </div>
    <el-dialog :title="key == 1 ? '重新稽核': (key==2 ? '重新报送' : '重新统计')" :visible.sync="dialogVisible" width="30%"
      :close-on-click-modal="false" @close="handleClose">
      <el-form :model="restartForm" ref="restartForm" label-width="100px">
        <el-form-item prop="date" :rules="rules" label="选择日期">
          <el-date-picker type="date" placeholder="请选择" v-model="restartForm.date" style="width: 100%;"
            value-format="yyyyMMdd" :picker-options="pickerOptions"></el-date-picker>
        </el-form-item>
        <template v-if="key == 1||key == 2">
          <el-form-item prop="provinceId" label="省份">
            <el-cascader v-model="restartForm.provinceId" :options="provinceAndGroup" :props="provinceCascaderProps"
              :show-all-levels="false" placeholder="请选择" filterable clearable style="width: 100%;"></el-cascader>
          </el-form-item>
          <el-form-item prop="entCode" label="运营商">
            <el-select v-model="restartForm.entCode" placeholder="请选择" filterable clearable>
              <el-option v-for="(label, value) in XTY_TARIFF_ENT" :key="value" :label="label"
                :value="value"></el-option>
            </el-select>
          </el-form-item>
        </template>
      </el-form>
      <span v-if="isLoading" style="color: red;">正在发起请求，请勿关闭窗口！</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleOk" :loading="isLoading" :disabled="isLoading">确 定</el-button>
      </span>
    </el-dialog>
    <order-controls ref="orderControls"></order-controls>
  </div>
</body>
<script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
<script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
<script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
<script src="/cc-base/static/js/my_i18n.js?v=202111"></script>
<script src="/cc-base/static/js/i18n.js?v=1"></script>
<script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.1"></script>
<script src="/cx-mix-tariff/pages/fillDetail/mixins/statisticMixins.js?v=20241224"></script>
<script type="text/javascript" src="/cx-mix-tariff/static/js/time.js"></script>
<script>
  var appPage = new Vue({
    el: '#auditResultStatisticsOrders_global',
    mixins: [statisticMixins],
    components: {
      'order-controls': httpVueLoader('./components/orderControls.vue')
    },
    data: function () {
      return {
        tableName_province: 'day',
        searchForm: {
          provinceCode: [
            "110000",
            "120000",
            "130000",
            "140000",
            "150000",
            "210000",
            "220000",
            "230000",
            "310000",
            "320000",
            "330000",
            "340000",
            "350000",
            "360000",
            "370000",
            "410000",
            "420000",
            "430000",
            "440000",
            "450000",
            "460000",
            "500000",
            "510000",
            "520000",
            "530000",
            "540000",
            "610000",
            "620000",
            "630000",
            "640000",
            "650000",
            "0"
          ],
          entType: ['0', '1', '2', '3', '5'],
          appearTime: getTodayDate(),
        },
        rules: {
          provinceCode: [{ required: true, message: '请选择订购省份' }],
          entType: [{ required: true, message: '请选择运营商' }],
          appearTime: [{ required: true, message: '请选择出现日期' }],
        },
        provinceAndGroup: [],
        provinceCascaderProps: {
          value: 'id',
          label: 'name',
          children: 'children',
          emitPath: false,
          multiple: true,
        },
        provinceCodeCheckAll: true,
        key: '',
        dialogVisible: false,
        isLoading: false,
        restartForm: {
          date: "",
          provinceId: "",
          entCode: "",
        },
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          }
        },
        XTY_TARIFF_ENT: {},
      }
    },
    created() {
      this.getProvinceAndGroup()
    },
    methods: {
      handleTabChange(val) {
        switch (val) {
          case 'day':
            this.searchForm.appearTime = getTodayDate()
            break;
          case 'month':
            this.searchForm.appearTime = getThisMonthStartDate2()
            break;
          default:
            break;
        }
        this.getList()
      },
      handleReset() {
        this.$refs['searchForm'].resetFields();
        this.handleChangeCheckAll(true, 'provinceCode')
        this.handleChangeCheckAll(true, 'entType')
        this.provinceCodeCheckAll = true
        this.handleTabChange(this.tableName_province)
        this.getList()
      },
      getList() {
        this.$refs.searchForm?.validate?.(valid => {
          if (!valid) {
            return false
          }

          this.loading = true
          const payload = {
            provinceCodes: this.searchForm.provinceCode,
            ent: this.searchForm.entType,
            searchTime: this.searchForm.appearTime.replace(/-/g, ''),
            type: this.tableName_province === 'day' ? '1' : '2',
          }

          yq.remoteCall('/cx-mix-tariff/webcall?action=tariffAuditStaticDao.getProvinceNotReportCount2', payload)
            .then(res => {
              if (res.data) {
                this.tableData = (res.data || [])
                  .sort((a, b) => customSort(a, b, 'provinceCode'))
                  .sort((a, b) => a.provinceCode === b.provinceCode ? customSort(a, b, 'ent') : 0)
              }
            })
            .finally(() => this.loading = false)
        })
      },
      getReporterName(row) {
        if (row.provinceCode === '0') {
          return row.entName
        } else {
          return row.provinceName + row.entName
        }
      },
      getCommonDict() {
        let data = {
          "controls": [
            "common.getDict(XTY_TARIFF_ENT)"
          ],
          "params": {}
        }
        return Promise.all([
          yq.remoteCall('/cx-mix-report/webcall', data).then(res => {
            this.XTY_TARIFF_ENT = res["common.getDict(XTY_TARIFF_ENT)"].data || {}
            this.companys = res["common.getDict(XTY_TARIFF_ENT)"].data || {}
            this.companys[0] = '全行业'
          }),
          yq.remoteCall('/cx-mix-report/webcall?action=dutyReport.provinceDict').then(res => {
            this.provinces = res.data || []
          }),
        ])
      },
      // 获取报送主体的省份
      getProvinceAndGroup() {
        return yq
          .remoteCall(
            "/cx-mix-tariff/webcall?action=common.queryTariffProvinceTree"
          )
          .then((res) => {
            if (res.state == 1) {
              this.provinceAndGroup = res.data || [];
            }
          });
      },
      handleDialogVisible(index) {
        this.dialogVisible = true
        this.key = index
      },
      handleOk() {
        let url = this.key === 1 ? "/cx-mix-tariff/servlet/tariff?action=Audit&dateId=" : (this.key === 2 ? '/cx-mix-tariff/servlet/tariff?action=BakTariff&dateId=' : '/cx-mix-tariff/servlet/tariff?action=OrderStat&dateId=')
        url += this.restartForm.date;
        const payload = {}
        if (this.key === 1 || this.key === 2) {
          let find = this.findProvince(this.restartForm.provinceId, 'id')
          find && (url += `&provinceCode=${find.provinceCode}`)
          find && (url += `&tariffProvinceCode=${find.tariffProvinceCode}`)
          this.restartForm.entCode && (url += `&entCode=${this.restartForm.entCode}`)
        } else {
          payload.date = this.restartForm.date
        }
        this.$refs['restartForm'].validate((valid) => {
          if (valid) {
            this.isLoading = true
            yq.remoteCall(
              url,
              payload,
              (res) => {
                if (res.state == 1) {
                  this.$message.success(res.msg);
                  this.isLoading = false
                  this.handleClose()

                } else {
                  this.$message.error(res.msg);
                  this.isLoading = false

                }
              }
            );
          }
        })
      },
      handleClose() {
        this.$refs.restartForm.resetFields();
        this.dialogVisible = false
      },
      handleToPlan(row, prop) {
        const data = {
          ent: row.ent === '0' ? '' : row.ent,
          provinceCode: row.provinceCode,
        }

        if (this.searchForm.appearTime) {
          if (this.tableName_province == 'month') {
            data.appearTime = [this.getMonthDates(this.searchForm.appearTime).firstDay, this.getMonthDates(this.searchForm.appearTime).lastDay].join(',')
          } else if (this.tableName_province == 'day') {
            data.appearTime = this.searchForm.appearTime + ',' + this.searchForm.appearTime
          }
        }

        if (prop === 'unreport') {
          top.popup.openTab({
            url: '/cx-mix-tariff/pages/fillDetail/unregistered-tariff-plan-new.html',
            title: getI18nValue('未报送资费方案'),
            data,
            id: 'unregistered-tariff-plan-new'
          })
        } else if (prop === 'unpublic') {
          top.popup.openTab({
            url: '/cx-mix-tariff/pages/fillDetail/tariff-plan-list-unpublic.html',
            title: getI18nValue('未公示资费方案'),
            data,
            id: 'tariff-plan-list-unpublic'
          })
        }
      },
    }
  });
</script>

</html>