package com.yunqu.tariff.handler.search;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.enums.EntEnum;
import com.yunqu.tariff.factory.EsQueryAggsBuilder;
import com.yunqu.tariff.factory.EsQueryFactory;
import com.yunqu.tariff.handler.SearchHandler;
import com.yunqu.xty.commonex.kit.ElasticsearchKit;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.EasySQL;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

import static com.yunqu.tariff.base.Constants.AREA_CODE_ALL;
import static com.yunqu.tariff.base.Constants.XTY_TARIFF_BAK_INFO_INDEX;

/**
 * 获取公示资费报送内容情况统计
 */
public class TariffReportChkStatSearchHandler implements SearchHandler<List<JSONObject>> {
    private JSONObject param;

    public TariffReportChkStatSearchHandler(JSONObject param) {
        this.param = param;
    }

    // 两个long类型数据相除，分母为0时返回0
    private static long divide(Long numerator, int denominator) {
        if (denominator == 0) {
            return 0;
        }
        return new BigDecimal(numerator).divide(new BigDecimal(denominator), 2, RoundingMode.HALF_UP).longValue();
    }

    /**
     * 统计平均值行
     */
    private static void statAvgRow(List<JSONObject> resultList, List<String> allCheckNos, Map<String, Long> checkNoCountMap) {
        if (CollectionUtils.isEmpty(resultList) || allCheckNos.isEmpty()) {
            return;
        }
        // 行数
        int rowNum = resultList.size();
        JSONObject avgRow = new JSONObject();
        avgRow.put("version", "平均");
        allCheckNos.forEach(it -> avgRow.put(it, divide(checkNoCountMap.getOrDefault(it, 0L), rowNum)));
        avgRow.put("total", divide(checkNoCountMap.getOrDefault("total", 0L), rowNum));
        resultList.add(avgRow);
    }

    @Override
    public List<JSONObject> search() {
        //1:构建ES查询条件
        JSONObject queryParams = buildDistributionTotalQueryParams();
        //2:执行ES查询
        JSONObject esResult = ElasticsearchKit.search(XTY_TARIFF_BAK_INFO_INDEX, queryParams);
        //3:解析查询结果
        return parseDistributionTotalResult(esResult);
    }

    private JSONObject buildDistributionTotalQueryParams() {
        EsQueryAggsBuilder aggsBuilder = EsQueryAggsBuilder.getInstance();
        JSONObject aggs = aggsBuilder.buildAggs(
                "version_group", aggsBuilder.aggTerms("PUBLIC_VERSIONS.keyword", 1000, "desc",
                        "field_check_group", aggsBuilder.aggCardinalityTerms("FIELD_CHECK_NOS.keyword", 30, null)));

        EsQueryFactory.EsQueryParamBuilder builder =
                EsQueryFactory.queryParamBuilder()
                        .terms("ENT", getStrList("endCode")) // 运营商
                        .terms("TYPE1", getStrList("type1")) // 一级分类
                        .terms("IS_PUBLIC.keyword", getStrList("isPublic")) // 是否公示
                        .terms("IS_TELECOM.keyword", getStrList("isTelecom"))     // 是否通信类
                        .terms("TYPE2", getStrList("type2")) // 二级分类
                        .terms("STATUS", getStrList("status"))
                        .terms("PUBLIC_VERSIONS.keyword", getStrList("versionNos")) // 版本号
                        .term("ENT_ID.keyword", param.getString("entId"))
                        .term("BUSI_ORDER_ID.keyword", param.getString("busiOrderId"))
                        .aggs(aggs)
                        .size(0);
        //  处理适用省份参数
        if (StringUtils.isNotBlank(param.getString("provinceCode"))) {
            List<String> provinceCodes = Arrays.asList(param.getString("provinceCode").split(","));
            builder.boolShouldOr(
                    new JSONObject().fluentPut("terms", new JSONObject().fluentPut("PROVINCE_CODES.keyword", provinceCodes)),
                    new JSONObject().fluentPut("term", new JSONObject().fluentPut("PROVINCE_CODES.keyword", AREA_CODE_ALL)));
        }
        //  处理适用地区参数
        if (StringUtils.isNotBlank(param.getString("areaCode"))) {
            List<String> areaCodes = Arrays.asList(param.getString("areaCode").split(","));
            builder.boolShouldOr(
                    new JSONObject().fluentPut("terms", new JSONObject().fluentPut("AREA_CODES", areaCodes)),
                    new JSONObject().fluentPut("term", new JSONObject().fluentPut("AREA_CODES", AREA_CODE_ALL)));
        }
        // 处理多选的reporter参数
        List<String> reporters = getStrList("reporter");
        if (CollectionUtils.isNotEmpty(reporters)) {
            String reporter = reporters.get(0);
            if (StringUtils.equalsAny(reporter, EntEnum.getAllEnt())) {
                builder.terms("ENT", reporters);
            } else {
                builder.wildcard("REPORTER.keyword", reporter);
            }
        }
        logger.info("TariffReportChkStatSearchHandler stat param  ==> " + JSON.toJSONString(builder.buildRequestParam()));
        return builder.buildRequestParam();
    }

    private List<JSONObject> parseDistributionTotalResult(JSONObject esResult) {
        List<JSONObject> resultList = new ArrayList<>();
        JSONObject aggregations = esResult.getJSONObject("aggregations");
        if (aggregations == null) {
            return resultList;
        }
        // 获取版本分组
        JSONArray versionBuckets = aggregations.getJSONObject("version_group").getJSONArray("buckets");
        List<String> filterVersions = getStrList("versionNos");
        List<String> allFieldCheckNo = getAllTariffRule();
        Map<String, Long> checkNoCountMap = new HashMap<>();
        // 抽取结果
        for (int i = 0; i < versionBuckets.size(); i++) {
            JSONObject versionBucket = versionBuckets.getJSONObject(i);
            String version = versionBucket.getString("key");
            long total = versionBucket.getLongValue("doc_count");
            checkNoCountMap.merge("total", total, Long::sum);
            JSONArray fieldCheckBuckets = versionBucket.getJSONObject("field_check_group").getJSONArray("buckets");
            if (fieldCheckBuckets == null) {
                continue;
            }
            // 如果当前版本号不在过滤列表中，则跳过
            if (CollectionUtils.isNotEmpty(filterVersions) && !filterVersions.contains(version)) {
                continue;
            }
            Map<String, Long> fieldCheckNum = new HashMap<>();
            for (int j = 0; j < fieldCheckBuckets.size(); j++) {
                JSONObject fieldCheckBucket = fieldCheckBuckets.getJSONObject(j);
                String checkNo = fieldCheckBucket.getString("key");
                Long docCount = fieldCheckBucket.getLong("doc_count");
                fieldCheckNum.put(checkNo, docCount);
                checkNoCountMap.merge(checkNo, docCount, Long::sum);
            }
            JSONObject row = new JSONObject();
            row.put("version", version);
            row.put("total", total);
            allFieldCheckNo.forEach(it -> row.put(it, fieldCheckNum.getOrDefault(it, 0L)));
            resultList.add(row);
        }
        // 统计平均值
        statAvgRow(resultList, allFieldCheckNo, checkNoCountMap);
        return resultList;
    }

    private List<String> getAllTariffRule() {
        EasySQL easySQL = new EasySQL("select RULE_NO from " + getTableName("xty_tariff_check_rule") + " order by RULE_NO");
        List<String> rules = new ArrayList<>();
        try {
            rules = QueryFactory.getReadQuery().queryForList(easySQL.getSQL(), easySQL.getParams(),
                    new EasyRowMapper<String>() {
                        @Override
                        public String mapRow(ResultSet rs, int i) {
                            try {
                                return rs.getString("RULE_NO");
                            } catch (SQLException e) {
                                logger.error(e.getMessage(), e);
                                throw new RuntimeException(e);
                            }
                        }
                    });
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return rules;
    }

    private List<String> getStrList(String paramKey) {
        JSONArray jsonArray = param.getJSONArray(paramKey);
        if (jsonArray == null || jsonArray.isEmpty() || StringUtils.isBlank(jsonArray.getString(0))) {
            return Collections.emptyList();
        }
        return JSONArray.parseArray(jsonArray.toJSONString(), String.class);
    }
}
