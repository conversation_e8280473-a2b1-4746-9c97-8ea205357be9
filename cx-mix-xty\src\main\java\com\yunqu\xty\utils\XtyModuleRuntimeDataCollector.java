package com.yunqu.xty.utils;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.JsonUtil;
import com.yq.busi.common.util.cache.RedisUtil;
import com.yunqu.xty.base.CommonLogger;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 用于采集每个模块各个场景的运行埋点数据，用于进行预警监控
 */
public class XtyModuleRuntimeDataCollector {
    private static Logger logger = CommonLogger.logger;

    /**
     * 各模块运行数据汇总的key
     * 采用hashset结构
     */
    private static final String runtimeDataHashKey = "ModuleRuntimeDataCollector";

    /**
     * 同一个key，间隔x秒后，才会更新缓存，避免频繁更新redis
     */
    private static int UPDATE_INTERVAL = 10;

    /**
     * 每个key上次更新时间戳，便于控制redis频繁写入
     */
    private static Map<String,Long> runtimeDataUpdateTimestamp = new ConcurrentHashMap<String,Long>();

    /**
     * 设置运行所需的初始化时长数据，项目按需设置，如无特需情况，无需修改
     * @param updateInterval       同一个场景key，限制更新redis的频繁，最小为10秒；避免频繁更新redis，造成redis压力问题
     */
    public static void setIntervalData(int updateInterval) {
        if(updateInterval>=10){
            UPDATE_INTERVAL = updateInterval;
        }
    }
    /**
     * 各个模块上报运行状态数据(包含关键的、影响业务运行的场景即可)
     * @param moduleName     模块名
     * @param busiKey        模块的运行场景key，由各个模块定义，如类名等
     * @param busiName       模块运行的场景名称，与key对应，描述该场景说明
     * @param intevalSeconds 描述该模块的该场景间隔多少秒执行一次; 如该场景正常10分钟运行一次，可以该值设置为 650、700等，比实际略大一些; 最小值为10秒
     * @return
     */
    public static boolean report(String moduleName,String busiKey,String busiName, int intevalSeconds) {
        if(StringUtils.isAnyBlank(moduleName,busiKey,busiName)){
            logger.error("参数 moduleName,busiKey,busiName 不能为空,moduleName:"+moduleName+",busiKey:"+busiKey+",busiName:"+busiName);
            return false;
        }
        if(intevalSeconds<=0){
            logger.error("参数 intevalSeconds 必须大于0,moduleName:"+moduleName+",busiKey:"+busiKey+",busiName:"+busiName+",intevalSeconds:"+intevalSeconds);
            return false;
        }

        try{
            String key =  moduleName + ":"+busiKey;

            //检测上次写入时间戳，避免频繁写入
            Long lastUpdateTimeStamp = runtimeDataUpdateTimestamp.get(key);
            if(lastUpdateTimeStamp!=null && (System.currentTimeMillis() - lastUpdateTimeStamp) < UPDATE_INTERVAL){
                return true;
            }

            //当该key的更新频率小于redis写入频率时，将 intevalSeconds 延长，避免后续检测出现问题
            int readIntevalSeconds = intevalSeconds;
            if( intevalSeconds <= UPDATE_INTERVAL){
                intevalSeconds = intevalSeconds + 5;
            }


            String str = RedisUtil.hget(runtimeDataHashKey,key);
            JSONObject json = null;
            if(StringUtils.isNotBlank(str)){
                json = JsonUtil.toJSONObject(str);
            }
            if(json==null){
                json =  new JSONObject();
            }
            json.put("moduleName",moduleName);
            json.put("busiKey",busiKey);
            json.put("busiName",busiName);
            json.put("intevalSeconds",intevalSeconds);  //用于检测运行状态的更新频率
            json.put("realIntevalSeconds",readIntevalSeconds);  //业务实际更新频繁
            json.put("runTime",DateUtil.getCurrentDateStr());
            json.put("runTimestamp",System.currentTimeMillis());
            json.put("nextRunTime", DateUtil.addSecond(DateUtil.TIME_FORMAT,DateUtil.getCurrentDateStr(),intevalSeconds));
            json.put("nextRunTimestamp", (System.currentTimeMillis() + intevalSeconds*1000 ));

            RedisUtil.hadd(runtimeDataHashKey,key,json.toJSONString());

            //更新时间戳
            runtimeDataUpdateTimestamp.put(key,System.currentTimeMillis());
            return true;
        } catch (Exception e) {
            logger.error("模块上报运行数据时出现异常:"+e.getMessage(),e);
        }
        return false;
    }

}
