package com.yunqu.xty.inf;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.ServiceUtil;
import com.yq.busi.common.util.thread.ThreadManager;
import com.yunqu.xty.base.CommonLogger;
import com.yunqu.xty.base.Constants;
import com.yunqu.xty.base.QueryFactory;
import com.yunqu.xty.excuotr.EventDispatcher;
import com.yunqu.xty.thread.ThreadMgr;
import com.yunqu.xty.utils.XtyModuleRuntimeDataCollector;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import java.util.List;
import java.util.Objects;

public class RestartDutyService extends IService {

    private Logger logger = CommonLogger.getLogger("duty");

    public static RestartDutyService getInstance() {
        return RstartDutyServiceInstance.instance;
    }

    @Override
    public JSONObject invoke(JSONObject jsonObject) throws ServiceException {
        logger.info("执行定责重新发起任务，开始时间：" + DateUtil.getCurrentDateStr());
        String startTime = DateUtil.addDay(DateUtil.TIME_FORMAT,DateUtil.getCurrentDateStr(),-7);
        String endTime = DateUtil.getCurrentDateStr();
        String entId = Constants.getEntId();
        return this.invoke(startTime, endTime, entId);
    }

    private static class RstartDutyServiceInstance {
        private static RestartDutyService instance = new RestartDutyService();
    }


    public JSONObject invoke (String startTime ,String endTime,String entId) {
        String key = "XTY_RSTART_DUTY_"+ entId;
        JSONObject result = new JSONObject();
        long start = System.currentTimeMillis();
        logger.info("执行定责重新发起任务，开始时间：" + DateUtil.getCurrentDateStr());
        if (StringUtils.isNotBlank(CacheUtil.get(key))) {
            logger.info("任务正在执行中，请勿重复提交");
            result.put("msg","任务正在执行中，请勿重复提交");
            return result;
        }
        String schema = SchemaService.findSchemaByEntId(entId);
        EasyQuery query = QueryFactory.getQuery(entId);
        try {
            CacheUtil.put(key, "Y");
            int protect = 0;
            int num = 0;
            int success = 0;
            int count = this.getNeedDealDataCount(query, schema, startTime, endTime);
            StringBuffer sb = new StringBuffer();
            result.put("sum",count);
            while (ThreadMgr.isStart() && num <= count && protect < 1000) {
                protect ++;
                List<JSONObject> needDealData = this.getNeedDealData(query, schema,startTime,endTime);
                if (CollectionUtils.isNotEmpty(needDealData)) {
                    success += needDealData.stream().map(item -> startDutyFlow(entId, item.getString("M_ID")))
                            .filter(object -> {
                                if (Objects.nonNull(object) ) {
                                    if (object.getString("state").equals("1")) {
                                        return true;
                                    } else {
                                        sb.append(object.getString("msg") + "\n");
                                        return false;
                                    }
                                } else {
                                    return false;
                                }
                            }).count();
                    num = num + needDealData.size();
                } else {
                    break;
                }
            }
            result.put("execNum",success);
        } catch (Exception e) {
            logger.error("超时处理异常：" + e.getMessage(),e);
        }finally {
            CacheUtil.delete(key);
            //上报运行状态
            XtyModuleRuntimeDataCollector.report(Constants.APP_NAME, "RestartDutyService", "定责重新发起", 61 * 60);
        }
        logger.info("执行定责重新发起任务，结束时间：" + DateUtil.getCurrentDateStr());
        logger.info("执行定责重新发起任务，耗时：" + (System.currentTimeMillis() - start) / 1000 + "秒");
        return result;
    }

    /**
     * 获取需要处理的数据
     * @param query
     * @param schema
     * @return
     */
    private List<JSONObject> getNeedDealData (EasyQuery query, String schema,String startTime,String endTime) {
        try {
            EasySQL sql = new EasySQL();
            sql.append(" select distinct M_ID from ( ");
            /** 受理工单 **/
            sql.append(" select t1.M_ID from " + schema + ".c_box_appeal_order t1 ");
            sql.append(" left join " + schema + ".c_box_duty_order t2 on t2.P_ID = t1.M_ID ");
            sql.append("  where 1=1 ");
            sql.append(" and t1.CLASS_NAME1 is not null and t1.CLASS_NAME1 != '' ");
            sql.appendIn(new String[]{Constants.XTY_SERVICE_STATUS_04,Constants.XTY_SERVICE_STATUS_24,Constants.XTY_SERVICE_STATUS_05,Constants.XTY_SERVICE_STATUS_25}, " and t1.SERVICE_STATUS  ");
            sql.append(" and t2.ID is null ");
            sql.appendIn(new String[]{Constants.ENT_TYPE_TELECOM, Constants.ENT_TYPE_MOBILE, Constants.ENT_TYPE_UNICOM, Constants.ENT_TYPE_BROAD}, " and t1.ENT_TYPE  ");
            sql.append(" and not exists (select 1 from " + schema + ".C_CF_COMMON_TREE t3 ");
            sql.append(" where t3.TYPE = 'XTY_CLASS_CODE'");
            sql.append(" and t3.NAME in ( t1.CLASS_NAME1, t1.CLASS_NAME2, t1.CLASS_NAME3 ) ");
            sql.append(" and t3.EX1 = '01' )");
            sql.append(startTime, " and t1.APPEAL_TIME >= ? ");
            sql.append(endTime, " and t1.APPEAL_TIME <= ? ");
            sql.append(" union all ");
            /** 不予受理 **/
            sql.append(" select t1.M_ID from " + schema + ".c_box_appeal_order t1 ");
            sql.append(" left join " + schema + ".c_box_duty_order t2 on t2.P_ID = t1.M_ID ");
            sql.append(" left join " + schema + ".c_box_appeal_order_ex t3 on t3.M_ID = t1.M_ID ");
            sql.append("  where 1=1 ");
            sql.append(" and t1.CLASS_NAME1 is not null and t1.CLASS_NAME1 != '' ");
            sql.append(Constants.XTY_SERVICE_STATUS_12," and t1.SERVICE_STATUS != ? ");
            sql.append(Constants.ACCEPT_RESULT_02," and ((t3.F_ACCEPT_RESULT = ? and t1.IS_COMPLETED = '01') ");
            sql.append(Constants.APPEAL_RESULT_01," or t1.APPEAL_RESULT = ?) ");
            sql.appendIn(new String[]{Constants.ENT_TYPE_TELECOM, Constants.ENT_TYPE_MOBILE, Constants.ENT_TYPE_UNICOM, Constants.ENT_TYPE_BROAD}, " and t1.ENT_TYPE  ");
            sql.appendIn(Constants.getUnAcceptReason().split(","), " and t3.F_UNHANDLE_REASON ");
            sql.append(" and t2.ID is null ");
            sql.append(" and not exists (select 1 from " + schema + ".C_CF_COMMON_TREE t3 ");
            sql.append(" where t3.TYPE = 'XTY_CLASS_CODE'");
            sql.append(" and t3.NAME in ( t1.CLASS_NAME1, t1.CLASS_NAME2, t1.CLASS_NAME3 ) ");
            sql.append(" and t3.EX1 = '01' )");
            sql.append(startTime, " and t1.APPEAL_TIME >= ?");
            sql.append(endTime, " and t1.APPEAL_TIME <= ? ");
            sql.append(" union all ");
            /** 和解、未和解、已撤诉 **/
            sql.append(" select t1.M_ID from " + schema + ".c_box_appeal_order t1 ");
            sql.append(" left join " + schema + ".c_box_duty_order t2 on t2.P_ID = t1.M_ID ");
            sql.append("  where 1=1 ");
            sql.append(" and t1.CLASS_NAME1 is not null and t1.CLASS_NAME1 != '' ");
            sql.append(Constants.XTY_SERVICE_STATUS_12," and t1.SERVICE_STATUS != ? ");
            sql.appendIn(new String[]{Constants.APPEAL_RESULT_02,Constants.APPEAL_RESULT_03,Constants.APPEAL_RESULT_04}," and t1.APPEAL_RESULT ");
            sql.appendIn(new String[]{Constants.ENT_TYPE_TELECOM, Constants.ENT_TYPE_MOBILE, Constants.ENT_TYPE_UNICOM, Constants.ENT_TYPE_BROAD}, " and t1.ENT_TYPE  ");
            sql.append(" and t2.ID is null ");
            sql.append(" and not exists (select 1 from " + schema + ".C_CF_COMMON_TREE t3 ");
            sql.append(" where t3.TYPE = 'XTY_CLASS_CODE'");
            sql.append(" and t3.NAME in ( t1.CLASS_NAME1, t1.CLASS_NAME2, t1.CLASS_NAME3 ) ");
            sql.append(" and t3.EX1 = '01' )");
            sql.append(startTime, " and t1.APPEAL_TIME >= ?");
            sql.append(endTime, " and t1.APPEAL_TIME <= ? ");
            sql.append(" ) temp ");
            sql.append(" limit 0,1000 ");
            logger.info("需要发起定责的工单sql：" + sql.getFullSq());
            return query.queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
        }catch ( Exception e) {
            logger.error(e.getMessage(),e);
        }
        return null;
    }

    private int getNeedDealDataCount (EasyQuery query, String schema,String startTime,String endTime) {
        try {
            EasySQL sql = new EasySQL();
            /** 受理工单 **/
            sql.append(" select count(distinct M_ID) from ( ");
            sql.append(" select t1.M_ID from " + schema + ".c_box_appeal_order t1 ");
            sql.append(" left join " + schema + ".c_box_duty_order t2 on t2.P_ID = t1.M_ID ");
            sql.append(" where 1=1 ");
            sql.append(" and t1.CLASS_NAME1 is not null and t1.CLASS_NAME1 != '' ");
            sql.appendIn(new String[]{Constants.XTY_SERVICE_STATUS_04,Constants.XTY_SERVICE_STATUS_24,Constants.XTY_SERVICE_STATUS_05,Constants.XTY_SERVICE_STATUS_25}, " and t1.SERVICE_STATUS  ");
            sql.append(" and t2.ID is null ");
            sql.appendIn(new String[]{Constants.ENT_TYPE_TELECOM, Constants.ENT_TYPE_MOBILE, Constants.ENT_TYPE_UNICOM, Constants.ENT_TYPE_BROAD}, " and t1.ENT_TYPE  ");
            sql.append(" and not exists (select 1 from " + schema + ".C_CF_COMMON_TREE t3 ");
            sql.append(" where t3.TYPE = 'XTY_CLASS_CODE'");
            sql.append(" and t3.NAME in ( t1.CLASS_NAME1, t1.CLASS_NAME2, t1.CLASS_NAME3 ) ");
            sql.append(" and t3.EX1 = '01' )");
            sql.append(startTime, " and t1.APPEAL_TIME >= ? ");
            sql.append(endTime, " and t1.APPEAL_TIME <= ? ");
            sql.append(" union all ");
            /** 不予受理 **/
            sql.append(" select t1.M_ID from " + schema + ".c_box_appeal_order t1 ");
            sql.append(" left join " + schema + ".c_box_duty_order t2 on t2.P_ID = t1.M_ID ");
            sql.append(" left join " + schema + ".c_box_appeal_order_ex t3 on t3.M_ID = t1.M_ID ");
            sql.append("  where 1=1 ");
            sql.append(" and t1.CLASS_NAME1 is not null and t1.CLASS_NAME1 != '' ");
            sql.append(Constants.XTY_SERVICE_STATUS_12," and t1.SERVICE_STATUS != ? ");
            sql.append(Constants.ACCEPT_RESULT_02," and ((t3.F_ACCEPT_RESULT = ? and t1.IS_COMPLETED = '01') ");
            sql.append(Constants.APPEAL_RESULT_01," or t1.APPEAL_RESULT = ?) ");
            sql.appendIn(new String[]{Constants.ENT_TYPE_TELECOM, Constants.ENT_TYPE_MOBILE, Constants.ENT_TYPE_UNICOM, Constants.ENT_TYPE_BROAD}, " and t1.ENT_TYPE  ");
            sql.appendIn(Constants.getUnAcceptReason().split(","), " and t3.F_UNHANDLE_REASON ");
            sql.append(" and t2.ID is null ");
            sql.append(" and not exists (select 1 from " + schema + ".C_CF_COMMON_TREE t3 ");
            sql.append(" where t3.TYPE = 'XTY_CLASS_CODE'");
            sql.append(" and t3.NAME in ( t1.CLASS_NAME1, t1.CLASS_NAME2, t1.CLASS_NAME3 ) ");
            sql.append(" and t3.EX1 = '01' )");
            sql.append(startTime, " and t1.APPEAL_TIME >= ?");
            sql.append(endTime, " and t1.APPEAL_TIME <= ? ");
            sql.append(" union all ");
            /** 和解、未和解、已撤诉 **/
            sql.append(" select t1.M_ID from " + schema + ".c_box_appeal_order t1 ");
            sql.append(" left join " + schema + ".c_box_duty_order t2 on t2.P_ID = t1.M_ID ");
            sql.append("  where 1=1 ");
            sql.append(" and t1.CLASS_NAME1 is not null and t1.CLASS_NAME1 != '' ");
            sql.append(Constants.XTY_SERVICE_STATUS_12," and t1.SERVICE_STATUS != ? ");
            sql.appendIn(new String[]{Constants.APPEAL_RESULT_02,Constants.APPEAL_RESULT_03,Constants.APPEAL_RESULT_04}," and t1.APPEAL_RESULT ");
            sql.appendIn(new String[]{Constants.ENT_TYPE_TELECOM, Constants.ENT_TYPE_MOBILE, Constants.ENT_TYPE_UNICOM, Constants.ENT_TYPE_BROAD}, " and t1.ENT_TYPE  ");
            sql.append(" and t2.ID is null ");
            sql.append(" and not exists (select 1 from " + schema + ".C_CF_COMMON_TREE t3 ");
            sql.append(" where t3.TYPE = 'XTY_CLASS_CODE'");
            sql.append(" and t3.NAME in ( t1.CLASS_NAME1, t1.CLASS_NAME2, t1.CLASS_NAME3 ) ");
            sql.append(" and t3.EX1 = '01' )");
            sql.append(startTime, " and t1.APPEAL_TIME >= ?");
            sql.append(endTime, " and t1.APPEAL_TIME <= ? ");
            sql.append(" ) temp");
            logger.info("需要发起定责的工单数量sql：" + sql.getFullSq());
            return query.queryForInt(sql.getSQL(),sql.getParams());
        }catch ( Exception e) {
            logger.error(e.getMessage(),e);
        }
        return 0;
    }

    private JSONObject startDutyFlow (String entId,String orderId) {
        try {
            long l = System.currentTimeMillis();
            if (Constants.getStartDuty().equals(Constants.MAGIC_01)) {
                JSONObject param = new JSONObject();
                param.put("schema", SchemaService.findSchemaByEntId(entId));
                param.put("entId",entId);
                param.put("busiOrderId",SchemaService.getCcBusiOrderIdByEntId(entId));
                param.put("orderId",orderId);
                param.put("command","addOrder");
                logger.info("发起定责流程：" + JSONObject.toJSONString(param));
                JSONObject jsonObject = ServiceUtil.invoke2("FIX_DUTY_SERVICE", param);
                logger.info("发起定责流程返回结果：" + JSONObject.toJSONString(jsonObject) + "，耗时："+(System.currentTimeMillis()-l)+"毫秒");
                return jsonObject;
            }
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        } finally {
            EventDispatcher.appendAddDocEvent(orderId, SchemaService.findSchemaByEntId(entId), "RstartDutyService.startDutyFlow");
        }
        return null;
    }

    public void startEvent() {
        new Thread(new RestartDutyThread()).start();
    }

    public class RestartDutyThread extends Thread {
        @Override
        public void run() {
            CommonUtil.sleep(3 * 60);
            if (!ThreadManager.getInstance().runJob()) {
                logger.info("当前机器不启用定责文件扫描");
                return;
            }
            while (ThreadMgr.isStart()) {
                String startTime = DateUtil.addDay(DateUtil.TIME_FORMAT,DateUtil.getCurrentDateStr(),-7);
                String endTime = DateUtil.getCurrentDateStr();
                String entId = Constants.getEntId();
                invoke(startTime,endTime,entId);
                CommonUtil.sleep(60 * 60);
            }
        }
    }


}
