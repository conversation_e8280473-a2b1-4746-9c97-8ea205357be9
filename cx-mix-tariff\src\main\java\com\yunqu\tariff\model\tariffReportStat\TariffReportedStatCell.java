package com.yunqu.tariff.model.tariffReportStat;

import lombok.Data;

@Data
public class TariffReportedStatCell {

    /**
     * ES id
     */
    private String id;

    /**
     * 资费编码
     */
    private String tariffNo;

    /**
     * 省份编码 (注意：字段名 entCode 可能指运营商编码，与注释 "省份编码" 不符)
     */
    private String entCode;

    /**
     * 订购编码
     */
    private String busiOrderId;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 区域编码
     */
    private String[] areaCodes;

    /**
     * 报送主体
     */
    private String reporter;

    /**
     * 一级分类
     */
    private String type1;

    /**
     * 二级分类
     */
    private String type2;

    /**
     * 是否公示
     */
    private String isPublic;

    /**
     * 资费状态
     */
    private String status;

    /**
     * 是否通信类
     */
    private String isTelecom;

    /**
     * 版本号
     */
    private String[] versionNos;

    /**
     * 1: 资费名称重复
     */
    private int tariffNameDuplication = 0;

    /**
     * 2: 资费标准缺失
     */
    private int tariffStandardMissing = 0;

    /**
     * 3: 服务内容缺失
     */
    private int serviceContentMissing = 0;

    /**
     * 4: 适用范围填写不规范
     */
    private int scopeInconsistent = 0;

    /**
     * 5: 有效期限填写不规范
     */
    private int validityInconsistent = 0;

    /**
     * 6: 销售渠道填写不规范
     */
    private int salesChannelInconsistent = 0;

    /**
     * 7: 免费业务附加在网期限
     */
    private int freeServiceTerm = 0;

    /**
     * 8: 退订方式填写不规范
     */
    private int unsubscribeInconsistent = 0;

    /**
     * 9: 违约责任填写不规范
     */
    private int liabilityInconsistent = 0;

    /**
     * 10: 下线时间填写不规范
     */
    private int offlineTimeInconsistent = 0;

    /**
     * 11: 资费方案内容冗长
     */
    private int contentVerbose = 0;

    /**
     * 12: 资费方案内容重复或矛盾
     */
    private int contentDuplicateOrConflict = 0;

    /**
     * 13: 流量表述不规范
     */
    private int dataQuotaInconsistent = 0; // "流量" -> "数据额度/配额"

    /**
     * 14: 加装包附加限制性条件
     */
    private int addonPackageRestriction = 0;

    /**
     * 15: 营销活动附加限制性条件
     */
    private int marketingCampaignRestriction = 0;

    /**
     * 16: 套餐附加限制性条件
     */
    private int packageRestriction = 0;
}