package com.yunqu.cc.expand.util.dataOld;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.ServiceUtil;
import com.yunqu.cc.expand.base.CommonLogger;
import com.yunqu.cc.expand.base.Constants;
import com.yunqu.cc.expand.base.QueryFactory;
import com.yunqu.cc.expand.util.DbFuncUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 根据CREATE_TIME进行老化的策略
 */
public class CreateTimeDataOldService implements DataOldService {

    private  Logger logger = CommonLogger.getDataOldLogger();

    private EasyQuery query;
    private String tableName;
    private String hisTableName;
    /**
     * 原始表保留的数据天数，只迁移之前的数据
     */
    private int dataOldDays;
    private JSONObject offlineExportParams;

    private String schemaName;

    private String filterName = "CREATE_TIME";

    /**
     * 本次老化的最大时长，超过该时长后，不再处理；先退出，下次再执行
     */
    private int execMaxMins = 30;

    public CreateTimeDataOldService(EasyQuery query, Logger logger,String tableName, String hisTableName, int dataOldDays, JSONObject offlineExportParams){
        if(query==null){
            this.query = QueryFactory.getWriteQuery();
        }else{
            this.query = query;
        }
        if(logger!=null){
            this.logger = logger;
        }
        this.tableName = tableName;
        this.hisTableName = hisTableName;
        this.dataOldDays = dataOldDays;
        this.offlineExportParams = offlineExportParams;
    }

    public void setSchemaName(String schemaName){
        this.schemaName = schemaName;
    }



    /**
     * 根据过滤字段定时将数据进行老化，并将老化数据通过离线导出进行备份，最后清理历史表数据
     */
    public void execute() {
        // 参数校验
        if (StringUtils.isBlank(tableName)) {
            logger.error("老化数据时，参数为空，tableName:" + tableName);
            return;
        }

        if (dataOldDays == 0) {
            logger.info("按配置不进行数据老化,tableName = " + tableName);
            return;
        }

        JSONArray entInfoArray = SchemaService.findEntBusiSchema();
        List<JSONObject> entSchemaInfos = entInfoArray.toJavaList(JSONObject.class);
        for (JSONObject entSchemaInfo : entSchemaInfos) {
            String entId = entSchemaInfo.getString("ENT_ID");
            String schemaName = entSchemaInfo.getString("SCHEMA_NAME");
            String busiOrderId = entSchemaInfo.getString("BUSI_ORDER_ID");

            // 处理数据迁移和备份
            this.handleOldData(schemaName, entId, busiOrderId);
        }
    }

    /**
     * 处理旧数据，将其迁移到历史表并选择发起离线导出备份
     * 此方法主要用于数据老化处理，将超过指定天数的旧数据从当前表迁移到历史表
     * 选择备份，查询T2天前的历史表数据通过离线导出进行备份清理
     * 不选择备份，直接清理T2天前的历史表数据
     *
     * @param schemaName          数据库模式名
     * @param entId               企业ID，用于日志信息中
     * @param busiOrderId         业务订单ID，用于离线导出时
     */
    private void handleOldData( String schemaName, String entId, String busiOrderId) {
        logger.info("------------["+DateUtil.getCurrentDateStr()+"["+tableName+"]----------------- ");
        logger.info("企业[" + entId + "]开始处理" + tableName + "老化数据，参数：dataOldDays = " + dataOldDays + ", offlineExportParams = " + offlineExportParams);

        try {

            List<String> columns = DbFuncUtils.getAllColumns(schemaName, tableName);
            if (columns == null) {
                logger.error("企业id：" + entId + ",处理" + tableName + "老化失败：无法获取到该表的字段名");
                return;
            }

            // 迁移原始表数据到历史表
            this.migrateOriginalTable( schemaName, entId, columns);

            // 迁移完毕后，清理历史表
            if (StringUtils.isNotBlank(hisTableName)) {
                this.clearHisTableAndBackup( schemaName, entId, busiOrderId);
            }

        } catch (Exception e) {
            logger.error("企业id：" + entId + ",处理" + tableName + "老化失败：" + e.getMessage(), e);
        }

        logger.info("企业[" + entId + "]完成处理" + tableName + "老化数据，参数：dataOldDays = " + dataOldDays + ", offlineExportParams = " + offlineExportParams);

    }

    /**
     * 清理历史表并备份
     * 该方法用于清理指定表的历史数据，并可选择性地进行数据备份
     * 如果指定了离线导出参数，则会将历史数据按天备份后删除
     * 否则，直接删除历史数据
     *
     * @param schemaName          业务库
     * @param entId               企业ID
     * @param busiOrderId         业务订购ID
     * @throws Exception 可能抛出的异常
     */
    private void clearHisTableAndBackup(String schemaName, String entId, String busiOrderId) throws Exception {

        int dateOldDays = Constants.getDaysHistoricalLogs();
        if (dateOldDays <= 0) {
            logger.error("hisTableName[" + hisTableName + "]获取历史数据保留天数为0，不进行历史数据清理");
            return;
        }

        // 查询历史表中最小的一天和清理的截止日期
        String beginTime = query.queryForString("SELECT MIN("+filterName+") FROM " + schemaName + "." + hisTableName);
        String endTime = this.getClearHisMaxDate();

        if (StringUtils.isAnyBlank(beginTime,endTime)) {
            logger.error("企业id：" + entId + ",hisTableName = " + tableName + " 没有配置历史表清理天数，不处理");
            return;
        }

        if(DateUtil.compareDate(beginTime,endTime,DateUtil.TIME_FORMAT) >0 ){
            logger.error("企业id：" + entId + ",hisTableName = " + tableName + " 历史表的数据，还未到清理时间，本次不处理");
            return;
        }


        final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime clearBeginDate = LocalDateTime.parse(beginTime, formatter);
        LocalDateTime clearEndDate = LocalDateTime.parse(endTime, formatter);

        // 迁移完毕后，发起离线导出备份历史表，并删除T2天之前的历史表数据
        if (offlineExportParams != null) {

            // 离线导出按天进行导出，一天的数据备份一次，防止可能有累计的情况导致数据过大，每次只处理前五天
            for (int i = 0; i < 5 && !clearBeginDate.isAfter(clearEndDate); i++) {
                String dayStart = clearBeginDate.format(formatter).substring(0, 10) + " 00:00:00";
                String dayEnd = dayStart.substring(0, 10) + " 23:59:59";

                // 调用导出方法
                this.startExp(hisTableName, dayStart, dayEnd, entId, busiOrderId, offlineExportParams);
                clearBeginDate = clearBeginDate.plusDays(1); // 加一天
            }

            return;
        }

        // 不选择备份，直接清除T2天之前的历史表数据
        this.deleteHisTable(schemaName,beginTime,endTime);
    }

    /**
     * 迁移原始表中的历史数据到历史表中，并删除原始表中的旧数据
     *
     * @param schemaName   数据库schema名
     * @param entId        企业ID，用于日志信息中
     * @param columns      需要迁移的列名列表
     */
    private void migrateOriginalTable( String schemaName, String entId, List<String> columns) {
        // 获取数据源
        try {
            // 记录开始时间
            long startTime = System.currentTimeMillis();

            // 获取dataOldDays天之前的日期，开始迁移数据
            String endTime = DateUtil.addDay(DateUtil.TIME_FORMAT,DateUtil.getCurrentDateStr(),-dataOldDays);

            // 获取数据库中最早的时间，将时分秒置0
            String beginDate = query.queryForString(" SELECT MIN("+filterName+") FROM " + schemaName + "." + tableName);

            if (StringUtils.isBlank(beginDate)) {
                logger.info("企业id：" + entId + ",tableName = " + tableName + " 没有数据，不进行数据老化操作");
                return;
            }

            logger.info("企业id：" + entId + ",tableName = " + tableName + " 开始清理数据, 库里最早时间: " + beginDate +" ,本次老化截止时间: "+endTime);

            beginDate = beginDate.substring(0, 10) + " 00:00:00";
            if(DateUtil.compareDate(beginDate,endTime,DateUtil.TIME_FORMAT) >0){
                logger.info("企业id：" + entId + ",tableName = " + tableName + " 数据还未到老化时间(只老化"+dataOldDays+"天的数据)，不进行数据老化操作");
                return;
            }

            //逐天处理
            String endDate = DateUtil.addDay(DateUtil.TIME_FORMAT,beginDate,1);

            // 最多执行1小时，这里一般只有一天的数据需要迁移，原表中保留的数据不超过T1天
            while (System.currentTimeMillis() - startTime < execMaxMins * 60 * 1000) {
                logger.info("企业id：" + entId + ",tableName = " + tableName + " 处理日期：" + beginDate +" - "+endDate);

                // 如果当前处理的日期小于T1天，终止操作
                if(DateUtil.compareDate(endDate,endTime,DateUtil.TIME_FORMAT) >0){
                    logger.info("企业id：" + entId + ",tableName = " + tableName + " 老化时间["+endDate+"]，超过允许老化的时间["+endTime+"]，不再执行.");
                    return;
                }

                // 历史表不为空，迁移数据
                if (StringUtils.isNotBlank(hisTableName)) {
                    // 迁移数据，防止当天数据过多，每次迁移3000条
                    int limit = 3000;
                    int total = query.queryForInt("SELECT COUNT(1) FROM " + schemaName + "." + tableName + " WHERE "+filterName+" >= ? AND "+filterName+" <= ?", beginDate, endDate);

                    logger.info("企业id：" + entId + ",tableName = " + tableName + " 需要迁移到历史表的数据量:"+total+" , " + beginDate +" - "+endDate);

                    int insertRow = 0;
                    if(total>0){
                        for (int page = 0; page < (total + limit - 1) / limit; page++) {
                            logger.info("企业id：" + entId + ",tableName = " + tableName + ",hisTableName = " + hisTableName + " 开始迁移日期：" + beginDate + ", 当前进度：" + (page + 1) + "/" + (total + limit - 1) / limit);

                            EasySQL moveToHisSql = DbFuncUtils.createMoveToHisSql(schemaName, tableName, hisTableName, columns);
                            moveToHisSql.append(beginDate, "WHERE "+filterName+" >= ?");
                            moveToHisSql.append(endDate, "AND "+filterName+" <= ?");
                            moveToHisSql.append(limit, "LIMIT ?");
                            moveToHisSql.append(page * limit, "OFFSET ?");
                            insertRow =  insertRow + query.executeUpdate(moveToHisSql.getSQL(), moveToHisSql.getParams());
                        }
                    }

                    logger.info("企业id：" + entId + ",tableName = " + tableName + " 完成迁移, 迁移到历史表的数据量:"+insertRow+" , " + beginDate +" - "+endDate);


                }

                // 删除日期+1天前的原始表数据（已迁移）
                this.deleteTableInBatchesByDdl(schemaName,tableName,  beginDate,endDate);

                // 日期+1
                beginDate = endDate;
                endDate = DateUtil.addDay(DateUtil.TIME_FORMAT,beginDate,1);

            }

        } catch (Exception e) {
            logger.error("企业id：" + entId + ",处理" + tableName + "老化失败：" + e.getMessage(), e);
        }

    }

    /**
     * 获取历史表清理的最大日期
     */
    private String getClearHisMaxDate() {
        // todo 可根据hisTableName获取不同的配置项
        int dateOldDays = Constants.getDaysHistoricalLogs();
        if (dateOldDays == 0) {
            logger.error("hisTableName[" + hisTableName + "]获取历史数据保留天数为0，不进行历史数据清理");
            return null;
        }
        return DateUtil.addDay(DateUtil.TIME_FORMAT,DateUtil.getCurrentDateStr(),-dateOldDays);
    }

    /**
     *
     */
    private void deleteHisTable( String schemaName,String beginTime,String endTime) {
        String retentionStartDateStr = this.getClearHisMaxDate();
        logger.info("开始清除[" + beginTime + "]-["+endTime+"]的历史表[" + hisTableName + "]数据...");

        try {
            String endDate = DateUtil.addDay(beginTime,DateUtil.TIME_FORMAT,1);
            while (DateUtil.compareDate(endDate,endTime,DateUtil.TIME_FORMAT) <0){
                this.deleteTableInBatchesByDdl(schemaName,hisTableName,  beginTime,endDate);
                beginTime = endDate;
                endDate = DateUtil.addDay(DateUtil.TIME_FORMAT,beginTime,1  );
            }

        } catch (Exception e) {
            logger.error("清除[" + retentionStartDateStr + "]天之前的历史表[" + hisTableName + "]数据失败：" + e.getMessage(), e);
        }
    }

    /**
     * 根据截止日期分批删除表数据
     *
     * @param tableName          要删除的表名
     * @param schemaName            数据库名
     * @param beginDate 截至日期
     */
    private void deleteTableInBatchesByDdl( String schemaName,String tableName, String beginDate,String endDate) throws Exception {
        logger.info("开始删除表["+schemaName+"][" + tableName + "]数据，删除日期：" + beginDate+" - "+endDate);
        EasySQL sql = new EasySQL(" SELECT COUNT(1) FROM " + schemaName + "." + tableName + " WHERE 1=1");
        sql.append(beginDate, "AND "+filterName+" >= ? ");
        sql.append(endDate, "AND "+filterName+" <= ? ");
        int row = query.queryForInt(sql.getSQL(), sql.getParams());
        if(row==0){
            logger.info("表["+schemaName+"][" + tableName + "]没有数据，无需删除,删除日期：" + beginDate+" - "+endDate);
        }

        //分多次删除
        deleteTableDataBatch(schemaName,tableName,beginDate,endDate,row);

        logger.info("表["+schemaName+"][" + tableName + "]数据清除结束: "+beginDate+" ~ "+endDate);

    }

    /**
     * 分批删除数据，每次删除3000
     * @param schemaName
     * @param tableName
     * @param beginDate
     * @param endDate
     * @param row
     */
    private void deleteTableDataBatch(String schemaName, String tableName, String beginDate, String endDate,int row) {
        try{
            if(StringUtils.isAnyBlank(schemaName,tableName,beginDate,endDate)){
                return;
            }
            if(row<=0){
                return;
            }
            int size = 3000;
            int page = row / size + 1;
            if(page<=0){
                page = 1;
            }
            logger.info("正式删除表["+schemaName+"]["+tableName+"]数据, beginDate = "+beginDate+",endDate="+endDate+",总条数:"+row+",批次:"+page);
            for(int i = 0;i<page;i++){
                EasySQL sql = new EasySQL(" DELETE FROM " + schemaName + "." + tableName + " WHERE 1=1");
                sql.append(beginDate, "AND "+filterName+" >= ? ");
                sql.append(endDate, "AND "+filterName+" <= ? ");
                sql.append(" ORDER BY "+filterName+" ASC LIMIT  "+ size);
                query.execute(sql.getSQL(),sql.getParams());
            }
        } catch (Exception e) {
            logger.info("正式删除表["+schemaName+"]["+tableName+"]数据, beginDate = "+beginDate+",endDate="+endDate+",出现异常:"+e.getMessage(),e);
        }


    }

    /**
     * 调用离线导出服务
     * 该方法负责组装参数并调用导出服务，主要用于操作日志的备份
     *
     * @param tableName           数据表名，用于指定需要备份的数据表
     * @param beginDate           备份的开始日期，用于限定备份数据的时间范围
     * @param endDate             备份的结束日期，与开始日期共同限定备份数据的时间范围
     * @param entId               企业标识，用于区分不同企业的数据
     * @param busiOrderId         业务订单ID，用于跟踪和记录备份操作相关的业务订单
     * @param offlineExportParams 离线导出服务需要的参数
     */
    private void startExp(String tableName, String beginDate, String endDate, String entId, String busiOrderId, JSONObject offlineExportParams) throws Exception {
        // 调用服务进行离线导出，在离线导出调的服务中进行备份删除
        JSONObject expParam = new JSONObject();
        expParam.put("tableName", tableName);
        expParam.put("beginDate", beginDate);
        expParam.put("endDate", endDate);

        JSONObject param = new JSONObject();
        param.put("reportName", beginDate.substring(0, 10) + "_backup.xlsx");
        param.put("expCommand", offlineExportParams.getString("expCommand"));
        param.put("expServiceId", offlineExportParams.getString("expServiceId"));
        param.put("expParam", expParam);

        param.put("serviceId", "CC_EXP_SERVICE");
        param.put("command", "addExpApply");
        param.put("userAcc", "system");
        param.put("userName", "system");
        param.put("userDeptCode", "system");
        param.put("module", Constants.APP_NAME);
        param.put("applyReason", offlineExportParams.getString("applyReason"));
        param.put("entId", entId);
        param.put("busiOrderId", busiOrderId);

        logger.info("离线导出申请服务入参：" + param.toJSONString());
        JSONObject invoke = ServiceUtil.invoke2("CC_EXP_SERVICE", param);
        logger.info("离线导出申请服务出参：" + invoke);
    }
}