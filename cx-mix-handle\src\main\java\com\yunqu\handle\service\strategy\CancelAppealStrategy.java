package com.yunqu.handle.service.strategy;

import java.sql.SQLException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.ServiceUtil;
import com.yunqu.handle.base.Constants;
import com.yunqu.handle.base.QueryFactory;


public class CancelAppealStrategy implements OutInterfaceStrategy{

    @Override
    public JSONObject execute(JSONObject params, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
                String id = params.getString("id");
                String procInstId = getProcInstId(id);
                boolean isCancel = true;
                try {
                    if (StringUtils.isBlank(id)) {
                        return createResult(false,"id不能为空", new JSONObject(){{put("orderId", id);}});
                    }
                    JSONObject appeal = getAppeal(id);
                    if (appeal == null) {
                        return createResult(false,"申诉单不存在", new JSONObject(){{put("orderId", id);}});
                    }
                    long startTime = System.currentTimeMillis();
                    JSONObject param = new JSONObject();
                    param.put("procInstId", procInstId);
                    param.put("orderId", id);
                    param.put("orderNo", appeal.getString("ORDER_NO"));
                    param.put("entId", Constants.getEntId());
                    param.put("busiOrderId", Constants.getBusiOrderId());
                    param.put("schema", Constants.getSysSchema());
                    param.put("command", "endProcess");
                    param.put("title", "用户撤诉");
                    param.put("content", "用户撤诉");
                    param.put("userAcc","system");
                    param.put("userName","系统");
                    param.put("reason", "用户自行撤诉，工单关闭。");
                    param.put("flowVar", new JSONObject() {{put("COMPLETED", "01");}});
                    logger.info("-->[关闭工单请求] param:"+param.toJSONString());
                    JSONObject result = new JSONObject();
                    try {
                        result = ServiceUtil.invoke2("CC_EORDER_OPERATION_HANDLE", param);
                        logger.info("<--[关闭工单返回内容] result:"+result.toJSONString());
                        if (result != null && !result.getString("state").equals("1")) {
                            isCancel = false;
                        }
                    } catch (Exception e) {
                        isCancel = false;
                        logger.error("关闭工单失败: " + e.getMessage(), e);
                    }
                    JSONObject resultData = result.getJSONObject("data");
                    
                    JSONObject data = new JSONObject();
                    data.put("flowKey", "xty_ss");
                    data.put("flowInstanceId", procInstId);
                    data.put("orderId", id);
                    data.put("lastTaskId", resultData != null ?resultData.getString("taskId"):"");
                    data.put("entId", Constants.getEntId());
                    data.put("busiOrderId", Constants.getBusiOrderId());
                    data.put("assignee", "");
                    data.put("schema", Constants.getSysSchema());
                    logger.info("-->[关闭工单请求EXTEND] data:"+data.toJSONString());
                    JSONObject extend = ServiceUtil.invoke2("ORDER_EXTEND_TODO_SERVICE", data);
                    logger.info("<--[关闭工单请求EXTEND返回内容] result:"+extend.toJSONString());

                    logger.info("修改申诉单业务状态 orderId:" + id + ",serviceStatus=06");
                    // 修改申诉单业务状态，若为办结操作，不在此更新，后续一并更新
                    EasySQL sql = new EasySQL();
                    sql.append("update " + Constants.getSysSchema() + ".C_BOX_APPEAL_ORDER ");
                    sql.append("06", " set SERVICE_STATUS = ? ", false);
                    sql.append("01", " ,IS_COMPLETED = ? ", false);
                    sql.append(DateUtil.getCurrentDateStr(), " ,COMPLETE_TIME = ? ", false);
                    sql.append("04", " ,APPEAL_RESULT = ? ");
                    sql.append(id, " where M_ID = ? ", false);
                    getQuery().execute(sql.getSQL(), sql.getParams());
                    long endTime = System.currentTimeMillis();
                    logger.info("工单编号：" + appeal.getString("ORDER_NO") + ",耗时:" + (endTime - startTime));
                    if (!isCancel && !existEndProcess(id, Constants.getSysSchema())) {
                         addFlow(Constants.getSysSchema(), id, "6", "用户撤诉", "用户自行撤诉，工单关闭。");
                    }
                    ServiceUtil.invoke2("XTY_ORDER_SYN_ES_SERVICE", new JSONObject() {{put("orderId", id);put("schema", Constants.getSysSchema());}});
                    return createResult(true, "申诉撤销成功", new JSONObject(){{put("orderId", id);}});
                } catch (Exception e) {
                    String message = e.getMessage();
                    logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + message, e);
                    // execution  doesn't exist
                    if (StringUtils.isNotBlank(message) && message.contains("execution  doesn't exist")) {
                        logger.info("工单{}流程已不存在！！！！",id);
                        return createResult(true, "申诉撤销成功", new JSONObject(){{put("orderId", id);}});

                    }
                }
                return createResult(false, "申诉撤销失败", null);
    }

    private boolean existEndProcess (String id,String schema) {
       try {
            EasySQL sql = new EasySQL();
            sql.append(" select count(1)  from " + schema + ".c_bo_order_follow ");
            sql.append(id," where ORDER_ID = ? ");
            sql.append("用户撤诉" ," and TASK_NAME =? ");
            return QueryFactory.getQuery().queryForExist(sql.getSQL(), sql.getParams());
       } catch (Exception e) {
            logger.error("查询工单是否已办结失败: " + e.getMessage(), e);
       }
       return false;
    }

    @Override
    public void confirm(JSONObject params, HttpServletRequest request, HttpServletResponse response) throws Exception {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'confirm'");
    }


   

    /**
	 * 获取流程实例ID
	 * @return
	 * @throws SQLException
	 */
	public String getProcInstId(String id) throws SQLException {
		return QueryFactory.getWriteQuery().queryForString("select PROC_INST_ID from " + Constants.getSysSchema() + ".C_BO_BASE_ORDER where ID = ?", id);
	}

    private JSONObject getAppeal (String id) throws SQLException {
        EasySQL sql = new EasySQL();
        sql.append("select t1.*,t2.ORDER_NO from " + getTableName("c_box_appeal_order t1"));
        sql.append(" left join " + getTableName("c_bo_base_order t2"));
        sql.append(" on t1.M_ID = t2.ID");
        sql.append(" where 1=1 ");
        sql.append(id," and M_ID =? ",false);
        return getQuery().queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
    }
    
     /**
     * 添加跟进记录
     *
     * @param schema
     * @param orderId
     * @param type    跟进类型 0-创建工单 1-办理工单 2-转派工单 3-撤回工单 4-退回工单 5-签收工单 6-其他 7-暂存 8-取消签收工单 9-挂起 10-取消挂起 11-异常暂存 12-到达 13-分派 14-自动回访 15-工单归档 16-用户催办 17-用户补充信息 18-用户撤单 19-用户评价 20-自动分派 21-自动回收
     * @param content
     * @throws ServiceException
     */
    private void addFlow(String schema, String orderId, String type, String taskName, String content) {
        try {
            JSONObject param = new JSONObject();
            param.put("command", "addOrderFollow");
            param.put("entId", Constants.getEntId());
            param.put("busiOrderId", Constants.getBusiOrderId());
            param.put("schema", schema);
            param.put("taskName", taskName);
            param.put("userAcc", "system");
            param.put("userName", "系统");
            param.put("type", type);
            param.put("content", content);
            param.put("orderId", orderId);
            param.put("deptCode",  "system");
            logger.info("-->[生成跟进记录请求] param:" + param.toJSONString());
            ServiceUtil.invoke2("CC_EORDER_OPERATION_HANDLE", param);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }
}
