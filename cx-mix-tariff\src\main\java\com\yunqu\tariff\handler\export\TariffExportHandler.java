package com.yunqu.tariff.handler.export;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.handler.SearchHandler;
import com.yunqu.tariff.handler.SearchHandlerFactory;
import com.yunqu.tariff.inf.RegInitDataService;
import com.yunqu.tariff.utils.ExcelUtil;
import com.yunqu.tariff.utils.excel.CellStyleUtil;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.utils.calendar.EasyDate;

import java.io.File;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * TariffExportHandler
 *
 * @ClassName TariffExportHandler
 * <AUTHOR> Copy This Tag)
 * @Since create in 2025/1/16 15:22
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
public class TariffExportHandler extends BaseExportHandler {


    @Override
    protected String excuete(JSONObject taskObject) {
        try {
            logger.info("开始执行导出任务:{}", taskObject);
            JSONObject param = taskObject.getJSONObject("PARAMS");
            String taskCode = taskObject.getString("TASK_CODE");
            return excuetExport(param, taskObject.getString("FILE_NAME"), taskCode);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    protected String getFileName(String userAcct) {
        return "";
    }


    // Excel表头定义
    private static final List<String> EXCEL_HEADERS = new ArrayList<String>() {{
        add("资费名称");
        add("方案编号");
        add("资费类别");
        add("一级分类");
        add("订购占比");
        add("订购省份");
        add("运营商");
        add("订单量");
        add("地市数量");
        add("涉及地市及订单量");
        add("首次出现日期");
        add("最后出现日期");
    }};

    // Excel字段定义
    private static final List<String> EXCEL_FIELDS = new ArrayList<String>() {{
        add("tariffName");
        add("reportNo");
        add("tariffType");
        add("type1");
        add("allOrderCount");
        add("provinceName");
        add("entName");
        add("orderCount");
        add("cityCount");
        add("citiesAndOrders");
        add("firstAppearance");
        add("lastAppearance");
    }};

    // 未公示资费Excel表头定义
    private static final List<String> UNPUBLIC_EXCEL_HEADERS = new ArrayList<String>() {{
        add("资费名称");
        add("方案编号");
        add("订购省份");
        add("运营商");
        add("首次出现日期");
        add("最后出现日期");
    }};

    // 未公示资费Excel字段定义
    private static final List<String> UNPUBLIC_EXCEL_FIELDS = new ArrayList<String>() {{
        add("tariffName");
        add("reportNo");
        add("provinceName");
        add("entName");
        add("firstAppearance");
        add("lastAppearance");
    }};

    /**
     * 导出资费报表的主要方法
     *
     * @throws Exception 如果导出过程中发生异常
     */
    public String excuetExport(JSONObject param, String fileName) throws Exception {
        return excuetExport(param, fileName, null);
    }

    /**
     * 导出资费报表的主要方法
     *
     * @param param 导出参数
     * @param fileName 文件名
     * @param taskCode 任务代码，用于区分不同类型的导出
     * @throws Exception 如果导出过程中发生异常
     */
    public String excuetExport(JSONObject param, String fileName, String taskCode) throws Exception {
        long startTime = System.currentTimeMillis();
        logger.info("开始导出资费报表... param:{},taskCode={}",param, taskCode);

        String currentDateString = EasyDate.getCurrentDateString("yyyyMMdd");
        String year = currentDateString.substring(0, 4);
        String month = currentDateString.substring(4, 6);
        String day = currentDateString.substring(6, 8);
        String filePath = Constants.FILE_BASE_PATH + File.separator + year + File.separator + month + File.separator + day;
        File folder = new File(filePath);
        if (!folder.exists()) {
            folder.mkdirs();
        }
        String fileAbsPath = filePath + File.separator + fileName;
        File file = new File(fileAbsPath);

        try (OutputStream os = Files.newOutputStream(file.toPath())) {
            // 根据不同的任务类型执行不同的导出逻辑
            if (StringUtils.equals("TariffUnpublicServlet", taskCode)) {
                // 未公示资费导出逻辑
                exportUnpublicData(param, os);
            } else {
                // 常规的活跃和非活跃资费导出逻辑
                ExcelWriter excelWriter = null;
                try {
                    excelWriter = EasyExcel.write(os)
                            .head(ExcelUtil.formatHeader(EXCEL_HEADERS, "用户订单"))
                            .registerWriteHandler(CellStyleUtil.getHorizontalCellStyleStrategy())
                            .build();

                    WriteSheet activeSheet = EasyExcel.writerSheet("活跃").build();
                    WriteSheet inactiveSheet = EasyExcel.writerSheet("非活跃").build();

                    // 流式查询并写入活跃数据
                    exportDataByStatus(param, excelWriter, activeSheet, true);

                    // 流式查询并写入非活跃数据
                    exportDataByStatus(param, excelWriter, inactiveSheet, false);
                } finally {
                    // 确保ExcelWriter正确关闭
                    if (excelWriter != null) {
                        excelWriter.finish();
                    }
                }
            }

            logger.info("资费报表导出完成,耗时:{}ms", System.currentTimeMillis() - startTime);
            return fileAbsPath;
        } catch (Exception e) {
            logger.error("导出资费报表异常", e);
            throw new Exception("导出失败:" + e.getMessage());
        }
    }

    /**
     * 导出未公示资费数据
     */
    private void exportUnpublicData(JSONObject param, OutputStream os) throws Exception {
        ExcelWriter excelWriter = null;
        try {
            excelWriter = EasyExcel.write(os)
                    .head(ExcelUtil.formatHeader(UNPUBLIC_EXCEL_HEADERS, "未公示资费"))
                    .registerWriteHandler(CellStyleUtil.getHorizontalCellStyleStrategy())
                    .build();

            WriteSheet sheet = EasyExcel.writerSheet("未公示资费").build();

            int pageIndex = 1;
            int total = 0;

            while (true) {
                param.put("pageIndex", String.valueOf(pageIndex));
                
                SearchHandler<JSONObject> searchHandler = SearchHandlerFactory.getTariffUnpublicSearchHandler(param);
                JSONObject result = searchHandler.search();
                
                List<Map<String, String>> records = result.getObject("data",
                        new TypeReference<List<Map<String, String>>>() {
                        });

                if (records == null || records.isEmpty()) {
                    break;
                }

                // 转换并写入数据
                List<List<Object>> rows = convertToUnpublicExcelRows(records);
                excelWriter.write(rows, sheet);

                total += records.size();
                pageIndex++;

                // 每1000条记录输出一次日志
                if (total % 1000 == 0) {
                    logger.info("已处理{}条未公示资费记录", total);
                }
            }

            logger.info("未公示资费数据导出完成,共{}条记录", total);
        } finally {
            // 确保ExcelWriter正确关闭
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    /**
     * 转换未公示资费数据为Excel行格式
     */
    private List<List<Object>> convertToUnpublicExcelRows(List<Map<String, String>> records) {
        List<List<Object>> rows = new ArrayList<>(records.size());
        
        for (Map<String, String> record : records) {
            List<Object> row = new ArrayList<>();
            // 按字段顺序添加数据
            row.add(record.get("tariffName")); // 资费名称
            row.add(record.get("reportNo"));   // 方案编号
            row.add(record.get("provinceName")); // 订购省份
            row.add(record.get("entName"));    // 运营商
            row.add(record.get("firstAppearance")); // 首次出现日期
            row.add(record.get("lastAppearance")); // 最后出现日期
            
            rows.add(row);
        }
        
        return rows;
    }

    /**
     * 获取数据字典
     */
    private JSONArray getDataDict() throws ServiceException {
        RegInitDataService service = new RegInitDataService();
        JSONObject result = service.invokeMethod(new JSONObject());
        JSONObject data = (JSONObject) result.get("data");
        return data.getJSONArray("dict");


    }    /**
     * 流式导出数据
     */
    private void exportDataByStatus(JSONObject param, ExcelWriter excelWriter,
                                    WriteSheet writeSheet, boolean isActive) throws Exception {

        int pageIndex = 1;
        int total = 0;

        while (true) {
            param.put("pageIndex", String.valueOf(pageIndex));

            // 根据状态获取不同的查询处理器
            SearchHandler<JSONObject> searchHandler = isActive ?
                    SearchHandlerFactory.getTariffActiveSearchHandler(param) :
                    SearchHandlerFactory.getTariffNotActiveSearchHandler(param);

            JSONObject result = searchHandler.search();
            List<Map<String, String>> records = result.getObject("data",
                    new TypeReference<List<Map<String, String>>>() {
                    });

            if (records == null || records.isEmpty()) {
                break;
            }

            // 转换并写入数据
            List<List<Object>> rows = convertToExcelRows(records);
            excelWriter.write(rows, writeSheet);

            total += records.size();
            pageIndex++;

            // 每1000条记录输出一次日志
            if (total % 1000 == 0) {
                logger.info("已处理{}条{}记录", total, isActive ? "活跃" : "非活跃");
            }
        }

        logger.info("{}数据导出完成,共{}条记录", isActive ? "活跃" : "非活跃", total);
    }

    /**
     * 转换数据为Excel行格式
     */
    private List<List<Object>> convertToExcelRows(List<Map<String, String>> records) {
        List<List<Object>> rows = new ArrayList<>(records.size());

        for (Map<String, String> record : records) {
            List<Object> row = new ArrayList<>();
            // 按字段顺序添加数据
            row.add(record.get("tariffName"));
            row.add(record.get("reportNo"));
            row.add(record.get("tariffType"));
            row.add(getType1Text(record.get("type1")));
            row.add(getDgzb(record.get("orderCount"), record.get("allOrderCount")));
            row.add(record.get("provinceName"));
            row.add(record.get("entName"));
            row.add(parseNumber(record.get("orderCount")));
            row.add(parseNumber(record.get("cityCount")));
            row.add(record.get("citiesAndOrders"));
            row.add(record.get("firstAppearance"));
            row.add(record.get("lastAppearance"));

            rows.add(row);
        }

        return rows;
    }

    /**
     * 数字格式转换
     */
    private Object parseNumber(String value) {
        if (StringUtils.isBlank(value) || "null".equals(value)) {
            return "";
        }
        try {
            if (value.matches("[0-9]{1,6}+")) {
                return Long.valueOf(value);
            }
            if (value.matches("^([0-9]{1,6}[.][0-9]*)$")) {
                return Double.valueOf(value);
            }
        } catch (NumberFormatException e) {
            logger.warn("数字转换异常:{}", value);
        }
        return value;
    }

    /**
     * 计算订购占比
     */
    private String getDgzb(String orderCount, String allOrderCount) {
        if (StringUtils.isNotBlank(orderCount) && StringUtils.isNotBlank(allOrderCount)) {
            try {
                double dgzb = Double.parseDouble(orderCount) / Double.parseDouble(allOrderCount);
                return String.format("%.4f", dgzb * 100) + "%";
            } catch (NumberFormatException e) {
                logger.warn("订购占比计算异常: orderCount={}, allOrderCount={}", orderCount, allOrderCount);
                return "0.0000%";
            }
        }
        return "0.0000%";
    }

    /**
     * 获取 一级分类 翻译
     *
     * @param type1 一级分类 编码
     * @return 一级分类
     */
    private static String getType1Text(String type1) {
        if (StringUtils.isBlank(type1)) {
            return "";
        }
        return "1".equals(type1) ? "公众" : "政企";
    }
}
