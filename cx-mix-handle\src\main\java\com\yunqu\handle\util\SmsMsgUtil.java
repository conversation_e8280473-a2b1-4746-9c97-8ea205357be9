package com.yunqu.handle.util;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.handle.base.CommonLogger;
import com.yunqu.handle.base.Constants;
import com.yunqu.handle.base.QueryFactory;

public class SmsMsgUtil {

       public static Logger logger = LoggerFactory.getLogger(CommonLogger.getLogger("sms").getName());

	   private static String[] WORK_TIMES = new String[]{"09","10","11","12","14","15","16","17","18"};

     /**
	 * 发送短信
     * @param content
	 * @return
	 * @throws Exception
	 */
	public static void sendMsg (String content,boolean force) throws Exception {
		//获取当前小时
        String currentHour = DateUtil.getCurrentDateStr("HH");
		//判断是否是工作时间
        if (!force && !Arrays.stream(WORK_TIMES).anyMatch(currentHour::equals)) {
            logger.info("当前时间[{}]不在工作时间范围内，短信发送失败",currentHour);
            return;
        }
		JSONObject defaultChannel = getDefaultChannel(Constants.getSysSchema());
		if (Objects.isNull(defaultChannel)) {
			throw new Exception("当前不存在默认发送渠道，短信发送失败哦");
		}
        String warnPhone = Constants.getWarnPhone();
        if (StringUtils.isBlank(warnPhone)) {
            throw new Exception("当前不存在默认告警号码，短信发送失败哦");
        }
        String[] phones = StringUtils.split(warnPhone, ",");
		JSONObject param = new JSONObject();
		param.put("sender", Constants.APP_NAME);
		param.put("serialId", IDGenerator.getDefaultNUMID());
		param.put("userAcc", "system");// system
		param.put("source", "1"); //1-普通短信，99-话务短信满意度 支持扩展

		param.put("chanelId", defaultChannel.getString("ID"));
		param.put("model", Constants.APP_NAME);
		param.put("command", ServiceCommand.SENDMESSAGE);
		param.put("schema", Constants.getSysSchema());
		param.put("epCode", Constants.getEntId());
		param.put("busiOrderId", Constants.getBusiOrderId());
		param.put("ex1", "tjfq");
		// 接收电话与内容
        List<JSONObject> list = new ArrayList<>();
        Arrays.asList(phones).stream().forEach(phone -> {
            JSONObject json = new JSONObject();
            json.put("busiId", "warn_12345678");
            json.put("receiver", phone);// 接收号码
            json.put("content", "【工信部申诉中心】" + content);
            list.add(json);
        });
		
		param.put("receivers", list);
        logger.info("发起短信参数：{}" ,JSONObject.toJSONString(param));
		IService service = ServiceContext.getService(ServiceID.SMSGW_INTEFACE);
		JSONObject result = service.invoke(param);
        logger.info("短信发送服务：{}" ,JSONObject.toJSONString(result));
		if (!result.getString("respCode").equals(GWConstants.RET_CODE_SUCCESS)) {
			throw new Exception(result.getString("respMsg"));
		}
	}

    /**
	 * 获取短信默认渠道
	 * @param schema
	 * @return
	 * @throws SQLException
	 */
	private static JSONObject getDefaultChannel (String schema) throws SQLException {
		EasySQL sql = new EasySQL();
		sql.append(" select * from " + schema + ".c_sms_channel ");
		sql.append(" where 1=1 ");
		sql.append("1"," and STATUS = ? ");
		sql.append("1","and DEFAULT_CHANEL =? ");
		return QueryFactory.getWriteQuery().queryForRow(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
	}
    
}
