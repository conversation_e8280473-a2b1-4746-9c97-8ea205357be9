package com.yunqu.handle.job;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.handle.base.CommonLogger;
import com.yunqu.handle.base.Constants;
import com.yunqu.handle.service.AppealOrderCreateService;
import com.yunqu.handle.util.RedisLockUtil;
import com.yunqu.handle.util.XtyModuleRuntimeDataCollector;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.List;

/**
 * 申诉工单创建定时任务
 * 定时查询未创建工单的申诉记录并调用OrderSubmitInfService发起工单
 */
public class AppealOrderCreateJob implements Job {
    
    private static final Logger logger = LoggerFactory.getLogger(CommonLogger.getLogger("create").getName());
    private static final String LOCK_KEY = "appeal_order_create_job_lock";
    private static final int LOCK_EXPIRE_TIME = 60 * 30; // 锁过期时间，30分钟
    
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        logger.info("开始执行申诉工单创建定时任务");
        
        // 使用分布式锁确保在集群环境中只有一个实例执行任务
        boolean locked = false;
        try {
            // 尝试获取锁
            locked = RedisLockUtil.lock(LOCK_KEY, LOCK_EXPIRE_TIME);
            if (!locked) {
                logger.info("未能获取到分布式锁，跳过本次执行");
                return;
            }
            // 执行申诉工单创建
            executeAppeal();
            
            // 执行投诉工单创建
            executeComplain();

            //上报运行状态
            XtyModuleRuntimeDataCollector.report(Constants.APP_NAME, "AppealOrderCreateJob", "申诉工单创建定时任务", 1200);


        } catch (Exception e) {
            logger.error("执行申诉/投诉工单创建定时任务异常: " + e.getMessage(), e);
            throw new JobExecutionException(e);
        } finally {
            // 释放锁
            if (locked) {
                RedisLockUtil.unlock(LOCK_KEY);
                logger.debug("已释放分布式锁");
            }
        }
    }

    private void executeAppeal () {
        AppealOrderCreateService service = new AppealOrderCreateService();
        // 分页查询未处理的申诉记录
        boolean hasMoreData = true;
        
        while (hasMoreData) {
            logger.info("查询第{}页未处理申诉记录，每页{}条", 1, 50);
            
            // 调用服务查询数据
            JSONObject result = service.queryUnprocessedAppeals(0, 50,"","");
            logger.info("查询未处理申诉记录结果: {}", JSONObject.toJSONString(result));
            if (result != null && result.getBooleanValue("success")) {
                List<JSONObject> dataList = JSONObject.parseArray(result.getString("data"),JSONObject.class);
                
                if (dataList != null && !dataList.isEmpty()) {
                    logger.info("本页获取到{}条未处理申诉记录", dataList.size());
                    
                    // 处理申诉记录，调用OrderSubmitInfService发起工单
                    service.processAppeals(dataList);
                    
                    // 如果返回的数据少于pageSize，说明没有更多数据了
                    if (dataList.size() < 50) {
                        hasMoreData = false;
                        logger.info("没有更多未处理申诉记录，结束处理");
                    } 
                } else {
                    // 没有数据，结束循环
                    hasMoreData = false;
                    logger.info("没有未处理申诉记录");
                }
            } else {
                // 查询失败，结束循环
                hasMoreData = false;
                String errorMsg = result != null ? result.getString("message") : "查询失败";
                logger.error("查询未处理申诉记录失败: {}", errorMsg);
            }
        }
        
        logger.info("申诉工单创建定时任务执行完成");
    }


    private void executeComplain () {
        AppealOrderCreateService service = new AppealOrderCreateService();
            
            // 分页查询未处理的申诉记录
            boolean hasMoreData = true;
            
            while (hasMoreData) {
                logger.info("查询第{}页未处理投诉记录，每页{}条", 1, 50);
                
                // 调用服务查询数据
                JSONObject result = service.queryUnprocessedComplains(0, 50,"","");
                
                if (result != null && result.getBooleanValue("success")) {
                    List<JSONObject> dataList = result.getJSONArray("data").toJavaList(JSONObject.class);
                    
                    if (dataList != null && !dataList.isEmpty()) {
                        logger.info("本页获取到{}条未处理投诉记录", dataList.size());
                        
                        // 处理申诉记录，调用OrderSubmitInfService发起工单
                        service.processComplains(dataList);
                        
                        // 如果返回的数据少于pageSize，说明没有更多数据了
                        if (dataList.size() < 50) {
                            hasMoreData = false;
                            logger.info("没有更多未处理投诉记录，结束处理");
                        } 
                    } else {
                        // 没有数据，结束循环
                        hasMoreData = false;
                        logger.info("没有未处理投诉记录");
                    }
                } else {
                    // 查询失败，结束循环
                    hasMoreData = false;
                    String errorMsg = result != null ? result.getString("message") : "查询失败";
                    logger.error("查询未处理投诉记录失败: {}", errorMsg);
                }
            }
            
            logger.info("投诉工单创建定时任务执行完成");
    }
}