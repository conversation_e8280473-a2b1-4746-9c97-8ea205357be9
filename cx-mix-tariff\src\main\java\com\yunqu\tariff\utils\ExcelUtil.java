package com.yunqu.tariff.utils;

import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.SystemParamUtil;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.utils.excel.ExcelExportDataHandle;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;


public class ExcelUtil {

	private EasyQuery query;

	private EasySQL easySql;

	private List<String> fields;

	private String title;

	private List<String> headerList;

	private ExcelExportDataHandle excelExportDataHandle;

	/*public void export(String fileName, List<String> headerList,HttpServletRequest request ,HttpServletResponse response)
			throws Exception {
		this.title = fileName.substring(0, fileName.indexOf("_"));
		this.headerList = headerList;
		ServletOutputStream os = null;
		response.reset();
		response.setContentType("application/octet-stream; charset=utf-8");
		response.setHeader("Content-Disposition", "attachment; filename="+fileName);
		export(os, getExpSize(UserUtil.getUser(request)));
	}



	private void export(OutputStream os, int size) throws SQLException {
		// 校验参数
		checkParams();

		PreparedStatement ps = null;
		ResultSet rs = null;
		String sql = easySql.getSQL();
		Object[] params = easySql.getParams();
		ExcelWriter excelWriter = EasyExcel.write(os).head(ExcelUtil.formatHeader(headerList, title))
				.registerWriteHandler(CellStyleUtil.getHorizontalCellStyleStrategy())
				.build();
		WriteSheet writeSheet = EasyExcel.writerSheet("sheet1").build();
		Connection conn = query.getConnection();

		try {
			ps = conn.prepareStatement(sql);
			if (size != 0) {
				ps.setMaxRows(size);
			}
			for (int i = 0; i < params.length; i++) {
				Object param = params[i];
				if (param instanceof String) {
					ps.setString(i + 1, (String) param);
				} else if (param instanceof Integer) {
					ps.setInt(i + 1, (Integer) param);
				}
			}
			rs = ps.executeQuery();

			while (rs.next()) {
				List<List<Object>> list = new ArrayList<List<Object>>();
				List<Object> data = new ArrayList<Object>();
				for (int i = 0; i < headerList.size(); i++) {
					String fieldName = null;
					if (fields != null && fields.size() > i) {
						fieldName = fields.get(i);
					}
					String value = "";
					value = StringUtils.isNotBlank(fieldName) ? rs.getString(fieldName) : rs.getString(i + 1);
					if (excelExportDataHandle != null) {
						value = excelExportDataHandle.handle(fieldName, value);
					}
					if (StringUtils.isNotBlank(value) && value.toString().matches("[0-9]{1,6}+")) {
						data.add(Long.valueOf(value.toString()));
					} else if (StringUtils.isNotBlank(value) && value.toString().matches("^([0-9]{1,6}[.][0-9]*)$")) {
						data.add(Double.valueOf(value.toString()));
					} else {
						data.add(StringUtils.isNotBlank(value) ? value : "");
					}

				}
				list.add(data);
				excelWriter.write(list, writeSheet);
			}
			excelWriter.write(new ArrayList<List<Object>>(), writeSheet);
		} catch (SQLException e) {
			CommonLogger.logger.error(e.getMessage(), e);
		} finally {
			try {
				if (ps != null) {
					ps.close();
				}
			} catch (Exception e2) {
			}
			try {
				if (rs != null) {
					rs.close();
				}
			} catch (Exception e2) {
			}
			try {
				if (conn != null) {
					conn.close();
				}
			} catch (Exception e2) {
			}

			if (excelWriter != null) {
				excelWriter.finish();
			}
		}
	}*/



	private void checkParams() {
		Assert.isTrue(StringUtils.isNotBlank(title) && headerList != null && !headerList.isEmpty(), "导出标题参数缺失");
		Assert.isTrue(query != null &&  easySql != null, "数据库参数缺失");
	}

	public ExcelExportDataHandle getExcelExportDataHandle() {
		return excelExportDataHandle;
	}

	public ExcelUtil setExcelExportDataHandle(ExcelExportDataHandle excelExportDataHandle) {
		this.excelExportDataHandle = excelExportDataHandle;
		return this;
	}



	public EasySQL getEasySql() {
		return easySql;
	}



	public ExcelUtil setEasySql(EasySQL easySql) {
		this.easySql = easySql;
		return this;
	}




	public EasyQuery getQuery() {
		return query;
	}



	public ExcelUtil setQuery(EasyQuery query) {
		this.query = query;
		return this;
	}

	public List<String> getFields() {
		return fields;
	}



	public ExcelUtil setFields(List<String> fields) {
		this.fields = fields;
		return this;
	}



	public String getTitle() {
		return title;
	}



	public ExcelUtil setTitle(String title) {
		this.title = title;
		return this;
	}



	public List<String> getHeaderList() {
		return headerList;
	}



	public ExcelUtil setHeaderList(List<String> headerList) {
		this.headerList = headerList;
		return this;
	}



	/**
	 * 构建表头
	 *
     */
	public static List<List<String>> formatHeader(List<String> head, String name) {
		List<List<String>> header = new ArrayList<>();
		for (String field : head) {
			List<String> list = new ArrayList<>();
//			list.add(name);
//			list.add(name);
			list.add(field);
			header.add(list);
		}
		return header;
	}

	/**
	 * 获取报表导出数量限制
	 *
     */
	public static int getExpSize(String schema, String entId, String busiOrderId) {
		String size = SystemParamUtil.getEntParam(schema, entId, busiOrderId, "cc-base", "ENT_MAX_EXPORT_SIZE");
		int expSize = CommonUtil.parseInt(size);
		if (expSize <= 0) {
			expSize = 1000;
		}

		return expSize;
	}

	/**
	 * 获取报表导出数量限制
     */
	public static int getExpSize(UserModel user) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" select EXPORT_NUM from " + user.getSchemaName() + ".xty_ent_config ");
			sql.append(" where 1=1 ");
			sql.append(user.getDeptCode()," and ENT = ? ",false);
			String count = QueryFactory.getQuery(user.getEpCode()).queryForString(sql.getSQL(), sql.getParams());
			int i = StringUtils.isNotBlank(count) ? Integer.parseInt(count) : getExpSize(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId());
			return i;
		} catch (Exception e) {
			CommonLogger.getLogger().error(e.getMessage(),e);
			return getExpSize(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId());
		}
	}

	/**
	 * 获取导出时间后缀
     */
	public static String getSufxTime(String startDate,String endDate){
		String result = "";
		try {
			result = startDate.replace("-", "")+"_"+endDate.replace("-", "");
		} catch (Exception e) {
			result = DateUtil.getCurrentDateStr("yyyyMMdd");
		}
		return result;
	}

}
