package com.yunqu.tariff.handler.search;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.factory.EsQueryFactory;
import com.yunqu.tariff.handler.SearchHandler;
import com.yunqu.tariff.thread.ThreadPoolManager;
import com.yunqu.tariff.utils.DateSortUtil;
import com.yunqu.xty.commonex.kit.ElasticsearchKit;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.slf4j.Logger;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import org.easitline.common.utils.calendar.EasyCalendar;

/**
 * 省份企业聚合统计搜索处理器
 * 返回格式：省份、企业、未报送资费数、资费报送率、未公示资费数、资费公示率
 */
public class ProvinceEntAggregationSearchHandler implements SearchHandler<JSONObject> {

    private JSONObject param;

    public ProvinceEntAggregationSearchHandler(JSONObject param) {
        this.param = param;
    }

    @Override
    public JSONObject search() {
        // 处理嵌套的data格式
        JSONObject actualParam = param;
        if (param.containsKey("data")) {
            actualParam = param.getJSONObject("data");
        }

        // 参数校验
        String type = actualParam.getString("type");
        String searchTime = actualParam.getString("searchTime");
        if (StringUtils.isBlank(searchTime)) {
            return EasyResult.fail("查询时间不能为空");
        }

        String dateFormat;
        String displayFormat;

        if ("1".equals(type)) {
            // 日报处理
            dateFormat = "yyyyMMdd";
            displayFormat = "yyyy年MM月dd日";
        } else if ("2".equals(type)) {
            // 月报处理
            dateFormat = "yyyyMM";
            displayFormat = "yyyy年MM月";
        } else {
            return EasyResult.fail("类型参数错误");
        }

        try {
            // 更新param引用为实际参数对象
            this.param = actualParam;

            // 1. 从ES获取未报送数据
            JSONObject esData = getUnreportDataFromES(type, searchTime);

            // 2. 从MySQL获取公示率数据
            List<JSONObject> publicData = getPublicDataFromMySQL(type, searchTime);

            // 3. 合并数据并格式化返回
            JSONObject result = mergeAndFormatData(esData, publicData, type, searchTime, dateFormat, displayFormat);

            return result;
        } catch (Exception e) {
            getLogger().error("省份企业聚合统计失败", e);
            return EasyResult.fail("省份企业聚合统计失败");
        }
    }

    /**
     * 从ES获取未报送数据
     */
    private JSONObject getUnreportDataFromES(String type, String searchTime) throws Exception {
        List<String> timeVals = new ArrayList<>();
        
        if ("1".equals(type)) {
            // 日报：构建单日查询
            EasyCalendar calendar = EasyCalendar.newInstance(searchTime, "yyyyMMdd");
            String month = calendar.getFullMonth();
            String date = String.valueOf(calendar.getDateInt());
            timeVals.add(month + "." + date);
        } else if ("2".equals(type)) {
            // 月报：构建整月查询
            timeVals.add(searchTime);
        }

        Set<Callable<JSONObject>> queryParams = buildESQueryParams(timeVals);
        List<Future<JSONObject>> results = ThreadPoolManager.getExecutorService().invokeAll(queryParams);

        return parseESResults(results);
    }

    /**
     * 构建ES查询参数
     */
    private Set<Callable<JSONObject>> buildESQueryParams(List<String> timeVals) {
        Set<Callable<JSONObject>> queryParams = new HashSet<>();

        // 获取省份和企业参数
        JSONArray provinceCodes = param.getJSONArray("provinceCodes");
        JSONArray entCodes = param.getJSONArray("ent");

        for (String timeVal : timeVals) {
            JSONObject aggs = createProvinceEntAggs();

            EsQueryFactory.EsQueryParamBuilder builder = EsQueryFactory.queryParamBuilder()
                    .terms("type.keyword", Arrays.asList("1", "3", "5"))
                    .exists(timeVal).aggs(aggs).size(0);

            // 添加省份过滤条件 - 如果包含"0"则不过滤
            if (provinceCodes != null && !provinceCodes.isEmpty()) {
                boolean hasAll = false;
                List<String> provinceList = new ArrayList<>();
                for (int i = 0; i < provinceCodes.size(); i++) {
                    String provinceCode = provinceCodes.getString(i);
                    if ("0".equals(provinceCode)) {
                        hasAll = true;
                        break;
                    } else {
                        provinceList.add(provinceCode);
                    }
                }
                // 只有在不包含"0"且有具体省份时才添加过滤条件
                if (!hasAll && !provinceList.isEmpty()) {
                    if (provinceList.size() == 1) {
                        builder.term("provinceCode.keyword", provinceList.get(0));
                    } else {
                        builder.terms("provinceCode.keyword", provinceList);
                    }
                }
            }

            // 添加企业过滤条件 - 如果包含"0"则不过滤
            if (entCodes != null && !entCodes.isEmpty()) {
                boolean hasAll = false;
                List<String> entList = new ArrayList<>();
                for (int i = 0; i < entCodes.size(); i++) {
                    String entCode = entCodes.getString(i);
                    if ("0".equals(entCode)) {
                        hasAll = true;
                        break;
                    } else {
                        entList.add(entCode);
                    }
                }
                // 只有在不包含"0"且有具体企业时才添加过滤条件
                if (!hasAll && !entList.isEmpty()) {
                    if (entList.size() == 1) {
                        builder.term("entCode.keyword", entList.get(0));
                    } else {
                        builder.terms("entCode.keyword", entList);
                    }
                }
            }

            JSONObject requestParam = builder.buildRequestParam();

            queryParams.add(() -> {
                logger.info("查询的es："+requestParam.toJSONString());
                JSONObject esResult = ElasticsearchKit.search("xty_tariff_audit_process_storage", requestParam);
                JSONObject result = new JSONObject();
                result.put("timeVal", timeVal);
                result.put("typeCount", esResult);
                return result;
            });
        }

        return queryParams;
    }

    /**
     * 创建省份企业聚合查询
     */
    private JSONObject createProvinceEntAggs() {
        JSONObject aggs = new JSONObject();
        JSONObject compositeAgg = new JSONObject();

        JSONObject composite = new JSONObject();
        JSONArray sources = new JSONArray();

        // 添加省份维度
        sources.add(new JSONObject()
                .fluentPut("province", new JSONObject()
                        .fluentPut("terms", new JSONObject()
                                .fluentPut("field", "provinceCode.keyword"))));
        // 添加企业维度
        sources.add(new JSONObject()
                .fluentPut("ent", new JSONObject()
                        .fluentPut("terms", new JSONObject()
                                .fluentPut("field", "entCode.keyword"))));
        // 添加类型维度
        sources.add(new JSONObject()
                .fluentPut("type", new JSONObject()
                        .fluentPut("terms", new JSONObject()
                                .fluentPut("field", "type.keyword"))));

        composite.put("sources", sources);
        composite.put("size", 1000);

        compositeAgg.put("composite", composite);
        aggs.put("ent_code_count", compositeAgg);
        return aggs;
    }

    /**
     * 解析ES查询结果
     */
    private JSONObject parseESResults(List<Future<JSONObject>> results) throws Exception {
        Map<String, Map<String, Integer>> unreportData = new HashMap<>(); // 省份_企业 -> 未报送数据

        for (Future<JSONObject> future : results) {
            JSONObject timeResult = future.get();
            if (timeResult != null) {
                JSONObject typeCount = timeResult.getJSONObject("typeCount");
                JSONObject aggregations = typeCount.getJSONObject("aggregations");

                if (aggregations != null) {
                    JSONObject compositeAgg = aggregations.getJSONObject("ent_code_count");
                    List<JSONObject> buckets = compositeAgg.getObject("buckets", new TypeReference<List<JSONObject>>() {});

                    // 补充缺失的企业类型数据
                    buckets = complementMissingEntTypeData(buckets);

                    if (buckets != null) {
                        for (JSONObject bucket : buckets) {
                            JSONObject key = bucket.getJSONObject("key");
                            String provinceCode = key.getString("province");
                            String entCode = key.getString("ent");
                            String type = key.getString("type");
                            int count = bucket.getIntValue("doc_count");

                            String entAlias = getEntAlias(entCode);
                            String keyStr = provinceCode + "_" + entAlias;

                            Map<String, Integer> typeStats = unreportData.computeIfAbsent(keyStr, k -> {
                                Map<String, Integer> stats = new HashMap<>();
                                Arrays.asList("1", "3", "5").forEach(t -> stats.put(t, 0));
                                return stats;
                            });
                            typeStats.put(type, count);
                        }
                    }
                }
            }
        }

        JSONObject result = new JSONObject();
        result.put("unreportData", unreportData);
        return result;
    }

    /**
     * 补充缺失的企业类型数据
     */
    public List<JSONObject> complementMissingEntTypeData(List<JSONObject> sourceList) {
        if (sourceList == null) {
            return new ArrayList<>();
        }

        // 需要检查的ent值列表
        List<Integer> entList = Arrays.asList(1, 2, 3, 5);

        // 创建新列表以保存结果
        List<JSONObject> resultList = new ArrayList<>(sourceList);

        // 获取所有已存在的省份
        Set<String> provinces = new HashSet<>();
        for (JSONObject item : sourceList) {
            JSONObject key = item.getJSONObject("key");
            if (key.containsKey("province")) {
                provinces.add(key.getString("province"));
            }
        }

        // 为每个省份的每个企业补充type=5的数据（如果不存在）
        for (String province : provinces) {
            for (Integer ent : entList) {
                // 检查是否存在province+ent+type=5的组合
                boolean exists = resultList.stream().anyMatch(item -> {
                    JSONObject key = item.getJSONObject("key");
                    return key.getString("province").equals(province)
                            && key.getString("ent").equals(String.valueOf(ent))
                            && key.getString("type").equals("5");
                });

                // 如果不存在,添加新对象
                if (!exists) {
                    JSONObject newItem = new JSONObject();
                    JSONObject key = new JSONObject();
                    key.put("province", province);
                    key.put("ent", String.valueOf(ent));
                    key.put("type", "5");
                    newItem.put("key", key);
                    newItem.put("doc_count", 0);
                    resultList.add(newItem);
                }
            }
        }

        return resultList;
    }

    /**
     * 从MySQL获取公示率数据
     */
    private List<JSONObject> getPublicDataFromMySQL(String type, String searchTime) {
        try {
            EasyQuery query = getQuery();
            EasySQL sql = new EasySQL();
            
            JSONArray provinceCodes = param.getJSONArray("provinceCodes");
            JSONArray entCodes = param.getJSONArray("ent");
            String searchTimeFormatted = StringUtils.replace(searchTime, "-", "");

            if("2".equals(type)) {
                // 月报查询
                sql.append("SELECT t.MONTH_ID AS DATE_VALUE, t.PROVINCE_CODE, t.ENT, t.ENT_NAME, ")
                        .append("ROUND(AVG(t.PUBLIC_NUM), 0) AS PUBLIC_NUM, ")
                        .append("ROUND(AVG(t.UN_PUBLIC_NUM), 0) AS UN_PUBLIC_NUM, ")
                        .append("ROUND(AVG(t.OFFLINE_NUM), 0) AS OFFLINE_NUM, ")
                        .append("ROUND(AVG(t.ACTIVE_NUM), 0) AS ACTIVE_NUM, ")
                        .append("CASE WHEN AVG(t.ACTIVE_NUM) = 0 THEN 0 ELSE ROUND((AVG(t.PUBLIC_NUM) + AVG(t.OFFLINE_NUM)) / AVG(t.ACTIVE_NUM) * 100, 2) END AS PUBLIC_RATE ")
                        .append("FROM ").append(Constants.getBusiSchema()).append(".xty_tariff_public_day t ")
                        .append("WHERE 1=1 ");
                
                // 添加省份过滤
                if (provinceCodes != null && !provinceCodes.isEmpty()) {
                    boolean hasAll = false;
                    List<String> provinceList = new ArrayList<>();
                    for (int i = 0; i < provinceCodes.size(); i++) {
                        String provinceCode = provinceCodes.getString(i);
                        if ("0".equals(provinceCode)) {
                            hasAll = true;
                            break;
                        } else {
                            provinceList.add(provinceCode);
                        }
                    }
                    // 只有在不包含"0"且有具体省份时才添加过滤条件
                    if (!hasAll && !provinceList.isEmpty()) {
                        sql.append("AND PROVINCE_CODE IN (");
                        for (int i = 0; i < provinceList.size(); i++) {
                            if (i > 0) sql.append(",");
                            sql.append("?", provinceList.get(i));
                        }
                        sql.append(") ");
                    }
                }
                
                // 添加企业过滤
                if (entCodes != null && !entCodes.isEmpty()) {
                    boolean hasAll = false;
                    List<String> entList = new ArrayList<>();
                    for (int i = 0; i < entCodes.size(); i++) {
                        String entCode = entCodes.getString(i);
                        if ("0".equals(entCode)) {
                            hasAll = true;
                            break;
                        } else {
                            entList.add(entCode);
                        }
                    }
                    // 只有在不包含"0"且有具体企业时才添加过滤条件
                    if (!hasAll && !entList.isEmpty()) {
                        sql.append("AND ENT IN (");
                        for (int i = 0; i < entList.size(); i++) {
                            if (i > 0) sql.append(",");
                            sql.append("?", entList.get(i));
                        }
                        sql.append(") ");
                    }
                }
                
                sql.append(searchTimeFormatted, "AND t.MONTH_ID = ? ");
                sql.append("GROUP BY t.MONTH_ID, t.PROVINCE_CODE, t.ENT, t.ENT_NAME ")
                        .append("ORDER BY t.MONTH_ID DESC, t.PROVINCE_CODE, t.ENT");
            } else {
                // 日报查询
                sql.append("SELECT t.DATE_ID AS DATE_VALUE, t.PROVINCE_CODE, t.ENT, t.ENT_NAME, ")
                        .append("SUM(t.PUBLIC_NUM) AS PUBLIC_NUM, ")
                        .append("SUM(t.UN_PUBLIC_NUM) AS UN_PUBLIC_NUM, ")
                        .append("SUM(t.OFFLINE_NUM) AS OFFLINE_NUM, ")
                        .append("SUM(t.ACTIVE_NUM) AS ACTIVE_NUM, ")
                        .append("CASE WHEN SUM(t.ACTIVE_NUM) = 0 THEN 0 ELSE ROUND((SUM(t.PUBLIC_NUM) + SUM(t.OFFLINE_NUM)) / SUM(t.ACTIVE_NUM) * 100, 2) END AS PUBLIC_RATE ")
                        .append("FROM ").append(Constants.getBusiSchema()).append(".xty_tariff_public_day t ")
                        .append("WHERE 1=1 ");

                // 添加省份过滤
                if (provinceCodes != null && !provinceCodes.isEmpty()) {
                    boolean hasAll = false;
                    List<String> provinceList = new ArrayList<>();
                    for (int i = 0; i < provinceCodes.size(); i++) {
                        String provinceCode = provinceCodes.getString(i);
                        if ("0".equals(provinceCode)) {
                            hasAll = true;
                            break;
                        } else {
                            provinceList.add(provinceCode);
                        }
                    }
                    // 只有在不包含"0"且有具体省份时才添加过滤条件
                    if (!hasAll && !provinceList.isEmpty()) {
                        sql.append("AND PROVINCE_CODE IN (");
                        for (int i = 0; i < provinceList.size(); i++) {
                            if (i > 0) sql.append(",");
                            sql.append("?", provinceList.get(i));
                        }
                        sql.append(") ");
                    }
                }

                // 添加企业过滤
                if (entCodes != null && !entCodes.isEmpty()) {
                    boolean hasAll = false;
                    List<String> entList = new ArrayList<>();
                    for (int i = 0; i < entCodes.size(); i++) {
                        String entCode = entCodes.getString(i);
                        if ("0".equals(entCode)) {
                            hasAll = true;
                            break;
                        } else {
                            entList.add(entCode);
                        }
                    }
                    // 只有在不包含"0"且有具体企业时才添加过滤条件
                    if (!hasAll && !entList.isEmpty()) {
                        sql.append("AND ENT IN (");
                        for (int i = 0; i < entList.size(); i++) {
                            if (i > 0) sql.append(",");
                            sql.append("?", entList.get(i));
                        }
                        sql.append(") ");
                    }
                }

                sql.append(searchTimeFormatted, "AND t.DATE_ID = ? ");
                sql.append("GROUP BY t.DATE_ID, t.PROVINCE_CODE, t.ENT, t.ENT_NAME ")
                        .append("ORDER BY t.DATE_ID DESC, t.PROVINCE_CODE, t.ENT");
            }
            
            getLogger().info("查询公示率统计SQL：{}，参数：{}", sql.getSQL(), sql.getParams());
            return query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        } catch (Exception e) {
            getLogger().error("查询公示率统计异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 合并ES和MySQL数据并格式化返回
     */
    private JSONObject mergeAndFormatData(JSONObject esData, List<JSONObject> publicData,
                                          String type, String searchTime, String dateFormat, String displayFormat) {

        Map<String, Map<String, Integer>> unreportData = (Map<String, Map<String, Integer>>) esData.get("unreportData");
        List<JSONObject> resultList = new ArrayList<>();

        // 创建省份企业公示数据映射
        Map<String, JSONObject> publicDataMap = new HashMap<>();
        for (JSONObject publicItem : publicData) {
            String provinceCode = publicItem.getString("PROVINCE_CODE");
            String entCode = publicItem.getString("ENT");
            String entAlias = getEntAlias(entCode);
            String key = provinceCode + "_" + entAlias;
            publicDataMap.put(key, publicItem);
        }

        // 检查是否需要添加汇总数据
        JSONArray provinceCodes = param.getJSONArray("provinceCodes");
        JSONArray entCodes = param.getJSONArray("ent");
        boolean needNationalSummary = false;
        boolean needIndustrySummary = false;

        // 确定需要显示的省份和企业
        Set<String> targetProvinces = new HashSet<>();
        Set<String> targetEnts = new HashSet<>();
        if (provinceCodes != null) {
            for (int i = 0; i < provinceCodes.size(); i++) {
                String provinceCode = provinceCodes.getString(i);
                if ("0".equals(provinceCode)) {
                    needNationalSummary = true;
                } else {
                    targetProvinces.add(provinceCode);
                }
            }
        }

        if (entCodes != null) {
            for (int i = 0; i < entCodes.size(); i++) {
                String entCode = entCodes.getString(i);
                if ("0".equals(entCode)) {
                    needIndustrySummary = true;
                } else {
                    // 将企业代码转换为别名
                    String entAlias = getEntAlias(entCode);
                    targetEnts.add(entAlias);
                }
            }
        }

        // 获取所有省份企业组合，但只处理目标省份和企业
        Set<String> allKeys = new HashSet<>();
        allKeys.addAll(unreportData.keySet());
        allKeys.addAll(publicDataMap.keySet());

        // 确保基础记录存在：为每个指定的省份和企业组合生成基础key
        Set<String> requiredKeys = new HashSet<>();
        for (String provinceCode : targetProvinces) {
            for (String entAlias : targetEnts) {
                requiredKeys.add(provinceCode + "_" + entAlias);
            }
        }
        allKeys.addAll(requiredKeys);

        // 过滤只处理指定省份和企业的数据
        Set<String> filteredKeys = new HashSet<>();
        for (String key : allKeys) {
            String[] parts = key.split("_");
            if (parts.length == 2) {
                String provinceCode = parts[0];
                String entAlias = parts[1];
                // 检查省份和企业是否都在目标范围内
                boolean provinceMatch = targetProvinces.isEmpty() || targetProvinces.contains(provinceCode);
                boolean entMatch = targetEnts.isEmpty() || targetEnts.contains(entAlias);
                if (provinceMatch && entMatch) {
                    filteredKeys.add(key);
                }
            }
        }

        // 用于汇总计算的数据结构
        Map<String, Map<String, Integer>> provinceSummary = new HashMap<>(); // 省份 -> 全行业汇总
        Map<String, Integer> nationalSummary = new HashMap<>(); // 全国汇总
        Map<String, JSONObject> provincePublicSummary = new HashMap<>(); // 省份公示数据汇总
        JSONObject nationalPublicSummary = new JSONObject(); // 全国公示数据汇总

        // 处理指定省份的基础数据并计算汇总
        for (String key : filteredKeys) {
            String[] parts = key.split("_");
            if (parts.length != 2) continue;

            String provinceCode = parts[0];
            String entAlias = parts[1];

            // 获取未报送数据，如果没有数据则使用默认值
            Map<String, Integer> typeStats = unreportData.getOrDefault(key, new HashMap<>());
            int salesCount = typeStats.getOrDefault("1", 0);
            int renewCount = typeStats.getOrDefault("3", 0);
            int unreportCount = typeStats.getOrDefault("5", 0);
            int activeCount = salesCount + renewCount;
            int allCount = salesCount + renewCount + unreportCount;

            // 计算资费报送率，如果没有数据则显示特殊值
            String reportRate;
            if (allCount == 0) {
                // 没有数据时显示特殊状态
                reportRate = "-";
            } else {
                reportRate = String.format("%.2f", (double) activeCount / allCount * 100);
            }

            // 获取公示数据，如果没有数据则使用默认值
            JSONObject publicItem = publicDataMap.get(key);
            int unPublicNum = 0;
            String publicRate = "-";
            if (publicItem != null) {
                unPublicNum = publicItem.getIntValue("UN_PUBLIC_NUM");
                double publicRateValue = publicItem.getDoubleValue("PUBLIC_RATE");
                publicRate = String.format("%.2f", publicRateValue);
            }

            // 构建基础结果对象
            JSONObject resultItem = new JSONObject();
            resultItem.put("provinceCode", provinceCode);
            resultItem.put("provinceName", getProvinceName(provinceCode));
            resultItem.put("ent", getEntCode(entAlias));
            resultItem.put("entName", getEntName(getEntCode(entAlias)));
            
            // 如果没有数据，显示特殊状态
            if (allCount == 0 && publicItem == null) {
                resultItem.put("unreportCount", "未报送，稽核失败");
                resultItem.put("reportRate", "-");
                resultItem.put("unpublicCount", "未报送，稽核失败");
                resultItem.put("publicRate", "-");
            } else {
                resultItem.put("unreportCount", unreportCount);
                resultItem.put("reportRate", reportRate.equals("-") ? "-" : reportRate + "%");
                resultItem.put("unpublicCount", unPublicNum);
                resultItem.put("publicRate", publicRate.equals("-") ? "-" : publicRate + "%");
            }

            resultList.add(resultItem);

            // 累计省份汇总数据（用于全行业计算）
            if (needIndustrySummary && allCount > 0) {
                Map<String, Integer> pStats = provinceSummary.computeIfAbsent(provinceCode, k -> {
                    Map<String, Integer> stats = new HashMap<>();
                    stats.put("sales", 0);
                    stats.put("renew", 0);
                    stats.put("unreport", 0);
                    return stats;
                });
                pStats.put("sales", pStats.get("sales") + salesCount);
                pStats.put("renew", pStats.get("renew") + renewCount);
                pStats.put("unreport", pStats.get("unreport") + unreportCount);

                // 累计省份公示数据
                JSONObject pPublic = provincePublicSummary.get(provinceCode);
                if (pPublic == null) {
                    pPublic = new JSONObject();
                    pPublic.put("PUBLIC_NUM", 0);
                    pPublic.put("UN_PUBLIC_NUM", 0);
                    pPublic.put("OFFLINE_NUM", 0);
                    pPublic.put("ACTIVE_NUM", 0);
                    provincePublicSummary.put(provinceCode, pPublic);
                }
                if (publicItem != null) {
                    pPublic.put("PUBLIC_NUM", pPublic.getIntValue("PUBLIC_NUM") + publicItem.getIntValue("PUBLIC_NUM"));
                    pPublic.put("UN_PUBLIC_NUM", pPublic.getIntValue("UN_PUBLIC_NUM") + publicItem.getIntValue("UN_PUBLIC_NUM"));
                    pPublic.put("OFFLINE_NUM", pPublic.getIntValue("OFFLINE_NUM") + publicItem.getIntValue("OFFLINE_NUM"));
                    pPublic.put("ACTIVE_NUM", pPublic.getIntValue("ACTIVE_NUM") + publicItem.getIntValue("ACTIVE_NUM"));
                }
            }
        }

        // 如果需要全国汇总，需要计算所有省份的数据（不只是指定省份）
        if (needNationalSummary) {
            for (String key : allKeys) {
                String[] parts = key.split("_");
                if (parts.length != 2) continue;

                String entAlias = parts[1];
                Map<String, Integer> typeStats = unreportData.getOrDefault(key, new HashMap<>());
                JSONObject publicItem = publicDataMap.get(key);

                int salesCount = typeStats.getOrDefault("1", 0);
                int renewCount = typeStats.getOrDefault("3", 0);
                int unreportCount = typeStats.getOrDefault("5", 0);

                // 累计全国汇总数据
                nationalSummary.put("sales", nationalSummary.getOrDefault("sales", 0) + salesCount);
                nationalSummary.put("renew", nationalSummary.getOrDefault("renew", 0) + renewCount);
                nationalSummary.put("unreport", nationalSummary.getOrDefault("unreport", 0) + unreportCount);

                // 累计全国公示数据
                if (publicItem != null) {
                    nationalPublicSummary.put("PUBLIC_NUM", nationalPublicSummary.getIntValue("PUBLIC_NUM") + publicItem.getIntValue("PUBLIC_NUM"));
                    nationalPublicSummary.put("UN_PUBLIC_NUM", nationalPublicSummary.getIntValue("UN_PUBLIC_NUM") + publicItem.getIntValue("UN_PUBLIC_NUM"));
                    nationalPublicSummary.put("OFFLINE_NUM", nationalPublicSummary.getIntValue("OFFLINE_NUM") + publicItem.getIntValue("OFFLINE_NUM"));
                    nationalPublicSummary.put("ACTIVE_NUM", nationalPublicSummary.getIntValue("ACTIVE_NUM") + publicItem.getIntValue("ACTIVE_NUM"));
                }
            }
        }

        // 添加全行业汇总数据（只为指定的省份添加ent=0的记录）
        if (needIndustrySummary) {
            // 为每个指定的省份添加全行业汇总，即使没有数据
            for (String provinceCode : targetProvinces) {
                Map<String, Integer> stats = provinceSummary.get(provinceCode);
                JSONObject pPublic = provincePublicSummary.get(provinceCode);

                JSONObject industryItem = new JSONObject();
                industryItem.put("provinceCode", provinceCode);
                industryItem.put("provinceName", getProvinceName(provinceCode));
                industryItem.put("ent", "0");
                industryItem.put("entName", getEntName("0"));

                if (stats != null && (stats.get("sales") + stats.get("renew") + stats.get("unreport")) > 0) {
                    // 有数据的情况
                    int totalSales = stats.get("sales");
                    int totalRenew = stats.get("renew");
                    int totalUnreport = stats.get("unreport");
                    int totalActive = totalSales + totalRenew;
                    int totalAll = totalSales + totalRenew + totalUnreport;

                    String industryReportRate = String.format("%.2f", (double) totalActive / totalAll * 100);

                    int totalUnPublic = 0;
                    double industryPublicRate = 0.0;
                    if (pPublic != null) {
                        totalUnPublic = pPublic.getIntValue("UN_PUBLIC_NUM");
                        int publicNum = pPublic.getIntValue("PUBLIC_NUM");
                        int offlineNum = pPublic.getIntValue("OFFLINE_NUM");
                        int activeNum = pPublic.getIntValue("ACTIVE_NUM");
                        if (activeNum > 0) {
                            industryPublicRate = (double) (publicNum + offlineNum) / activeNum * 100;
                        }
                    }

                    industryItem.put("unreportCount", totalUnreport);
                    industryItem.put("reportRate", industryReportRate + "%");
                    industryItem.put("unpublicCount", totalUnPublic);
                    industryItem.put("publicRate", String.format("%.2f", industryPublicRate) + "%");
                } else {
                    // 没有数据的情况
                    industryItem.put("unreportCount", "未报送，稽核失败");
                    industryItem.put("reportRate", "-");
                    industryItem.put("unpublicCount", "未报送，稽核失败");
                    industryItem.put("publicRate", "-");
                }

                resultList.add(industryItem);
            }
        }

        // 添加全国汇总数据（provinceCode=0）
        if (needNationalSummary) {
            int nationalSales = nationalSummary.getOrDefault("sales", 0);
            int nationalRenew = nationalSummary.getOrDefault("renew", 0);
            int nationalUnreport = nationalSummary.getOrDefault("unreport", 0);
            int nationalActive = nationalSales + nationalRenew;
            int nationalAll = nationalSales + nationalRenew + nationalUnreport;

            String nationalReportRate = "0.00";
            if (nationalAll > 0) {
                nationalReportRate = String.format("%.2f", (double) nationalActive / nationalAll * 100);
            }

            // 全国公示数据
            int nationalUnPublic = nationalPublicSummary.getIntValue("UN_PUBLIC_NUM");
            double nationalPublicRateValue = 0.0;
            int nationalPublicNum = nationalPublicSummary.getIntValue("PUBLIC_NUM");
            int nationalOfflineNum = nationalPublicSummary.getIntValue("OFFLINE_NUM");
            int nationalActiveNum = nationalPublicSummary.getIntValue("ACTIVE_NUM");
            if (nationalActiveNum > 0) {
                nationalPublicRateValue = (double) (nationalPublicNum + nationalOfflineNum) / nationalActiveNum * 100;
            }

            // 为全国添加各企业的汇总数据
            Map<String, Map<String, Integer>> nationalEntStats = new HashMap<>();
            Map<String, JSONObject> nationalEntPublicStats = new HashMap<>();

            // 按企业汇总全国数据（使用所有省份数据）
            for (String key : allKeys) {
                String[] parts = key.split("_");
                if (parts.length != 2) continue;

                String entAlias = parts[1];
                Map<String, Integer> typeStats = unreportData.getOrDefault(key, new HashMap<>());
                JSONObject publicItem = publicDataMap.get(key);

                // 累计企业数据
                Map<String, Integer> entStats = nationalEntStats.computeIfAbsent(entAlias, k -> {
                    Map<String, Integer> stats = new HashMap<>();
                    stats.put("sales", 0);
                    stats.put("renew", 0);
                    stats.put("unreport", 0);
                    return stats;
                });
                entStats.put("sales", entStats.get("sales") + typeStats.getOrDefault("1", 0));
                entStats.put("renew", entStats.get("renew") + typeStats.getOrDefault("3", 0));
                entStats.put("unreport", entStats.get("unreport") + typeStats.getOrDefault("5", 0));

                // 累计企业公示数据
                JSONObject entPublic = nationalEntPublicStats.get(entAlias);
                if (entPublic == null) {
                    entPublic = new JSONObject();
                    entPublic.put("PUBLIC_NUM", 0);
                    entPublic.put("UN_PUBLIC_NUM", 0);
                    entPublic.put("OFFLINE_NUM", 0);
                    entPublic.put("ACTIVE_NUM", 0);
                    nationalEntPublicStats.put(entAlias, entPublic);
                }
                if (publicItem != null) {
                    entPublic.put("PUBLIC_NUM", entPublic.getIntValue("PUBLIC_NUM") + publicItem.getIntValue("PUBLIC_NUM"));
                    entPublic.put("UN_PUBLIC_NUM", entPublic.getIntValue("UN_PUBLIC_NUM") + publicItem.getIntValue("UN_PUBLIC_NUM"));
                    entPublic.put("OFFLINE_NUM", entPublic.getIntValue("OFFLINE_NUM") + publicItem.getIntValue("OFFLINE_NUM"));
                    entPublic.put("ACTIVE_NUM", entPublic.getIntValue("ACTIVE_NUM") + publicItem.getIntValue("ACTIVE_NUM"));
                }
            }

            // 为全国添加各企业汇总记录
            for (Map.Entry<String, Map<String, Integer>> entEntry : nationalEntStats.entrySet()) {
                String entAlias = entEntry.getKey();
                Map<String, Integer> entStats = entEntry.getValue();

                int entSales = entStats.get("sales");
                int entRenew = entStats.get("renew");
                int entUnreport = entStats.get("unreport");
                int entActive = entSales + entRenew;
                int entAll = entSales + entRenew + entUnreport;

                String entReportRate = "0.00";
                if (entAll > 0) {
                    entReportRate = String.format("%.2f", (double) entActive / entAll * 100);
                }

                JSONObject entPublic = nationalEntPublicStats.get(entAlias);
                int entUnPublic = 0;
                double entPublicRate = 0.0;
                if (entPublic != null) {
                    entUnPublic = entPublic.getIntValue("UN_PUBLIC_NUM");
                    int publicNum = entPublic.getIntValue("PUBLIC_NUM");
                    int offlineNum = entPublic.getIntValue("OFFLINE_NUM");
                    int activeNum = entPublic.getIntValue("ACTIVE_NUM");
                    if (activeNum > 0) {
                        entPublicRate = (double) (publicNum + offlineNum) / activeNum * 100;
                    }
                }

                JSONObject nationalEntItem = new JSONObject();
                nationalEntItem.put("provinceCode", "0");
                nationalEntItem.put("provinceName", "全国");
                nationalEntItem.put("ent", getEntCode(entAlias));
                nationalEntItem.put("entName", getEntName(getEntCode(entAlias)));
                nationalEntItem.put("unreportCount", entUnreport);
                nationalEntItem.put("reportRate", entReportRate + "%");
                nationalEntItem.put("unpublicCount", entUnPublic);
                nationalEntItem.put("publicRate", String.format("%.2f", entPublicRate) + "%");

                resultList.add(nationalEntItem);
            }

            // 如果需要全行业汇总，还要添加全国全行业汇总
            if (needIndustrySummary) {
                JSONObject nationalIndustryItem = new JSONObject();
                nationalIndustryItem.put("provinceCode", "0");
                nationalIndustryItem.put("provinceName", "全国");
                nationalIndustryItem.put("ent", "0");
                nationalIndustryItem.put("entName", getEntName("0"));
                nationalIndustryItem.put("unreportCount", nationalUnreport);
                nationalIndustryItem.put("reportRate", nationalReportRate + "%");
                nationalIndustryItem.put("unpublicCount", nationalUnPublic);
                nationalIndustryItem.put("publicRate", String.format("%.2f", nationalPublicRateValue) + "%");

                resultList.add(nationalIndustryItem);
            }
        }

        // 按省份代码和企业代码排序
        resultList.sort((a, b) -> {
            int provinceCompare = a.getString("provinceCode").compareTo(b.getString("provinceCode"));
            if (provinceCompare != 0) return provinceCompare;
            return a.getString("ent").compareTo(b.getString("ent"));
        });

        JSONObject result = new JSONObject();
        result.put("state", 1);
        result.put("msg", "success");
        result.put("data", resultList);
        return result;
    }

    /**
     * 根据企业别名获取企业代码
     */
    private String getEntCode(String entAlias) {
        switch (entAlias) {
            case "TELECOM":
                return "1";
            case "MOBILE":
                return "2";
            case "UNICOM":
                return "3";
            case "BROAD":
                return "5";
            default:
                return "0";
        }
    }


    /**
     * 根据省份代码获取省份名称
     */
    private String getProvinceName(String provinceCode) {
        if ("0".equals(provinceCode)) {
            return "全国";
        }
        switch (provinceCode) {
            case "110000":
                return "北京";
            case "120000":
                return "天津";
            case "130000":
                return "河北";
            case "140000":
                return "山西";
            case "150000":
                return "内蒙古";
            case "210000":
                return "辽宁";
            case "220000":
                return "吉林";
            case "230000":
                return "黑龙江";
            case "310000":
                return "上海";
            case "320000":
                return "江苏";
            case "330000":
                return "浙江";
            case "340000":
                return "安徽";
            case "350000":
                return "福建";
            case "360000":
                return "江西";
            case "370000":
                return "山东";
            case "410000":
                return "河南";
            case "420000":
                return "湖北";
            case "430000":
                return "湖南";
            case "440000":
                return "广东";
            case "450000":
                return "广西";
            case "460000":
                return "海南";
            case "500000":
                return "重庆";
            case "510000":
                return "四川";
            case "520000":
                return "贵州";
            case "530000":
                return "云南";
            case "540000":
                return "西藏";
            case "610000":
                return "陕西";
            case "620000":
                return "甘肃";
            case "630000":
                return "青海";
            case "640000":
                return "宁夏";
            case "650000":
                return "新疆";
            default:
                return "未知省份";
        }
    }


}