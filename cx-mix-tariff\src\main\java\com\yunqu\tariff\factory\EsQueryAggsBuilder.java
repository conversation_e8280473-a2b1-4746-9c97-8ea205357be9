package com.yunqu.tariff.factory;

import com.alibaba.fastjson.JSONObject;

import java.util.List;
import java.util.Map;

public class EsQueryAggsBuilder {

    private static class SingletonHolder {
        private static final EsQueryAggsBuilder INSTANCE = new EsQueryAggsBuilder();
    }

    private EsQueryAggsBuilder() {
        // Private constructor to prevent instantiation
    }

    public static EsQueryAggsBuilder getInstance() {
        return SingletonHolder.INSTANCE;
    }


    /**
     * 构建聚合查询参数
     *
     * @return JSONObject
     * <AUTHOR>
     * @date 2025/1/13 15:14
     */
    public JSONObject aggCardinalityFilterMatchAll(String aggsKey, JSONObject aggs) {
        return new JSONObject()
                .fluentPut("filter", new JSONObject()
                        .fluentPut("match_all", new JSONObject()))
                .fluentPut("aggs", new JSONObject().fluentPut(aggsKey, aggs));
    }

    /**
     * 构建聚合查询参数
     * @param termField 聚合字段
     * @param termSize 聚合字段大小
     * @param cardinaltyKey 聚合字段
     * @param cardinaltyVal 聚合字段
     */
    public JSONObject aggCardinalityTerms(String termField, int termSize, String cardinaltyKey, String cardinaltyVal) {
        return new JSONObject()
                    .fluentPut("terms", new JSONObject()
                            .fluentPut("field", termField)
                            .fluentPut("size", termSize))
                .fluentPut("aggs", new JSONObject()
                        .fluentPut(cardinaltyKey, new JSONObject()
                                .fluentPut("cardinality", new JSONObject()
                                        .fluentPut("field", cardinaltyVal))));
    }

    /**
     * 构建聚合查询参数
     *
     * @param termField 聚合字段
     * @param termValue 聚合字段值
     * @param aggs      聚合参数
     */
    public JSONObject aggCardinalityFilterTerm(String termField, Object termValue, JSONObject aggs) {
        return new JSONObject()
                .fluentPut("filter", new JSONObject()
                        .fluentPut("term", new JSONObject()
                                .fluentPut(termField, termValue)))
                .fluentPut("aggs", aggs);
    }

    /**
     * 构建聚合查询参数
     * @param termField 聚合字段
     * @param termSize 聚合字段大小
     */
    public JSONObject aggCardinalityTerms(String termField, int termSize, JSONObject aggs) {
        return new JSONObject()
                    .fluentPut("terms", new JSONObject()
                            .fluentPut("field", termField)
                            .fluentPut("size", termSize))
                .fluentPut("aggs", aggs);
    }

    /**
     * 构建聚合查询参数
     * @param termField 聚合字段
     * @param termSize 聚合字段大小
     * @param aggs 聚合参数
     */
    public JSONObject aggTerms(String termField, int termSize, String aggsKey, JSONObject aggs) {
        return new JSONObject()
                    .fluentPut("terms", new JSONObject()
                            .fluentPut("field", termField)
                            .fluentPut("size", termSize))
                .fluentPut("aggs", new JSONObject().fluentPut(aggsKey, aggs));
    }


    /**
     * 构建聚合查询参数
     * @param termField 聚合字段
     * @param termSize 聚合字段大小
     * @param aggs 聚合参数
     */
    public JSONObject aggTerms(String termField, int termSize, String orderDir, String aggsKey, JSONObject aggs) {
        return new JSONObject()
                .fluentPut("terms", new JSONObject()
                        .fluentPut("field", termField)
                        .fluentPut("size", termSize)
                        .fluentPut("order", new JSONObject().fluentPut("_key", orderDir)))
                .fluentPut("aggs", new JSONObject().fluentPut(aggsKey, aggs));
    }

    /**
     * 构建聚合查询参数
     * @param key 聚合key
     * @param aggs 聚合参数
     */
    public JSONObject buildAggs(String key, JSONObject aggs) {
        return new JSONObject()
                .fluentPut(key, aggs);
    }


    public JSONObject buildAggs(List<Map<String, Object>> aggs) {
        JSONObject aggsJson = new JSONObject();
        aggs.forEach(agg -> aggsJson.putAll(agg));
        return aggsJson;
    }

    /**
     * 构建带 key 的 value_count 聚合
     */
    public JSONObject buildValueCountAgg(String field) {
        return new JSONObject().fluentPut("value_count", new JSONObject()
                        .fluentPut("field", field));
    }

    /**
     * 构建过滤器
     */
    public JSONObject buildFilterTerm(JSONObject termObj) {
        return buildFilter("term", termObj);
    }

    /**
     * 构建过滤器
     */
    public JSONObject buildFilterBool(String key, JSONObject... termObj) {
        return buildFilter("bool", new JSONObject().fluentPut(key, termObj));
    }

    /**
     * 构建过滤器
     */
    public JSONObject buildFilter(String subKey, JSONObject termObj) {
        return new JSONObject()
                .fluentPut("filter", new JSONObject()
                        .fluentPut(subKey, termObj));
    }
}
