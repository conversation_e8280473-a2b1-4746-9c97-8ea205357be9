package com.yunqu.tariff.servlet;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.cache.MapCache;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.tariff.base.AppBaseServlet;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.dao.CommonDao;
import com.yunqu.tariff.inf.DimDateService;
import com.yunqu.tariff.inf.TariffJobService;
import com.yunqu.tariff.inf.tariff.TariffDiffServiceExcutor;
import com.yunqu.tariff.listener.imports.ImportSysRegListener;
import com.yunqu.tariff.model.ImportSysRegModel;
import com.yunqu.tariff.model.RespResult;
import com.yunqu.tariff.service.TariffAuditProcessStorageService;
import com.yunqu.tariff.task.TariffInfoAnalysisTask;
import com.yunqu.tariff.task.TariffInfoCheckTask;
import com.yunqu.tariff.utils.BusiUtil;
import com.yunqu.tariff.utils.ReportUtils;
import com.yunqu.tariff.utils.TariffPublicUtil;
import org.apache.poi.ss.usermodel.*;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;
import org.slf4j.Logger;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.Part;
import java.io.InputStream;
import java.net.URLEncoder;
import java.sql.SQLException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
//@MultipartConfig
@WebServlet("/test/*")
public class TestServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;

	private Logger logger = CommonLogger.logger;


	public void actionForRunDiff() {
		String begin = getPara("begin");
		String end = getPara("end");
		EasyCalendar beginCalendar = EasyCalendar.newInstance(begin, "yyyyMMdd");
		EasyCalendar endCalendar = EasyCalendar.newInstance(end, "yyyyMMdd");
		while(!beginCalendar.getCalendar().getTime().before(endCalendar.getCalendar().getTime())) {
			TariffDiffServiceExcutor tariffDiffServiceExcutor = new TariffDiffServiceExcutor();
			String s = String.valueOf(beginCalendar.getDateInt());
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("dateId", s);
			tariffDiffServiceExcutor.execute("", jsonObject);
			beginCalendar.add(Calendar.DAY_OF_MONTH, 1);
		}
	}


	public void actionForInitReportedTariff() {
		String type = getPara("type");
		String reportNo = getPara("reportNo");
		if(StringUtils.isNotBlank(type)) {
			String reporter = getPara("reporter");
			new Thread(()-> TariffAuditProcessStorageService.getInstance().initReportedTariff(reporter, type, null, reportNo)).start();
		} else {
			String reporter = getPara("reporter");
			new Thread(()-> TariffAuditProcessStorageService.getInstance().initReportedTariff(reporter, null, null, reportNo)).start();
		}
	}

	public void actionForCheckTariffExist() {
		TariffInfoCheckTask tariffInfoCheckTask = new TariffInfoCheckTask();
		tariffInfoCheckTask.execute();
	}
	public void actionForAnalysisTariffByNlp() {
		String dateId = getRequest().getParameter("dateId");
		if(StringUtils.isBlank(dateId)) {
			Render.renderJson(getRequest(), getResponse(), EasyResult.fail());
			return;
		}
		TariffInfoAnalysisTask tariffInfoAnalysisTask = new TariffInfoAnalysisTask();
		tariffInfoAnalysisTask.analysis(dateId);
	}

    /**
     */
	public JSONObject actionForOption() {
		try {
			HttpServletRequest req = this.getRequest();
			JSONObject param = new JSONObject();
			param.put("command", "getAuditResult");
			param.put("dateId", req.getParameter("dateId"));

			TariffJobService service = new TariffJobService();
			JSONObject invoke = service.invoke(param);

			return invoke;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return RespResult.error();
		}
	}


	public JSONObject actionForFixAreaCode() {
		try {
		EasySQL sql = new EasySQL("select * from "+Constants.getBusiSchema()+".xty_tariff_record where 1=1");
		sql.appendLike("除","and area_desc like ? ");
		List<JSONObject> list = QueryFactory.getWriteQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		logger.info("查询地市表sql：{},param:{},list: {}",sql.getSQL(),sql.getParams(),list.size());
		for (JSONObject jsonObject : list) {
			handleAreaCode(jsonObject);
		}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}


	private void handleAreaCode(JSONObject jsonObject) {
		String recordId = jsonObject.getString("ID");
		EasyQuery query = QueryFactory.getWriteQuery();
		String unAreas= jsonObject.getString("AREA_DESC").replaceAll("除", "");
		EasySQL sql = new EasySQL("");
		sql.append("delete from " + Constants.getBusiSchema() + ".xty_tariff_areq where 1=1 ");
		sql.append(recordId, " and  TARIFF_RECORD_ID = ? ",false);
		sql.appendIn(unAreas.split(","), "AND PROVINCE_NAME");
        try {
			logger.info("删除地市表sql：{},param:{}",sql.getSQL(),sql.getParams());
            query.execute(sql.getSQL(), sql.getParams());

			EasySQL sql2 = new EasySQL("SELECT GROUP_CONCAT(AREA_CODE SEPARATOR ',') AREA_CODE FROM "+ Constants.getBusiSchema() + ".xty_tariff_areq where 1=1 ");
			sql2.append(recordId, "and TARIFF_RECORD_ID = ?",false);
			logger.info("查询地市表sql：{},param:{}",sql2.getSQL(),sql2.getParams());
			String areaCode = query.queryForString(sql2.getSQL(), sql2.getParams());
			logger.info("查询地市表结果：{}",areaCode);

			EasyRecord record = new EasyRecord(Constants.getBusiSchema()+".xty_tariff_record","ID");
			record.setPrimaryValues(recordId);
			record.set("APPLICABLE_AREA", areaCode);
			query.update(record);
        } catch (SQLException e) {
			logger.error("删除地市表数据异常", e);
            throw new RuntimeException(e);
        }
    }


	public JSONObject actionForBakTariff() {
		String date = this.getPara("date");
        logger.info("前端获取的时间为: {}", date);
		try {
			TariffJobService tariffJobService = new TariffJobService();
			JSONObject param = new JSONObject();
			param.put("command", "bakTariff");
			param.put("date", date);
			JSONObject invoke = tariffJobService.invoke(param);
			if(invoke != null){
				return EasyResult.ok();
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);

		}
		return EasyResult.fail("备案失败！");
	}

	/**
	 *获取该省下所有的地市个数
	 */
	private int getAreaCountByProvince(String provinceCode){
		try {
			EasyQuery easyQuery = QueryFactory.getReadQuery();
			EasySQL sql1 = new EasySQL("select  count(1) ");
			sql1.append(" from "+Constants.getBusiSchema()+".xty_tariff_area t1");
			sql1.append(" where 1=1");
			sql1.append("Y", "and t1.BUSI_ORDER = ?");
			sql1.append(provinceCode, " and t1.PROVINCE_CODE = ?  ");
			int values = easyQuery.queryForInt(sql1.getSQL(), sql1.getParams());
			return values;
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return 0;
	}

	/**
	 * 获取登录用户信息
     */
	public JSONObject actionForTestUser() {
		try {
			HttpServletRequest request = null;
			UserModel user = UserUtil.getUser(request);
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("data",user);
			return jsonObject;
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return null;
	}

	/**
	 * 修复备案者名称数据
     */
	public JSONObject actionForBuildReporterName() {
		try {
			UserModel user = UserUtil.getUser(getRequest());

			String startDate = getPara("startDate");
			String endDate = getPara("endDate");
			// 默认三个月
			if (StringUtils.isBlank(endDate)) {
				endDate = DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD);
			}
			if (StringUtils.isBlank(startDate)) {
				startDate = DateUtil.addMonth(DateUtil.TIME_FORMAT_YMD, endDate, -3);
			}
			EasySQL sql = new EasySQL();
			sql.append("select t1.* from "+ getTableName("XTY_TARIFF_RECORD") +" t1");
			sql.append("where 1=1");
			sql.append("and (t1.REPORTER_NAME is null or t1.REPORTER_NAME='')");
			sql.append(startDate +" 00:00:00", "and t1.CREATE_TIME>=?");
			sql.append(endDate +" 23:59:59", "and t1.CREATE_TIME<=?");
			EasyQuery query = QueryFactory.getWriteQuery();
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

			String updateSql = "update "+ getTableName("XTY_TARIFF_RECORD") +" set REPORTER_NAME=? where ID=?";
			List<Object[]> params = new ArrayList<>();
			for (int i=0; i<list.size(); i++) {
				JSONObject row = list.get(i);
				String id = row.getString("ID");
				String reporter = row.getString("REPORTER");

				String reporterName = BusiUtil.getReporterName(reporter, user.getSchemaName());
				params.add(new Object[] {reporterName, id});

				if (i%100 == 0) {
					query.executeBatch(updateSql, params);
					CommonUtil.sleep(1);
				}
			}
			if (params.size() > 0) {
				query.executeBatch(updateSql, params);
			}
			return EasyResult.ok();
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			return EasyResult.fail();
		}
	}

	public JSONObject actionForDimDate() {
		try {
			DimDateService dimDateService = new DimDateService();
			dimDateService.invoke(new JSONObject());
			return EasyResult.ok();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return EasyResult.fail();
		}
	}

	public static boolean isNonNegativeNumber(String input) {
		// 定义匹配非负数的正则表达式
		String regex = "\\d+(\\.\\d+)?";
		// 编译正则表达式
		Pattern pattern = Pattern.compile(regex);

		// 创建匹配器
		Matcher matcher = pattern.matcher(input);

		// 进行匹配
		return matcher.matches();
	}
	public JSONObject actionForTestChangRECORDAreaCode() {
		try {
			EasyQuery queryWrite = QueryFactory.getWriteQuery();
			EasyQuery queryRead = QueryFactory.getReadQuery();
			// 1.查询出所有资费信息中资费属性为全国，的省资费资费信息 的id
			EasySQL sql = new EasySQL("");
			sql.append("select id, APPLICABLE_AREA  from "+Constants.getBusiSchema()+".xty_tariff_record ");
			List<JSONObject> jsonObjects = queryRead.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			int size = jsonObjects.size();
            logger.info("共操作：{}条数据", size);
			// 2.循环进行数据操作

			jsonObjects.stream().forEach(json->{
				try {
					String applicableArea = json.getString("APPLICABLE_AREA");
					if(StringUtils.isNotBlank(applicableArea) && !"000".equals(applicableArea)){
						// 获取XTY_TARIFF_RECORD.APPLICABLE_AREA的新值
						String[] split = applicableArea.split(",");
						EasyQuery easyQuery = QueryFactory.getReadQuery();
						EasySQL sql1 = new EasySQL("select  GROUP_CONCAT(TARIFF_AREA_CODE SEPARATOR ',')  TARIFF_AREA_CODES");
						sql1.append(" from "+Constants.getBusiSchema()+".xty_tariff_area t1");
						sql1.append(" where 1=1");
						if (split.length > 1) {
							sql1.appendIn(split, " and t1.AREA_CODE   ");
						} else {
							sql1.append(split[0], " and t1.AREA_CODE = ?   ");
						}
						sql1.append(" order by TARIFF_AREA_CODE asc    ");
						String values = easyQuery.queryForString(sql1.getSQL(), sql1.getParams());
						if(StringUtils.isNotBlank(values)){
						// 更新XTY_TARIFF_RECORD表格
						EasyRecord record = new EasyRecord(Constants.getBusiSchema()+ ".XTY_TARIFF_RECORD", "ID");
						record.put("ID",json.getString("ID"));
						record.put("APPLICABLE_AREA",values);
						queryWrite.update(record);
						}
					}
				} catch (SQLException e) {
					logger.error(e.getMessage(),e);
				}
			});
			return EasyResult.ok("操作成功，共操作："+size+"条数据");
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return EasyResult.fail();
	}

	public JSONObject actionForTestChangRECORDHisAreaCode() {
		try {
			EasyQuery queryWrite = QueryFactory.getWriteQuery();
			EasyQuery queryRead = QueryFactory.getReadQuery();
			// 1.查询出所有资费信息中资费属性为全国，的省资费资费信息 的id
			EasySQL sql = new EasySQL("");
			sql.append("select id, APPLICABLE_AREA  from "+Constants.getBusiSchema()+".xty_tariff_record_his ");
			List<JSONObject> jsonObjects = queryRead.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			int size = jsonObjects.size();
            logger.info("共操作：{}条数据", size);
			// 2.循环进行数据操作

			jsonObjects.stream().forEach(json->{
				try {
					String applicableArea = json.getString("APPLICABLE_AREA");
					if(StringUtils.isNotBlank(applicableArea)&&!"000".equals(applicableArea)){
						// xty_tariff_record_his.APPLICABLE_AREA的新值
						String[] split = applicableArea.split(",");
						EasyQuery easyQuery = QueryFactory.getReadQuery();
						EasySQL sql1 = new EasySQL("select  GROUP_CONCAT(TARIFF_AREA_CODE SEPARATOR ',')  TARIFF_AREA_CODES");
						sql1.append(" from "+Constants.getBusiSchema()+".xty_tariff_area t1");
						sql1.append(" where 1=1");
						if (split.length > 1) {
							sql1.appendIn(split, " and t1.AREA_CODE   ");
						} else {
							sql1.append(split[0], " and t1.AREA_CODE = ?   ");
						}
						sql1.append(" order by TARIFF_AREA_CODE asc    ");
						String values = easyQuery.queryForString(sql1.getSQL(), sql1.getParams());
						// 更新xty_tariff_record_his表格
						if(StringUtils.isNotBlank(values)){
						EasyRecord record = new EasyRecord(Constants.getBusiSchema()+ ".xty_tariff_record_his", "ID");
						record.put("ID",json.getString("ID"));
						record.put("APPLICABLE_AREA",values);
						queryWrite.update(record);
						}
					}
				} catch (SQLException e) {
					logger.error(e.getMessage(),e);
					return;
				}
			});
			return EasyResult.ok("操作成功，共操作："+size+"条数据");
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return EasyResult.fail();
	}


	public JSONObject actionForImportSysReg() throws Exception {
		Part part = getFile("file");

		try (InputStream is = part.getInputStream();InputStream ins = part.getInputStream();Workbook workbook = WorkbookFactory.create(ins)) {
			// 1.进行文档合理性校验
			//workbook = WorkbookFactory.create(ins);
			Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表
			int rowCount = sheet.getPhysicalNumberOfRows(); // 获取总行数
			if(rowCount>999){
				this.getResponse().setHeader("errorMyMsg", URLEncoder.encode("文档数据超过999行,导入失败!", "utf-8"));
				logger.info("文档数据超过999行,导入失败!");
				return EasyResult.fail("文档数据超过999行,导入失败!");
			} else if (rowCount < 2) {
				this.getResponse().setHeader("errorMyMsg", URLEncoder.encode("请至少导入1行数据,导入失败!", "utf-8"));
				logger.info("请至少导入1行数据,导入失败!");
				return EasyResult.fail("请至少导入1行数据,导入失败!");
			}
			if (rowCount > 0) {
				Set excelHeaders = getExcelHeaders();
				Row firstRow = sheet.getRow(0); // 获取第一行
				int cellCount = firstRow.getPhysicalNumberOfCells(); // 获取单元格数
				if(cellCount < excelHeaders.size()){
					this.getResponse().setHeader("errorMyMsg", URLEncoder.encode("表头不全，导入失败！", "utf-8"));
                    logger.info("表头不全！传入表头数为:{}---应该为：{}", cellCount, excelHeaders.size());
					return EasyResult.fail("表头不全，导入失败！");
				}
				for (int i = 0; i < cellCount; i++) {
					Cell cell = firstRow.getCell(i);
					String cellValue = cell.getStringCellValue(); // 获取单元格的值
					if(StringUtils.isNotBlank(cellValue) && !excelHeaders.contains(cellValue)){
						this.getResponse().setHeader("errorMyMsg", URLEncoder.encode("表头["+cellValue+"]不合规，导入失败！", "utf-8"));
                        logger.info("表头数据【{}】不合规", cellValue);
						return EasyResult.fail("表头["+cellValue+"]不合规，导入失败！");
					}
				}
			}
			// 2.进行导入操作
			// 导入
			UserModel user = UserUtil.getUser(this.getRequest());
			EasyExcel.read(is, ImportSysRegModel.class, new ImportSysRegListener(getEntId(), getBusiOrderId(), getDbName(),user)).readCache(new MapCache()).sheet().doRead();
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return EasyResult.ok();
	}

	private static Set getExcelHeaders() {
		Set<String> set = new HashSet<>();
		set.add("省份");
		set.add("企业");
		set.add("报送主体");
		set.add("clientId");
		set.add("ip地址");
		set.add("企业接口人");
		set.add("联系方式");
		return set;
	}

}
