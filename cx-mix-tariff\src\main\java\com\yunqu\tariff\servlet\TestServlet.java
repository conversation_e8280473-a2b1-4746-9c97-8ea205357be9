package com.yunqu.tariff.servlet;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.cache.MapCache;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.tariff.base.AppBaseServlet;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.dao.CommonDao;
import com.yunqu.tariff.inf.DimDateService;
import com.yunqu.tariff.inf.TariffJobService;
import com.yunqu.tariff.inf.tariff.TariffDiffServiceExcutor;
import com.yunqu.tariff.listener.imports.ImportSysRegListener;
import com.yunqu.tariff.model.ImportSysRegModel;
import com.yunqu.tariff.model.RespResult;
import com.yunqu.tariff.service.TariffAuditProcessStorageService;
import com.yunqu.tariff.task.TariffInfoAnalysisTask;
import com.yunqu.tariff.task.TariffInfoCheckTask;
import com.yunqu.tariff.utils.BusiUtil;
import com.yunqu.tariff.utils.ReportUtils;
import com.yunqu.tariff.utils.TariffPublicUtil;
import org.apache.poi.ss.usermodel.*;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;
import org.slf4j.Logger;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.Part;
import java.io.InputStream;
import java.net.URLEncoder;
import java.sql.SQLException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
//@MultipartConfig
@WebServlet("/test/*")
public class TestServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;

	private Logger logger = CommonLogger.logger;



	public void actionForRunDiff() {
		String begin = getPara("begin");
		String end = getPara("end");
		EasyCalendar beginCalendar = EasyCalendar.newInstance(begin, "yyyyMMdd");
		EasyCalendar endCalendar = EasyCalendar.newInstance(end, "yyyyMMdd");
		while(!beginCalendar.getCalendar().getTime().before(endCalendar.getCalendar().getTime())) {
			TariffDiffServiceExcutor tariffDiffServiceExcutor = new TariffDiffServiceExcutor();
			String s = String.valueOf(beginCalendar.getDateInt());
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("dateId", s);
			tariffDiffServiceExcutor.execute("", jsonObject);
			beginCalendar.add(Calendar.DAY_OF_MONTH, 1);
		}
	}


	public void actionForInitReportedTariff() {
		String type = getPara("type");
		String reportNo = getPara("reportNo");
		if(StringUtils.isNotBlank(type)) {
			String reporter = getPara("reporter");
			new Thread(()-> TariffAuditProcessStorageService.getInstance().initReportedTariff(reporter, type, null, reportNo)).start();
		} else {
			String reporter = getPara("reporter");
			new Thread(()-> TariffAuditProcessStorageService.getInstance().initReportedTariff(reporter, null, null, reportNo)).start();
		}
	}

	public void actionForCheckTariffExist() {
		TariffInfoCheckTask tariffInfoCheckTask = new TariffInfoCheckTask();
		tariffInfoCheckTask.execute();
	}
	public void actionForAnalysisTariffByNlp() {
		String dateId = getRequest().getParameter("dateId");
		if(StringUtils.isBlank(dateId)) {
			Render.renderJson(getRequest(), getResponse(), EasyResult.fail());
			return;
		}
		TariffInfoAnalysisTask tariffInfoAnalysisTask = new TariffInfoAnalysisTask();
		tariffInfoAnalysisTask.analysis(dateId);
	}

    /**
     */
	public JSONObject actionForOption() {
		try {
			HttpServletRequest req = this.getRequest();
			JSONObject param = new JSONObject();
			param.put("command", "getAuditResult");
			param.put("dateId", req.getParameter("dateId"));

			TariffJobService service = new TariffJobService();
			JSONObject invoke = service.invoke(param);

			return invoke;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return RespResult.error();
		}
	}

	public JSONObject actionForBakTariff() {
		String date = this.getPara("date");
        logger.info("前端获取的时间为: {}", date);
		try {
			TariffJobService tariffJobService = new TariffJobService();
			JSONObject param = new JSONObject();
			param.put("command", "bakTariff");
			param.put("date", date);
			JSONObject invoke = tariffJobService.invoke(param);
			if(invoke != null){
				return EasyResult.ok();
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);

		}
		return EasyResult.fail("备案失败！");
	}
	public JSONObject actionForTestPublic() {
		TariffPublicUtil.delPublicVersionTariff("V20250611_1","20250611");
		return EasyResult.ok();
	}

//	public JSONObject  actionForTestTariffJob(){
//		EasyQuery query = getQuery();
//		List<String> reporters = getBakTariffReporter("20240529", "ycbusi_ekf", query);
//		try {
//			for (String reporter : reporters) {
//				String reporterPrefix = reporter.substring(0, reporter.length() - 2);
//
//				JSONArray data = new JSONArray();
//				EasySQL countSql = getBakTariffSql("20240529", reporterPrefix, "ycbusi_ekf", true);
//				int size = query.queryForInt(countSql.getSQL(), countSql.getParams());
//				// 一页多少条数据
//				int onePageSize = 1000;
//				// 总共多少页数据
//				long totalPage = size % onePageSize > 0 ? size / onePageSize + 1 : size / onePageSize;
//				logger.info("=====job=size:"+size+"==totalPage:"+totalPage+"==reporterPrefix:"+reporterPrefix);
//				EasySQL sql = getBakTariffSql("20240529", reporterPrefix, "ycbusi_ekf");
//				for (int i = 1; i <= totalPage; i++) {
//					List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), i, onePageSize, new JSONMapperImpl());
//					data.addAll(list);
//				}
//				logger.info("["+ reporterPrefix +"]待上传的数据量: "+ data.size());
//			}
//		}catch (Exception e){
//			logger.error(e.getMessage(),e);
//		}
//		return null;
//	}
//
//	private List<String> getBakTariffReporter(String date, String schema, EasyQuery query) {
//		try {
//			EasySQL sql = new EasySQL();
//			sql.append("select distinct t1.REPORTER");
//			sql.append("from "+ schema +".XTY_TARIFF_RECORD t1");
//			sql.append("where 1=1");
//			sql.append(date, "and t1.ONLINE_DAY<=?");
//			sql.append(date, "and (t1.OFFLINE_DAY>=? or t1.OFFLINE_DAY is null or t1.OFFLINE_DAY='')");
//			sql.append(Constants.TARIFF_STATUS_2, "and t1.STATUS <> ?", false);
//			return query.queryForList(sql.getSQL(), sql.getParams(), new EasyRowMapper<String>() {
//				@SuppressWarnings("unchecked")
//				@Override
//				public String mapRow(ResultSet rs, int arg1) {
//					try {
//						return rs.getString("REPORTER");
//					} catch (Exception e) {
//					}
//					return null;
//				}
//			});
//		} catch (Exception e) {
//			return null;
//		}
//	}
//
//	private EasySQL getBakTariffSql(String date, String reporter, String schema) {
//		return getBakTariffSql(date, reporter, schema, false);
//	}
//	private EasySQL getBakTariffSql(String date, String reporter, String schema, boolean isCount) {
//		EasySQL sql = new EasySQL();
//		if (isCount) {
//			sql.append("select count(1)");
//		} else {
//			sql.append("select  t1.id ,t1.REPORT_NO,t1.REPORTER,t1.NAME,t1.ONLINE_DAY,t1.OFFLINE_DAY,t1.TARIFF_ANOTHER_NAME,   GROUP_CONCAT(t2.AREA_CODE SEPARATOR ',')  APPLICABLE_AREA ");
//		}
//		sql.append("from "+ schema +".XTY_TARIFF_RECORD t1");
//		sql.append(" LEFT JOIN "+ schema +".xty_tariff_areq t2 on t1.id = t2.TARIFF_RECORD_ID ");
//		sql.append("where 1=1");
//		sql.appendRLike(reporter, "and t1.REPORTER like ?");
//		sql.append(date, "and t1.ONLINE_DAY<=?");
//		sql.append(date, "and (t1.OFFLINE_DAY > ? or t1.OFFLINE_DAY is null or t1.OFFLINE_DAY='')");
//		sql.append(Constants.TARIFF_STATUS_2, "and t1.STATUS != ?", false);
//		if (!isCount) {
//			sql.append(" GROUP BY t1.id ,t1.REPORT_NO,t1.REPORTER,t1.NAME,t1.ONLINE_DAY,t1.OFFLINE_DAY,t1.TARIFF_ANOTHER_NAME ");
//		}
//		return sql;
//	}
	/*
	  根据适用省市编辑地区描述字段
	 */
	/*public JSONObject actionForTestSetValueAreaDesc() {
		try {
			EasyQuery easyQuery = QueryFactory.getReadQuery();
			EasySQL sql = new EasySQL();
			sql.append("SELECT  id,CREATE_TIME  from  ");
			sql.append(getDbName() + ".XTY_TARIFF_RECORD ");
			List<JSONObject> list = easyQuery.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
*//*--------------------------------------循环开始----------------------------------------------*//*
			if(list.size()>0){
				// 获取适用地区树
				for (int a = 0; a < list.size(); a++) {

						JSONObject object = new JSONObject();
						try {
							EasySQL sql2 = new EasySQL();
							sql2.append("SELECT distinct PROVINCE_CODE  VALUE, PROVINCE_NAME  LABEL   from  ");
							sql2.append(getDbName() + ".XTY_TARIFF_AREQ ");
							sql2.append(" where 1 = 1");
							sql2.append( list.get(a).getString("ID")," and TARIFF_RECORD_ID = ?");
							List<JSONObject> provinceList = easyQuery.queryForList(sql2.getSQL(), sql2.getParams(), new JSONMapperImpl());

							String provinceNames = "";// 省市名称字段
							int provinceCount = provinceList.size();// 省个数
							int countArea = 0;
							// 获取省下的市信息，组织成树
							for (int i = 0; i < provinceCount; i++) {
								if(i == provinceCount-1){
									provinceNames = provinceNames+provinceList.get(i).getString("LABEL");
								}else {
									provinceNames = provinceNames+provinceList.get(i).getString("LABEL")+"，";
								}

								try {
									EasySQL sql3 = new EasySQL();
									sql3.append("SELECT AREA_CODE  VALUE, AREA_NAME  LABEL   from  ");
									sql3.append(getDbName() + ".XTY_TARIFF_AREQ ");
									sql3.append(" where 1 = 1");
									sql3.append( list.get(a).getString("ID")," and TARIFF_RECORD_ID = ?");
									sql3.append( provinceList.get(i).getString("VALUE")," and PROVINCE_CODE = ?");
									List<JSONObject> areaList = easyQuery.queryForList(sql3.getSQL(), sql3.getParams(), new JSONMapperImpl());
									countArea = areaList.size()+countArea;
									object.put("TARIFF_ATTR","1"); // 默认全国
									// 进行适用省份展示名称设计
									if(provinceCount == 1){
										if("000".equals(provinceList.get(i).getString("VALUE"))){
											provinceNames = "全国";
											object.put("TARIFF_ATTR","1"); // 全国
										}else {
											if(areaList.size() == 1){
												object.put("TARIFF_ATTR","3"); // 本地
											}else {
												object.put("TARIFF_ATTR","2"); // 省内
											}
											// 根据省获取该省下所有地市个数
											int allAreaCountByProvince = getAreaCountByProvince(provinceList.get(i).getString("VALUE"));
											if(allAreaCountByProvince == areaList.size() && allAreaCountByProvince != 1){ // 为该省全部地市
												provinceNames = provinceNames+"全部地市";// 一省全地市
											}else {
												provinceNames = provinceNames+": ";
												for (int i1 = 0; i1 < areaList.size(); i1++) {
													if(i1 == areaList.size()-1){
														provinceNames = provinceNames + areaList.get(i1).getString("LABEL");
													}else {
														provinceNames = provinceNames + areaList.get(i1).getString("LABEL")+"、";
													}
												}
											}
										}
									}

								} catch (Exception e) {
									logger.error(e.getMessage(),e);
								}
							} // provinceList循环结束
							object.put("AREA_DESC",provinceNames+"等"+provinceCount+"个省，"+countArea+"个地市");
							if(provinceCount == 1){
								object.put("AREA_DESC",provinceNames);
							}

							String id = list.get(a).getString("ID") ;
							String tableName = getTableName("XTY_TARIFF_RECORD");
							EasyRecord record = new EasyRecord(tableName, "ID");
							EasyQuery writeQuery = QueryFactory.getWriteQuery();
							record.setColumns(object);
							record. setPrimaryValues(id);
							// 执行更新适用地区描述和资费属性
							writeQuery.update(record);
						} catch (Exception e) {
							logger.error(e.getMessage(),e);
						}
					 // list循环结束
				}

			}

*//*--------------------------------------循环结束----------------------------------------------*//*
			return EasyResult.ok("查询的数据条数为："+list.size()+"。更新结束，更新成功");
		} catch (SQLException e) {
			logger.error(e.getMessage(),e);
		}
		return EasyResult.fail("更新失败！");
	}*/

	/**
	 *获取该省下所有的地市个数
	 */
	private int getAreaCountByProvince(String provinceCode){
		try {
			EasyQuery easyQuery = QueryFactory.getReadQuery();
			EasySQL sql1 = new EasySQL("select  count(1) ");
			sql1.append(" from "+Constants.getBusiSchema()+".xty_tariff_area t1");
			sql1.append(" where 1=1");
			sql1.append("Y", "and t1.BUSI_ORDER = ?");
			sql1.append(provinceCode, " and t1.PROVINCE_CODE = ?  ");
			int values = easyQuery.queryForInt(sql1.getSQL(), sql1.getParams());
			return values;
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return 0;
	}

	/**
	 * 获取登录用户信息
     */
	public JSONObject actionForTestUser() {
		try {
			HttpServletRequest request = null;
			UserModel user = UserUtil.getUser(request);
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("data",user);
			return jsonObject;
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return null;
	}

	/**
	 * 修复备案者名称数据
     */
	public JSONObject actionForBuildReporterName() {
		try {
			UserModel user = UserUtil.getUser(getRequest());

			String startDate = getPara("startDate");
			String endDate = getPara("endDate");
			// 默认三个月
			if (StringUtils.isBlank(endDate)) {
				endDate = DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD);
			}
			if (StringUtils.isBlank(startDate)) {
				startDate = DateUtil.addMonth(DateUtil.TIME_FORMAT_YMD, endDate, -3);
			}
			EasySQL sql = new EasySQL();
			sql.append("select t1.* from "+ getTableName("XTY_TARIFF_RECORD") +" t1");
			sql.append("where 1=1");
			sql.append("and (t1.REPORTER_NAME is null or t1.REPORTER_NAME='')");
			sql.append(startDate +" 00:00:00", "and t1.CREATE_TIME>=?");
			sql.append(endDate +" 23:59:59", "and t1.CREATE_TIME<=?");
			EasyQuery query = QueryFactory.getWriteQuery();
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

			String updateSql = "update "+ getTableName("XTY_TARIFF_RECORD") +" set REPORTER_NAME=? where ID=?";
			List<Object[]> params = new ArrayList<>();
			for (int i=0; i<list.size(); i++) {
				JSONObject row = list.get(i);
				String id = row.getString("ID");
				String reporter = row.getString("REPORTER");

				String reporterName = BusiUtil.getReporterName(reporter, user.getSchemaName());
				params.add(new Object[] {reporterName, id});

				if (i%100 == 0) {
					query.executeBatch(updateSql, params);
					CommonUtil.sleep(1);
				}
			}
			if (params.size() > 0) {
				query.executeBatch(updateSql, params);
			}
			return EasyResult.ok();
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			return EasyResult.fail();
		}
	}

	public JSONObject actionForDimDate() {
		try {
			DimDateService dimDateService = new DimDateService();
			dimDateService.invoke(new JSONObject());
			return EasyResult.ok();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return EasyResult.fail();
		}
	}

	public static void main(String[] args) throws Exception {
		String aa = "";
	System.out.println("chag:"+aa.length());
		System.out.println("csnv::"+String.format("%04d", 456));
		double a = 0;
		double c = 0;
		for (int i = 2; i <= 30; i++) {
			a= a+ (double)1/i;
			c= c+i;
		}
		double b = (double)a/29;
		System.out.println("bbbb"+b);
		System.out.println("cccc"+(double)29/c);

		String stime = "2024-04";
		String etime = "2024-04";
		stime = stime.replace("-", "");
		etime = etime.replace("-", "");
		stime = stime+"01";
		etime = ReportUtils.getCurrMonthEndDay(etime);
		stime = stime.substring(0, 4) +"-"+ stime.substring(4, 6) +"-"+ stime.substring(6, 8);
		etime = etime.substring(0, 4) +"-"+ etime.substring(4, 6) +"-"+ etime.substring(6, 8);


		System.out.println(stime);
		System.out.println(etime);


		String currDate = DateUtil.getCurrentDateStr("yyyyMMdd");
		String date = DateUtil.addDay("yyyyMMdd", currDate, 0);
		System.out.println(date);


		String ftpFileName = "/home/<USER>/2024/04/27/sichuan002-20240427-result.zip";
		String sftpFileName = ftpFileName.substring(0,ftpFileName.lastIndexOf("/"));


		System.out.println(sftpFileName);

	}
	public static boolean isNonNegativeNumber(String input) {
		// 定义匹配非负数的正则表达式
		String regex = "\\d+(\\.\\d+)?";
		// 编译正则表达式
		Pattern pattern = Pattern.compile(regex);

		// 创建匹配器
		Matcher matcher = pattern.matcher(input);

		// 进行匹配
		return matcher.matches();
	}
	/**
	 * 资费属性和适用地区修改（省企业）
	 */
	/*public JSONObject actionForTestChangArea() {
		try {
			EasyQuery queryWrite = QueryFactory.getWriteQuery();
			EasyQuery queryRead = QueryFactory.getReadQuery();
			// 1.查询出所有资费信息中资费属性为全国，的省资费资费信息 的id
			EasySQL sql = new EasySQL();
			sql.append("select  ID, REPORT_OBJ, REPORT_NO from "+ getTableName("XTY_TARIFF_RECORD") );
			sql.append(" where 1=1");
			sql.append(" and TARIFF_ATTR = '1'"); // 资费属性为1
			sql.append(" and PROVINCE != '' "); // 为省的资费
			sql.append(" and REPORT_OBJ != 'group' "); // 为省的资费
			List<JSONObject> jsonObjects = queryRead.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			int size = jsonObjects.size();
			logger.info("共操作："+size+"条数据");
			// 2.循环进行数据操作
			if(size == 0){
				return EasyResult.fail("没有资费属性为全国的省资费资费信息");
			}
			jsonObjects.stream().forEach(json->{
				try {
					// 2.1 删除对应的关联地区表信息
					EasySQL sql2 = new EasySQL();
					sql2.append("delete from "+ getDbName() +".xty_tariff_areq ");
					sql2.append(" where 1=1 ");
					sql2.append(json.getString("ID"),"and TARIFF_RECORD_ID = ?");
					queryWrite.execute(sql2.getSQL(), sql2.getParams());
					// 2.2 查询该省的省编码，省名称，市编码串
					EasySQL sql3 = new EasySQL();
					sql3.append("select  PROVINCE_CODE, PROVINCE_NAME from CC_PROVINCE" );
					sql3.append(" where 1=1");
					sql3.append(json.getString("REPORT_OBJ")," and pinyin = ?");
					JSONObject provinceInfo = queryRead.queryForRow(sql3.getSQL(), sql3.getParams(), new JSONMapperImpl());

					EasySQL sql4 = new EasySQL("select  GROUP_CONCAT(AREA_CODE SEPARATOR ',')  a");
					sql4.append(" from CC_AREA t1");
					sql4.append(" where 1=1");
					sql4.append("Y", "and t1.BUSI_ORDER = ?");
					sql4.append(provinceInfo.getString("PROVINCE_CODE"), "and t1.PROVINCE_CODE = ?");
					sql4.append("order by AREA_CODE+0 asc");
					String areaInfo = queryRead.queryForString(sql4.getSQL(), sql4.getParams());

					// 2.3修改地区描述、省编码、市编码、地区选择类型
					EasyRecord record = new EasyRecord(getDbName()+ ".XTY_TARIFF_RECORD", "ID");
					record.put("ID", json.getString("ID"));
					record.put("AREA_SELECT_TYPE", "2");
					record.put("AREA_DESC", provinceInfo.getString("PROVINCE_NAME")+"全部地市");
					record.put("APPLICABLE_PROVINCE",provinceInfo.getString("PROVINCE_CODE") );
					record.put("APPLICABLE_AREA",areaInfo);
					queryWrite.update(record);
					// 2.4重建关联的适用地区表
					saveAreaInfo(areaInfo, getDbName(), queryWrite, json.getString("ID"), json.getString("REPORT_NO"));
				} catch (SQLException e) {
					logger.error(e.getMessage(),e);
				}
			});
			return EasyResult.ok("操作成功，共操作："+size+"条数据");
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return EasyResult.fail();
	}


	private void saveAreaInfo(String areaInfos, String schema, EasyQuery query, String tariffId, String reporterNo)
			throws SQLException {
		// 先删除历史的区域信息
		EasyRecord areqRecord = new EasyRecord(schema +".XTY_TARIFF_AREQ", "TARIFF_RECORD_ID");
		areqRecord.put("TARIFF_RECORD_ID", tariffId);
		query.deleteById(areqRecord);
		// 获取所有区域信息
		String applicableArea = areaInfos;
		// 如果所选地区是000，表示全国通用
		if (Constants.AREA_CODE_ALL.equals(applicableArea)) {
			EasyRecord addAreqRecord = new EasyRecord(schema +".XTY_TARIFF_AREQ", "ID");
			addAreqRecord.put("ID", IDGenerator.getDefaultNUMID());
			addAreqRecord.put("TARIFF_RECORD_ID", tariffId);
			addAreqRecord.put("AREA_CODE", Constants.AREA_CODE_ALL);
			addAreqRecord.put("AREA_NAME", "全国");
			addAreqRecord.put("PROVINCE_CODE", Constants.AREA_CODE_ALL);
			addAreqRecord.put("PROVINCE_NAME", "全国");
			addAreqRecord.put("REPORT_NO", reporterNo);
			query.save(addAreqRecord);
			return;
		}
		String[] applicableAreaArray = applicableArea.split(",");
		Set<String> areaCodeSet = new HashSet<>();
		// 去掉前面的零，保持和数据库的区号数据一致
		for (String areaCode:applicableAreaArray) {
			areaCodeSet.add(areaCode);
			areaCodeSet.add(CommonUtil.parseInt(areaCode) + "");
		}
		EasySQL sql = new EasySQL();
		sql.append("select t1.AREA_CODE, t1.AREA_NAME, t2.PROVINCE_CODE, t2.PROVINCE_NAME from CC_AREA t1");
		sql.append("left join CC_PROVINCE t2 on t2.PROVINCE_CODE=t1.PROVINCE_CODE");
		sql.append("where 1=1");
		sql.appendIn(areaCodeSet.toArray(new String[areaCodeSet.size()]), "and t1.AREA_CODE");
		List<JSONObject> areaInfoList = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		for (JSONObject areaInfo:areaInfoList) {
			EasyRecord addAreqRecord = new EasyRecord(schema +".XTY_TARIFF_AREQ", "ID");
			addAreqRecord.put("ID", IDGenerator.getDefaultNUMID());
			addAreqRecord.put("TARIFF_RECORD_ID", tariffId);
			addAreqRecord.put("AREA_CODE", areaInfo.getString("AREA_CODE"));
			addAreqRecord.put("AREA_NAME", areaInfo.getString("AREA_NAME"));
			addAreqRecord.put("PROVINCE_CODE", areaInfo.getString("PROVINCE_CODE"));
			addAreqRecord.put("PROVINCE_NAME", areaInfo.getString("PROVINCE_NAME"));
			addAreqRecord.put("REPORT_NO", reporterNo);
			query.save(addAreqRecord);
		}
	}
*/

	public JSONObject actionForTestChangRECORDAreaCode() {
		try {
			EasyQuery queryWrite = QueryFactory.getWriteQuery();
			EasyQuery queryRead = QueryFactory.getReadQuery();
			// 1.查询出所有资费信息中资费属性为全国，的省资费资费信息 的id
			EasySQL sql = new EasySQL("");
			sql.append("select id, APPLICABLE_AREA  from "+Constants.getBusiSchema()+".xty_tariff_record ");
			List<JSONObject> jsonObjects = queryRead.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			int size = jsonObjects.size();
            logger.info("共操作：{}条数据", size);
			// 2.循环进行数据操作

			jsonObjects.stream().forEach(json->{
				try {
					String applicableArea = json.getString("APPLICABLE_AREA");
					if(StringUtils.isNotBlank(applicableArea) && !"000".equals(applicableArea)){
						// 获取XTY_TARIFF_RECORD.APPLICABLE_AREA的新值
						String[] split = applicableArea.split(",");
						EasyQuery easyQuery = QueryFactory.getReadQuery();
						EasySQL sql1 = new EasySQL("select  GROUP_CONCAT(TARIFF_AREA_CODE SEPARATOR ',')  TARIFF_AREA_CODES");
						sql1.append(" from "+Constants.getBusiSchema()+".xty_tariff_area t1");
						sql1.append(" where 1=1");
						if (split.length > 1) {
							sql1.appendIn(split, " and t1.AREA_CODE   ");
						} else {
							sql1.append(split[0], " and t1.AREA_CODE = ?   ");
						}
						sql1.append(" order by TARIFF_AREA_CODE asc    ");
						String values = easyQuery.queryForString(sql1.getSQL(), sql1.getParams());
						if(StringUtils.isNotBlank(values)){
						// 更新XTY_TARIFF_RECORD表格
						EasyRecord record = new EasyRecord(Constants.getBusiSchema()+ ".XTY_TARIFF_RECORD", "ID");
						record.put("ID",json.getString("ID"));
						record.put("APPLICABLE_AREA",values);
						queryWrite.update(record);
						}
					}
				} catch (SQLException e) {
					logger.error(e.getMessage(),e);
				}
			});
			return EasyResult.ok("操作成功，共操作："+size+"条数据");
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return EasyResult.fail();
	}

	public JSONObject actionForTestChangRECORDHisAreaCode() {
		try {
			EasyQuery queryWrite = QueryFactory.getWriteQuery();
			EasyQuery queryRead = QueryFactory.getReadQuery();
			// 1.查询出所有资费信息中资费属性为全国，的省资费资费信息 的id
			EasySQL sql = new EasySQL("");
			sql.append("select id, APPLICABLE_AREA  from "+Constants.getBusiSchema()+".xty_tariff_record_his ");
			List<JSONObject> jsonObjects = queryRead.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			int size = jsonObjects.size();
            logger.info("共操作：{}条数据", size);
			// 2.循环进行数据操作

			jsonObjects.stream().forEach(json->{
				try {
					String applicableArea = json.getString("APPLICABLE_AREA");
					if(StringUtils.isNotBlank(applicableArea)&&!"000".equals(applicableArea)){
						// xty_tariff_record_his.APPLICABLE_AREA的新值
						String[] split = applicableArea.split(",");
						EasyQuery easyQuery = QueryFactory.getReadQuery();
						EasySQL sql1 = new EasySQL("select  GROUP_CONCAT(TARIFF_AREA_CODE SEPARATOR ',')  TARIFF_AREA_CODES");
						sql1.append(" from "+Constants.getBusiSchema()+".xty_tariff_area t1");
						sql1.append(" where 1=1");
						if (split.length > 1) {
							sql1.appendIn(split, " and t1.AREA_CODE   ");
						} else {
							sql1.append(split[0], " and t1.AREA_CODE = ?   ");
						}
						sql1.append(" order by TARIFF_AREA_CODE asc    ");
						String values = easyQuery.queryForString(sql1.getSQL(), sql1.getParams());
						// 更新xty_tariff_record_his表格
						if(StringUtils.isNotBlank(values)){
						EasyRecord record = new EasyRecord(Constants.getBusiSchema()+ ".xty_tariff_record_his", "ID");
						record.put("ID",json.getString("ID"));
						record.put("APPLICABLE_AREA",values);
						queryWrite.update(record);
						}
					}
				} catch (SQLException e) {
					logger.error(e.getMessage(),e);
					return;
				}
			});
			return EasyResult.ok("操作成功，共操作："+size+"条数据");
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return EasyResult.fail();
	}


	public JSONObject actionForImportSysReg() throws Exception {
		Part part = getFile("file");


		try (InputStream is = part.getInputStream();InputStream ins = part.getInputStream();Workbook workbook = WorkbookFactory.create(ins)) {
			// 1.进行文档合理性校验
			//workbook = WorkbookFactory.create(ins);
			Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表
			int rowCount = sheet.getPhysicalNumberOfRows(); // 获取总行数
			if(rowCount>999){
				this.getResponse().setHeader("errorMyMsg", URLEncoder.encode("文档数据超过999行,导入失败!", "utf-8"));
				logger.info("文档数据超过999行,导入失败!");
				return EasyResult.fail("文档数据超过999行,导入失败!");
			} else if (rowCount < 2) {
				this.getResponse().setHeader("errorMyMsg", URLEncoder.encode("请至少导入1行数据,导入失败!", "utf-8"));
				logger.info("请至少导入1行数据,导入失败!");
				return EasyResult.fail("请至少导入1行数据,导入失败!");
			}
			if (rowCount > 0) {
				Set excelHeaders = getExcelHeaders();
				Row firstRow = sheet.getRow(0); // 获取第一行
				int cellCount = firstRow.getPhysicalNumberOfCells(); // 获取单元格数
				if(cellCount < excelHeaders.size()){
					this.getResponse().setHeader("errorMyMsg", URLEncoder.encode("表头不全，导入失败！", "utf-8"));
                    logger.info("表头不全！传入表头数为:{}---应该为：{}", cellCount, excelHeaders.size());
					return EasyResult.fail("表头不全，导入失败！");
				}
				for (int i = 0; i < cellCount; i++) {
					Cell cell = firstRow.getCell(i);
					String cellValue = cell.getStringCellValue(); // 获取单元格的值
					if(StringUtils.isNotBlank(cellValue) && !excelHeaders.contains(cellValue)){
						this.getResponse().setHeader("errorMyMsg", URLEncoder.encode("表头["+cellValue+"]不合规，导入失败！", "utf-8"));
                        logger.info("表头数据【{}】不合规", cellValue);
						return EasyResult.fail("表头["+cellValue+"]不合规，导入失败！");
					}
				}
			}
			// 2.进行导入操作
			// 导入
			UserModel user = UserUtil.getUser(this.getRequest());
			EasyExcel.read(is, ImportSysRegModel.class, new ImportSysRegListener(getEntId(), getBusiOrderId(), getDbName(),user)).readCache(new MapCache()).sheet().doRead();
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return EasyResult.ok();
	}
//
//	public JSONObject actionForImportdeptIndex() throws Exception {
//		EasyQuery query = getQuery();
//		EasySQL sql = new EasySQL("select * from cc_province");
//		List<String> ents =  Arrays.asList("1", "2", "3","5");
//		List<JSONObject> provinceData = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
//		List<Object[]> addParams = new ArrayList<>();
//		String batchSql = "insert into "+getTableName("xty_dept_index_config")+"(ID,ENT_ID,BUSI_ORDER_ID,CREATE_TIME,CREATE_ACC,CREATE_DEPT,PROVINCE_CODE,PROVINCE_NAME,PROVINCE_PINYIN,ENT) " +
//				" values(?,?,?,?,?,?,?,?,?,?)";
//		for(JSONObject province:provinceData){
//			for(String ent:ents){
//				addParams.add(new Object[]{RandomKit.uniqueStr(),"1000","83880897233159997654981", EasyDate.getCurrentDateString(),"system","500502500",province.getString("PROVINCE_CODE"),province.getString("PROVINCE_NAME"),province.getString("PINYIN"),ent});
//			}
//		}
//		//异步执行
//		CompletableFuture.runAsync(() -> {
//			try {
//				getQuery().executeBatch(batchSql, addParams);
//			} catch (SQLException e) {
//				logger.error("异步批量执行sql异常-->"+e.getMessage(), e);
//			}
//		});
//		return EasyResult.ok();
//	}


	private static Set getExcelHeaders() {
		Set<String> set = new HashSet<>();
		set.add("省份");
		set.add("企业");
		set.add("报送主体");
		set.add("clientId");
		set.add("ip地址");
		set.add("企业接口人");
		set.add("联系方式");
		return set;
	}

}
