package com.yunqu.tariff.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.model.Yqlogger;
import com.yq.busi.common.service.BaseService;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.LogUtil;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.container.XtyTariffContainer;
import com.yunqu.tariff.handler.tariff.TariffReportedHandler;
import com.yunqu.tariff.model.RespResult;
import com.yunqu.tariff.thread.BakDataThreadManager;
import com.yunqu.tariff.thread.BakThreadManager;
import com.yunqu.tariff.utils.BusiUtil;
import com.yunqu.xty.commonex.kit.ElasticsearchKit;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.slf4j.Logger;

import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 资费相关处理
 */
public class TariffService extends BaseService {

    private static final Map<String, Object> LOCKMAP = new ConcurrentHashMap<>();

    private static Logger logger = CommonLogger.getLogger();

    // 添加TariffBakInfoInitService引用
    private TariffBakInfoInitService bakInfoInitService = new TariffBakInfoInitService();

    public JSONObject addOrUpdate(JSONObject req, UserModel user, String source) {
        Set<String> errorMessage = new HashSet<>();
        try {
            logger.info("开始操作资费信息: {}", req.toJSONString());
            /* 1、获取来自接口或网页手工录入的资费 */
            JSONObject data = req.getJSONObject("data");
            String authConfig = req.getString("BAKUP");

            String seqNo = data.getString("seq_no");
            // 校验数据
            if (StringUtils.isBlank(seqNo)) {
                errorMessage.add("序列号不能为空");
            }
            String reporter = data.getString("reporter");
            // 必须以当前账号备案主体为开头的备案主体信息才有权限
            if (StringUtils.isBlank(reporter)) {
                errorMessage.add("报送主体不能为空");
            } else {
                if (reporter.length() < 3) {
                    errorMessage.add("报送主体不能小于3位");
                }
            }

            // A（新增）、M（修改）、D（删除）C（校验）
            String actionType = data.getString("action_type");
            // 设置来源 1-人工填报 2-接口填报
            data.put("CREATE_TYPE", source);
            data.put("areaFlag", false); // 设置标识-适用地区不为排除操作
            // 进行适用地区格式转换（！类型）
            String applicable_area = data.getString("applicable_area");
            if (StringUtils.isNotBlank(applicable_area) && applicable_area.contains("!")) { // 为排除省份操作-导入、接口
                logger.info("进行适用地区格式转换（！类型）"+applicable_area);
                applicable_area = applicable_area.replaceAll("!", "");
                String provinceCodes = getProvinceCodes(applicable_area.split(","));
                String provinceNames = "除" + getProvinceNames(provinceCodes.split(","));
                String[] split = provinceCodes.split(",");
                String values = getAreaCodes(split);
                data.put("applicable_area_mapping", applicable_area);
                data.put("applicable_area", values);
                logger.info("applicable_area_mapping："+applicable_area);
                logger.info("applicable_area："+values);
                data.put("applicable_province", provinceCodes);
                data.put("area_desc", provinceNames); // 排除省
                data.put("areaFlag", true); // 适用地区为排除操作
            }
            if ("3".equals(data.getString("area_select_type"))) { // web排除操作--获取市编码集
                String applicableProvince = data.getString("applicable_province");
                String[] provinceSplit = applicableProvince.split(",");
                String provinceNames = "除" + getProvinceNames(provinceSplit);
                String values = getAreaCodes(provinceSplit);
                String valuesMapping = getAreaCodes2(provinceSplit);
                data.put("applicable_area", values);
                data.put("applicable_area_mapping", valuesMapping);
                logger.info("applicable_area_mapping："+valuesMapping);
                logger.info("applicable_area："+values);
                data.put("area_desc", provinceNames);
                data.put("areaFlag", true); // 适用地区为排除操作
            }
            JSONObject historyInfo = findNewInfo(data, user);
            if ( (Constants.ACTION_TYPE_EDIT.equals(actionType) || Constants.ACTION_TYPE_EDIT_VERIFY.equals(actionType)) && historyInfo == null) {
                logger.error("找不到需要修改的记录");
                errorMessage.add("找不到需要修改的记录！");

                List<String> editRequiredFields = Arrays.asList("seq_no", "reporter", "action_type", "origin_report_no");
                for (String field : editRequiredFields) {
                    String val = data.getString(field);
                    String fieldZhName = FieldZhMap.get(field);
                    if (!StringUtils.isNotBlank(val)) {
                        logger.error("'{}'必填参数为空", fieldZhName);
                        errorMessage.add("'" + fieldZhName + "'必填参数为空");
                    }
                }
                return RespResult.error("402", String.join(", ", errorMessage));
            }
            if (Constants.ACTION_TYPE_DEL.equals(actionType) && historyInfo == null) {
                logger.error("找不到需要删除的记录");
                errorMessage.add("找不到需要删除的记录！");

                List<String> delRequiredFields = Arrays.asList("seq_no", "reporter", "action_type", "origin_report_no");
                for (String field : delRequiredFields) {
                    String val = data.getString(field);
                    String fieldZhName = FieldZhMap.get(field);
                    if (!StringUtils.isNotBlank(val)) {
                        logger.error("'{}'必填参数为空", fieldZhName);
                        errorMessage.add("'" + fieldZhName + "'必填参数为空");
                    }
                }
                return RespResult.error("402", String.join(", ", errorMessage));
            }
            Set<String> errorList1 = new HashSet<>();
            Set<String> errorList2 = new HashSet<>();
            try {
                /* 2、校验字段完整性 */
                errorList1 = checkFieldComplete(data, user, historyInfo,authConfig);
                // 添加修改内容字段
                errorList2 = addModification(authConfig, data, actionType, historyInfo);
            } catch (Exception e) {
                logger.error("资费校验代码报错"+e.getMessage(),e);
                throw e;
            }

            errorMessage.addAll(errorList1);
            errorMessage.addAll(errorList2);

            if (!errorMessage.isEmpty()) {
                data.put("errorMsgList", errorMessage);
                return RespResult.error("402", String.join(", ", errorMessage));
            }

            // 设置个性化的字段
            setCustParam(user, data);
            /* 3、判断资费是否存在历史版本，判断是否有修改，如未修改，直接返回 */
            if ((Constants.ACTION_TYPE_ADD.equals(actionType) || Constants.ACTION_TYPE_VERIFY.equals(actionType)) && historyInfo != null) {
                return RespResult.error("402", "【origin_report_no】="+data.getString("origin_report_no")+"数据已存在，请去掉传参后重试");
            }

            // 获取备案号
            int versionNo = -1;
            String reportKey = null;
            if (historyInfo != null) {
                versionNo = historyInfo.getIntValue("VERSION_NO");
                reportKey = historyInfo.getString("REPORT_KEY");
            }
            // 获取备案号
            String reporterNo = data.getString("report_no");
            if (!Constants.ACTION_TYPE_VERIFY.equals(actionType)) {
                reporterNo = getReporterNo(data, actionType, versionNo, reportKey, reporter);
                data.put("reporter_no", reporterNo);
            }
            // 保存数据
            try {
                // 保存数据
                saveTariff(data, historyInfo, user, source,authConfig);
            } catch (SQLException e) {
                // 捕获唯一索引冲突异常
                if (isDuplicateKeyError(e)) {
                    logger.error("系统繁忙,请重试 方案编号/序列号重复: {}", reporterNo, e);
                    return RespResult.error("402", "系统繁忙,请重试");
                }
                // 其他SQL异常继续抛出
                throw e;
            }
            /* 4、将该资费的历史版本全部设置为历史版本、将当前版本设置为最新版本 */
            // 已经写了，在saveTariff方法里边
            /* 5、写入本次修改字段明细 */
            // 已经写了，在saveTariff方法里边

            //A（新增）、M（修改）、D（删除）C（校验）
            if (Constants.ACTION_TYPE_ADD.equals(actionType)) {
                return RespResult.ok("新增成功", reporterNo);
            } else if (Constants.ACTION_TYPE_EDIT.equals(actionType)) {
                return RespResult.ok("修改成功");
            } else if (Constants.ACTION_TYPE_DEL.equals(actionType)) {
                return RespResult.ok("删除成功");
            } else if (Constants.ACTION_TYPE_VERIFY.equals(actionType)) {
                return RespResult.ok("新增校验成功");
            } else if (Constants.ACTION_TYPE_EDIT_VERIFY.equals(actionType)) {
                return RespResult.ok("修改校验成功");
            } else {
                return RespResult.ok("请求成功", reporterNo);
            }

        } catch (IllegalArgumentException e) {
            logger.error("系统错误: {}", e.getMessage());
            if (!errorMessage.isEmpty()) {
                return RespResult.error("402", String.join(", ", errorMessage));
            } else {
                return RespResult.error("402", "系统异常");
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            if (!errorMessage.isEmpty()) {
                return RespResult.error("402", String.join(", ", errorMessage));
            } else {
                return RespResult.error("402", "系统异常");
            }
        }
    }

    /**
     * 判断是否为唯一索引冲突异常
     */
    private boolean isDuplicateKeyError(SQLException e) {
        // MySQL
        if (e.getErrorCode() == 1062) {
            return true;
        }
        // 通用方式:检查错误消息
        String message = e.getMessage().toLowerCase();
        return message.contains("duplicate") ||
                message.contains("unique") ||
                message.contains("违反唯一约束");
    }

    /**
     * 获取备案号,如果是删除则返回原备案号,否作生成新备案号
     */
    private String getReporterNo(JSONObject data, String actionType, int versionNo, String reportKey, String reporter) throws SQLException {
        synchronized (LOCKMAP.computeIfAbsent(reporter, (k)-> new Object())) {
            String reporterNo = null;
            if (Constants.ACTION_TYPE_DEL.equals(actionType)) {
                reporterNo = reportKey;//+"v"+ versionNo;
            } else {
                if (StringUtils.isNotBlank(reportKey)) {
                    reporterNo = reportKey;//+"v"+ (++versionNo);
                } else {
                    // String isPublic = data.getString("is_public");
                    String date = data.getString("date");
                    reporterNo = BusiUtil.getPublicReportNo(reporter, date);
                    while(checkReportNoIsExist(reporterNo)){
                        reporterNo = BusiUtil.getPublicReportNo(reporter, date);
                    }
                }
            }
            return reporterNo;
        }
    }

    private boolean checkReportNoIsExist(String reporterNo) throws SQLException {
        JSONObject queryParams = new JSONObject();
        JSONObject query = new JSONObject();
        JSONObject term = new JSONObject();
        JSONObject reportNoField = new JSONObject();
        
        reportNoField.put("REPORT_NO", reporterNo);
        term.put("term", reportNoField);
        query.put("query", term);
        queryParams.put("size", 1);
        queryParams.putAll(query);
        
        JSONObject esResult = ElasticsearchKit.search(
            Constants.XTY_TARIFF_BAK_INFO_INDEX,
            queryParams
        );
        JSONArray hits = esResult.getJSONObject("hits").getJSONArray("hits");
        boolean isExist = hits != null && !hits.isEmpty();
        if(!isExist){ //ES可能有入库延迟 加个数据库兜底
            String sql = "select count(1) from " + Constants.getBusiSchema() + ".xty_tariff_record where REPORT_NO =? ";
            return QueryFactory.getReadQuery().queryForExist(sql, new Object[]{reporterNo});
        }
        return isExist;
    }


    private Set<String> addModification(String authConfig, JSONObject data, String actionType, JSONObject historyInfo) throws Exception {

        CompletableFuture<List<String>> future3 = CompletableFuture.supplyAsync(() -> {
            List<String> errorMessage = new ArrayList<>();
            if (Constants.ACTION_TYPE_EDIT.equals(actionType) || Constants.ACTION_TYPE_EDIT_VERIFY.equals(actionType)) {
                String modification = "";
                Map<String, String> requiredFieldMap = RequiredUpFields.stream().collect(Collectors.toMap(item -> item, item -> item));
                // 是否修改字段内容
                boolean isEditFieldContent = false;
                boolean isPermission = true;

                boolean hasFullEditPermission = StringUtils.isBlank(authConfig) || Arrays.asList(authConfig.split(",")).contains(Constants.AUTH_EDIT_ALL);
                boolean hasPartialEditPermission = !hasFullEditPermission &&
                        Arrays.asList(authConfig.split(",")).contains(Constants.AUTH_EDIT_PART);

                for (Entry<String, String> entry : modificationMap.entrySet()) {
                    String fieldName = entry.getValue();
                    String oldVal = historyInfo.getString(fieldName.toUpperCase());
                    if (oldVal == null) {
                        oldVal = "";
                    }
                    if("offline_day".equals(fieldName) && oldVal.equals(Constants.OFFLINE_DAY_DEFAULT)){
                        oldVal = "";
                    }
                    if (!data.containsKey(fieldName)) {
                        continue;
                    }
                    String newVal = data.getString(fieldName);
                    if (StringUtils.isBlank(newVal)) {
                        newVal = "";
                    }
                    String fieldZhName = FieldZhMap.get(fieldName);
                    if (!(!requiredFieldMap.containsKey(fieldName) || StringUtils.isNotBlank(newVal))) {
                        logger.error("'{}'必填参数为空", fieldZhName);
                        errorMessage.add("'" + fieldZhName + "'必填参数为空");
                    }
                    //"一级分类"，"二级分类"字段不能修改type1type2不能修改为空
                    if ("type1".equals(fieldName) || "type2".equals(fieldName)) {
                        if (StringUtils.isBlank(newVal)) {
                            logger.error("'" + fieldZhName + "'不能为空");
                            errorMessage.add("'" + fieldZhName + "'不能为空");
                        }
                    }
                    // Assert.isTrue(!requiredFieldMap.containsKey(fieldName) || StringUtils.isNotBlank(newVal), "'"+ fieldZhName +"'必填参数为空");
                    if (!StringUtils.equals(oldVal, newVal)) {
                        logger.info("[{}]oldVal: {}【newVal】: {}", fieldName, oldVal, newVal);
                        isEditFieldContent = true;

                        if(hasPartialEditPermission){
                            if(!"applicable_area".equals(fieldName) && !"online_day".equals(fieldName) && !"offline_day".equals(fieldName)){
                                isPermission = false;
                                logger.error("用户只有部分编辑权限，不允许修改字段: {},{},序列号:{}", fieldZhName,fieldName,data.getString("seq_no"));
//                                errorMessage.add("您只有部分编辑权限，不允许修改字段: '" + fieldZhName + "'");
                            }
                        }else{
                            //不能修改别名
                            if("tariff_another_name".equals(fieldName)){
                                logger.error("'" + fieldZhName + "'不能修改");
                                errorMessage.add("'" + fieldZhName + "'不能修改");
                            }
                        }

                        if (StringUtils.isNotBlank(modification)) {
                            modification += ",";
                        }
                        modification += entry.getKey();
                    } else {
                        logger.info("[{}]oldVal: {}, newVal: {}", fieldName, oldVal, newVal);
                    }
                }
                // 如果没有修改过任何字段，则不允许提交
                if (!isEditFieldContent) {
                    errorMessage.add("未修改任何字段, 不允许提交");
                }
                if(!isPermission){
                    errorMessage.add("只有部分编辑权限，只允许修改字段: 上线日期、下线日期、适用地区");
                }
                // Assert.isTrue(isEditFieldContent, "未修改任何字段, 不允许提交");
                data.put("modification", modification);
            }
            return errorMessage;
        }, BakThreadManager.getExecutorService());

         return new HashSet<>(future3.get());
    }

    private void saveTariff(JSONObject data, JSONObject historyInfo, UserModel user, String source,String authConfig) throws SQLException {
        // 获取当前时间
        String currDate = DateUtil.getCurrentDateStr("yyyyMMdd");
        String date = DateUtil.addDay("yyyyMMdd", currDate, 0);
        // 是否是排除操作-排除需进行数据交换
        boolean flag = data.getBoolean("areaFlag");
        if (flag) { // 排除操作，数据交换
//            String temp = data.getString("applicable_area");
//            data.put("applicable_area", data.getString("applicable_area_mapping"));
//            data.put("applicable_area_mapping", temp);
        }
        EasyQuery query = QueryFactory.getWriteQuery();
        // A（新增）、M（修改）、D（删除）C（校验）
        String actionType = data.getString("action_type");
        TariffCrawlCheckService crawlService = new TariffCrawlCheckService();
        if (Constants.ACTION_TYPE_ADD.equals(actionType)) {
            String id = IDGenerator.getDefaultNUMID();
            String dateYMD = DateUtil.getCurrentDateStr("yyyyMMdd");
            String monthId = DateUtil.getCurrentDateStr("yyyyMM");
            EasyRecord record = new EasyRecord(user.getSchemaName() + ".XTY_TARIFF_RECORD", "ID");
            for (String dataKey : data.keySet()) {
                if (FieldZhMap.containsKey(dataKey)) {
                    record.put(dataKey, data.getString(dataKey));
                }
            }
            record.remove("action_type");
            record.remove("area");
            record.remove("origin_report_no");
            record.remove("province");
            record.put("CALL_NUM", data.getString("call"));
            record.remove("call");
            record.put("DATA_NUM", data.getString("data"));
            record.remove("data");
            record.put("SMS_NUM", data.getString("sms"));
            record.remove("sms");

            String applicable_area = data.getString("applicable_area");

            if (flag) { // 为排除省份操作-导入、接口、网页  applicable_area为非排除编码
                record.put("AREA_SELECT_TYPE", "3");
                record.put("AREA_DESC", data.getString("area_desc")); // 地区描述
            }
            // 处理适用省份编码,如果是接口过来的,如果是全国,则适用省份给000,否则需要通过地区编码反查省份编码
            String applicableProvince = data.getString("applicable_province");
            if (StringUtils.isBlank(applicableProvince)) { // 接口来的
                String applicableArea = data.getString("applicable_area");
                if ("000".equals(applicableArea)) {
                    applicableProvince = "000";
                    record.put("AREA_SELECT_TYPE", "1");
                } else {
                    String[] applicableAreaArray = applicableArea.split(",");
                    applicableProvince = BusiUtil.getProvinceByAreaCode(applicableAreaArray);
                }
            }
            logger.info("省份地市信息为：{}省份:{} flag:{}", applicable_area, applicableProvince, flag);
            int areaCount = data.getString("applicable_area").split(",").length;
            record.put("APPLICABLE_PROVINCE", applicableProvince);
            String[] provinceSplit = applicableProvince.split(",");

            // 设置资费属性 设置适用区域描述
            if (!flag) { // 1.不是排除操作
                String provinceNames = getProvinceNames(provinceSplit);
                // 设置资费属性 设置适用区域描述
                if (provinceSplit.length > 1) {
                    provinceNames = provinceNames.replaceAll("除", "");
                    int provinceCount = provinceNames.split(",").length;
                    provinceNames = provinceNames + "等" + provinceCount + "个省" + areaCount + "个市";
                    record.put("AREA_DESC", provinceNames); // 多省多地市
                } else {
                    String applicableArea = data.getString("applicable_area");
                    logger.info("applicableArea:{}", applicableArea);
                    if ("000".equals(applicableArea)) {
                        // record.put("TARIFF_ATTR","1");// 全国
                        record.put("AREA_DESC", "全国");  // 全国
                        record.put("AREA_SELECT_TYPE", "1");
                    } else {
                        String[] applicableAreaArray = applicableArea.split(",");
                        // 获取市编码集
                        String areaNames = getAreaNames(applicableAreaArray);
                        record.put("AREA_DESC", provinceNames + ": " + areaNames); // 一省多地市
                        if (applicableAreaArray.length > 1) {
                            // 根据省获取该省下所有地市个数
                            int allAreaCountByProvince = getAreaCountByProvince(provinceSplit[0]);
                            logger.info("allAreaCountByProvince:{} applicableAreaArray:{}", allAreaCountByProvince, applicableAreaArray.length);
                            if (allAreaCountByProvince == applicableAreaArray.length) { // 为该省全部地市
                                record.put("AREA_DESC", provinceNames + "全部地市"); // 一省全地市
                            }
                        }
                    }
                }
            } else { // 2.排除操作，不再进行适用区域描述记录
                // 只设置资费属性
                // record.put("TARIFF_ATTR","1");// 全国
            }

            record.put("ENT", data.getString("ent"));
            record.put("ENT_NAME", data.getString("ent_name"));

            String reporterType = data.getString("reporter_type");
            record.put("REPORTER_TYPE", reporterType);
            record.put("CREATE_TYPE", data.getString("CREATE_TYPE"));
            record.put("ID", id);
            record.put("ENT_ID", user.getEpCode());
            record.put("BUSI_ORDER_ID", user.getBusiOrderId());
            String createTime = DateUtil.getCurrentDateStr();
            record.put("CREATE_TIME", createTime);
            record.put("CREATE_ACC", user.getUserAcc());
            record.put("CREATE_DEPT", user.getDeptCode());

            record.put("CITY", user.getDeptCityCode());
            // record.put("CITY_NAME", user.getDeptCityCode());
            record.put("DATE_ID", dateYMD);
            String reporter = data.getString("reporter");
            String reportObj = BusiUtil.getReportObj(reporter);
            record.put("REPORT_OBJ", reportObj);
            // 主体名称
            String reporterName = BusiUtil.getReporterName(reporter, user.getSchemaName());
            record.put("REPORTER_NAME", reporterName);
            String reporterNo = data.getString("reporter_no");
//			String reportKey = reporterNo.substring(0, reporterNo.lastIndexOf("v"));
//			String versionNo = reporterNo.substring(reporterNo.lastIndexOf("v") +1);

            if (StringUtils.isNotBlank(user.getDeptProvinceCode())) {
                record.put("PROVINCE", user.getDeptProvinceCode());
            } else {
                record.put("PROVINCE", getProvinceByReporter(data.getString("reporter")));
            }
            String provinceName = BusiUtil.getProvinceNameBySimplePinyin(reportObj);
            if(StringUtils.isBlank(provinceName) && StringUtils.isNotBlank(reporterName)){
                provinceName = reporterName.substring(0,2);
            }

            record.put("PROVINCE_NAME", provinceName);
            record.put("report_no", reporterNo);
            record.remove("reporter_no");
            record.put("REPORT_KEY", reporterNo);
            record.put("VERSION_NO", 0);
            record.put("VERSION_NUM", "V0");
            if (StringUtils.isBlank(record.getString("AREA_SELECT_TYPE"))) {
                record.put("AREA_SELECT_TYPE", "2");
            }
            // 资费状态设置
            String status = Constants.TARIFF_STATUS_1;
            if (date.compareTo(data.getString("online_day")) < 0) {
                status = Constants.TARIFF_STATUS_4;
            }
            if (StringUtils.isNotBlank(data.getString("offline_day")) && date.compareTo(data.getString("offline_day")) > 0) {
                status = Constants.TARIFF_STATUS_3;
            }
            record.put("STATUS", status); // 在售

            if(StringUtils.isBlank(data.getString("offline_day"))){
                record.remove("offline_day");
                record.put("OFFLINE_DAY", Constants.OFFLINE_DAY_DEFAULT); // 下架日期默认值
            }
            // 进行市编码保存操作
            record.remove("applicable_area");
            record.remove("tariff_attr");
            record.put("TARIFF_ATTR", data.getString("tariff_attr"));
            logger.info("flag:{},applicable_area:{},applicable_area_mapping:{}", flag,data.getString("applicable_area"),data.getString("applicable_area_mapping"));
            record.put("APPLICABLE_AREA",data.getString("applicable_area"));
            record.remove("unsubscribe");
            record.put("UNSUBSCRIBE", data.getString("unsubscribe")); //退订方式
            query.save(record);
            // 保存地区信息
            saveAreaInfo(data, user,  id, reporterNo,null);
            //更新缓存信息
            TariffReported model = new TariffReported(id, reporterNo, data.getString("name"), "",
                    data.getString("ent"), data.getString("ent_name"), record.getString("PROVINCE"),
                    record.getString("PROVINCE_NAME"), data.getString("online_day"), record.getString("OFFLINE_DAY"), RandomKit.uniqueStr(), createTime, dateYMD, monthId, status);
            model.setReportObj(reportObj);
            model.setReporter(reporter);
            model.setReporterType(reporterType);
            TariffReportedHandler.getInstance().addNew(model);
            //更新ES库信息
            JSONObject o = new JSONObject();
            o.put("id", record.getString("ID"));
            o.put("name", data.getString("name"));
            o.put("reportNo", reporterNo);
            o.put("provinceCode", record.getString("PROVINCE"));
            o.put("provinceName", record.getString("PROVINCE_NAME"));
            o.put("ent", record.getString("ENT"));
            o.put("entName", record.getString("ENT_NAME"));
            o.put("onlineDay", data.getString("online_day"));
            o.put("offlineDay", record.getString("OFFLINE_DAY"));
            logger.info("Record:{}", record.toJSONString());
            logger.info("新增ES库内容:{}", o.toJSONString());
            try {
                BusiUtil.sendToEsForAdd(o);

                // 同步数据到备份索引
                syncToBakIndex( id);
                //判断是否公示
//                crawlService.addPublicLibRecord(reporterNo,record.getString("PROVINCE"), record.getString("ENT"), record.getString("id"));
            } catch (Exception e) {
                logger.error("报错",e);
            }
        } else if (Constants.ACTION_TYPE_EDIT.equals(actionType)) {
            if (StringUtils.isNotBlank(data.getString("applicable_area"))) {
                adit(data, historyInfo, user, source, query, flag, date,authConfig);
            } else {
                aditNoArea(data, historyInfo, user, source, query, flag, date,authConfig);
            }
            // 异步调用检查服务进行重新核查，等待2秒以确保ES数据同步完成
            final String tariffId = historyInfo.getString("ID");
            new Thread(() -> {
                try {
                    // 等待5秒确保ES数据同步
                    Thread.sleep(5000);

                    TariffCheckService checkService = new TariffCheckService();
                    checkService.checkTariffFields(null, null, true, tariffId, null, 1);

                    crawlService.updatePublicLibRecord(tariffId);
                } catch (Exception e) {
                    logger.error("异步执行核查服务失败", e);
                }
            }).start();

//			if("Y".equals(isMagUp)){ // 为管理员直接修改，不留痕
//				magDirectUpdate(data, historyInfo, user, flag, date, query);
//			}else { // 为普通修改，创建新资费
//				if(StringUtils.isNotBlank(data.getString("applicable_area"))){
//					adit(data, historyInfo, user, source, query, flag, date);
//				}else {
//					aditNoArea(data, historyInfo, user, source, query, flag, date);
//				}
//			}
        } else if (Constants.ACTION_TYPE_DEL.equals(actionType)) {

            String dateTime = DateUtil.getCurrentDateStr();
            String id = historyInfo.getString("ID");
            //直接写历史表
            EasySQL sql = new EasySQL();
            sql.append("insert into " + user.getSchemaName() + ".XTY_TARIFF_RECORD_HIS ");
            sql.append("( ID, ENT_ID, BUSI_ORDER_ID, CREATE_TIME, UPDATE_TIME, CREATE_ACC, UPDATE_ACC, CREATE_DEPT,");
            sql.append("PROVINCE, PROVINCE_NAME, CITY, CITY_NAME, ENT, ENT_NAME, REPORTER_TYPE, CREATE_TYPE, DATE_ID,");
            sql.append("SEQ_NO, REPORT_OBJ, REPORT_NO, REPORT_KEY, VERSION_NO, IS_HISTORY, REPORTER, IS_PUBLIC, REASON_NO_PUBLIC, ");
            sql.append("TYPE1, TYPE2, NAME, FEES, CALL_NUM, DATA_NUM, SMS_NUM, INTERNATIONAL_CALL, INTERNATIONAL_ROAMING_DATA, INTERNATIONAL_SMS, ");
            sql.append("ORIENT_TRAFFIC, IPTV, BANDWIDTH, RIGHTS, OTHER_CONTENT, APPLICABLE_PEOPLE, APPLICABLE_AREA, VALID_PERIOD, RESPONSIBILITY, RESTRICTIONS, ONLINE_DAY,");
            sql.append("OFFLINE_DAY, OTHERS, CHANNEL, DURATION, STATUS, DEL_TIME, REASON, PLAN, DEL_ACC, APPLICABLE_PROVINCE, FEES_UNIT, DATA_UNIT, ORIENT_TRAFFIC_UNIT,");
            sql.append(" APPLICABLE_AREA_NAME, REPORTER_NAME, TARIFF_ATTR, AREA_DESC, AREA_SELECT_TYPE, TARIFF_ANOTHER_NAME,VERSION_NUM,UNSUBSCRIBE,EXTRA_FEES,OTHER_FEES,IS_TELECOM  )");

            sql.append("select ID, ENT_ID, BUSI_ORDER_ID, CREATE_TIME, '"+ EasyDate.getCurrentDateString() +"' as UPDATE_TIME, CREATE_ACC,  UPDATE_ACC, CREATE_DEPT,");
            sql.append("PROVINCE, PROVINCE_NAME, CITY, CITY_NAME, ENT, ENT_NAME, REPORTER_TYPE, CREATE_TYPE, DATE_ID,");
            sql.append("SEQ_NO, REPORT_OBJ, REPORT_NO, REPORT_KEY, VERSION_NO, 'Y' as IS_HISTORY, REPORTER, IS_PUBLIC, REASON_NO_PUBLIC, ");
            sql.append("TYPE1, TYPE2, NAME, FEES, CALL_NUM, DATA_NUM, SMS_NUM, INTERNATIONAL_CALL, INTERNATIONAL_ROAMING_DATA, INTERNATIONAL_SMS, ");
            sql.append("ORIENT_TRAFFIC, IPTV, BANDWIDTH, RIGHTS, OTHER_CONTENT, APPLICABLE_PEOPLE, APPLICABLE_AREA, VALID_PERIOD, RESPONSIBILITY, RESTRICTIONS, ONLINE_DAY,");
            sql.append("OFFLINE_DAY, OTHERS, CHANNEL, DURATION,"+Constants.TARIFF_STATUS_2+" as STATUS, '"+dateTime+"' as DEL_TIME, REASON, PLAN, '"+user.getUserName()+"' as DEL_ACC, APPLICABLE_PROVINCE, FEES_UNIT, DATA_UNIT, ORIENT_TRAFFIC_UNIT,");
            sql.append(" APPLICABLE_AREA_NAME, REPORTER_NAME, TARIFF_ATTR, AREA_DESC, AREA_SELECT_TYPE, TARIFF_ANOTHER_NAME,VERSION_NUM,UNSUBSCRIBE,EXTRA_FEES,OTHER_FEES,IS_TELECOM  ");
            sql.append("from " + user.getSchemaName() + ".XTY_TARIFF_RECORD");
            sql.append(id, "where ID=?", false);
            logger.info("删除记录 开始写历史表 --> sql={}", sql.toFullSql());
            query.execute(sql.getSQL(), sql.getParams());

            String reportNo = historyInfo.getString("REPORT_NO");
            EasyRecord record = new EasyRecord(user.getSchemaName() + ".XTY_TARIFF_RECORD", "ID");
            record.put("ID", id);
            query.deleteById(record);

            //更新redis缓存
            // TariffReportedHandler.delete(historyInfo.getString("NAME"), historyInfo.getString("PROVINCE"), historyInfo.getString("ENT"));
            TariffReportedHandler.getInstance().delete(reportNo);

            crawlService.deletePublicLibRecord(id);
            //更新ES库信息
            try {
                BusiUtil.sendToEsForDelete(reportNo);
                // 同步删除操作到备份索引
                BusiUtil.deleteTariffFromBakIndex(reportNo);
            } catch (Exception e) {
                logger.error("删除ES信息报错",e);
            }
            Yqlogger yqlogger = new Yqlogger();
            yqlogger.setCreateAcc(user.getUserAcc());
            yqlogger.setCreateNo(user.getUserNo());
            yqlogger.setCreateName(user.getUserName());
            yqlogger.setCreateTime(DateUtil.getCurrentDateStr());
            yqlogger.setContent("用户【" + user.getUserAcc() + "】删除了资费ID、方案编号为【" + id + "】【" + reportNo + "】的资费信息");
            yqlogger.setModule(Constants.APP_NAME);
            yqlogger.setOperType(yqlogger.OPER_TYPE_DEL);
            yqlogger.setBakup("删除资费");
            yqlogger.setEntId(Constants.getEntId());
            yqlogger.setBusiOrderId(user.getBusiOrderId());
            LogUtil.insertLog(user.getSchemaName(), yqlogger);
        }
    }

    // 管理员开后门修改
    private void magDirectUpdate(JSONObject data, JSONObject historyInfo, UserModel user, boolean flag, String date, EasyQuery query) {

        try {
            String id = historyInfo.getString("ID");
            String reporterNo = data.getString("report_no");

            // 2.进行数据写入历史表---修改不进行备案了
/*			EasyQuery queryWrite = QueryFactory.getWriteQuery();
			EasyQuery queryRead = QueryFactory.getReadQuery();
			EasySQL sql = new EasySQL();
			sql.append("select *");
			sql.append("from "+ user.getSchemaName() +".XTY_TARIFF_RECORD");
			sql.append("where 1=1");
			sql.append(id, "and ID =?");
			JSONObject jsonObject = queryRead.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			jsonObject.put("IS_HISTORY","Y");
			EasyRecord saveRecord = new EasyRecord(user.getSchemaName()+ ".XTY_TARIFF_RECORD_HIS", "ID");
			saveRecord.putAll(jsonObject);
			queryWrite.save(saveRecord);*/
            // 3.执行修改
            String modification = data.getString("modification");
            EasyRecord record = new EasyRecord(user.getSchemaName() + ".XTY_TARIFF_RECORD", "ID");
            record.put("ID", id);
            record.put("SEQ_NO", data.getString("seq_no"));
            // String reporter = data.getString("reporter");
            // String reportObj = BusiUtil.getReportObj(reporter);
            // record.put("REPORT_OBJ", reportObj);
            record.put("AREA_SELECT_TYPE", data.getString("area_select_type"));
            if (flag) { // 为排除省份操作-导入、接口、网页  applicable_area为非排除编码
                record.put("AREA_SELECT_TYPE", "3");
                record.put("AREA_DESC", data.getString("area_desc")); // 地区描述
            }
            String applicableProvince = data.getString("applicable_province");
            if (StringUtils.isNotBlank(applicableProvince)) {
                record.put("APPLICABLE_PROVINCE", applicableProvince);
            }

            int areaCount = data.getString("applicable_area").split(",").length;
            String[] provinceSplit = applicableProvince.split(",");
            // 设置资费属性 设置适用区域描述
            if (!flag) { // 1.不是排除操作
                String provinceNames = getProvinceNames(provinceSplit);
                // 设置资费属性 设置适用区域描述
                if (provinceSplit.length > 1) {
                    provinceNames = provinceNames.replaceAll("除", "");
                    int provinceCount = provinceNames.split(",").length;
                    provinceNames = provinceNames + "等" + provinceCount + "个省" + areaCount + "个市";
                    record.put("AREA_DESC", provinceNames); // 多省多地市
                    // record.put("TARIFF_ATTR","1");// 全国

                } else {
                    String applicableArea = data.getString("applicable_area");
                    if ("000".equals(applicableArea)) {
                        // record.put("TARIFF_ATTR","1");// 全国
                        record.put("AREA_DESC", "全国");  // 全国
                        record.put("AREA_SELECT_TYPE", "1");
                    } else {
                        String[] applicableAreaArray = applicableArea.split(",");
                        // 获取市编码集
                        String areaNames = getAreaNames(applicableAreaArray);
                        record.put("AREA_DESC", provinceNames + ": " + areaNames); // 一省多地市
                        if (applicableAreaArray.length > 1) {
                            // 根据省获取该省下所有地市个数
                            int allAreaCountByProvince = getAreaCountByProvince(provinceSplit[0]);
                            if (allAreaCountByProvince == applicableAreaArray.length) { // 为该省全部地市
                                record.put("AREA_DESC", provinceNames + "全部地市"); // 一省全地市
                            }
                            //	record.put("TARIFF_ATTR","2");// 省内
                        } else {
                            // record.put("TARIFF_ATTR","3");// 本地
                        }
                    }
                }
            } else { // 2.排除操作，不再进行适用区域描述记录
                // 只设置资费属性
                //	record.put("TARIFF_ATTR","1");// 全国
            }
            // String reportKey = reporterNo.substring(0, reporterNo.lastIndexOf("v"));
            // String versionNo = reporterNo.substring(reporterNo.lastIndexOf("v") +1);
            // record.put("report_no", reporterNo);
            record.remove("reporter_no");
            record.remove("province");
            record.remove("REPORT_NO");
            // record.put("REPORT_KEY", reportKey);
            // record.put("VERSION_NO", versionNo);
            // record.put("IS_HISTORY", DictConstants.DICT_SY_YN_N);
            // 增加修改字段
            logger.info("modification::{}", modification);
            String[] modificationArray = modification.split(",");
            for (String fieldIndex : modificationArray) {
                String fieldName = modificationMap.get(fieldIndex);
                String val = data.getString(fieldName);
                record.put(fieldName.toUpperCase(), val);
            }
            record.remove("action_type");
            record.remove("area");
            record.put("CALL_NUM", record.getString("CALL"));
            record.remove("CALL");
            record.put("DATA_NUM", record.getString("DATA"));
            record.remove("DATA");
            record.put("SMS_NUM", record.getString("SMS"));
            if (StringUtils.isBlank(record.getString("AREA_SELECT_TYPE"))) {
                record.put("AREA_SELECT_TYPE", "2");
            }
            record.remove("SMS");
            // 资费状态设置
//            record.put("STATUS", Constants.TARIFF_STATUS_1); // 在售
//            if (date.compareTo(data.getString("online_day")) < 0) {
//                record.put("STATUS", Constants.TARIFF_STATUS_4); // 未售
//            }
//            if (StringUtils.isNotBlank(data.getString("offline_day")) && date.compareTo(data.getString("offline_day")) > 0) {
//                record.put("STATUS", Constants.TARIFF_STATUS_3); // 下架
//            }
            String onlineDay = data.getString("online_day");
            String offlineDay = data.getString("offline_day");
            if (StringUtils.isNotBlank(onlineDay) || StringUtils.isNotBlank(offlineDay)) {
                record.put("STATUS", Constants.TARIFF_STATUS_1); // 在售
                if (StringUtils.isNotBlank(onlineDay) && date.compareTo(onlineDay) < 0) {
                    record.put("STATUS", Constants.TARIFF_STATUS_4); // 未售
                }
                if (StringUtils.isNotBlank(offlineDay) && date.compareTo(offlineDay) > 0) {
                    record.put("STATUS", Constants.TARIFF_STATUS_3); // 下架
                }
            }
            // 进行市编码保存操作
            record.remove("applicable_area");
            record.remove("tariff_attr");
            record.put("TARIFF_ATTR", data.getString("tariff_attr"));
            logger.info("flag:{},applicable_area:{},applicable_area_mapping:{}", flag,data.getString("applicable_area"),data.getString("applicable_area_mapping"));
            record.put("APPLICABLE_AREA", data.getString("applicable_area"));
            record.put("UNSUBSCRIBE", data.getString("unsubscribe"));
            query.update(record);
            // 如果适用地区被修改则要重新保存地区信息
            String applicableArea = data.getString("applicable_area");
            // 4.删除关联的适用地区信息
            EasySQL sql2 = new EasySQL();
            sql2.append("delete from " + user.getSchemaName() + ".xty_tariff_areq ");
            sql2.append(" where 1=1 ");
            sql2.append(id, "and TARIFF_RECORD_ID = ?");
            query.execute(sql2.getSQL(), sql2.getParams());
            if (StringUtils.isNotBlank(applicableArea)) {
                // 5.保存地区信息
                saveAreaInfo(data, user,  id, reporterNo,historyInfo.getString("APPLICABLE_AREA"));
            }

            // 调用检查服务进行重新核查
            new Thread(() -> {
                try {
                    // 等待5秒确保ES数据同步
                    Thread.sleep(5000);
                    TariffCheckService checkService = new TariffCheckService();
                    checkService.checkTariffFields(null, null, true, historyInfo.getString("ID"), null, 1);
                } catch (Exception e) {
                    logger.error("异步执行核查服务失败", e);
                }
            }).start();


            // 1.进行日志记录
            Yqlogger yqlogger = new Yqlogger();
            yqlogger.setCreateAcc(user.getUserAcc());
            yqlogger.setCreateNo(user.getUserNo());
            yqlogger.setCreateName(user.getUserName());
            yqlogger.setCreateTime(DateUtil.getCurrentDateStr());
            yqlogger.setContent("用户【" + user.getUserAcc() + "】将资费ID为【" + id + "】从[未报送资费]变更为[续订资费]");
            yqlogger.setModule(Constants.APP_NAME);
            yqlogger.setOperType(yqlogger.OPER_TYPE_UPDATE);
            yqlogger.setBakup("管理员直接修改");
            yqlogger.setEntId(Constants.getEntId());
            yqlogger.setBusiOrderId(user.getBusiOrderId());
            LogUtil.insertLog(user.getSchemaName(), yqlogger);
        } catch (SQLException e) {
            logger.error(e.getMessage(), e);
        }
    }

    private void aditNoArea(JSONObject data, JSONObject historyInfo, UserModel user, String source,
                            EasyQuery query, boolean flag, String date,String authConfig) throws SQLException {
        try {
            String dateTime = DateUtil.getCurrentDateStr();
            String id = historyInfo.getString("ID");

            //直接写历史表
            EasySQL sql = new EasySQL();
            sql.append("insert into " + user.getSchemaName() + ".XTY_TARIFF_RECORD_HIS ");
            sql.append("( ID, ENT_ID, BUSI_ORDER_ID, CREATE_TIME, UPDATE_TIME, CREATE_ACC, UPDATE_ACC, CREATE_DEPT,");
            sql.append("PROVINCE, PROVINCE_NAME, CITY, CITY_NAME, ENT, ENT_NAME, REPORTER_TYPE, CREATE_TYPE, DATE_ID,");
            sql.append("SEQ_NO, REPORT_OBJ, REPORT_NO, REPORT_KEY, VERSION_NO, IS_HISTORY, REPORTER, IS_PUBLIC, REASON_NO_PUBLIC, ");
            sql.append("TYPE1, TYPE2, NAME, FEES, CALL_NUM, DATA_NUM, SMS_NUM, INTERNATIONAL_CALL, INTERNATIONAL_ROAMING_DATA, INTERNATIONAL_SMS, ");
            sql.append("ORIENT_TRAFFIC, IPTV, BANDWIDTH, RIGHTS, OTHER_CONTENT, APPLICABLE_PEOPLE, APPLICABLE_AREA, VALID_PERIOD, RESPONSIBILITY, RESTRICTIONS, ONLINE_DAY,");
            sql.append("OFFLINE_DAY, OTHERS, CHANNEL, DURATION, STATUS, DEL_TIME, REASON, PLAN, DEL_ACC, APPLICABLE_PROVINCE, FEES_UNIT, DATA_UNIT, ORIENT_TRAFFIC_UNIT,");
            sql.append(" APPLICABLE_AREA_NAME, REPORTER_NAME, TARIFF_ATTR, AREA_DESC, AREA_SELECT_TYPE, TARIFF_ANOTHER_NAME,VERSION_NUM,UNSUBSCRIBE,EXTRA_FEES,OTHER_FEES,IS_TELECOM  )");

            sql.append("select " + RandomKit.uniqueStr() + " as ID, ENT_ID, BUSI_ORDER_ID, CREATE_TIME,  UPDATE_TIME, CREATE_ACC,  UPDATE_ACC, CREATE_DEPT,");
            sql.append("PROVINCE, PROVINCE_NAME, CITY, CITY_NAME, ENT, ENT_NAME, REPORTER_TYPE, CREATE_TYPE, DATE_ID,");
            sql.append("SEQ_NO, REPORT_OBJ, REPORT_NO, REPORT_KEY, VERSION_NO, 'Y' as IS_HISTORY, REPORTER, IS_PUBLIC, REASON_NO_PUBLIC, ");
            sql.append("TYPE1, TYPE2, NAME, FEES, CALL_NUM, DATA_NUM, SMS_NUM, INTERNATIONAL_CALL, INTERNATIONAL_ROAMING_DATA, INTERNATIONAL_SMS, ");
            sql.append("ORIENT_TRAFFIC, IPTV, BANDWIDTH, RIGHTS, OTHER_CONTENT, APPLICABLE_PEOPLE, APPLICABLE_AREA, VALID_PERIOD, RESPONSIBILITY, RESTRICTIONS, ONLINE_DAY,");
            sql.append("OFFLINE_DAY, OTHERS, CHANNEL, DURATION, STATUS, DEL_TIME, REASON, PLAN, DEL_ACC, APPLICABLE_PROVINCE, FEES_UNIT, DATA_UNIT, ORIENT_TRAFFIC_UNIT,");
            sql.append(" APPLICABLE_AREA_NAME, REPORTER_NAME, TARIFF_ATTR, AREA_DESC, AREA_SELECT_TYPE, TARIFF_ANOTHER_NAME,VERSION_NUM,UNSUBSCRIBE,EXTRA_FEES,OTHER_FEES,IS_TELECOM  ");
            sql.append("from " + user.getSchemaName() + ".XTY_TARIFF_RECORD");
            sql.append(id, "where ID=?", false);
            query.execute(sql.getSQL(), sql.getParams());

            // 开始更新字段-创建新的一条数据
            String modification = data.getString("modification");
            EasyRecord record = new EasyRecord(user.getSchemaName() + ".XTY_TARIFF_RECORD", "ID");
            record.putAll(historyInfo);
            record.put("ID", id);
            record.put("UPDATE_TIME", dateTime);
            record.put("UPDATE_ACC", user.getUserAcc());

            // 检查权限
            boolean hasFullEditPermission = StringUtils.isBlank(authConfig) || Arrays.asList(authConfig.split(",")).contains(Constants.AUTH_EDIT_ALL);
            boolean hasPartialEditPermission = !hasFullEditPermission &&
                    Arrays.asList(authConfig.split(",")).contains(Constants.AUTH_EDIT_PART);

            // 如果有完全编辑权限或没有指定权限配置，执行原有逻辑
            if (hasFullEditPermission) {
                record.put("SEQ_NO", data.getString("seq_no"));
                String reporter = data.getString("reporter");
                String reportObj = BusiUtil.getReportObj(reporter);
                record.put("REPORT_OBJ", reportObj);
                String reporterNo = data.getString("reporter_no");

                record.put("report_no", reporterNo);
                record.remove("reporter_no");
                record.remove("province");
                record.remove("REPORT_NO");
                record.put("REPORT_KEY", reporterNo);
                int versionNo = historyInfo.getIntValue("VERSION_NO") + 1;
                record.put("VERSION_NO", versionNo);

                String versionNum = historyInfo.getString("VERSION_NUM");
                versionNum = Optional.ofNullable(versionNum)
                        .filter(StringUtils::isNotBlank)
                        .map(version -> {
                            String versionSuffix = version.substring(version.lastIndexOf("V") + 1);
                            int newVersion = Integer.parseInt(versionSuffix) + 1;
                            return String.format("V%d", newVersion);
                        })
                        .orElseGet(() -> String.format("V1"));
                record.put("VERSION_NUM", versionNum);

                record.put("IS_HISTORY", DictConstants.DICT_SY_YN_N);

                // 增加修改字段
                String[] modificationArray = modification.split(",");
                for (String fieldIndex : modificationArray) {
                    String fieldName = modificationMap.get(fieldIndex);
                    String val = data.getString(fieldName);
                    record.put(fieldName.toUpperCase(), val);
                }

                record.remove("action_type");
                record.remove("area");
                record.put("CALL_NUM", record.getString("CALL"));
                record.remove("CALL");
                record.put("DATA_NUM", record.getString("DATA"));
                record.remove("DATA");
                record.put("SMS_NUM", record.getString("SMS"));
                record.remove("SMS");

                //退订方式
                if(StringUtils.isNotBlank(data.getString("unsubscribe"))){
                    record.put("UNSUBSCRIBE", data.getString("unsubscribe"));
                }
            } else if (hasPartialEditPermission) {
                // 部分编辑权限，只允许修改三个特定字段
                logger.info("用户只有部分编辑权限，仅允许修改上线日期和下线日期");

                // 版本号处理
                int versionNo = historyInfo.getIntValue("VERSION_NO") + 1;
                record.put("VERSION_NO", versionNo);

                String versionNum = historyInfo.getString("VERSION_NUM");
                versionNum = Optional.ofNullable(versionNum)
                        .filter(StringUtils::isNotBlank)
                        .map(version -> {
                            String versionSuffix = version.substring(version.lastIndexOf("V") + 1);
                            int newVersion = Integer.parseInt(versionSuffix) + 1;
                            return String.format("V%d", newVersion);
                        })
                        .orElseGet(() -> String.format("V1"));
                record.put("VERSION_NUM", versionNum);
                record.put("IS_HISTORY", DictConstants.DICT_SY_YN_N);

                // 修改数组只包含允许的字段
                List<String> modifiedFields = new ArrayList<>();

                // 处理上线日期
                if (data.containsKey("online_day")) {
                    String onlineDay = data.getString("online_day");
                    if (StringUtils.isNotBlank(onlineDay)) {
                        record.put("ONLINE_DAY", onlineDay);
                        modifiedFields.add("10");
                    }
                }

                // 处理下线日期
                if (data.containsKey("offline_day")) {
                    String offlineDay = data.getString("offline_day");
                    if (StringUtils.isNotBlank(offlineDay)) {
                        record.put("OFFLINE_DAY", offlineDay);
                    } else {
                        record.remove("offline_day");
                        record.put("OFFLINE_DAY", Constants.OFFLINE_DAY_DEFAULT);
                    }
                    modifiedFields.add("11");
                }

                // 更新修改字段数组
                modification = String.join(",", modifiedFields);
            }

            // 资费状态设置 - 对所有权限类型都需要处理
            String onlineDay = data.getString("online_day");
            String offlineDay = data.getString("offline_day");
            String status = "";
            if (StringUtils.isNotBlank(onlineDay) || StringUtils.isNotBlank(offlineDay)) {
                status = Constants.TARIFF_STATUS_1;
                record.put("STATUS", Constants.TARIFF_STATUS_1); // 在售
                if (StringUtils.isNotBlank(onlineDay) && date.compareTo(onlineDay) < 0) {
                    record.put("STATUS", Constants.TARIFF_STATUS_4); // 未售
                    status = Constants.TARIFF_STATUS_4;
                }
                if (StringUtils.isNotBlank(offlineDay) && date.compareTo(offlineDay) > 0) {
                    record.put("STATUS", Constants.TARIFF_STATUS_3); // 下架
                    status = Constants.TARIFF_STATUS_3;
                }
            }

            if(data.containsKey("offline_day") && StringUtils.isBlank(offlineDay)){
                record.remove("offline_day");
                record.put("OFFLINE_DAY", Constants.OFFLINE_DAY_DEFAULT); // 下架日期默认值
            }
            record.remove("CALL");
            record.remove("DATA");
            record.remove("SMS");
            logger.info("修改资费详情record =="+record);
            query.update(record);

            /* 5、写入本次修改字段明细 */
            String[] modificationArray = modification.split(",");
            saveTariffUpdateData(data, historyInfo, user, source, query, id, dateTime, modificationArray);

            //6.更新redis 缓存
            String reporterNo = data.getString("reporter_no");
            String reporter = data.getString("reporter");
            String reportObj = BusiUtil.getReportObj(reporter);

            TariffReported model = new TariffReported(id, reporterNo,
                    StringUtils.isNotBlank(record.getString("NAME")) ? record.getString("NAME") : historyInfo.getString("NAME"),
                    historyInfo.getString("NAME"),
                    historyInfo.getString("ENT"),
                    historyInfo.getString("ENT_NAME"),
                    historyInfo.getString("PROVINCE"),
                    historyInfo.getString("PROVINCE_NAME"),
                    StringUtils.isNotBlank(data.getString("online_day")) ? data.getString("online_day") : historyInfo.getString("ONLINE_DAY"),
                    StringUtils.isNotBlank(record.getString("OFFLINE_DAY")) ? record.getString("OFFLINE_DAY") : historyInfo.getString("OFFLINE_DAY"),
                    RandomKit.uniqueStr());
            model.setStatus(status);
            model.setReportObj(reportObj);
            model.setReporter(reporter);
            String reporterName = BusiUtil.getReporterName(reporter, Constants.getBusiSchema());
            model.setReporterName(reporterName);
            model.setReportObj(reportObj);
            TariffReportedHandler.getInstance().update(model);

            //修改ES
            JSONObject o = new JSONObject();
            o.put("id", id);
            o.put("name", StringUtils.isNotBlank(record.getString("NAME")) ? record.getString("NAME") : historyInfo.getString("NAME"));
            o.put("reportNo", reporterNo);
            o.put("provinceCode", record.getString("PROVINCE"));
            o.put("provinceName", record.getString("PROVINCE_NAME"));
            o.put("ent", record.getString("ENT"));
            o.put("entName", record.getString("ENT_NAME"));
            o.put("onlineDay", StringUtils.isNotBlank(data.getString("online_day")) ? data.getString("online_day") : historyInfo.getString("ONLINE_DAY"));
            o.put("offlineDay", StringUtils.isNotBlank(record.getString("OFFLINE_DAY")) ? record.getString("OFFLINE_DAY") : historyInfo.getString("OFFLINE_DAY"));
            try {
                BusiUtil.sendToEsForUpdate(id, o);
            } catch (Exception e) {
                logger.error("更新ES信息报错", e);
            }

        } catch (SQLException e) {
            logger.error(e.getMessage(), e);
            throw e;
        }

        // 在方法末尾添加，确保修改后的数据同步到备份索引
        try {
            // 同步修改后的数据到备份索引
            String recordId = historyInfo.getString("ID");
            syncToBakIndex( recordId);
            logger.info("修改资费(无地区)后同步到备份索引成功，ID: {}", recordId);
        } catch (Exception e) {
            logger.error("修改资费(无地区)后同步到备份索引失败", e);
        }
    }

    private void adit(JSONObject data, JSONObject historyInfo, UserModel user, String source,
                      EasyQuery query, boolean flag, String date,String authConfig) throws SQLException {
        try {
            String dateTime = DateUtil.getCurrentDateStr();
            String id = historyInfo.getString("ID");

            //直接写历史表
            EasySQL sql = new EasySQL();
            sql.append("insert into " + user.getSchemaName() + ".XTY_TARIFF_RECORD_HIS ");
            sql.append("( ID, ENT_ID, BUSI_ORDER_ID, CREATE_TIME, UPDATE_TIME, CREATE_ACC, UPDATE_ACC, CREATE_DEPT,");
            sql.append("PROVINCE, PROVINCE_NAME, CITY, CITY_NAME, ENT, ENT_NAME, REPORTER_TYPE, CREATE_TYPE, DATE_ID,");
            sql.append("SEQ_NO, REPORT_OBJ, REPORT_NO, REPORT_KEY, VERSION_NO, IS_HISTORY, REPORTER, IS_PUBLIC, REASON_NO_PUBLIC, ");
            sql.append("TYPE1, TYPE2, NAME, FEES, CALL_NUM, DATA_NUM, SMS_NUM, INTERNATIONAL_CALL, INTERNATIONAL_ROAMING_DATA, INTERNATIONAL_SMS, ");
            sql.append("ORIENT_TRAFFIC, IPTV, BANDWIDTH, RIGHTS, OTHER_CONTENT, APPLICABLE_PEOPLE, APPLICABLE_AREA, VALID_PERIOD, RESPONSIBILITY, RESTRICTIONS, ONLINE_DAY,");
            sql.append("OFFLINE_DAY, OTHERS, CHANNEL, DURATION, STATUS, DEL_TIME, REASON, PLAN, DEL_ACC, APPLICABLE_PROVINCE, FEES_UNIT, DATA_UNIT, ORIENT_TRAFFIC_UNIT,");
            sql.append(" APPLICABLE_AREA_NAME, REPORTER_NAME, TARIFF_ATTR, AREA_DESC, AREA_SELECT_TYPE, TARIFF_ANOTHER_NAME,VERSION_NUM,UNSUBSCRIBE,EXTRA_FEES,OTHER_FEES,IS_TELECOM  )");

            sql.append("select " + RandomKit.uniqueStr() + " as ID, ENT_ID, BUSI_ORDER_ID, CREATE_TIME,  UPDATE_TIME, CREATE_ACC,  UPDATE_ACC, CREATE_DEPT,");
            sql.append("PROVINCE, PROVINCE_NAME, CITY, CITY_NAME, ENT, ENT_NAME, REPORTER_TYPE, CREATE_TYPE, DATE_ID,");
            sql.append("SEQ_NO, REPORT_OBJ, REPORT_NO, REPORT_KEY, VERSION_NO, 'Y' as IS_HISTORY, REPORTER, IS_PUBLIC, REASON_NO_PUBLIC, ");
            sql.append("TYPE1, TYPE2, NAME, FEES, CALL_NUM, DATA_NUM, SMS_NUM, INTERNATIONAL_CALL, INTERNATIONAL_ROAMING_DATA, INTERNATIONAL_SMS, ");
            sql.append("ORIENT_TRAFFIC, IPTV, BANDWIDTH, RIGHTS, OTHER_CONTENT, APPLICABLE_PEOPLE, APPLICABLE_AREA, VALID_PERIOD, RESPONSIBILITY, RESTRICTIONS, ONLINE_DAY,");
            sql.append("OFFLINE_DAY, OTHERS, CHANNEL, DURATION, STATUS, DEL_TIME, REASON, PLAN, DEL_ACC, APPLICABLE_PROVINCE, FEES_UNIT, DATA_UNIT, ORIENT_TRAFFIC_UNIT,");
            sql.append(" APPLICABLE_AREA_NAME, REPORTER_NAME, TARIFF_ATTR, AREA_DESC, AREA_SELECT_TYPE, TARIFF_ANOTHER_NAME,VERSION_NUM,UNSUBSCRIBE,EXTRA_FEES,OTHER_FEES,IS_TELECOM  ");
            sql.append("from " + user.getSchemaName() + ".XTY_TARIFF_RECORD");
            sql.append(id, "where ID=?", false);
            logger.info("开始写历史表 --> sql={}", sql.toFullSql());
            query.execute(sql.getSQL(), sql.getParams());

            // 开始更新字段-创建新的一条数据
            String modification = data.getString("modification");
            logger.info("修改字段modification=={}.", modification);
            EasyRecord record = new EasyRecord(user.getSchemaName() + ".XTY_TARIFF_RECORD", "ID");
            record.putAll(historyInfo);
            String hisId = historyInfo.getString("ID");
            record.put("ID", hisId);
            record.put("UPDATE_TIME", dateTime);
            record.put("UPDATE_ACC", user.getUserAcc());

            // 检查权限
            boolean hasFullEditPermission = StringUtils.isBlank(authConfig) || Arrays.asList(authConfig.split(",")).contains(Constants.AUTH_EDIT_ALL);
            boolean hasPartialEditPermission = !hasFullEditPermission &&
                    Arrays.asList(authConfig.split(",")).contains(Constants.AUTH_EDIT_PART);

            if (hasFullEditPermission) {
                record.put("SEQ_NO", data.getString("seq_no"));
                String reporter = data.getString("reporter");
                String reportObj = BusiUtil.getReportObj(reporter);
                record.put("REPORT_OBJ", reportObj);
                record.put("AREA_SELECT_TYPE", data.getString("area_select_type"));
                //			record.put("PLAN",data.getString("plan"));
                if (flag) { // 为排除省份操作-导入、接口、网页  applicable_area为非排除编码
                    record.put("AREA_SELECT_TYPE", "3");
                    record.put("AREA_DESC", data.getString("area_desc")); // 地区描述
                }
                String applicableProvince = data.getString("applicable_province");
                if (StringUtils.isBlank(applicableProvince)) { // 接口来的
                    String applicableArea = data.getString("applicable_area");
                    if ("000".equals(applicableArea)) {
                        applicableProvince = "000";
                        record.put("AREA_SELECT_TYPE", "1");
                    } else {
                        String[] applicableAreaArray = applicableArea.split(",");
                        applicableProvince = BusiUtil.getProvinceByAreaCode(applicableAreaArray);
                    }
                }
                if (StringUtils.isNotBlank(applicableProvince)) {
                    record.put("APPLICABLE_PROVINCE", applicableProvince);
                }

                int areaCount = data.getString("applicable_area").split(",").length;
                String[] provinceSplit = applicableProvince.split(",");
                // 设置资费属性 设置适用区域描述
                if (!flag) { // 1.不是排除操作
                    String provinceNames = getProvinceNames(provinceSplit);
                    // 设置资费属性 设置适用区域描述
                    if (provinceSplit.length > 1) {
                        provinceNames = provinceNames.replaceAll("除", "");
                        int provinceCount = provinceNames.split(",").length;
                        provinceNames = provinceNames + "等" + provinceCount + "个省" + areaCount + "个市";
                        record.put("AREA_DESC", provinceNames); // 多省多地市
                        // record.put("TARIFF_ATTR","1");// 全国

                    } else {
                        String applicableArea = data.getString("applicable_area");
                        if ("000".equals(applicableArea)) {
                            //	record.put("TARIFF_ATTR","1");// 全国
                            record.put("AREA_DESC", "全国");  // 全国
                            record.put("AREA_SELECT_TYPE", "1");
                        } else {
                            String[] applicableAreaArray = applicableArea.split(",");
                            // 获取市编码集
                            String areaNames = getAreaNames(applicableAreaArray);
                            record.put("AREA_DESC", provinceNames + ": " + areaNames); // 一省多地市
                            if (applicableAreaArray.length > 1) {
                                // 根据省获取该省下所有地市个数
                                int allAreaCountByProvince = getAreaCountByProvince(provinceSplit[0]);
                                if (allAreaCountByProvince == applicableAreaArray.length) { // 为该省全部地市
                                    record.put("AREA_DESC", provinceNames + "全部地市"); // 一省全地市
                                }
                                //	record.put("TARIFF_ATTR","2");// 省内
                            } else {
                                // record.put("TARIFF_ATTR","3");// 本地
                            }
                        }
                    }
                } else { // 2.排除操作，不再进行适用区域描述记录
                    // 只设置资费属性
                    //	record.put("TARIFF_ATTR","1");// 全国
                }
                String reporterNo = data.getString("reporter_no");
                //			String reportKey = reporterNo.substring(0, reporterNo.lastIndexOf("v"));
                //			String versionNo = reporterNo.substring(reporterNo.lastIndexOf("v") +1);
                record.put("report_no", reporterNo);
                record.remove("reporter_no");
                record.remove("province");
                record.remove("REPORT_NO");
                record.put("REPORT_KEY", reporterNo);
                int versionNo = historyInfo.getIntValue("VERSION_NO") + 1;
                record.put("VERSION_NO", versionNo);
                //退订方式
                if (StringUtils.isNotBlank(data.getString("unsubscribe"))) {
                    record.put("UNSUBSCRIBE", data.getString("unsubscribe"));
                }
                String versionNum = historyInfo.getString("VERSION_NUM");
                versionNum = Optional.ofNullable(versionNum)
                        .filter(StringUtils::isNotBlank)
                        .map(version -> {
                            String versionSuffix = version.substring(version.lastIndexOf("V") + 1);
                            int newVersion = Integer.parseInt(versionSuffix) + 1;
                            return String.format("V%d", newVersion);
                        })
                        .orElseGet(() -> String.format("V1"));
                record.put("VERSION_NUM", versionNum);
                logger.info("VERSION_NO ={},VERSION_NUM={}", versionNo, record);
                record.put("IS_HISTORY", DictConstants.DICT_SY_YN_N);
                // 增加修改字段
                logger.info("修改字段：{}", modification);
                logger.info("修改字段split：{}", modification.split(","));
                String[] modificationArray = modification.split(",");
                for (String fieldIndex : modificationArray) {
                    String fieldName = modificationMap.get(fieldIndex);
                    String val = data.getString(fieldName);
                    record.put(fieldName.toUpperCase(), val);
                    logger.info("修改字段：{}={},fieldIndex={}", fieldName, val, fieldIndex);
                }
                record.remove("action_type");
                record.remove("area");
                record.put("CALL_NUM", record.getString("CALL"));
                record.remove("CALL");
                record.put("DATA_NUM", record.getString("DATA"));
                record.remove("DATA");
                record.put("SMS_NUM", record.getString("SMS"));
                if (StringUtils.isBlank(record.getString("AREA_SELECT_TYPE"))) {
                    record.put("AREA_SELECT_TYPE", "2");
                }
                record.remove("SMS");

                // 进行市编码保存操作
                record.remove("applicable_area");
                record.remove("tariff_attr");
                record.put("TARIFF_ATTR", data.getString("tariff_attr"));
            } else if (hasPartialEditPermission) {

                // 部分编辑权限，只允许修改三个特定字段
                record.remove("REPORT_NO");
                record.remove("CALL");
                record.remove("DATA");
                record.remove("SMS");
                logger.info("用户只有部分编辑权限，仅允许修改适用地区、上线日期和下线日期");
                // 处理适用地区
                String applicableArea = data.getString("applicable_area");
                if (flag) { // 为排除省份操作-导入、接口、网页  applicable_area为非排除编码
                    record.put("AREA_SELECT_TYPE", "3");
                    record.put("AREA_DESC", data.getString("area_desc")); // 地区描述
                }
                // 更新适用省份
                String applicableProvince = data.getString("applicable_province");
                if (StringUtils.isBlank(applicableProvince)) {
                    if ("000".equals(applicableArea)) {
                        applicableProvince = "000";
                        record.put("AREA_SELECT_TYPE", "1");
                    } else {
                        String[] applicableAreaArray = applicableArea.split(",");
                        applicableProvince = BusiUtil.getProvinceByAreaCode(applicableAreaArray);
                    }
                }
                if (StringUtils.isNotBlank(applicableProvince)) {
                    record.put("APPLICABLE_PROVINCE", applicableProvince);
                }
                // 更新区域描述
                if (!flag) {
                    String[] provinceSplit = applicableProvince.split(",");
                    String provinceNames = getProvinceNames(provinceSplit);

                    if (provinceSplit.length > 1) {
                        provinceNames = provinceNames.replaceAll("除", "");
                        int provinceCount = provinceNames.split(",").length;
                        int areaCount = applicableArea.split(",").length;
                        provinceNames = provinceNames + "等" + provinceCount + "个省" + areaCount + "个市";
                        record.put("AREA_DESC", provinceNames);
                    } else {
                        if ("000".equals(applicableArea)) {
                            record.put("AREA_DESC", "全国");
                            record.put("AREA_SELECT_TYPE", "1");
                        } else {
                            String[] applicableAreaArray = applicableArea.split(",");
                            String areaNames = getAreaNames(applicableAreaArray);
                            record.put("AREA_DESC", provinceNames + ": " + areaNames);

                            if (applicableAreaArray.length > 1) {
                                int allAreaCountByProvince = getAreaCountByProvince(provinceSplit[0]);
                                if (allAreaCountByProvince == applicableAreaArray.length) {
                                    record.put("AREA_DESC", provinceNames + "全部地市");
                                }
                            }
                        }
                    }
                }
                // 版本号处理
                int versionNo = historyInfo.getIntValue("VERSION_NO") + 1;
                record.put("VERSION_NO", versionNo);

                String versionNum = historyInfo.getString("VERSION_NUM");
                versionNum = Optional.ofNullable(versionNum)
                        .filter(StringUtils::isNotBlank)
                        .map(version -> {
                            String versionSuffix = version.substring(version.lastIndexOf("V") + 1);
                            int newVersion = Integer.parseInt(versionSuffix) + 1;
                            return String.format("V%d", newVersion);
                        })
                        .orElseGet(() -> String.format("V1"));
                record.put("VERSION_NUM", versionNum);
                record.put("IS_HISTORY", DictConstants.DICT_SY_YN_N);

                // 修改数组只包含允许的三个字段
                List<String> modifiedFields = new ArrayList<>();
                // 处理上线日期
                if (data.containsKey("online_day")) {
                    String onlineDay = data.getString("online_day");
                    if (StringUtils.isNotBlank(onlineDay)) {
                        record.put("ONLINE_DAY", onlineDay);
                        modifiedFields.add("10");
                    }
                }

                // 处理下线日期
                if (data.containsKey("offline_day")) {
                    String offlineDay = data.getString("offline_day");
                    if (StringUtils.isNotBlank(offlineDay)) {
                        record.put("OFFLINE_DAY", offlineDay);
                        modifiedFields.add("11");
                    } else {
                        record.remove("offline_day");
                        record.put("OFFLINE_DAY", Constants.OFFLINE_DAY_DEFAULT);
                    }
                }
            }//end


            // 资费状态设置 - 对所有权限类型都需要处理
            String onlineDay = data.getString("online_day");
            String offlineDay = data.getString("offline_day");
            String status = "";
            if (StringUtils.isNotBlank(onlineDay) || StringUtils.isNotBlank(offlineDay)) {
                record.put("STATUS", Constants.TARIFF_STATUS_1); // 在售
                status = Constants.TARIFF_STATUS_1;
                if (StringUtils.isNotBlank(onlineDay) && date.compareTo(onlineDay) < 0) {
                    record.put("STATUS", Constants.TARIFF_STATUS_4); // 未售
                    status = Constants.TARIFF_STATUS_4;
                }
                if (StringUtils.isNotBlank(offlineDay) && date.compareTo(offlineDay) > 0) {
                    record.put("STATUS", Constants.TARIFF_STATUS_3); // 下架
                    status = Constants.TARIFF_STATUS_3;
                }
            }
            if(data.containsKey("offline_day") && StringUtils.isBlank(offlineDay)){
                record.remove("offline_day");
                record.put("OFFLINE_DAY", Constants.OFFLINE_DAY_DEFAULT); // 下架日期默认值
            }

            // 进行市编码保存操作
            record.remove("applicable_area");
            record.remove("tariff_attr");
            record.put("TARIFF_ATTR", data.getString("tariff_attr"));
            logger.info("flag:{},applicable_area:{},applicable_area_mapping:{}", flag,data.getString("applicable_area"),data.getString("applicable_area_mapping"));
            record.put("APPLICABLE_AREA", data.getString("applicable_area"));
            logger.info("开始更新表 --> record={}", record);
            query.update(record); //改为更新
            // 如果适用地区被修改则要重新保存地区信息
            String applicableArea = data.getString("applicable_area");
            if (StringUtils.isNotBlank(applicableArea)) {
                // 保存地区信息
                saveAreaInfo(data, user,  id, data.getString("reporter_no"),historyInfo.getString("APPLICABLE_AREA"));
            }
            data.remove("applicable_area");
            /* 5、写入本次修改字段明细 */
            String[] modificationArray = modification.split(",");
            saveTariffUpdateData(data, historyInfo, user, source, query, id, dateTime, modificationArray);
            //6、更新redis缓存
            String reporterNo = data.getString("reporter_no");
            String reporter = data.getString("reporter");
            String reportObj = BusiUtil.getReportObj(reporter);

            TariffReported model = new TariffReported(hisId, reporterNo, StringUtils.isNotBlank(record.getString("NAME"))?record.getString("NAME"):historyInfo.getString("NAME"), historyInfo.getString("NAME"),
                    historyInfo.getString("ENT"), historyInfo.getString("ENT_NAME"), historyInfo.getString("PROVINCE"),
                    historyInfo.getString("PROVINCE_NAME"),StringUtils.isNotBlank(data.getString("online_day"))? data.getString("online_day"):historyInfo.getString("ONLINE_DAY"),
                    StringUtils.isNotBlank(record.getString("OFFLINE_DAY"))? record.getString("OFFLINE_DAY"):historyInfo.getString("OFFLINE_DAY"), RandomKit.uniqueStr());
            model.setStatus(status);
            model.setReporter(reporter);
            String reporterName = BusiUtil.getReporterName(reporter, Constants.getBusiSchema());
            model.setReporterName(reporterName);
            model.setReportObj(reportObj);
            TariffReportedHandler.getInstance().update(model);
            //修改ES
            JSONObject o = new JSONObject();
            o.put("id", id);
            o.put("name", StringUtils.isNotBlank(record.getString("NAME"))?record.getString("NAME"):historyInfo.getString("NAME"));
            o.put("reportNo", reporterNo);
            o.put("provinceCode", record.getString("PROVINCE"));
            o.put("provinceName", record.getString("PROVINCE_NAME"));
            o.put("ent", record.getString("ENT"));
            o.put("entName", record.getString("ENT_NAME"));
            o.put("onlineDay", StringUtils.isNotBlank(data.getString("online_day"))? data.getString("online_day"):historyInfo.getString("ONLINE_DAY"));
            o.put("offlineDay", StringUtils.isNotBlank(record.getString("OFFLINE_DAY"))? record.getString("OFFLINE_DAY"):historyInfo.getString("OFFLINE_DAY"));
            try {
                BusiUtil.sendToEsForUpdate(id, o);
            } catch (Exception e) {
                logger.error("更新ES信息报错",e);
            }

        } catch (SQLException e) {
            logger.error(e.getMessage(), e);
            throw e;
        }

        // 在方法末尾添加，确保修改后的数据同步到备份索引
        try {
            // 同步修改后的数据到备份索引
            String recordId = historyInfo.getString("ID");
            syncToBakIndex( recordId);
            logger.info("修改资费后同步到备份索引成功，ID: {}", recordId);
        } catch (Exception e) {
            logger.error("修改资费后同步到备份索引失败", e);
        }
    }

    private void saveTariffUpdateData(JSONObject data, JSONObject historyInfo, UserModel user, String source,
                                      EasyQuery query, String id, String dateTime, String[] modificationArray) throws SQLException {
        String updateFields = "";
        String tariffUpdateId = IDGenerator.getDefaultNUMID();
        for (String fieldIndex : modificationArray) {
            String fieldName = modificationMap.get(fieldIndex);
            String val = data.getString(fieldName);
            String oldVal = historyInfo.getString(fieldName.toUpperCase());
            if (StringUtils.isNotBlank(updateFields)) {
                updateFields += ",";
            }
            updateFields += fieldName;

            EasyRecord tariffUpdateDetailRecord = new EasyRecord(user.getSchemaName() + ".XTY_TARIFF_UPDATE_DETAIL", "ID");
            tariffUpdateDetailRecord.put("ID", IDGenerator.getDefaultNUMID());
            tariffUpdateDetailRecord.put("TARIFF_UPDATE_ID", tariffUpdateId);
            tariffUpdateDetailRecord.put("FIELD_NAME", fieldName);
            tariffUpdateDetailRecord.put("OLD_VAL", oldVal);
            tariffUpdateDetailRecord.put("NEW_VAL", val);
            query.save(tariffUpdateDetailRecord);
        }
        EasyRecord tariffUpdateRecord = new EasyRecord(user.getSchemaName() + ".XTY_TARIFF_UPDATE", "ID");
        tariffUpdateRecord.put("ID", tariffUpdateId);
        tariffUpdateRecord.put("BUSI_ID", id);
        tariffUpdateRecord.put("BUSI_TABLE_NAME", "XTY_TARIFF_RECORD");
        tariffUpdateRecord.put("UPDATE_TIME", dateTime);
        tariffUpdateRecord.put("UPDATE_TYPE", source);
        tariffUpdateRecord.put("UPDATE_USER", user.getUserAcc());
        tariffUpdateRecord.put("UPDATE_USER_NAME", user.getUserName());
        tariffUpdateRecord.put("UPDATE_DEPT", user.getDeptCode());
        tariffUpdateRecord.put("UPDATE_DEPT_NAME", user.getDeptName());
        tariffUpdateRecord.put("UPDATE_FIELDS", updateFields);
        query.save(tariffUpdateRecord);
    }


    private List<JSONObject> getTariffArea() throws SQLException {
        return XtyTariffContainer.TARIFF_CAFFEINE.get("TARIFF_AREA", k->{
            String sql = "select * from " + Constants.getBusiSchema() + ".xty_tariff_area";
            List<JSONObject> jsonObjects;
            try {
                jsonObjects = QueryFactory.getReadQuery().queryForList(sql, new Object[]{}, new JSONMapperImpl());
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
            return jsonObjects;
        });
    }


    /**
     *根据排除的区域串，获取省编码串
     */
    private String getProvinceCodes(String[] split) {
        List<String> list = Arrays.asList(split);
        try {
            List<JSONObject> tariffArea = getTariffArea();
            Set<String> set = tariffArea.stream().filter(o -> list.contains(o.getString("TARIFF_AREA_CODE"))).map(o -> o.getString("PROVINCE_CODE")).collect(Collectors.toSet());
            if (set != null && !set.isEmpty()) {
                return CollUtil.join(set, ",");
            }
			/*EasyQuery easyQuery = QueryFactory.getReadQuery();
			EasySQL sql1 = new EasySQL("select  distinct PROVINCE_CODE ");
			sql1.append(" from "+Constants.getBusiSchema()+".xty_tariff_area t1");
			sql1.append(" where 1=1");
			sql1.append("Y", "and t1.BUSI_ORDER = ?");
			if(split.length>1){
				sql1.appendIn(split ," and t1.TARIFF_AREA_CODE  ");
			}else {
				sql1.append(split[0] ," and t1.TARIFF_AREA_CODE = ?  ");
			}
			List<JSONObject> jsonObjects = easyQuery.queryForList(sql1.getSQL(), sql1.getParams(), new JSONMapperImpl());
			String values = "";
			for (int i = 0; i < jsonObjects.size(); i++) {
				if( i == jsonObjects.size()-1){
					values += jsonObjects.get(i).getString("PROVINCE_CODE");
				}else {
					values += jsonObjects.get(i).getString("PROVINCE_CODE")+",";
				}
			}
			return values;*/
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }


    private List<JSONObject> getProvinces() throws SQLException {
        return XtyTariffContainer.TARIFF_CAFFEINE.get("PROVINCES", k->{
            String sql = "select * from "+Constants.getBusiSchema()+".XTY_TARIFF_PROVINCE";
            List<JSONObject> jsonObjects = null;
            try {
                jsonObjects = QueryFactory.getWriteQuery().queryForList(sql, new Object[]{}, new JSONMapperImpl());
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
            return jsonObjects;
        });
    }


    /**
     *根据排除的区域串，获取省编码串
     */
    private String getProvinceNames(String[] split) {
        try {
            List<JSONObject> provinces = getProvinces();
            List<String> list = Arrays.asList(split);
            Set<String> set = provinces.stream().filter(o -> list.contains(o.getString("PROVINCE_CODE"))).map(o -> o.getString("PROVINCE_NAME")).collect(Collectors.toSet());
            if (set != null && !set.isEmpty()) {
                if (set.size() > 1) {
                    return CollUtil.join(set, ",除");
                } else {
                    return CollUtil.get(set, 0);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
		/*try {
			EasyQuery easyQuery = QueryFactory.getReadQuery();
			EasySQL sql1 = new EasySQL("select  GROUP_CONCAT(PROVINCE_NAME SEPARATOR ',除')  a");
			sql1.append(" from CC_PROVINCE t1");
			sql1.append(" where 1=1");
			if (split.length > 1) {
				sql1.appendIn(split, " and t1.PROVINCE_CODE   ");
			} else {
				sql1.append(split[0], " and t1.PROVINCE_CODE = ?   ");
			}
            return easyQuery.queryForString(sql1.getSQL(), sql1.getParams());
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}*/
        return null;
    }


    private static final String PROVINCE_AREA_COUNT = "PAC";

    /**
     *获取该省下所有的地市个数
     */
    private int getAreaCountByProvince(String provinceCode) {
        int result = 0;
        try {
            List<JSONObject> tariffAreas = getTariffArea();
            long count = tariffAreas.stream().filter(o -> StringUtils.equals("Y", o.getString("BUSI_ORDER"))
                    && StringUtils.equals(provinceCode, o.getString("PROVINCE_CODE"))).count();
            result = Convert.toInt(count);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }


    /**
     *获取市编码集
     */
    private String getAreaNames(String[] split) {
        try {
            List<JSONObject> tariffArea = getTariffArea();
            List<String> list = Arrays.asList(split);
            Set<String> collect = tariffArea.stream().filter(o -> StringUtils.equals("Y", o.getString("BUSI_ORDER"))
                            && list.contains(o.getString("TARIFF_AREA_CODE")))
                    .map(o -> o.getString("TARIFF_AREA_NAME"))
                    .collect(Collectors.toSet());
            if (collect.size() > 1) {
                return CollUtil.join(collect, "、");
            } else {
                return CollUtil.get(collect, 0);
            }
			/*EasyQuery easyQuery = QueryFactory.getReadQuery();
			EasySQL sql1 = new EasySQL("select  GROUP_CONCAT(TARIFF_AREA_NAME SEPARATOR '、')  a");
			sql1.append(" from "+Constants.getBusiSchema()+".xty_tariff_area t1");
			sql1.append(" where 1=1");
			sql1.append("Y", "and t1.BUSI_ORDER = ?");
			if(split.length>1){
				sql1.appendIn(split ," and t1.TARIFF_AREA_CODE   ");
			}else {
				sql1.append(split[0] ," and t1.TARIFF_AREA_CODE = ?  ");
			}

			String values = easyQuery.queryForString(sql1.getSQL(), sql1.getParams());
			return values;*/
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 根据省编码串获取市编码串集
     */
    private String getAreaCodes2(String[] split) {
        try {
            List<JSONObject> tariffArea = getTariffArea();
            List<String> list = Arrays.asList(split);
            Set<String> collect = tariffArea.stream().filter(o -> StringUtils.equals("Y", o.getString("BUSI_ORDER"))
                            && list.contains(o.getString("TARIFF_AREA_CODE")))
                    .map(o -> o.getString("TARIFF_AREA_CODE"))
                    .collect(Collectors.toSet());
            return CollUtil.join(collect, ",");
			/*EasyQuery easyQuery = QueryFactory.getReadQuery();
			EasySQL sql2 = new EasySQL("select  TARIFF_AREA_CODE AREA_CODE ");
			sql2.append(" from "+Constants.getBusiSchema()+".xty_tariff_area t1");
			sql2.append(" where 1=1");
			sql2.append("Y", "and t1.BUSI_ORDER = ?");
			if(split.length>1){
				sql2.appendIn(split ," and t1.PROVINCE_CODE    ");
			}else {
				sql2.append(split[0] ," and t1.PROVINCE_CODE = ?  ");
			}
			sql2.append("order by TARIFF_AREA_CODE asc");
			List<JSONObject> list = easyQuery.queryForList(sql2.getSQL(), sql2.getParams(), new JSONMapperImpl());
			String values = list.stream()
					.map(jsonObject -> jsonObject.getString("AREA_CODE"))
					.collect(Collectors.joining(","));
			return values;*/
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 根据省编码串获取市编码串集
     */
    private String getAreaCodes(String[] split) {
        try {
            List<JSONObject> tariffArea = getTariffArea();
            List<String> list = Arrays.asList(split);
            Set<String> collect = tariffArea.stream().filter(o -> StringUtils.equals("Y", o.getString("BUSI_ORDER"))
                            && !list.contains(o.getString("PROVINCE_CODE")))
                    .map(o -> o.getString("TARIFF_AREA_CODE"))
                    .collect(Collectors.toSet());
            return CollUtil.join(collect, ",");
			/*EasyQuery easyQuery = QueryFactory.getReadQuery();
			EasySQL sql2 = new EasySQL("select  TARIFF_AREA_CODE AREA_CODE ");
			sql2.append(" from "+Constants.getBusiSchema()+".xty_tariff_area t1");
			sql2.append(" where 1=1");
			sql2.append("Y", "and t1.BUSI_ORDER = ?");
			if(split.length>1){
				sql2.appendIn(split ," and t1.PROVINCE_CODE not   ");
			}else {
				sql2.append(split[0] ," and t1.PROVINCE_CODE != ?  ");
			}
			List<JSONObject> list = easyQuery.queryForList(sql2.getSQL(), sql2.getParams(), new JSONMapperImpl());
			String values = list.stream()
					.map(jsonObject -> jsonObject.getString("AREA_CODE"))
					.collect(Collectors.joining(","));
			return values;*/
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 新增地市
     */
    private void saveAreaInfo(JSONObject data, UserModel user, String tariffId, String reporterNo,String his_applicable_area) throws SQLException {
        EasyQuery query = QueryFactory.getWriteQuery();
        query.setTimeout(180);
        // 获取所有区域信息
        String applicableArea = data.getString("applicable_area");
        String actionType = data.getString("action_type");
        logger.info("actionType:{},applicableArea:{},his_applicable_are:{}",actionType,applicableArea,his_applicable_area);
        // 如果所选地区是000，表示全国通用
        if (Constants.AREA_CODE_ALL.equals(applicableArea)) {
            // 对于全国通用类型，不论是新增还是修改，都使用原来的逻辑处理
            if (!Constants.ACTION_TYPE_ADD.equals(actionType)) {
                // 全国适用情况下，删除历史数据后重新添加
                deleteAreaInfo(user,query,tariffId);
            }

            // 添加一条全国通用记录
            EasyRecord addAreqRecord = new EasyRecord(user.getSchemaName() + ".XTY_TARIFF_AREQ", "ID");
            addAreqRecord.put("ID", IDGenerator.getDefaultNUMID());
            addAreqRecord.put("TARIFF_RECORD_ID", tariffId);
            addAreqRecord.put("AREA_CODE", Constants.AREA_CODE_ALL);
            addAreqRecord.put("AREA_NAME", "全国");
            addAreqRecord.put("PROVINCE_CODE", Constants.AREA_CODE_ALL);
            addAreqRecord.put("PROVINCE_NAME", "全国");
            addAreqRecord.put("REPORT_NO", reporterNo);
            addAreqRecord.put("TYPE", "1"); // 在售状态
            query.save(addAreqRecord);
            return;
        }
        // 针对非全国通用的情况
        if (Constants.ACTION_TYPE_ADD.equals(actionType)) {
            // 新增资费时，直接添加所有地区
            String[] applicableAreaArray = applicableArea.split(",");
            Set<String> areaCodeSet = new HashSet<>();
            for (String areaCode : applicableAreaArray) {
                areaCodeSet.add(areaCode);
            }

            List<JSONObject> lists = getDeptIndex();
            Set<JSONObject> collect = lists.stream().filter(o -> areaCodeSet.contains(o.getString("AREA_CODE"))).collect(Collectors.toSet());

            String insertSql = "insert into " + Constants.getBusiSchema() + ".xty_tariff_areq (ID,TARIFF_RECORD_ID,AREA_CODE,AREA_NAME,PROVINCE_CODE,PROVINCE_NAME,REPORT_NO,TYPE) values (?,?,?,?,?,?,?,?)";
            List<Object[]> params = new ArrayList<>();

            for (JSONObject areaInfo : collect) {
                params.add(new Object[]{
                        IDGenerator.getDefaultNUMID(),
                        tariffId,
                        areaInfo.getString("AREA_CODE"),
                        areaInfo.getString("AREA_NAME"),
                        areaInfo.getString("PROVINCE_CODE"),
                        areaInfo.getString("PROVINCE_NAME"),
                        reporterNo,
                        "1" // 在售状态
                });
            }

            BakDataThreadManager.excuete(() -> {
                try {
                    logger.info("seqNo={}insertSql:{},params:{}", data.getString("seq_no"), insertSql, params);
                    QueryFactory.getWriteQuery().executeBatch(insertSql, params);
                } catch (SQLException e) {
                    logger.error(e.getMessage(), e);
                }
            });
        } else {
            if(applicableArea.equals(his_applicable_area)){
                logger.info("适用地区未修改，不执行修改地市sql");
                return;
            }
            // 修改资费时，处理地区变更
            // 1. 获取当前资费的所有地区记录
            EasySQL sql = new EasySQL();
            sql.append("select * from " + Constants.getBusiSchema() + ".xty_tariff_areq");
            sql.append("where 1=1");
            sql.append(tariffId, "and TARIFF_RECORD_ID = ? ");
            List<JSONObject> existingAreas = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

            // 2. 解析新的适用地区
            String[] applicableAreaArray = applicableArea.split(",");
            Set<String> newAreaCodeSet = new HashSet<>(Arrays.asList(applicableAreaArray));

            // 3. 创建现有地区代码集合
            Set<String> existingAreaCodes = existingAreas.stream()
                .map(area -> area.getString("AREA_CODE"))
                .collect(Collectors.toSet());

            // 检查是否从全国变为特定地市的情况
            boolean fromAllToSpecific = existingAreaCodes.contains(Constants.AREA_CODE_ALL) && !Constants.AREA_CODE_ALL.equals(applicableArea);

            if (fromAllToSpecific) {
                // 从全国变为特定地市的情况
                logger.info("从全国变为特定地市，需要处理所有地市记录");

                // 1. 先删除全国地区记录
                try {
                    String delSql = "delete from " + Constants.getBusiSchema() + ".xty_tariff_areq where TARIFF_RECORD_ID = ? and AREA_CODE = ?";
                    List<Object[]> delParams = new ArrayList<>();
                    delParams.add(new Object[]{tariffId, Constants.AREA_CODE_ALL});
                    QueryFactory.getWriteQuery().executeBatch(delSql, delParams);
                } catch (SQLException e) {
                    logger.error("删除全国地区记录失败", e);
                }
                // 2. 获取所有地区列表
                List<JSONObject> allAreas = getDeptIndex();

                // 3. 添加所有地区，区分新的适用地区和待下架地区
                List<Object[]> insertAllParams = new ArrayList<>();
                String insertAllSql = "insert into " + Constants.getBusiSchema() + ".xty_tariff_areq (ID,TARIFF_RECORD_ID,AREA_CODE,AREA_NAME,PROVINCE_CODE,PROVINCE_NAME,REPORT_NO,TYPE) values (?,?,?,?,?,?,?,?)";

                for (JSONObject areaInfo : allAreas) {
                    String areaCode = areaInfo.getString("AREA_CODE");
                    // 忽略全国区号
                    if (Constants.AREA_CODE_ALL.equals(areaCode)) {
                        continue;
                    }
                    // 如果在新的地区列表中，设为在售状态，否则设为待下架状态
                    String areaType = newAreaCodeSet.contains(areaCode) ? "1" : "2";
                    insertAllParams.add(new Object[]{
                            IDGenerator.getDefaultNUMID(),
                            tariffId,
                            areaCode,
                            areaInfo.getString("AREA_NAME"),
                            areaInfo.getString("PROVINCE_CODE"),
                            areaInfo.getString("PROVINCE_NAME"),
                            reporterNo,
                            areaType
                    });
                }

                if (!insertAllParams.isEmpty()) {
                    try {
                        logger.info("seqNo={}insertAllSql:{},params数量:{}", data.getString("seq_no"), insertAllSql, insertAllParams.size());
                        QueryFactory.getWriteQuery().executeBatch(insertAllSql, insertAllParams);
                    } catch (SQLException e) {
                        logger.error(e.getMessage(), e);
                    }
                }
                return; // 已处理完所有地区，直接返回
            }
            // 正常情况：非全国变为特定地市的处理流程
            // 4. 将现有但不在新地区列表中的地区标记为"待下架"(TYPE=2)
            String updateSql = "update " + Constants.getBusiSchema() + ".xty_tariff_areq set TYPE = ? where TARIFF_RECORD_ID = ? and AREA_CODE = ?";
            List<Object[]> updateParams = new ArrayList<>();
            for (JSONObject area : existingAreas) {
                String areaCode = area.getString("AREA_CODE");
                // 如果是全国区号，忽略
                if (Constants.AREA_CODE_ALL.equals(areaCode)) {
                    continue;
                }
                // 如果现有区号不在新的地区列表中，标记为待下架
                if (!newAreaCodeSet.contains(areaCode)) {
                    updateParams.add(new Object[]{"2", tariffId, areaCode}); // 2-待下架
                } else {
                    // 如果在新列表中，确保它是"在售"状态
                    String currentType = area.getString("TYPE");
                    if (!"1".equals(currentType)) {
                        updateParams.add(new Object[]{"1", tariffId, areaCode}); // 1-在售
                    }
                }
            }

            // 5. 添加新的地区（不在现有地区中的）
            List<JSONObject> lists = getDeptIndex();
            List<Object[]> insertParams = new ArrayList<>();
            String insertSql = "insert into " + Constants.getBusiSchema() + ".xty_tariff_areq (ID,TARIFF_RECORD_ID,AREA_CODE,AREA_NAME,PROVINCE_CODE,PROVINCE_NAME,REPORT_NO,TYPE) values (?,?,?,?,?,?,?,?)";

            for (String areaCode : newAreaCodeSet) {
                // 如果该地区不在现有记录中，则新增
                if (!existingAreaCodes.contains(areaCode)) {
                    Optional<JSONObject> areaInfoOpt = lists.stream()
                        .filter(o -> areaCode.equals(o.getString("AREA_CODE")))
                        .findFirst();

                    if (areaInfoOpt.isPresent()) {
                        JSONObject areaInfo = areaInfoOpt.get();
                        insertParams.add(new Object[]{
                                IDGenerator.getDefaultNUMID(),
                                tariffId,
                                areaInfo.getString("AREA_CODE"),
                                areaInfo.getString("AREA_NAME"),
                                areaInfo.getString("PROVINCE_CODE"),
                                areaInfo.getString("PROVINCE_NAME"),
                                reporterNo,
                                "1" // 在售状态
                        });
                    }
                }
            }

            // 6. 批量执行更新和插入
            BakDataThreadManager.excuete(() -> {
                try {
                    if (!updateParams.isEmpty()) {
                        logger.info("seqNo={}updateSql:{},params:{}", data.getString("seq_no"), updateSql, updateParams);
                        QueryFactory.getWriteQuery().executeBatch(updateSql, updateParams);
                    }

                    if (!insertParams.isEmpty()) {
                        logger.info("seqNo={}insertSql:{},params:{}", data.getString("seq_no"), insertSql, insertParams);
                        QueryFactory.getWriteQuery().executeBatch(insertSql, insertParams);
                    }
                } catch (SQLException e) {
                    logger.error(e.getMessage(), e);
                }
            });
        }
    }

    private void deleteAreaInfo(UserModel user,EasyQuery query,String tariffId) {
        try {
            EasyRecord areqRecord = new EasyRecord(user.getSchemaName() + ".XTY_TARIFF_AREQ", "TARIFF_RECORD_ID");
            areqRecord.put("TARIFF_RECORD_ID", tariffId);
            query.deleteById(areqRecord);
        } catch (SQLException e) {
            logger.error("删除历史的区域信息失败---saveAreaInfo--", e);
        }
    }

    public List<JSONObject> getDeptIndex() throws SQLException {
        return XtyTariffContainer.TARIFF_CAFFEINE.get("TARIFF_DEPT_INDEXS", k->{
            EasySQL sql = new EasySQL();
            sql.append("select DISTINCT t1.TARIFF_AREA_CODE AREA_CODE, t1.TARIFF_AREA_NAME AREA_NAME, t2.PROVINCE_CODE, t2.PROVINCE_NAME from " + Constants.getBusiSchema() + ".xty_tariff_area t1");
            sql.append("left join "+ Constants.getBusiSchema() + ".xty_tariff_province  t2 on t2.PROVINCE_CODE=t1.PROVINCE_CODE");
            sql.append("where 1=1");
            List<JSONObject> list = null;
            try {
                list = QueryFactory.getReadQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
            return list;
        });
    }

    /**
     * 获取最新的版本
     */
    private JSONObject findNewInfo(JSONObject data, UserModel user) {
        try {
            String originReportNo = data.getString("origin_report_no");
            if(StringUtils.isBlank(originReportNo)){
                return null;
            }
            JSONObject queryParams = new JSONObject();
            JSONObject esQuery = new JSONObject();
            JSONObject bool = new JSONObject();
            JSONArray must = new JSONArray();
            
            if (StringUtils.isNotBlank(originReportNo)) {
                JSONObject reportNoTerm = new JSONObject();
                JSONObject reportNoField = new JSONObject();
                reportNoField.put("REPORT_NO.keyword", originReportNo);
                reportNoTerm.put("term", reportNoField);
                must.add(reportNoTerm);
            }

            bool.put("must", must);
            esQuery.put("bool", bool);
            queryParams.put("query", esQuery);
            queryParams.put("size", 1);
            
            JSONObject esResult = ElasticsearchKit.search(
                Constants.XTY_TARIFF_BAK_INFO_INDEX,
                queryParams
            );
            
            JSONArray hits = esResult.getJSONObject("hits").getJSONArray("hits");
            if (hits != null && !hits.isEmpty()) {
                JSONObject source = hits.getJSONObject(0).getJSONObject("_source");
                logger.info("ES查询结果：{}", source);
                return source;
            }
            
            EasyQuery query = QueryFactory.getWriteQuery();
            EasySQL sql = new EasySQL();
            sql.append("select * from " + user.getSchemaName() + ".XTY_TARIFF_RECORD t1");
            sql.append("where 1=1");
            sql.append(originReportNo, "and t1.REPORT_NO=?", false);
            sql.append(DictConstants.DICT_SY_YN_N, "and t1.IS_HISTORY=?", false);
            sql.append(user.getEpCode(), "and t1.ENT_ID=?", false);
            sql.append(user.getBusiOrderId(), "and t1.BUSI_ORDER_ID=?", false);
            return query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    private void setCustParam(UserModel user, JSONObject data) {
        // 企业信息
        String reporter = data.getString("reporter");
        JSONObject entJson = BusiUtil.getEnt(reporter);
        String reporterType = entJson.getString("reporterType");
        String ent = entJson.getString("ent");
        String entName = entJson.getString("entName");
        data.put("ent", ent);
        data.put("ent_name", entName);
        data.put("reporter_type", reporterType);
    }

    private Set<String> checkFieldComplete(JSONObject data, UserModel user, JSONObject historyInfo,String authConfig) throws Exception {

        String actionType = data.getString("action_type");
        String applicableArea = data.getString("applicable_area");
        // 1.校验字典值、
        // A（新增）、M（修改）、D（删除）C(校验）

        CompletableFuture<List<String>> future1 = CompletableFuture.supplyAsync(() -> {
            List<String> errorMessage = new ArrayList<>();
            checkReporter(data, user, errorMessage);
            checkDict("XTY_TARIFF_ACTION_TYPE", data, "action_type", user, errorMessage);
            checkDict("XTY_TARIFF_ONE_TYPE", data, "type1", user, errorMessage);
            checkDict("XTY_TARIFF_TWO_TYPE", data, "type2", user, errorMessage);
            // 校验资费属性
            // 进行资费属性于适用地区是否匹配--放到checkArea()里面了
            checkDict("XTY_TARIFF_ATTRIBUTES", data, "tariff_attr", user, errorMessage);
            checkDict("XTY_TARIFF_TELECOM", data, "is_telecom", user, errorMessage);
//		if(StringUtils.isNotBlank(data.getString("errorMsg"))){
//			return;
//		}

            // 2.检查必填参数
            if (Constants.ACTION_TYPE_ADD.equals(actionType) || Constants.ACTION_TYPE_VERIFY.equals(actionType)) {
                List<JSONObject> tariffInfoList = getTariffInfo(data.getString("seq_no"), data.getString("reporter"), Constants.getBusiSchema());
                if (tariffInfoList.size() > 0) {
                    errorMessage.add("数据重复，你已新增过该序列号资费！ 方案编号为[" + tariffInfoList.get(0).getString("REPORT_NO") + "]");
//				data.put("errorMsg","数据重复，你已新增过该序列号资费！");
//				return;
                }
                data.remove("origin_report_no");
                data.remove("plan");
                data.remove("reason");
                for (String field : RequiredFields) {
                    String val = data.getString(field);
                    String fieldZhName = FieldZhMap.get(field);
                    if (StringUtils.isBlank(val)) {
                        errorMessage.add("'" + fieldZhName + "'必填参数为空");
                    }
                }
                //试行新接口
                if(data.containsKey("is_telecom")){
                    if(!data.containsKey("offline_day")){
                        errorMessage.add("'下线日期'必填参数为空");
                    }
                    String offlineDay = data.getString("offline_day");
                    if(data.containsKey("offline_day") && StringUtils.isBlank(offlineDay)){
                        errorMessage.add("'下线日期'不能为空");
                    }
                    String type2 = data.getString("type2");
                    if(StringUtils.isNotBlank(type2) && ("6".equals(type2) || "7".equals(type2))){
                        errorMessage.add("'二级分类'字典值无效");
                    }
                }
                //格式化传入资费名称
                if(StringUtils.isNotBlank(data.getString("name"))){
                    data.put("name",BusiUtil.formatFeeName(data.getString("name")));
                }
            }
            if (!(Constants.ACTION_TYPE_ADD.equals(actionType) || Constants.ACTION_TYPE_VERIFY.equals(actionType)) && historyInfo == null) {
                if (Constants.ACTION_TYPE_EDIT.equals(actionType) || Constants.ACTION_TYPE_EDIT_VERIFY.equals(actionType)) {
                    logger.error("找不到需要修改的记录");
                    errorMessage.add("找不到需要修改的记录！");
                } else if (Constants.ACTION_TYPE_DEL.equals(actionType)) {
                    logger.error("找不到需要删除的记录");
                    errorMessage.add("找不到需要删除的记录！");
                }
            }
            if (!(Constants.ACTION_TYPE_ADD.equals(actionType) || Constants.ACTION_TYPE_VERIFY.equals(actionType)) && historyInfo != null) {
                String seqNo1 = historyInfo.getString("SEQ_NO");
                if (StringUtils.isNotBlank(seqNo1) && !seqNo1.equals(data.getString("seq_no")) && (Constants.ACTION_TYPE_EDIT.equals(actionType) || Constants.ACTION_TYPE_EDIT_VERIFY.equals(actionType))) {
//                    if(checkSeqNoExists(data.getString("seq_no"))){
//                        errorMessage.add("修改的序列号已存在！");
//                    }
                    errorMessage.add("不允许修改序列号！");
                } else if (StringUtils.isNotBlank(seqNo1) && !seqNo1.equals(data.getString("seq_no")) && Constants.ACTION_TYPE_DEL.equals(actionType)) {
                    errorMessage.add("序列号不对，删除失败！");
                }
                //update by yx 2025-4-17 客户要求暂时放开删除限制
//                if (Constants.ACTION_TYPE_DEL.equals(actionType) && !"4".equals(historyInfo.getString("STATUS"))) {
//                    errorMessage.add("只能删除未售状态资费，删除失败！");
//                }
            }

            List<String> editRequiredFields = Arrays.asList("seq_no", "reporter", "action_type", "origin_report_no");
            if (Constants.ACTION_TYPE_EDIT.equals(actionType) || Constants.ACTION_TYPE_EDIT_VERIFY.equals(actionType)) {
                data.remove("reason");
                if (StringUtils.isBlank(data.getString("tariff_attr")) && StringUtils.isNotBlank(data.getString("applicable_area"))) {
                    data.put("tariff_attr", historyInfo.getString("TARIFF_ATTR"));
                }
                for (String field : editRequiredFields) {
                    String val = data.getString(field);
                    String fieldZhName = FieldZhMap.get(field);
                    if (!StringUtils.isNotBlank(val)) {
                        logger.error("'{}'必填参数为空", fieldZhName);
                        errorMessage.add("'"+fieldZhName + "'必填参数为空");
                    }
                }
                if(data.containsKey("is_telecom")){
                    String offlineDay = data.getString("offline_day");
                    if(data.containsKey("offline_day") && StringUtils.isBlank(offlineDay)){
                        errorMessage.add("'下线日期'不能为空");
                    }
                    String type2 = data.getString("type2");
                    if(StringUtils.isNotBlank(type2) && ("6".equals(type2) || "7".equals(type2))){
                        errorMessage.add("'二级分类'字典值无效");
                    }
                }
                //格式化传入资费名称
                if(StringUtils.isNotBlank(data.getString("name"))){
                    data.put("name",BusiUtil.formatFeeName(data.getString("name")));
                }
            }
            List<String> delRequiredFields = Arrays.asList("seq_no", "reporter", "action_type", "origin_report_no");
            if (Constants.ACTION_TYPE_DEL.equals(actionType)) {
                for (String field : delRequiredFields) {
                    String val = data.getString(field);
                    String fieldZhName = FieldZhMap.get(field);
                    if (!StringUtils.isNotBlank(val)) {
                        logger.error("'{}'必填参数为空", fieldZhName);
                        errorMessage.add("'" + fieldZhName + "'必填参数为空");
                    }
                }
            }

            if (historyInfo != null) {
                if ("2".equals(historyInfo.getString("STATUS"))) {
                    errorMessage.add("该资费已被删除，不可操作！");
//				data.put("errorMsg","该资费已被删除，不可操作！");
//				return;
                }
                historyInfo.put("CALL", historyInfo.getString("CALL_NUM"));
                historyInfo.put("DATA", historyInfo.getString("DATA_NUM"));
                historyInfo.put("SMS", historyInfo.getString("SMS_NUM"));
            }


            /*
              校验数字格式-必须为大于0整数或空或-
              1.短信
             */
            List<String> numberCheckList = Arrays.asList("sms");
            for (String field : numberCheckList) {
                String val = data.getString(field);
                if (StringUtils.isNotBlank(val) && !"-".equals(val)) {
                    if (!isNonNegativeInteger(val)) {
                        errorMessage.add("'" + FieldZhMap.get(field) + "'参数错误,应为非负整型数字或空或-");
//					data.put("errorMsg","'"+ FieldZhMap.get(field) +"'参数错误,应为非负整型数字或空或-");
//					return;
                    }
                    // Assert.isTrue(isNonNegativeInteger(val), "'"+ FieldZhMap.get(field) +"'参数错误,应为非负整型数字或空或-");
                }
            }
            /*
              校验数字格式-必须为大于0数字-整数+小数或空或-
              1.资费标准、2.语音、3.通用流量、4.定向流量
             */
            List<String> decimalsCheckList = Arrays.asList("fees", "call", "data", "orient_traffic");
            for (String field : decimalsCheckList) {
                String val = data.getString(field);
                if (StringUtils.isNotBlank(val) && !"-".equals(val)) {
                    if (!isNonNegativeNumber(val)) {
                        errorMessage.add("'" + FieldZhMap.get(field) + "'参数错误,应为非负数字或空或-");
//					data.put("errorMsg","'"+ FieldZhMap.get(field) +"'参数错误,应为非负数字或空或-");
//					return;
                    }
                    // Assert.isTrue(isNonNegativeNumber(val), "'"+ FieldZhMap.get(field) +"'参数错误,应为非负数字或空或-");
                }
            }
            return errorMessage;
        }, BakThreadManager.getExecutorService());

        CompletableFuture<List<String>> future2 = CompletableFuture.supplyAsync(() -> {
            List<String> errorMessage = new ArrayList<>();
            // 校验字段长度
            checkFieldLength(data, "seq_no", 64, errorMessage); // 序列号
//		checkFieldLength(data, "is_public", 1); // 是否公示
//		checkFieldLength(data, "reason_no_public", 500); // 不公示原因
            checkFieldLength(data, "type1", 1, errorMessage); // 一级分类
            checkFieldLength(data, "type2", 1, errorMessage); // 二级分类
            checkFieldLength(data, "is_telecom", 1, errorMessage); // 是否通信类
            checkFieldLength(data, "name", 200, errorMessage); // 资费名称
            checkFieldLength(data, "fees", 20, errorMessage); // 资费标准
            checkFieldLength(data, "fees_unit", 50, errorMessage); // 资费单位
            checkFieldLength(data, "fees_unit", 10, errorMessage); // 资费单位
            checkFieldLength(data, "call", 10, errorMessage); // 语音
            checkFieldLength(data, "data", 15, errorMessage); // 通用流量
            checkFieldLength(data, "data_unit", 50, errorMessage); // 流量单位
            checkFieldLength(data, "extra_fees", 1000, errorMessage); // 超出资费
            checkFieldLength(data, "other_fees", 1000, errorMessage); // 其他费用
            checkFieldLength(data, "sms", 10, errorMessage); // 彩短信
            checkFieldLength(data, "international_call", 10, errorMessage); // 国际语音
            checkFieldLength(data, "international_roaming_data", 10, errorMessage); // 国际漫游流量
            checkFieldLength(data, "international_sms", 10, errorMessage); // 国际短信
            checkFieldLength(data, "orient_traffic", 10, errorMessage); // 定向流量
            checkFieldLength(data, "orient_traffic_unit", 20, errorMessage); // 定向流量单位
            checkFieldLength(data, "iptv", 500, errorMessage); // iptv
            checkFieldLength(data, "bandwidth", 500, errorMessage); // 带宽
            checkFieldLength(data, "rights", 1000, errorMessage); // 权益
            checkFieldLength(data, "other_content", 6000, errorMessage); // 其他服务内容
            checkFieldLength(data, "applicable_people", 4000, errorMessage); // 适用范围
            checkFieldLength(data, "valid_period", 1000, errorMessage); // 有效期限
            checkFieldLength(data, "responsibility", 1000, errorMessage); // 违约责任
            // checkFieldLength(data, "restrictions", 1000); // 限制条件
            checkFieldLength(data, "others", 6000, errorMessage); // 其他事项
            checkFieldLength(data, "channel", 1000, errorMessage); // 销售渠道
            checkFieldLength(data, "duration", 1000, errorMessage); // 在网期限
            checkFieldLength(data, "unsubscribe", 1000, errorMessage); // 退订方式
//		checkFieldLength(data, "reason", 500); // 删除原因
//		checkFieldLength(data, "plan", 6000); // 存量订购用户服务预案
            // 添加修改校验日期字段
            if (Constants.ACTION_TYPE_ADD.equals(actionType) || Constants.ACTION_TYPE_VERIFY.equals(actionType)
                    || Constants.ACTION_TYPE_EDIT.equals(actionType) || Constants.ACTION_TYPE_EDIT_VERIFY.equals(actionType)) {
                String onlineDay = data.getString("online_day");
                String offlineDay = data.getString("offline_day");
                if (StringUtils.isNotBlank(onlineDay)) {
                    if (!isValidDateFormat(onlineDay)) {
                        errorMessage.add("上线日期格式应为YYYYMMDD");
//					data.put("errorMsg","上线日期格式应为YYYYMMDD");
//					return;
                    }
                    // Assert.isTrue(isValidDateFormat(onlineDay), "上线日期格式应为YYYYMMDD");
                }
                if (StringUtils.isNotBlank(offlineDay)) {
                    if (!isValidDateFormat(offlineDay)) {
                        errorMessage.add("下线日期格式应为YYYYMMDD");
//					data.put("errorMsg","下线日期格式应为YYYYMMDD");
//					return;
                    }
                    // Assert.isTrue(isValidDateFormat(offlineDay), "下线日期格式应为YYYYMMDD");
                }
                if (StringUtils.isNotBlank(onlineDay) && StringUtils.isNotBlank(offlineDay)) {
                    if (offlineDay.compareTo(onlineDay) < 0) {
                        errorMessage.add("下线日期小于上线日期");
//					data.put("errorMsg","下线日期小于上线日期！");
//					return;
                    }
                }
                if(StringUtils.isNotBlank(onlineDay) && !isValidDate(onlineDay)){
                    errorMessage.add("上线日期无效");
                }
                if(StringUtils.isNotBlank(offlineDay) && !isValidDate(offlineDay)){
                    errorMessage.add("下线日期无效");
                }
            }

            //修改增加上线时间调整限制 update 2025-2-25
            //去掉上下线日期限制 update by yx 2025-4-29 18:56:53
//            if((Constants.ACTION_TYPE_EDIT.equals(actionType) || Constants.ACTION_TYPE_EDIT_VERIFY.equals(actionType)) && historyInfo != null){
//                String onlineDay = data.getString("online_day");
//                String offlineDay = data.getString("offline_day");
//                String currentOnlineDay = historyInfo.getString("ONLINE_DAY");
//                String currentOfflineDay = historyInfo.getString("OFFLINE_DAY");
//                String currentDate = DateUtil.getCurrentDateStr("yyyyMMdd");
//
//                // 判断是否需要进行时间验证
//                boolean shouldValidateTime = StringUtils.isBlank(authConfig) ||
//                    (StringUtils.isNotBlank(authConfig) &&
//                     !Arrays.asList(authConfig.split(",")).contains(Constants.AUTH_EDIT_ALL) &&
//                     Arrays.asList(authConfig.split(",")).contains(Constants.AUTH_EDIT_PART));
//
//                if (shouldValidateTime) {
//                    validateDateModification(onlineDay, currentOnlineDay, currentDate, "上线", errorMessage);
////                    validateDateModification(offlineDay, currentOfflineDay, currentDate, "下线", errorMessage);
//                }
//
//                //  去掉页面/导入 下线日期校验
//                boolean shouldValidateTime2 = (StringUtils.isNotBlank(authConfig) &&
//                                !Arrays.asList(authConfig.split(",")).contains(Constants.AUTH_EDIT_ALL) &&
//                                Arrays.asList(authConfig.split(",")).contains(Constants.AUTH_EDIT_PART));
//
//                if(shouldValidateTime2){
//                    validateDateModification(offlineDay, currentOfflineDay, currentDate, "下线", errorMessage);
//                }
//
//            }

            // 检查"备案主体""操作类型""一级分类""二级分类""适用地区"等字典值参数
            if (!Constants.ACTION_TYPE_DEL.equals(actionType)) {
                if (StringUtils.isNotBlank(applicableArea) || StringUtils.isNotBlank(data.getString("tariff_attr"))) {
                    try {
                        checkArea(historyInfo, data, applicableArea, user, data.getString("reporter"), data.getString("tariff_attr"), data.getBoolean("areaFlag"), errorMessage);
                    } catch (SQLException e) {
                        logger.error("校验区域数据异常", e);
                    }
                }
            }

            // 操作类型为"修改"和"删除"时，检查"原备案号"
            // 操作类型为"修改"和"删除"时，检查"原备案号"对应的"备案主体"
            if (Constants.ACTION_TYPE_EDIT.equals(actionType) || Constants.ACTION_TYPE_EDIT_VERIFY.equals(actionType) || Constants.ACTION_TYPE_DEL.equals(actionType)) {
                String originReportNo = data.getString("origin_report_no");
                JSONObject originTariffInfo = getOriginTariffInfo(originReportNo, user);
                if (originTariffInfo == null) {
                    errorMessage.add("原方案编号不存在");
//				data.put("errorMsg","原方案编号不存在");
//				return;
                }
                // Assert.isTrue(originTariffInfo!=null, "原备案号不存在");
                String originReporter = originTariffInfo.getString("REPORTER");
                if (!StringUtils.equals(data.getString("reporter"), originReporter)) {
                    errorMessage.add("报送主体参数错误");
//				data.put("errorMsg","报送主体参数错误");
//				return;
                }
                // Assert.isTrue(StringUtils.equals(reporter, originReporter), "备案主体参数错误");
            }
            return errorMessage;
        }, BakThreadManager.getExecutorService());

        Set<String> fetureList = new HashSet<>();
        List<String> fetureList1 = future1.get();
        List<String> fetureList2 = future2.get();
        fetureList.addAll(fetureList1);
        fetureList.addAll(fetureList2);
        return fetureList;
    }

    private void checkFieldLength(JSONObject data, String fieldName, int maxNum, List<String> errorMessage) {
        if (!data.containsKey(fieldName)) {
            return;
        }
        String val = data.getString(fieldName);
        if (StringUtils.isBlank(val)) {
            return;
        }
        String fieldZhName = FieldZhMap.get(fieldName);
        if (val.length() > maxNum) {
            errorMessage.add("'" + fieldZhName + "'参数长度不能超过" + maxNum);
        }
    }

    private boolean isValidDate(String dateStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        sdf.setLenient(false);
        try {
            sdf.parse(dateStr);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

    private void checkReporter(JSONObject data, UserModel user, List<String> errorMessage) {
        String reporter = data.getString("reporter");
        JSONObject ent = BusiUtil.getEnt(reporter);
        if (!StringUtils.isNotBlank(ent.getString("entName"))) {
            logger.error("===entName为空===={}", reporter);
            // update by yx 2024-9-27
            errorMessage.add("'报送主体'参数错误");
//			data.put("errorMsg","'报送主体'参数错误");
//			return;
        }
        // Assert.isTrue(StringUtils.isNotBlank(ent.getString("entName")), "'备案主体'参数错误");
        String reportObj = BusiUtil.getReportObj(reporter);
        String province = getProvinceByReporter(reporter);
        if (!("JT".equals(reportObj) || StringUtils.isNotBlank(province))) {
            logger.error("===province为空====reportObj={}==province={}", reportObj, province);
            errorMessage.add("'报送主体'参数错误");
//			data.put("errorMsg","'报送主体'参数错误");
//			return;
        }
        // Assert.isTrue("JT".equals(reportObj) || StringUtils.isNotBlank(province), "'备案主体'参数错误");
        //备案主体没有部门序号了
//		String deptIndex = reporter.substring(reporter.length()-2);
//		String deptCode = BusiUtil.getDeptCode(deptIndex, province, user.getSchemaName());
//		if (!org.springframework.util.StringUtils.hasText(deptCode)) {
//			logger.error("===deptCode校验不通过====deptCode="+deptCode+"==deptIndex="+deptIndex);
//			data.put("errorMsg","'备案主体'参数错误");
//			return;@
//		}
        // Assert.hasText(deptCode, "'备案主体'参数错误");
    }


    /**
     * 根据备案主体获取省份编码
     */
    public String getProvinceByReporter(String reporter) {
        try {
            String reportObj = BusiUtil.getReportObj(reporter);
            if ("JT".equals(reportObj)) {
                return null;
            }
            List<JSONObject> provinces = getProvinces();
            JSONObject province=XtyTariffContainer.getProvice(reportObj);
            if(province!=null) {
                return province.getString("PROVINCE_CODE");
            }
            Set<JSONObject> collect = provinces.stream()
                    .filter(o -> StringUtils.equals(reportObj, o.getString("TARIFF_PROVINCE_CODE")))
                    .collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(collect)) return CollUtil.get(collect, 0).getString("PROVINCE_CODE");
			/*EasyQuery query = QueryFactory.getWriteQuery();
			EasySQL sql = new EasySQL();
			sql.append("select PROVINCE_CODE from CC_PROVINCE where 1=1");
			sql.append(reportObj, "and TARIFF_PROVINCE_CODE = ?", false);
			String provinceCode = query.queryForString(sql.getSQL(), sql.getParams());
			return provinceCode;*/
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 判断日期是否yyyyMMdd格式
     */
    public static boolean isValidDateFormat(String date) {
        String regex = "^(\\d{4})(\\d{2})(\\d{2})$";
        Pattern pattern = Pattern.compile(regex);
        return pattern.matcher(date).matches();
    }

    private JSONObject getOriginTariffInfo(String originReportNo, UserModel user) {
        try {
            if(StringUtils.isBlank(originReportNo)){
                return null;
            }

            JSONObject queryParams = new JSONObject();
            JSONObject esQuery = new JSONObject();
            JSONObject term = new JSONObject();
            JSONObject reportNoField = new JSONObject();
            
            reportNoField.put("REPORT_NO.keyword", originReportNo);
            term.put("term", reportNoField);
            esQuery.put("query", term);
            queryParams.put("size", 1);
            queryParams.putAll(esQuery);
            
            JSONObject esResult = ElasticsearchKit.search(
                Constants.XTY_TARIFF_BAK_INFO_INDEX,
                queryParams
            );
            
            JSONArray hits = esResult.getJSONObject("hits").getJSONArray("hits");
            if (hits != null && !hits.isEmpty()) {
                JSONObject source = hits.getJSONObject(0).getJSONObject("_source");
                logger.info("ES查询原资费信息结果：{}", source);
                return source;
            }
            EasyQuery query = QueryFactory.getWriteQuery();
            EasySQL sql = new EasySQL("select t1.*");
            sql.append("from " + user.getSchemaName() + ".XTY_TARIFF_RECORD t1");
            sql.append("where 1=1");
            sql.append(originReportNo, "and t1.REPORT_NO = ?", false);
            JSONObject row = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
            return row;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 校验地区
     */
    private void checkArea(JSONObject historyInfo, JSONObject data, String applicableArea, UserModel user, String reporter, String tariffAttr, boolean flag, List<String> errorMessage) throws SQLException {
		/*省企业账号：1.资费属性为"本地"，适用地区允许填写"单地市代码"、2.资费属性为"省内"，适用地区允许填写"多地市""省"、3.资费属性为"全国"，适用地区允许填写 本省
		 集团账号：1.资费属性只能为"全国"，适用地区允许填写 全国、多省、除省*/
        // 进行权限判断
        if (StringUtils.isNotBlank(applicableArea)) {
            checkArea2(data, applicableArea, reporter, tariffAttr, flag, errorMessage);
        } else { // 为只修改资费属性
            String provincePinYinSimple = reporter.substring(0, reporter.length() - 1);
            if ("JT".equals(provincePinYinSimple)) { // 集团
                //update by yx2025-4-8 问题列表366 集团报送资费，资费属性不要再限制为全国
//                if (!"1".equals(tariffAttr)) {
//                    logger.error("'{}'集团资费资费属性只能为1'全国'", FieldZhMap.get("tariff_attr"));
//                    errorMessage.add("'" + FieldZhMap.get("tariff_attr") + "'集团资费资费属性只能为'全国'");
//                }
                //update by yx2025-6-11 集团报送资费，资费属性不能为本地
                if ("3".equals(tariffAttr)) {
                    logger.error("'{}'集团资费资费属性不能为3'本地'", FieldZhMap.get("tariff_attr"));
                    errorMessage.add("'" + FieldZhMap.get("tariff_attr") + "'集团资费资费属性不能为'本地'");
                }

            } else { // 企业
                if (historyInfo == null) return;
                String attr = historyInfo.getString("TARIFF_ATTR");
                if ("3".equals(attr) && !"3".equals(tariffAttr)) { // 原来为本地
                    errorMessage.add("'" + FieldZhMap.get("tariff_attr") + "参数错误,适用地区为单地市,资费属性只能为本地！");
                }
                if ("2".equals(attr) && historyInfo.getString("AREA_DESC").contains("全部") && "3".equals(tariffAttr)) { // 原来为省内
                    errorMessage.add("'" + FieldZhMap.get("tariff_attr") + "参数错误,适用地区为全地市,资费属性不能为本地！");
                }
                if ("2".equals(attr) && !historyInfo.getString("AREA_DESC").contains("全部") && !"2".equals(tariffAttr)) {// 原来为省内
                    errorMessage.add("'" + FieldZhMap.get("tariff_attr") + "参数错误,适用地区为多地市,资费属性只能为省内！");
                }
                if ("1".equals(attr) && "3".equals(tariffAttr)) { // 原来为全国
                    errorMessage.add("'" + FieldZhMap.get("tariff_attr") + "参数错误,适用地区为全地市,资费属性不能为本地！");
                }
            }

        }
    }

    public String getAreaCodeBytariffProvinceSimple(String tariffProvinceSimple) {
        try {
            List<JSONObject> provinces = getProvinces();
            Set<String> provinceCodes = provinces.stream().filter(o -> StringUtils.equals(o.getString("TARIFF_PROVINCE_CODE"), tariffProvinceSimple)).map(o -> o.getString("PROVINCE_CODE")).collect(Collectors.toSet());
            if (CollUtil.isEmpty(provinceCodes)) return "";
            String provinceByReporter = CollUtil.get(provinceCodes, 0);
            logger.info("provinceByReporter=={}", provinceByReporter);
            List<JSONObject> tariffArea = getTariffArea();
            Set<String> collect = tariffArea.stream().filter(o -> StringUtils.equals(provinceByReporter, o.getString("PROVINCE_CODE"))).map(o -> o.getString("TARIFF_AREA_CODE")).collect(Collectors.toSet());
            logger.info("collect=={}", collect);
            return CollUtil.join(collect, ",");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return "";
        }
    }


    private void checkArea2(JSONObject data, String applicableArea, String reporter, String tariffAttr, boolean flag, List<String> errorMessage) throws SQLException {
        if (StringUtils.isBlank(reporter)) return;
        String provincePinYinSimple = reporter.substring(0, reporter.length() - 1);
        String areaAllCodes = getAreaCodeBytariffProvinceSimple(provincePinYinSimple); // 获取到省的全部地市
        String province = data.getString("applicable_province");
        if (StringUtils.isBlank(province)) {
            province = getProvinceByReporter(reporter);
        }

        if (Constants.AREA_CODE_ALL.equals(applicableArea) && StringUtils.isBlank(areaAllCodes)) { // 非省账号
//            if (!"1".equals(tariffAttr)) {
//                logger.error("'{}'集团资费资费属性只能为2'全国'", FieldZhMap.get("tariff_attr"));
//                errorMessage.add("'" + FieldZhMap.get("tariff_attr") + "'集团资费资费属性只能为'全国'");
//            }
            if ("3".equals(tariffAttr)) {
                logger.error("'{}'集团资费资费属性不能为2'本地'", FieldZhMap.get("tariff_attr"));
                errorMessage.add("'" + FieldZhMap.get("tariff_attr") + "'集团资费资费属性不能为'本地'");
            }
        }
        if (StringUtils.isNotBlank(areaAllCodes)) { // 不为空，则为省账号，需进行权限判断
            if (flag) { // 省账号不能操作排除省-直接报错
                logger.error("'{}'参数错误,只能为本省地区 1", FieldZhMap.get("applicable_area"));
                errorMessage.add("'" + FieldZhMap.get("applicable_area") + "'参数错误,只能为本省地区");
            }
            String[] areaAllSplit = areaAllCodes.split(",");
            String[] areaSplit = applicableArea.split(",");
            if (areaAllSplit.length < areaSplit.length) {
                if (true) {
                    logger.error("'{}'参数错误,只能为本省地区 2", FieldZhMap.get("applicable_area"));
                    errorMessage.add("'" + FieldZhMap.get("applicable_area") + "'参数错误,只能为本省地区");
                }
            } else {
                for (String s : areaSplit) {
                    if (!Arrays.asList(areaAllSplit).contains(s)) {
                        logger.error("'{}'参数错误,只能为本省地区 3", FieldZhMap.get("applicable_area"));
                        errorMessage.add("'" + FieldZhMap.get("applicable_area") + "'参数错误,只能为本省地区");
                        break;
                    }
                }
                // 进行省企业资费资费属性校验
                if ("1".equals(tariffAttr)) { // 全国
                    if (areaSplit.length == 0) {
                        logger.error("'{}'资费属性为全国，适用地区应为本省地市", FieldZhMap.get("tariff_attr"));
                        errorMessage.add("'" + FieldZhMap.get("tariff_attr") + "'资费属性为全国，适用地区应为本省地市");
                    }
                } else if ("2".equals(tariffAttr)) { // 省内
                    //直辖市（只有一个市）和海南省（只有一个本地网)单独处理
                    if (!("110000".equals(province) || "120000".equals(province) || "310000".equals(province) || "500000".equals(province) || "8980".equals(applicableArea))) {
                        if (areaSplit.length <= 1) {
                            logger.error("'{}'资费属性为省内，适用地区应为本省多地市", FieldZhMap.get("tariff_attr"));
                            errorMessage.add("'" + FieldZhMap.get("tariff_attr") + "'资费属性为省内，适用地区应为本省多地市");
                        }
                    }
                } else if ("3".equals(tariffAttr)) { // 本地
                    if ("110000".equals(province) || "120000".equals(province) || "310000".equals(province) || "500000".equals(province) || "8980".equals(applicableArea)) {
                        logger.error("直辖市，资费属性只能为省内或者全国");
                        errorMessage.add("直辖市，资费属性只能为省内或者全国");
                    }
                    if (1 != areaSplit.length) {
                        logger.error("'{}'资费属性为本地，适用地区应为本省单地市", FieldZhMap.get("tariff_attr"));
                        errorMessage.add("'" + FieldZhMap.get("tariff_attr") + "'资费属性为本地，适用地区应为本省单地市");
                    }
                }
            }
        } else { // 集团账号-资费属性只能为全国
//            if (!"1".equals(tariffAttr)) {
//                logger.error("'{}'集团资费资费属性只能为3'全国'", FieldZhMap.get("tariff_attr"));
//                errorMessage.add("'" + FieldZhMap.get("tariff_attr") + "'集团资费资费属性只能为'全国'");
//            }
            if ("3".equals(tariffAttr)) {
                logger.error("'{}'集团资费资费属性不能为1'本地'", FieldZhMap.get("tariff_attr"));
                errorMessage.add("'" + FieldZhMap.get("tariff_attr") + "'集团资费资费属性不能为'本地'");
            }
        }
        List<String> applicableAreas = StrUtil.split(applicableArea, ",");
        List<JSONObject> tariffArea = getTariffArea();
        Set<JSONObject> collect = tariffArea.stream().filter(o -> applicableAreas.contains(o.getString("TARIFF_AREA_CODE")) && StringUtils.equals("Y", o.getString("BUSI_ORDER"))).collect(Collectors.toSet());

        if ((collect == null || collect.size() < applicableAreas.size()) && !Constants.AREA_CODE_ALL.equals(applicableArea)) {
            logger.info("'{}'参数错误", FieldZhMap.get("applicable_area"));
            errorMessage.add("'" + FieldZhMap.get("applicable_area") + "'参数错误");
        }
    }

    /**
     * 校验字典值存不存在
     */
    private void checkDict(String dictCode, JSONObject data, String fieldName, UserModel user, List<String> errorMessage) {
        if (!data.containsKey(fieldName) && !"action_type".equals(fieldName)) {
            return;
        }
        String val = data.getString(fieldName);
        if (StringUtils.isBlank(val) && !"action_type".equals(fieldName)) {
//            return;
            logger.error("'{}'字典参数错误，val={}", FieldZhMap.get(fieldName), val);
            errorMessage.add("'" + FieldZhMap.get(fieldName) + "'字典参数错误");
        }
        Map<String, Object> dictMap = DictCache.getMapEnableDictListByGroupCode(user.getEpCode(), dictCode);
        if (!dictMap.containsKey(val)) {
            logger.error("'{}'字典参数错误，val={}", FieldZhMap.get(fieldName), val);
            errorMessage.add("'" + FieldZhMap.get(fieldName) + "'字典参数错误");
        }
    }

    /**
     * 判断传入参数是否是数字且为非负整型
     */
    public static boolean isNonNegativeInteger(String input) {
        // 定义匹配整数的正则表达式
        String regex = "^[0-9]+$";
        // 编译正则表达式
        Pattern pattern = Pattern.compile(regex);
        // 创建匹配器
        Matcher matcher = pattern.matcher(input);
        // 进行匹配
        if (matcher.matches()) {
            // 如果匹配成功，再将字符串转换成整数，判断是否大于等于0
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断传入参数是数字且为非负数
     */
    public static boolean isNonNegativeNumber(String input) {
        // 定义匹配非负数的正则表达式
        String regex = "\\d+(\\.\\d+)?";

        // 编译正则表达式
        Pattern pattern = Pattern.compile(regex);

        // 创建匹配器
        Matcher matcher = pattern.matcher(input);

        // 进行匹配
        return matcher.matches();
    }

    private List<JSONObject> getTariffInfo(String seqNo, String reporter, String schema) {
        try {
            if(StringUtils.isBlank(seqNo) && StringUtils.isBlank(reporter)){
                return new ArrayList<>();
            }

            JSONObject queryParams = new JSONObject();
            JSONObject esQuery = new JSONObject();
            JSONObject bool = new JSONObject();
            JSONArray must = new JSONArray();
            
            if (StringUtils.isNotBlank(seqNo)) {
                JSONObject seqNoTerm = new JSONObject();
                JSONObject seqNoField = new JSONObject();
                seqNoField.put("SEQ_NO.keyword", seqNo);
                seqNoTerm.put("term", seqNoField);
                must.add(seqNoTerm);
            }
            
            if (StringUtils.isNotBlank(reporter)) {
                JSONObject reporterTerm = new JSONObject();
                JSONObject reporterField = new JSONObject();
                reporterField.put("REPORTER.keyword", reporter);
                reporterTerm.put("term", reporterField);
                must.add(reporterTerm);
            }
            
            bool.put("must", must);
            esQuery.put("bool", bool);
            queryParams.put("query", esQuery);
            queryParams.put("size", 1000); // Ensure we get multiple results
            
            JSONObject esResult = ElasticsearchKit.search(
                Constants.XTY_TARIFF_BAK_INFO_INDEX,
                queryParams
            );
            
            JSONArray hits = esResult.getJSONObject("hits").getJSONArray("hits");
            if (hits != null && !hits.isEmpty()) {
                List<JSONObject> resultList = new ArrayList<>();
                for (int i = 0; i < hits.size(); i++) {
                    JSONObject source = hits.getJSONObject(i).getJSONObject("_source");
                    resultList.add(source);
                }
                logger.info("ES查询getTariffInfo成功，结果数: {}", resultList.size());
                return resultList;
            }
            
            EasyQuery query = QueryFactory.getWriteQuery();
            EasySQL sql = new EasySQL();
            sql.append("select * from " + schema + ".XTY_TARIFF_RECORD where 1=1");
            sql.append(seqNo, "and SEQ_NO=?", false);
            sql.append(reporter, "and REPORTER=?", false);
            return query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    private static Map<String, String> FieldZhMap = new HashMap<>();
    private static List<String> RequiredFields;
    private static List<String> RequiredUpFields;
    private static Map<String, String> modificationMap = new HashMap<>();
    public static  List<String> CheckSpellFilds;
    static {
        // 必填字段
        RequiredFields = Arrays.asList("seq_no", "reporter", "type1", "type2", "name", "applicable_people",
                "applicable_area", "valid_period", "channel", "duration", "responsibility", "online_day", "tariff_attr", "unsubscribe");
        // 修改字段
        RequiredUpFields = Arrays.asList("seq_no", "reporter", "name", "applicable_people",
                "applicable_area", "valid_period", "channel", "duration", "responsibility", "online_day", "tariff_attr", "unsubscribe");
        //校验字段拼写
        CheckSpellFilds = Arrays.asList("seq_no","reporter","action_type","type1","type2","name","fees","fees_unit","call","data","data_unit",
                "sms","international_call","international_roaming_data","international_sms","orient_traffic","orient_traffic_unit","iptv",
                "bandwidth","rights","other_content","applicable_people","applicable_area","valid_period","channel","duration","responsibility",
                "restrictions","online_day","offline_day","others","unsubscribe","origin_report_no","modification","tariff_attr","is_telecom","extra_fees","other_fees");

        // 字段中文映射
        FieldZhMap.put("seq_no", "序列号");
        FieldZhMap.put("reporter", "报送主体");
        FieldZhMap.put("action_type", "操作类型");
//		FieldZhMap.put("is_public", "公示");
//		FieldZhMap.put("reason_no_public", "不公示原因");
        FieldZhMap.put("type1", "一级分类");
        FieldZhMap.put("type2", "二级分类");
        FieldZhMap.put("is_telecom", "是否通信类");
        FieldZhMap.put("name", "资费名称");
        FieldZhMap.put("fees", "资费标准");
        FieldZhMap.put("fees_unit", "资费单位");
        FieldZhMap.put("extra_fees", "超出资费");
        FieldZhMap.put("other_fees", "其他费用");
        FieldZhMap.put("call", "语音");
        FieldZhMap.put("data", "通用流量");
        FieldZhMap.put("data_unit", "流量单位");
        FieldZhMap.put("sms", "短信");
        FieldZhMap.put("international_call", "国际语音");
        FieldZhMap.put("international_roaming_data", "国际漫游流量");
        FieldZhMap.put("international_sms", "国际短信");
        FieldZhMap.put("orient_traffic", "定向流量");
        FieldZhMap.put("orient_traffic_unit", "定向流量单位");
        FieldZhMap.put("iptv", "IPTV");
        FieldZhMap.put("bandwidth", "带宽");
        FieldZhMap.put("rights", "权益");
        FieldZhMap.put("other_content", "其他服务内容");
        FieldZhMap.put("applicable_people", "适用范围");
        FieldZhMap.put("applicable_area", "适用地区");
        FieldZhMap.put("valid_period", "有效期限");
        FieldZhMap.put("channel", "销售渠道");
        FieldZhMap.put("duration", "在网要求");
        FieldZhMap.put("responsibility", "违约责任");
        FieldZhMap.put("restrictions", "限制条件");
        FieldZhMap.put("online_day", "上线日期");
        FieldZhMap.put("offline_day", "下线日期");
        FieldZhMap.put("others", "其他事项");
        FieldZhMap.put("unsubscribe", "退订方式");
        // 修改
        FieldZhMap.put("origin_report_no", "原方案编号");
        FieldZhMap.put("modification", "修改内容");
//		FieldZhMap.put("plan", "存量订购用户服务预案");
        // 删除
//		FieldZhMap.put("reason", "删除原因");
        FieldZhMap.put("tariff_attr", "资费属性");
        // 修改内容映射
        modificationMap.put("1", "name");
        modificationMap.put("2", "other_content");
        modificationMap.put("3", "applicable_people");
        modificationMap.put("4", "applicable_area");
        modificationMap.put("5", "valid_period");
        modificationMap.put("6", "channel");
        modificationMap.put("7", "duration");
        modificationMap.put("8", "responsibility");
        // modificationMap.put("9", "restrictions");
        modificationMap.put("10", "online_day");
        modificationMap.put("11", "offline_day");
        modificationMap.put("12", "others");
//		modificationMap.put("13", "plan");
        modificationMap.put("14", "call");
        modificationMap.put("15", "data");
        modificationMap.put("16", "sms");
        modificationMap.put("17", "international_call");
        modificationMap.put("18", "international_roaming_data");
        modificationMap.put("19", "international_sms");
        modificationMap.put("20", "orient_traffic");
        modificationMap.put("21", "iptv");
        modificationMap.put("22", "bandwidth");
        modificationMap.put("23", "rights");
        modificationMap.put("24", "fees");
        modificationMap.put("25", "type1");
        modificationMap.put("26", "type2");
        modificationMap.put("27", "fees_unit");
        modificationMap.put("28", "data_unit");
        modificationMap.put("29", "orient_traffic_unit");
        modificationMap.put("30", "area_select_type");
        modificationMap.put("31", "tariff_attr");
//		modificationMap.put("32", "is_public");
//		modificationMap.put("33", "reason_no_public");
        modificationMap.put("34", "tariff_another_name");
        modificationMap.put("35", "unsubscribe");
        modificationMap.put("36", "is_telecom");
        modificationMap.put("37", "extra_fees");
        modificationMap.put("38", "other_fees");
    }

    /**
     * 验证日期修改是否合法
     * @param newDate 新日期
     * @param currentDate 当前日期
     * @param systemDate 系统日期
     * @param dateType 日期类型(上线/下线)
     * @param errorMessage 错误信息集合
     */
    private void validateDateModification(String newDate, String currentDate, String systemDate,
                                       String dateType, List<String> errorMessage) {
        if (StringUtils.isNotBlank(newDate) && !newDate.equals(currentDate)) {
            if (StringUtils.isNotBlank(currentDate) && systemDate.compareTo(currentDate) > 0) {
                errorMessage.add(String.format("资费已%s，不可修改%s时间",
                    dateType.equals("上线") ? "在售/下架" : "下架",
                    dateType));
            }
        }
    }

    /**
     * 同步资费数据到备份索引
     */
    private void syncToBakIndex(String tariffId) {
        try {
            EasyQuery query = QueryFactory.getWriteQuery();

            // 构建完整的资费对象查询
            EasySQL sql = new EasySQL();
            sql.append("SELECT * FROM " + Constants.getBusiSchema() + ".xty_tariff_record");
            sql.append(" WHERE ID = ?");
            JSONObject tariffRecord = query.queryForRow(sql.getSQL(), new Object[]{tariffId}, new JSONMapperImpl());

            if (tariffRecord != null) {
                // 预处理数值型字段
                String feesStr = tariffRecord.getString("FEES");
                tariffRecord.put("FEES_STR",feesStr);
                if (StringUtils.isNotBlank(feesStr) && !"-".equals(feesStr)) {
                    try {
                        double fees = Double.parseDouble(feesStr);
                        tariffRecord.put("FEES", fees);
                    } catch (Exception e) {
                        logger.warn("转换FEES为数值类型失败: {}", feesStr);
                    }
                } else {
                    tariffRecord.remove("FEES");
                }

                // 获取关联的地区信息
                enrichWithAreaInfo(tariffRecord);

                // 提交到备份索引
                bakInfoInitService.sendTariffToEs().accept(tariffRecord);
                logger.info("成功同步资费ID: {} 到备份索引", tariffId);
            } else {
                logger.warn("找不到需要同步到备份索引的资费记录, ID: {}", tariffId);
            }
        } catch (Exception e) {
            logger.error("同步资费到备份索引失败, ID: {}", tariffId, e);
        }
    }

    /**
     * 为资费信息增加地区数据
     */
    private void enrichWithAreaInfo(JSONObject json) {
        try {
            String tariffRecordId = json.getString("ID");
            if (StringUtils.isBlank(tariffRecordId)) {
                return;
            }

            EasyQuery query = QueryFactory.getWriteQuery();

            // 查询关联的XTY_TARIFF_AREQ表数据（只获取TYPE为1的记录）
            EasySQL areaSql = new EasySQL();
            areaSql.append("SELECT ID, TARIFF_RECORD_ID, AREA_CODE, AREA_NAME, REPORT_NO, PROVINCE_CODE, PROVINCE_NAME, TYPE");
            areaSql.append(" FROM ").append(Constants.getBusiSchema()).append(".XTY_TARIFF_AREQ");
            areaSql.append(" WHERE TARIFF_RECORD_ID = ?");
            areaSql.append(" AND TYPE = '1'"); // 只获取TYPE为1的记录

            List<JSONObject> areaList = query.queryForList(areaSql.getSQL(), new Object[]{tariffRecordId}, new JSONMapperImpl());

            // 创建地区代码和地区名称数组
            JSONArray areaCodes = new JSONArray();
            JSONArray areaNames = new JSONArray();
            JSONArray provinceCodesArray = new JSONArray();

            if (areaList != null && !areaList.isEmpty()) {
                for (JSONObject area : areaList) {
                    String areaCode = area.getString("AREA_CODE");
                    String areaName = area.getString("AREA_NAME");
                    String provinceCode = area.getString("PROVINCE_CODE");

                    if (StringUtils.isNotBlank(areaCode)) {
                        areaCodes.add(areaCode);
                    }

                    if (StringUtils.isNotBlank(areaName)) {
                        areaNames.add(areaName);
                    }

                    if (StringUtils.isNotBlank(provinceCode) && !provinceCodesArray.contains(provinceCode)) {
                        provinceCodesArray.add(provinceCode);
                    }
                }
            }else{
                logger.warn("没有找到关联的XTY_TARIFF_AREQ表数据, tariffRecordId: {}", tariffRecordId);
            }

            // 将地区信息添加到资费信息中
            json.put("AREA_LIST", areaList);
            json.put("AREA_CODES", areaCodes);
            json.put("AREA_NAMES", areaNames);
            json.put("PROVINCE_CODES", provinceCodesArray);

            // 构建地区树结构，便于前端展示
            List<JSONObject> areaTree = buildAreaTree(tariffRecordId);
            json.put("AREA_TREE", areaTree);

        } catch (Exception e) {
            logger.error("资费地区信息补充失败, tariffId: {}", json.getString("ID"), e);
        }
    }

    /**
     * 构建地区树结构
     */
    private List<JSONObject> buildAreaTree(String tariffRecordId) {
        List<JSONObject> areaTree = new ArrayList<>();
        try {
            EasyQuery query = QueryFactory.getWriteQuery();
            query.setConvertField(1);

            // 先获取省编码
            EasySQL provinceSql = new EasySQL();
            provinceSql.append("SELECT DISTINCT PROVINCE_CODE as value, PROVINCE_NAME as label FROM ");
            provinceSql.append(Constants.getBusiSchema()).append(".XTY_TARIFF_AREQ");
            provinceSql.append(" WHERE 1 = 1");
            provinceSql.append(" AND PROVINCE_CODE != ''");
            provinceSql.append(" AND TARIFF_RECORD_ID = ?");

            List<JSONObject> provinceList = query.queryForList(provinceSql.getSQL(), new Object[]{tariffRecordId}, new JSONMapperImpl());

            // 获取省下的市信息，组织成树
            for (JSONObject province : provinceList) {
                try {
                    EasySQL areaSql = new EasySQL();
                    areaSql.append("SELECT AREA_CODE as value, AREA_NAME as label FROM ");
                    areaSql.append(Constants.getBusiSchema()).append(".XTY_TARIFF_AREQ");
                    areaSql.append(" WHERE 1 = 1");
                    areaSql.append(" AND TARIFF_RECORD_ID = ?");
                    areaSql.append(" AND PROVINCE_CODE = ?");
                    areaSql.append(" AND TYPE = ?");

                    List<JSONObject> areaList = query.queryForList(
                            areaSql.getSQL(),
                            new Object[]{tariffRecordId, province.getString("value"),Constants.TARIFF_AREA_TYPE_1},
                            new JSONMapperImpl()
                    );

                    province.put("children", areaList);
                } catch (Exception e) {
                    logger.error("构建省市树结构失败, 省份: {}", province.getString("value"), e);
                }
            }

            areaTree = provinceList;
        } catch (Exception e) {
            logger.error("构建地区树结构失败, tariffId: {}", tariffRecordId, e);
        }

        return areaTree;
    }
}
