package com.yunqu.cc.expand.inf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.service.BaseRegInitDataService;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.NoticeHelper;
import com.yunqu.cc.expand.base.Constants;
import com.yunqu.cc.expand.base.QueryFactory;
import com.yunqu.cc.oss.base.CommonLogger;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;

import java.io.File;

public class RegInitDataService extends BaseRegInitDataService {

	private Logger logger = CommonLogger.logger;

	/**
	 * 获取该模块里的定时任务
	 * @return
	 */
	@Override
	public JSONArray getInitJobs() {
		JSONArray jobArray = new JSONArray();
		JSONObject job1 = new JSONObject();
		job1.put("reqMain", "XTY_EXPAND_SERVICE");
		job1.put("reqParam", "dataOld");
		job1.put("name", "每天凌晨01:00处理老化数据,包含操作日志、短信回执、历史工单的工作流数清理等");
		job1.put("jobDesc", "每天凌晨01:00处理老化数据,包含操作日志、历史工单的工作流数清理等");
		job1.put("execType", DictConstants.EXEC_TYPE_BY_DAY);
		job1.put("execRule", "01:00:00");
		job1.put("execDelay", 10);
		job1.put("type", DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job1.put("builtIn", DictConstants.DICT_SY_YN_Y);
		jobArray.add(job1);


		return jobArray;
	}

	/**
	 * 获取本模块所需的数据字段集合
	 * @return
	 */
	@Override
	public JSONArray getInitDict() {
		JSONArray dictArray = new JSONArray();
		return dictArray;
	}


	public JSONObject createDictGroup(String code, String val, String type,String module) {
			JSONObject dictGroup1 = new JSONObject();
			dictGroup1.put("CODE", code);
			dictGroup1.put("NAME", val);
			dictGroup1.put("TYPE", type);
			dictGroup1.put("MODULE", module);
			return dictGroup1;
	}

	@Override
	public String getAppName() {
		return Constants.APP_NAME;
	}

	@Override
	public Logger getLogger() {
		return logger;
	}

	@Override
	public EasyQuery getEasyQuery() {
		return QueryFactory.getWriteQuery();
	}

	@Override
	public JSONArray getNoticeInfo() {
		return NoticeHelper.getNoticeInfo(Constants.APP_NAME);
	}


	@Override
	public JSONObject initData(JSONObject json) {
//		String path = this.getClass().getResource("/").getPath()+ File.separator+"data_init.sql" ;
//		logger.info("初始化脚本文件：" + path);
////		String path2 = this.getClass().getClassLoader().getResource("").getPath()+File.separator+"data_init.sql" ;//保留一版
//		return  CommonUtil.initData(json,getLogger(),getEasyQuery(),path);
		return null;
	}

}
