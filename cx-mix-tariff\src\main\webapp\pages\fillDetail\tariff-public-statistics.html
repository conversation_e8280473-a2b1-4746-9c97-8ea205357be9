<!DOCTYPE html>
<html>

<head>
  <title>公示资费数量变化</title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
  <!-- 基础的 css js 资源 -->
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.4">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.3">
  <link rel="stylesheet" href="./common.css?v=20241127">
  <link rel="stylesheet" href="/cx-mix-tariff/static/css/searchForm.css">
  <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
  <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
  <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
  <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.1"></script>
  <script src="/cc-base/static/js/my_i18n.js?v=202111"></script>
  <script src="/cc-base/static/js/i18n.js?v=1"></script>
  <script src="/cc-base/static/cdn/axios@0.26.1/axios.min.js"></script>
  <style>
    #tariff-public-statistics .search-form.grid-5 {
      gap: 16px 4px !important;
    }
  </style>
</head>

<body class="yq-page-full vue-box">
  <div id="tariff-public-statistics" class="flex yq-table-page" v-auth:[permissions]="'cx-xty-tariff-province-chg-export'">
    <div class="yq-card">
      <div class="card-header">
        <div class="title">{{ getI18nValue('公示资费数量变化') }}</div>
        <div class="yq-table-control">
          <el-button type="primary" plain size="small" @click="handleExport"
            v-if="permissions['cx-xty-tariff-province-chg-export']">
            <i class="el-icon-download"></i>导出
          </el-button>
        </div>
      </div>
      <div class="card-content">
        <div class="search-box">
          <senior-search :show.sync="moreSearch">
            <el-form :inline="false" :model="searchForm" ref="form" class="search-form grid-5" label-width="80px"
              size="small">
              <!-- 默认搜索条件 -->
              <el-form-item label="省份" prop="provinceCode">
                <el-select v-model="searchForm.provinceCode" placeholder="请选择" filterable clearable>
                  <el-option v-for="(item, index) in reportObj" :key="index" :label="item.PROVINCE_NAME"
                    :value="item.PROVINCE_CODE"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="资费状态" prop="tariffState">
                <el-select v-model="searchForm.tariffState" placeholder="请选择" multiple collapse-tags clearable>
                  <el-option v-for="(label, value) in XTY_TARIFF_STATUS" :key="value" :label="label"
                    :value="value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="是否通信类" prop="isTelecom">
                <el-select v-model="searchForm.isTelecom" placeholder="请选择" multiple collapse-tags clearable>
                  <el-option v-for="(label, value) in XTY_TARIFF_TELECOM" :key="value" :label="label"
                    :value="value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="报送主体" prop="tariffReporter">
                <div class="baseflex">
                  <el-cascader v-model="searchForm.label_1" :options="provinceAndGroup" :props="provinceCascaderProps"
                    :show-all-levels="false" placeholder="请选择" clearable style="margin-right: 2px"></el-cascader>
                  <el-select v-model="searchForm.label_2" placeholder="请选择" filterable clearable>
                    <el-option v-for="(label, value) in XTY_REPORTER_ENT" :key="value" :label="label"
                      :value="value"></el-option>
                  </el-select>
                </div>
              </el-form-item>
              <!-- 高级搜索条件 -->
              <template v-if="moreSearch">
                <el-form-item label="一级分类" prop="classicTypeOne">
                  <el-select v-model="searchForm.classicTypeOne" placeholder="请选择" multiple collapse-tags clearable>
                    <el-option v-for="(label, value) in XTY_TARIFF_ONE_TYPE" :key="value" :label="label"
                      :value="value"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="二级分类" prop="classicTypeTwo">
                  <el-select v-model="searchForm.classicTypeTwo" placeholder="请选择" multiple collapse-tags clearable>
                    <el-option v-for="(label, value) in XTY_TARIFF_TWO_TYPE" :key="value" :label="label"
                      :value="value"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="版本" prop="versionNos">
                  <el-select v-model="searchForm.versionNos" placeholder="请选择版本" multiple collapse-tags clearable>
                    <el-option v-for="(label, value) in versionOptions" :key="value" :label="label.VERSION_NO"
                      :value="label.VERSION_NO"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="是否报送" prop="reported">
                  <el-select v-model="searchForm.reported" placeholder="请选择" clearable>
                    <el-option v-for="(label, value) in XTY_TARIFF_CRAWL_REPORT" :key="value" :label="label"
                      :value="value"></el-option>
                  </el-select>
                </el-form-item>
              </template>
              <el-form-item class="btns" label-width="16px">
                <el-button type="primary" plain size="small" icon="el-icon-refresh"
                  @click="handleReset">{{getI18nValue('重置')}}</el-button>
                <el-button type="primary" size="small" icon="el-icon-search"
                  @click="getList">{{getI18nValue('搜索')}}</el-button>
                <el-button type="primary" plain size="small" @click.stop="moreSearch = !moreSearch">
                  <img src="/easitline-cdn/vue-yq/static/imgs/filter.png" alt="">高级搜索
                </el-button>
              </el-form-item>
            </el-form>
          </senior-search>
        </div>
        <!-- 统计表格 -->
        <div class="yq-table">
          <el-table :data="tableData" style="width: 100%" v-loading="loading" border stripe>
            <el-table-column prop="version" label="版本" min-width="150px">
            </el-table-column>
            <el-table-column prop="count1" label="电信" min-width="120px">
              <template slot-scope="scope">
                <el-link v-if="scope.row.version !== '平均'" type="primary" :underline="false"
                  @click="openTab(scope.row,'1')">{{
                  formatNumber(scope.row.count1) }}</el-link>
                <span v-else>{{formatNumber(scope.row.count1)}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="count2" label="移动" min-width="120px">
              <template slot-scope="scope">
                <el-link v-if="scope.row.version !== '平均'" type="primary" :underline="false"
                  @click="openTab(scope.row,'2')">{{
                  formatNumber(scope.row.count2) }}</el-link>
                <span v-else>{{formatNumber(scope.row.count2)}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="count3" label="联通" min-width="120px">
              <template slot-scope="scope">
                <el-link v-if="scope.row.version !== '平均'" type="primary" :underline="false"
                  @click="openTab(scope.row,'3')">{{
                  formatNumber(scope.row.count3) }}</el-link>
                <span v-else>{{formatNumber(scope.row.count3)}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="count5" label="广电" min-width="120px">
              <template slot-scope="scope">
                <el-link v-if="scope.row.version !== '平均'" type="primary" :underline="false"
                  @click="openTab(scope.row,'5')">{{
                  formatNumber(scope.row.count5) }}</el-link>
                <span v-else>{{formatNumber(scope.row.count5)}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="countALL" label="全行业" min-width="120px">
              <template slot-scope="scope">
                <el-link v-if="scope.row.version !== '平均'" type="primary" :underline="false"
                  @click="openTab(scope.row)">{{
                  formatNumber(scope.row.countALL) }}</el-link>
                <span v-else>{{formatNumber(scope.row.countALL)}}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
  <script type="text/javascript" src="/cx-mix-tariff/static/js/time.js"></script>
  <script src="./mixins.js?v=20250723"></script>
  <script>
    var tariffPublicStatistics = new Vue({
      el: '#tariff-public-statistics',
      mixins: [mixins],
      data: function () {
        return {
          searchForm: {
            provinceCode: '',     // 省份代码
            tariffState: [],      // 资费状态
            isTelecom: [],        // 是否通信类
            classicTypeOne: [],   // 一级分类
            classicTypeTwo: [],   // 二级分类
            versionNos: [],       // 版本号
            reported: '',         // 是否报送
            label_1: '',          // 报送主体
            label_2: '',          // 报送主体
          },
          tableData: [],
          loading: false,
          provinceCascaderProps: {
            value: "id",
            label: "name",
            children: "children",
            emitPath: false,
          },
          versionOptions: [],
          XTY_TARIFF_ONE_TYPE: {},
          XTY_TARIFF_TWO_TYPE: {},
          XTY_TARIFF_STATUS: {},
          XTY_TARIFF_TELECOM: {},
          XTY_TARIFF_CRAWL_REPORT: {},
        }
      },
      methods: {
        getPayload() {
          const data = {
            provinceCode: [this.searchForm.provinceCode],
            tariffState: this.searchForm.tariffState,
            isTelecom: this.searchForm.isTelecom,
            classicTypeOne: this.searchForm.classicTypeOne,
            classicTypeTwo: this.searchForm.classicTypeTwo,
            versionNos: this.searchForm.versionNos,
            reported: [this.searchForm.reported]
          };

          let label_1 = this.findProvince(this.searchForm.label_1, 'id')
          data.tariffReporter = [label_1 ? (label_1.tariffProvinceCode + this.searchForm.label_2) : this.searchForm.label_2]
          return data
        },
        // 获取列表数据
        getList() {
          this.loading = true;
          yq.remoteCall(
            "/cx-mix-tariff/webcall?action=tariffPublicLibStatDao.provTariffChgStat",
            this.getPayload()
          ).then((res) => {
            if (res.state == 1) {
              this.tableData = res.data || [];
            } else {
              this.$message.error(res.msg || '获取数据失败');
              this.tableData = [];
            }
          }).finally(() => {
            this.loading = false;
          })
        },
        handleExport() {
          const data = this.getPayload()
          const url = '/cx-mix-tariff/servlet/tariffPublicLib?action=provPublicChgStat&' + $.param(data);
          yq.remoteCall(
            url,
            {},
            (res) => {
              this.$message({
                type: res.state == 1 ? 'success' : 'error',
                message: res.msg
              })
            }
          )
        },
        // 重置搜索条件
        handleReset() {
          this.$refs.form.resetFields();
          this.searchForm = {
            provinceCode: '',     // 省份代码
            tariffState: [],      // 资费状态
            isTelecom: [],        // 是否通信类
            classicTypeOne: [],   // 一级分类
            classicTypeTwo: [],   // 二级分类
            versionNos: [],       // 版本号
            reported: '',         // 是否报送
            label_1: '',          // 报送主体
            label_2: '',          // 报送主体
          }
          this.moreSearch = false;
          this.getList()
        },
        // 格式化数字显示
        formatNumber(num) {
          if (num === null || num === undefined || num === '') {
            return '0';
          }
          return num
        },
        // 获取报送主体的省份
        getProvinceAndGroup() {
          return yq
            .remoteCall(
              "/cx-mix-tariff/webcall?action=common.queryTariffProvinceTree"
            )
            .then((res) => {
              if (res.state == 1) {
                this.provinceAndGroup = res.data || [];
              }
            });
        },
        getDict() {
          yq.daoCall(
            {
              controls: [
                "common.getDict(XTY_TARIFF_ONE_TYPE)",
                "common.getDict(XTY_TARIFF_TWO_TYPE)",
                "common.getDict(XTY_TARIFF_STATUS)",
                "common.getDict(XTY_TARIFF_TELECOM)",
                "common.getDict(XTY_TARIFF_CRAWL_REPORT)",
              ],
              params: {},
            },
            (data) => {
              this.XTY_TARIFF_ONE_TYPE =
                data["common.getDict(XTY_TARIFF_ONE_TYPE)"].data;
              this.XTY_TARIFF_TWO_TYPE =
                data["common.getDict(XTY_TARIFF_TWO_TYPE)"].data;
              this.XTY_TARIFF_STATUS =
                data["common.getDict(XTY_TARIFF_STATUS)"].data;
              this.XTY_TARIFF_TELECOM =
                data["common.getDict(XTY_TARIFF_TELECOM)"].data;
              this.XTY_TARIFF_CRAWL_REPORT =
                data["common.getDict(XTY_TARIFF_CRAWL_REPORT)"].data;
            },
            { contextPath: "cx-mix-tariff" }
          );

          yq.remoteCall(
            "/cx-mix-tariff/webcall?action=crawlerTariffData.getVersionList"
          ).then((res) => {
            this.versionOptions = res.data || [];
          });
        },
        openTab(row, ent) {
          const data = {
            sourceChannel: 'tariff-public-statistics',
            provinceName: this.searchForm.provinceCode,
            entName: ent,
            isTelecom: this.searchForm.isTelecom.join(),
            label_1: this.searchForm.label_1,
            label_2: this.searchForm.label_2,
            versionNos: row.version,
            tariffState: this.searchForm.tariffState.join(),
            classicTypeOne: this.searchForm.classicTypeOne.join(),
            classicTypeTwo: this.searchForm.classicTypeTwo.join(),
            reported: this.searchForm.reported,
          }
          top.popup.openTab({
            url: '/cx-mix-tariff/pages/undisclosed-tariff-statistics/check.html',
            title: '公示字段检查（全行业）',
            data,
            id: 'cx-xty-public-tariff-list-crawl-check',
          });
        },
      },
      mounted() {
      },
    })
  </script>
</body>

</html>