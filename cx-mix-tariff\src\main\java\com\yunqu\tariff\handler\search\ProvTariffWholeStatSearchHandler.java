package com.yunqu.tariff.handler.search;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.enums.EntEnum;
import com.yunqu.tariff.factory.EsQueryAggsBuilder;
import com.yunqu.tariff.factory.EsQueryFactory;
import com.yunqu.tariff.handler.SearchHandler;
import com.yunqu.xty.commonex.kit.ElasticsearchKit;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.yunqu.tariff.base.Constants.XTY_TARIFF_PUBLIC_LIB_INDEX;

/**
 * 获取省份下资费变更统计
 */
public class ProvTariffWholeStatSearchHandler implements SearchHandler<JSONObject> {

    /**
     * 全部（省份/行业）
     */
    private static final String ALL = "999999";

    /**
     * 二级分类不含港澳台 ：（套餐+加装包+营销活动+国内（不含港澳台）标准资费）
     */
    private static final List<String> TYPES2_NOT_GAT = Arrays.asList("1", "2", "3", "5", "7");

    /**
     * 二级分类含港澳台
     */
    private static final String[] TYPES2_GAT = new String[]{"4", "6"};

    private JSONObject param;

    public ProvTariffWholeStatSearchHandler(JSONObject param) {
        this.param = param;
    }

    /**
     * 公众 + 在售 + 非港澳台 资费 (或指定二级分类)
     *
     * @return
     */
    private static JSONObject buildPublicOnSaleDomesticQuery(String... type2) {
        if (type2 == null || type2.length == 0 || StringUtils.isBlank(type2[0])) {
            return new JSONObject()
                    .fluentPut("bool", new JSONObject()
                            .fluentPut("must", Arrays.asList(
                                    new JSONObject().fluentPut("term", new JSONObject().fluentPut("classic_type_one.keyword", "1")),
                                    new JSONObject().fluentPut("term", new JSONObject().fluentPut("tariff_state.keyword", "1")),
                                    new JSONObject().fluentPut("terms", new JSONObject().fluentPut("classic_type_two.keyword", TYPES2_NOT_GAT))
                            ))
                    );
        }
        return new JSONObject()
                .fluentPut("bool", new JSONObject()
                        .fluentPut("must", Arrays.asList(
                                new JSONObject().fluentPut("term", new JSONObject().fluentPut("classic_type_one.keyword", "1")),
                                new JSONObject().fluentPut("term", new JSONObject().fluentPut("tariff_state.keyword", "1")),
                                new JSONObject().fluentPut("terms", new JSONObject().fluentPut("classic_type_two.keyword", type2))
                        ))
                );
    }

    /**
     * 解析聚合结果为接口响应结构
     *
     * @param esResponse ES 返回的 aggregations 结果（JSONObject）
     * @return 标准化响应结构
     */
    public static JSONObject parseDistributionTotalResult(JSONObject esResponse) {
//        logger.info("ProvTariffWholeStatSearchHandler 查询结果 => {}", esResponse);
        JSONObject data = new JSONObject();
        JSONArray statistics = new JSONArray();

        JSONObject aggregations = esResponse.getJSONObject("aggregations");
        if (aggregations == null) {
            logger.error("聚合结果中未找到 aggregations 字段");
            data.put("statistics", statistics);
            data.put("summary", new JSONObject().fluentPut("totalRecords", 0));
            return data;
        }

        // 1. 解析：省份 + 企业 数据
        List<JSONObject> provinceBuckets = getBuckets(aggregations, "group_by_province");
        for (JSONObject provinceBucket : provinceBuckets) {
            String provinceCode = provinceBucket.getString("key");
            String provinceName = getProvinceName(provinceBucket);

            // 1.1 解析该省下各企业数据
            List<JSONObject> entBuckets = getBuckets(provinceBucket, "group_by_ent");
            for (JSONObject entBucket : entBuckets) {
                String entCode = entBucket.getString("key");
                String entName = EntEnum.getEntName(entCode);

                JSONObject stat = buildStatItem(provinceCode, provinceName, entCode, entName, entBucket);
                statistics.add(stat);
            }

            // 1.2 解析该省“全行业”数据
            JSONObject provinceWholeIndustry = provinceBucket.getJSONObject("province_whole_industry");
            if (provinceWholeIndustry != null) {
                JSONObject stat = buildStatItem(
                        provinceCode,
                        provinceName,
                        "999999",  // 全行业
                        "全行业",
                        provinceWholeIndustry
                );
                statistics.add(stat);
            }
        }

        // 2. 解析：全国 + 企业 数据
        List<JSONObject> nationalByEntBuckets = getBuckets(aggregations, "national_by_ent");
        for (JSONObject entBucket : nationalByEntBuckets) {
            String entCode = entBucket.getString("key");
            String entName = EntEnum.getEntName(entCode);

            JSONObject stat = buildStatItem(
                    "999999",          // 全国
                    "全国",
                    entCode,
                    entName,
                    entBucket
            );
            statistics.add(stat);
        }

        // 3. 解析：全国 “全行业”数据
        JSONObject nationalWholeIndustry = aggregations.getJSONObject("national_whole_industry");
        if (nationalWholeIndustry != null) {
            JSONObject stat = buildStatItem(
                    "999999",
                    "全国",
                    "999999",
                    "全行业",
                    nationalWholeIndustry
            );
            statistics.add(stat);
        }
        data.put("statistics", statistics);
        data.put("summary", new JSONObject().fluentPut("totalRecords", statistics.size()));
        return data;
    }

    // 提取指标的通用方法
    private static long getMetricValue(JSONObject parent, String metricKey) {
        if (parent == null || !parent.containsKey(metricKey)) {
            return 0L;
        }
        JSONObject metricObj = parent.getJSONObject(metricKey);
        if (metricObj == null) return 0L;

        // 优先去去重标签
        if (metricObj.containsKey("uniqueTariff")) {
            return metricObj.getJSONObject("uniqueTariff").getLongValue("value");
        }
        return metricObj.getLongValue("doc_count");
    }

    // 构建单条统计记录
    private static JSONObject buildStatItem(String provinceCode, String provinceName,
                                            String entCode, String entName,
                                            JSONObject metricsContainer) {
        JSONObject stat = new JSONObject();

        // 提取行头信息
        stat.put("provinceCode", provinceCode);
        stat.put("provinceName", provinceName);
        stat.put("entCode", entCode);
        stat.put("entName", entName);

        // 提取指标
        stat.put("tariffCount", getMetricValue(metricsContainer, "total_tariffs"));
        stat.put("enterpriseTariffCount", getMetricValue(metricsContainer, "zq_tariffs"));
        stat.put("notReportedCount", getMetricValue(metricsContainer, "unreported_tariffs"));
        stat.put("publicTariffCount", getMetricValue(metricsContainer, "public_tariffs"));
        stat.put("publicForSaleCount", getMetricValue(metricsContainer, "public_on_sale"));
        stat.put("publicOfflineCount", getMetricValue(metricsContainer, "public_offline"));
        stat.put("publicNotForSaleCount", getMetricValue(metricsContainer, "public_no_sale"));
        stat.put("publicForSaleIntlCount", getMetricValue(metricsContainer, "public_on_sale_intl"));
        stat.put("pfsdCount", getMetricValue(metricsContainer, "public_on_sale_domestic"));
        stat.put("pfsdNationalCount", getMetricValue(metricsContainer, "public_on_sale_domestic_jt"));
        stat.put("pfsdNonTelecomCount", getMetricValue(metricsContainer, "public_on_sale_domestic_non_telecom"));
        stat.put("pfsdTelecomCount", getMetricValue(metricsContainer, "public_on_sale_domestic_telecom"));
        stat.put("pfsdTelecomNationalCount", getMetricValue(metricsContainer, "public_on_sale_domestic_telecom_jt"));
        stat.put("pfsdTelecomPackageCount", getMetricValue(metricsContainer, "public_on_sale_domestic_telecom_1"));
        stat.put("pfsdTelecomAddonCount", getMetricValue(metricsContainer, "public_on_sale_domestic_telecom_2"));
        stat.put("pfsdTelecomMarketingCount", getMetricValue(metricsContainer, "public_on_sale_domestic_telecom_3"));
        stat.put("pfsdTelecomStandardCount", getMetricValue(metricsContainer, "public_on_sale_domestic_telecom_5"));

        // 手动计算
        // 公众在售国内（不含港澳台）通信类（省内） = 公众在售国内（不含港澳台）通信类 - 公众在售国内（不含港澳台）通信类（全国）
        stat.put("pfsdTelecomProvincialCount", Math.max(0, stat.getLongValue("pfsdTelecomCount") - stat.getLongValue("pfsdTelecomNationalCount")));
        // 公众在售国内（不含港澳台）（省内） = 公众在售国内（不含港澳台） - 公众在售国内（不含港澳台）（全国）
        stat.put("pfsdProvincialCount", Math.max(0, stat.getLongValue("pfsdCount") - stat.getLongValue("pfsdNationalCount")));

        return stat;
    }

    // 从聚合结果中提取 buckets
    private static List<JSONObject> getBuckets(JSONObject parent, String aggName) {
        if (parent == null) return new ArrayList<>();
        JSONObject agg = parent.getJSONObject(aggName);
        if (agg == null) return new ArrayList<>();
        JSONArray buckets = agg.getJSONArray("buckets");
        if (buckets == null) return new ArrayList<>();
        return buckets.toJavaList(JSONObject.class);
    }

    private static JSONObject getTopName(String field) {
        return new JSONObject()
                .fluentPut("top_hits", new JSONObject()
                        .fluentPut("size", 1)
                        .fluentPut("_source", new JSONObject()
                                .fluentPut("includes", new String[]{field})));
    }

    private static String getProvinceName(JSONObject provinceBucket) {
        JSONObject hits = provinceBucket.getJSONObject("province_name")
                .getJSONObject("hits");
        if (hits == null) {
            return null;
        }
        JSONArray hitArray = hits.getJSONArray("hits");
        if (hitArray != null && !hitArray.isEmpty()) {
            return hitArray.getJSONObject(0)
                    .getJSONObject("_source")
                    .getString("province_name");
        }
        return null;
    }

    /**
     * 添加去重
     *
     * @param dedupFields 需要用于去重的字段名数组
     * @param aggItem     聚合项
     */
    private static void addDeDupAggs(String[] dedupFields, JSONObject aggItem) {
        // 将去重聚合添加到当前聚合的 'aggs' 下
        JSONObject subsAggs = aggItem.getJSONObject("aggs");
        if (subsAggs == null) {
            aggItem.put("aggs", subsAggs = new JSONObject());
        }
        subsAggs.put("uniqueTariff", buildDedupAggregation(dedupFields));
    }

    /**
     * 构建联合去重的 scripted_metric 聚合
     *
     * @param fields 需要用于联合去重的字段名数组（如 ["tariff_no.keyword", "name.keyword"]）
     *               必须不为 null 且长度 > 0 (由调用方保证)
     * @return 表示去重聚合的 JSONObject
     */
    private static JSONObject buildDedupAggregation(String[] fields) {
        // 假设调用方已确保 fields != null && fields.length > 0

        JSONObject scriptedMetric = new JSONObject();

        // 初始化脚本：创建一个 HashSet 用于存储唯一组合
        scriptedMetric.put("init_script", "state.dedupSet = new HashSet();");

        // 映射脚本：检查字段存在且非空，然后拼接成唯一键并加入集合
        StringBuilder mapScript = new StringBuilder("if (");

        // 使用 Stream API 生成字段检查条件
        String fieldConditions = Arrays.stream(fields)
                .map(field -> String.format("doc.containsKey('%s') && !doc['%s'].empty", field, field))
                .collect(Collectors.joining(" && "));
        mapScript.append(fieldConditions);

        mapScript.append(") { ");

        // 使用 Stream API 生成键拼接逻辑
        String keyConcat = Arrays.stream(fields)
                .map(field -> String.format("doc['%s'].value", field))
                .collect(Collectors.joining(" + '_' + "));
        mapScript.append(String.format("String key = %s; state.dedupSet.add(key); }", keyConcat));

        scriptedMetric.put("map_script", mapScript.toString());

        // 合并脚本：返回当前分片的状态集合 将 Set 转换为 List (兼容旧版本 Painless)
        scriptedMetric.put("combine_script", "def list = new ArrayList(); for (item in state.dedupSet) { list.add(item); } return list;");

        // 归约脚本：合并所有分片的集合，并返回总大小
        scriptedMetric.put("reduce_script", "def result = new HashSet(); for (s in states) { result.addAll(s); } return result.size();");

        // 封装到顶层聚合对象
        JSONObject dedupAgg = new JSONObject();
        dedupAgg.put("scripted_metric", scriptedMetric);
        return dedupAgg;
    }

    private JSONObject buildDistributionTotalQueryParams() {
        // 1. 获取指标聚合（每次调用都是新对象）
        JSONObject metricsAggs = buildMetricsAggregations();

        // 2. 构建：按运营商聚合（省份内各企业）
        JSONObject groupByEnt = EsQueryAggsBuilder.getInstance()
                .aggCardinalityTerms("ent_code.keyword", 10, metricsAggs);

        // 3. 构建“省份全行业”聚合（使用 filter + aggs 包装）
        JSONObject provinceWholeIndustry = new JSONObject()
                .fluentPut("filter", new JSONObject().fluentPut("match_all", new JSONObject())) // 合法聚合类型
                .fluentPut("aggs", buildMetricsAggregations()); // 内部放指标

        // 4. 构建：按省份聚合
        JSONObject groupByProvince = new JSONObject()
                .fluentPut("terms", new JSONObject()
                        .fluentPut("field", "province_code.keyword")
                        .fluentPut("size", 100))
                .fluentPut("aggs", new JSONObject()
                        .fluentPut("province_name", getTopName("province_name"))
                        .fluentPut("group_by_ent", groupByEnt)  // 省份 + 运营商
                        .fluentPut("province_whole_industry", provinceWholeIndustry) // 省份全行业
                );

        // 5. 构建：全国各企业维度
        JSONObject nationalByEnt = EsQueryAggsBuilder.getInstance()
                .aggCardinalityTerms("ent_code.keyword", 10, buildMetricsAggregations("tariff_no.keyword", "name.keyword"));

        // 6. 构建“全国全行业”聚合
        JSONObject nationalWholeIndustry = new JSONObject()
                .fluentPut("filter", new JSONObject().fluentPut("match_all", new JSONObject()))
                .fluentPut("aggs", buildMetricsAggregations("tariff_no.keyword", "name.keyword"));

        // 7. 组合根聚合
        JSONObject rootAggregations = new JSONObject();
        rootAggregations.put("group_by_province", groupByProvince);  // 按省份 + 运营商
        rootAggregations.put("national_by_ent", nationalByEnt);      // 全国各企业
        rootAggregations.put("national_whole_industry", nationalWholeIndustry);  // 全国全行业

        // 8. 构建查询
        EsQueryFactory.EsQueryParamBuilder builder = EsQueryFactory.queryParamBuilder()
                .term("version_nos.keyword", param.getString("version")) // 版本号
                .terms("ent_code.keyword", param.getString("entCode"))
                .terms("province_code.keyword",
                        StringUtils.contains(param.getString("provinceCode"), ALL) ?
                                null : param.getString("provinceCode"))  // 省份代码(如果包含全部则不设置)
                .aggs(rootAggregations).size(0);
        JSONObject requestParam = builder.buildRequestParam();
        logger.info("ProvTariffWholeStatSearchHandler 查询参数 => {}", requestParam);
        return requestParam;
    }

    /**
     * 构建指标聚合
     *
     * @param dedupFields 可选去重字段数组
     */
    private JSONObject buildMetricsAggregations(String... dedupFields) {
        JSONObject aggs = new JSONObject();

        // 添加指标聚合逻辑
        EsQueryAggsBuilder aggsBuilder = EsQueryAggsBuilder.getInstance();

        // total_tariffs: value_count on tariff_no.keyword
        aggs.put("total_tariffs", aggsBuilder.buildFilter("match_all", new JSONObject()));

        // public_tariffs: classic_type_one = "1"
        aggs.put("public_tariffs", aggsBuilder.buildFilterTerm(
                new JSONObject().fluentPut("classic_type_one.keyword", "1")));

        // zq_tariffs: classic_type_one = "2"
        aggs.put("zq_tariffs", aggsBuilder.buildFilterTerm(
                new JSONObject().fluentPut("classic_type_one.keyword", "2")));

        // unreported_tariffs: reported 不存在 或 不为 "1"
        aggs.put("unreported_tariffs", aggsBuilder.buildFilterBool("must_not",
                new JSONObject().fluentPut("bool",
                        new JSONObject().fluentPut("must",
                                Arrays.asList(
                                        new JSONObject().fluentPut("term", new JSONObject().fluentPut("reported.keyword", "1")),
                                        new JSONObject().fluentPut("exists", new JSONObject().fluentPut("field", "reported.keyword")))))
        ));

        // public_on_sale: classic_type_one=1 AND tariff_state=1
        aggs.put("public_on_sale", aggsBuilder.buildFilterBool("must",
                new JSONObject().fluentPut("term", new JSONObject().fluentPut("classic_type_one.keyword", "1")),
                new JSONObject().fluentPut("term", new JSONObject().fluentPut("tariff_state.keyword", "1"))
        ));

        // public_offline: classic_type_one=1 AND tariff_state=3
        aggs.put("public_offline", aggsBuilder.buildFilterBool("must",
                new JSONObject().fluentPut("term", new JSONObject().fluentPut("classic_type_one.keyword", "1")),
                new JSONObject().fluentPut("term", new JSONObject().fluentPut("tariff_state.keyword", "3"))
        ));

        // public_on_sale: classic_type_one=1 AND tariff_state=4
        aggs.put("public_no_sale", aggsBuilder.buildFilterBool("must",
                new JSONObject().fluentPut("term", new JSONObject().fluentPut("classic_type_one.keyword", "1")),
                new JSONObject().fluentPut("term", new JSONObject().fluentPut("tariff_state.keyword", "4"))
        ));

        // public_on_sale_intl: type1=1, state=1, type2=4
        aggs.put("public_on_sale_intl", new JSONObject().fluentPut("filter",
                buildPublicOnSaleDomesticQuery(TYPES2_GAT)
        ));

        // 公众在售国内（不含港澳台）: type1=1, state=1, type2!=4
        aggs.put("public_on_sale_domestic", new JSONObject().fluentPut("filter",
                buildPublicOnSaleDomesticQuery()));

        // 公众在售国内（不含港澳台） + 报送主体（集团）
        aggs.put("public_on_sale_domestic_jt", aggsBuilder.buildFilterBool("must",
                buildPublicOnSaleDomesticQuery(),
                new JSONObject().fluentPut("wildcard", new JSONObject().fluentPut("tariff_reporter.keyword", "*JT*"))
        ));
        // 公众在售国内（不含港澳台） + 报送主体（本省） -- 作差计算

        // 公众在售国内（不含港澳台） + 非通信类
        aggs.put("public_on_sale_domestic_non_telecom", aggsBuilder.buildFilterBool("must",
                buildPublicOnSaleDomesticQuery(),
                new JSONObject().fluentPut("term", new JSONObject().fluentPut("is_telecom.keyword", "2"))
        ));

        // 公众在售国内（不含港澳台） + 通信类
        aggs.put("public_on_sale_domestic_telecom", aggsBuilder.buildFilterBool("must",
                buildPublicOnSaleDomesticQuery(),
                new JSONObject().fluentPut("term", new JSONObject().fluentPut("is_telecom.keyword", "1"))
        ));

        // 公众在售国内（不含港澳台）通信类细分
        // 公众在售国内（不含港澳台）通信类 -- 全国 （报送主体（集团））
        aggs.put("public_on_sale_domestic_telecom_jt", aggsBuilder.buildFilterBool("must",
                buildPublicOnSaleDomesticQuery(),
                new JSONObject().fluentPut("term", new JSONObject().fluentPut("is_telecom.keyword", "1")),
                new JSONObject().fluentPut("wildcard", new JSONObject().fluentPut("tariff_reporter.keyword", "*JT*"))
        ));

        // 公众在售国内（不含港澳台）通信类 -- 省内 （报送主体（本省）） -- 作差计算

        // 公众在售国内（不含港澳台）通信类 -- 套餐  (二级分类 = 1）
        aggs.put("public_on_sale_domestic_telecom_1", aggsBuilder.buildFilterBool("must",
                buildPublicOnSaleDomesticQuery("1"),
                new JSONObject().fluentPut("term", new JSONObject().fluentPut("is_telecom.keyword", "1"))
        ));

        // 公众在售国内（不含港澳台）通信类 -- 加装包 (二级分类 = 2）
        aggs.put("public_on_sale_domestic_telecom_2", aggsBuilder.buildFilterBool("must",
                buildPublicOnSaleDomesticQuery("2"),
                new JSONObject().fluentPut("term", new JSONObject().fluentPut("is_telecom.keyword", "1"))
        ));

        // 公众在售国内（不含港澳台）通信类 -- 营销活动 (二级分类 = 3）
        aggs.put("public_on_sale_domestic_telecom_3", aggsBuilder.buildFilterBool("must",
                buildPublicOnSaleDomesticQuery("3"),
                new JSONObject().fluentPut("term", new JSONObject().fluentPut("is_telecom.keyword", "1"))
        ));

        // 公众在售国内（不含港澳台）通信类 -- 国内（不含港澳台）标准资费 (二级分类 = 5）
        aggs.put("public_on_sale_domestic_telecom_5", aggsBuilder.buildFilterBool("must",
                buildPublicOnSaleDomesticQuery("5"),
                new JSONObject().fluentPut("term", new JSONObject().fluentPut("is_telecom.keyword", "1"))
        ));

        // 如果需要添加去重聚合
        if (dedupFields != null && dedupFields.length > 0) {
            // 为每个已定义的聚合添加去重子聚合
            for (String key : aggs.keySet()) {
                JSONObject agg = aggs.getJSONObject(key);
                addDeDupAggs(dedupFields, agg);
            }
        }

        return aggs;
    }

    @Override
    public JSONObject search() {
        //1:构建ES查询条件
        JSONObject queryParams = buildDistributionTotalQueryParams();
        //2:执行ES查询
        JSONObject esResult = ElasticsearchKit.search(XTY_TARIFF_PUBLIC_LIB_INDEX, queryParams);
        //3:解析查询结果
        JSONObject result = parseDistributionTotalResult(esResult);
        return result;
    }
}
