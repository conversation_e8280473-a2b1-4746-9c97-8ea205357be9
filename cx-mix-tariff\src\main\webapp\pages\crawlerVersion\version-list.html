<!DOCTYPE html>
<html>

<head>
  <title>爬虫版本管理</title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
  <!-- 基础的 css js 资源 -->
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.4">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.3">
  <link rel="stylesheet" href="./common.css?v=20241104">
  <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
  <script src="/easitline-cdn/vue-yq/libs/httpVueLoader.js"></script>
  <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
  <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
  <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.1"></script>
  <script src="/cc-base/static/js/my_i18n.js?v=202111"></script>
  <script src="/cc-base/static/js/i18n.js?v=1"></script>
</head>

<body class="yq-page-full vue-box">
  <div id="versionManage" class="flex yq-table-page" v-loading="loading" element-loading-text="加载中..." v-cloak>
    <div class="yq-card">
      <div class="card-header">
        <div class="head-title">{{ getI18nValue('爬虫版本管理') }}</div>
        <div class="yq-table-control">
          <el-button type="primary" plain size="small" icon="el-icon-setting" 
            @click="openVersionConfig">{{getI18nValue('版本调度配置')}}</el-button>
        </div>
      </div>
      <div class="card-content">

        <!-- 搜索区域 -->
        <div class="search-box">
          <senior-search :show.sync="moreSearch">
            <el-form class="search-form grid-5" :inline="false" :model="searchForm" ref="searchForm" size="small"
              label-width="80px">
              <el-form-item :label="getI18nValue('版本号')" prop="versionNo">
                <el-input v-model="searchForm.versionNo" :placeholder="getI18nValue('请输入')" clearable></el-input>
              </el-form-item>
              <el-form-item :label="getI18nValue('版本状态')" prop="status">
                <el-select v-model="searchForm.status" placeholder="请选择" filterable clearable>
                  <el-option label="正常" value="ACTIVE"></el-option>
                  <el-option label="已作废" value="INVALID"></el-option>
                </el-select>
              </el-form-item>
              <template v-if="moreSearch">
                <el-form-item :label="getI18nValue('生成时间')" style="grid-column-start: span 2;">
                  <el-date-picker v-model="searchForm.generateTime" type="datetimerange"
                    value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']" start-placeholder="开始时间"
                    end-placeholder="结束时间" clearable :unlink-panels="true">
                  </el-date-picker>
                </el-form-item>
                <el-form-item :label="getI18nValue('记录数量')" style="grid-column-start: span 2;">
                  <div style="display: flex">
                    <el-input v-model="searchForm.recordCountMin" :placeholder="getI18nValue('最小值')" clearable></el-input>
                    <div style="margin: 0 8px;">~</div>
                    <el-input v-model="searchForm.recordCountMax" :placeholder="getI18nValue('最大值')" clearable></el-input>
                  </div>
                </el-form-item>
              </template>
              <el-form-item class="btns" label-width="0px">
                <el-button type="primary" plain icon="el-icon-refresh" @click="handleReset">重置</el-button>
                <el-button type="primary" icon="el-icon-search" @click="getList(1)">搜索</el-button>
                <el-button type="primary" plain size="small" @click.stop="moreSearch = !moreSearch">
                  <img src="/easitline-cdn/vue-yq/static/imgs/filter.png" alt="">高级搜索
                </el-button>
              </el-form-item>
            </el-form>
          </senior-search>
        </div>

        <!-- 表格区域 - 重点字段展示 -->
        <div class="yq-table">
          <el-table ref="table" :data="tableData.data" style="width: 100%" height="100%" v-loading="tableData.loading"
            border stripe>
            <el-table-column prop="VERSION_NO" :label="getI18nValue('版本号')" width="180" fixed="left"></el-table-column>
            <el-table-column prop="VERSION_STATUS" :label="getI18nValue('版本状态')" width="120">
              <template slot-scope="scope">
                <el-tag size="small" :type="getVersionStatusTagType(scope.row.VERSION_STATUS)">
                  {{formatVersionStatus(scope.row.VERSION_STATUS)}}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="CRAWL_STATUS" :label="getI18nValue('爬取状态')" width="120">
              <template slot-scope="scope">
                <el-tag size="small" :type="getAuditStatusTagType(scope.row.CRAWL_STATUS)" v-if="scope.row.CRAWL_STATUS">
                  {{formatAuditStatus(scope.row.CRAWL_STATUS)}}
                </el-tag>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="CRAWL_START_TIME" :label="getI18nValue('爬取开始时间')" width="180" v-if="showInvalidColumns">
              <template slot-scope="scope">
                {{formatDateTime(scope.row.CRAWL_START_TIME)}}
              </template>
            </el-table-column>
            <el-table-column prop="CRAWL_END_TIME" :label="getI18nValue('爬取结束时间')" width="180" v-if="showInvalidColumns">
              <template slot-scope="scope">
                {{formatDateTime(scope.row.CRAWL_END_TIME)}}
              </template>
            </el-table-column>
            <!-- 作废相关信息 - 动态显示 -->
            <el-table-column prop="INVALID_TIME" :label="getI18nValue('作废时间')" width="180" v-if="showInvalidColumns">
              <template slot-scope="scope">
                {{formatDateTime(scope.row.INVALID_TIME)}}
              </template>
            </el-table-column>
            <el-table-column prop="INVALID_REASON" :label="getI18nValue('作废原因')" min-width="200" show-overflow-tooltip v-if="showInvalidColumns">
              <template slot-scope="scope">
                {{scope.row.INVALID_REASON || '-'}}
              </template>
            </el-table-column>
            <el-table-column prop="CREATE_TIME" :label="getI18nValue('创建时间')" width="180">
              <template slot-scope="scope">
                {{formatDateTime(scope.row.CREATE_TIME)}}
              </template>
            </el-table-column>
            <el-table-column prop="opt" :label="getI18nValue('操作')" min-width="120" fixed="right">
              <template slot-scope="scope">
                <el-link v-if="scope.row.VERSION_STATUS === 'ACTIVE'" class="custlink" type="danger"
                  @click="invalidVersion(scope.row)">{{getI18nValue('版本作废')}}</el-link>
                <el-link v-else-if="scope.row.VERSION_STATUS === 'INVALID'" class="custlink" type="danger"
                  @click="deleteVersion(scope.row)">{{getI18nValue('删除')}}</el-link>
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination background @current-change="onPageChange" @size-change="onPageSizeChange"
            :current-page="tableData.pageIndex" :page-size="tableData.pageSize" :page-sizes="[15, 30, 50, 100]"
            layout="total, prev, pager, next, jumper,sizes" :total="tableData.totalRow">
          </el-pagination>
        </div>
      </div>
    </div>


    <!-- 版本作废弹窗 -->
    <el-dialog title="版本作废" :visible.sync="invalidDialog" width="30%" :close-on-click-modal="false">
      <el-form :model="invalidForm" ref="invalidForm">
        <el-form-item label="作废原因" prop="invalidReason" :rules="[{required: true, message: '请输入作废原因'}]">
          <el-input v-model="invalidForm.invalidReason" type="textarea" :rows="4" maxlength="500" show-word-limit
            placeholder="请输入版本作废原因"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="invalidDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmInvalid" :loading="invalidLoading">确定</el-button>
      </span>
    </el-dialog>

  </div>

  <script type="text/javascript" src="../common/api.js"></script>
  <script src="/cx-mix-tariff/static/js/crawlerVersion/version-list.js"></script>
  <script>
    var versionManage = new Vue({
      el: '#versionManage',
      data: function () {
        return {
          loading: false,
          moreSearch: false,
          searchForm: {
            versionNo: '',
            status: '',
            generateTime: '',
            recordCountMin: '',
            recordCountMax: ''
          },
          tableData: {
            pageIndex: 1,
            pageSize: 15,
            totalRow: 0,
            loading: false,
            data: []
          },
          invalidDialog: false,
          invalidForm: {
            versionId: '',
            invalidReason: ''
          },
          invalidLoading: false,
          deleteDialog: false,
          deleteForm: {
            versionId: '',
            versionNo: ''
          },
          deleteLoading: false,
          showInvalidColumns: false // 控制作废相关列的显示
        }
      },
      computed: {
        // 动态控制作废列显示
        hasInvalidData: function() {
          return this.tableData.data.some(item => item.VERSION_STATUS === 'INVALID')
        }
      },
      watch: {
        // 监听数据变化，动态显示作废列
        'tableData.data': {
          handler: function(newData) {
            this.showInvalidColumns = true
          },
          immediate: true
        }
      },
      methods: {
        // 列表增删改查 分页查询
        onPageChange: function (page) {
          this.tableData.pageIndex = page;
          this.getList();
        },
        onPageSizeChange: function (size) {
          this.tableData.pageSize = size;
          this.getList();
        },
        handleReset: function () {
          this.$refs['searchForm'].resetFields();
          for (var key in this.searchForm) {
            this.searchForm[key] = ""
          }
        },
        // 获取版本列表
        getList: function (page) {
          this.tableData.loading = true
          this.tableData.pageIndex = page || this.tableData.pageIndex
          const data = JSON.parse(JSON.stringify(this.searchForm))
          data.pageIndex = this.tableData.pageIndex
          data.pageSize = this.tableData.pageSize
          
          yq.daoCall({
            controls: ["tariffCrawlerVersion.getVersionList"],
            params: data
          }, (res) => {
            if (res && res['tariffCrawlerVersion.getVersionList']) {
              const result = res['tariffCrawlerVersion.getVersionList']
              if (result.state === 1) {
                this.tableData.data = result.data || []
                this.tableData.totalRow = result.totalRow || 0
              } else {
                this.$message.error(result.msg || '获取版本列表失败')
              }
            }
            this.tableData.loading = false
          }, {
            contextPath: '/cx-mix-tariff'
          })
        },
        // 版本作废
        invalidVersion: function(row) {
          this.invalidForm.versionId = row.ID
          this.invalidForm.invalidReason = ''
          this.invalidDialog = true
          this.$nextTick(() => {
            this.$refs['invalidForm'].resetFields()
          })
        },
        // 确认作废
        confirmInvalid: function() {
          this.$refs['invalidForm'].validate((valid) => {
            if (valid) {
              this.invalidLoading = true
              yq.remoteCall("/cx-mix-tariff/servlet/crawlerVersion?action=InvalidVersion", {
                versionId: this.invalidForm.versionId,
                invalidReason: this.invalidForm.invalidReason
              }, (res) => {
                if (res.state === 1) {
                  this.$message.success('版本作废成功')
                  this.invalidDialog = false
                  this.getList()
                } else {
                  this.$message.error(res.msg || '版本作废失败')
                }
                this.invalidLoading = false
              })
            }
          })
        },
        // 删除版本
        deleteVersion: function(row) {
          this.deleteForm.versionId = row.ID
          this.deleteForm.versionNo = row.VERSION_NO
          this.$confirm(`确定要删除版本 "${row.VERSION_NO}" 吗？删除后不可恢复！`, '删除确认', {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            confirmButtonClass: 'el-button--danger'
          }).then(() => {
            this.confirmDelete()
          }).catch(() => {
            // 用户取消删除
          })
        },
        // 确认删除
        confirmDelete: function() {
          this.deleteLoading = true
          yq.remoteCall("/cx-mix-tariff/servlet/crawlerVersion?action=DeleteVersion", {
            versionId: this.deleteForm.versionId
          }, (res) => {
            if (res.state === 1) {
              this.$message.success('版本删除成功')
              this.getList()
            } else {
              this.$message.error(res.msg || '版本删除失败')
            }
            this.deleteLoading = false
          })
        },
        // 打开版本调度配置（新标签页）
        openVersionConfig: function() {
          top.popup.openTab({
            title: '版本调度配置',
            url: '/cx-mix-tariff/pages/crawlerVersion/version-config.html',
            id: 'versionConfig'
          })
        },
        // 格式化数字
        formatNumber: function(num) {
          if (!num || num === '0') return '0'
          return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        },
        // 格式化时间
        formatDateTime: function(dateTime) {
          if (!dateTime) return '-'
          // 如果是完整的datetime格式，只显示日期和时间部分
          if (dateTime.includes('.0')) {
            return dateTime.replace('.0', '')
          }
          return dateTime
        },
        // 格式化所属日期
        formatBelongDate: function(dateId) {
          if (!dateId) return '-'
          // 将20250722格式转换为2025-07-22
          const dateStr = dateId.toString()
          if (dateStr.length === 8) {
            return dateStr.substring(0, 4) + '-' + dateStr.substring(4, 6) + '-' + dateStr.substring(6, 8)
          }
          return dateId
        },
        // 格式化版本状态
        formatVersionStatus: function(status) {
          return status === 'ACTIVE' ? '正常' : '已作废'
        },
        // 获取版本状态标签类型
        getVersionStatusTagType: function(status) {
          return status === 'ACTIVE' ? 'success' : 'danger'
        },

        // 格式化稽核状态
        formatAuditStatus: function(status) {
          const statusMap = {
            'PENDING': '待爬取',
            'PROCESSING': '爬取中',
            'COMPLETED': '已完成'
          }
          return statusMap[status] || status
        },
        // 获取稽核状态标签类型
        getAuditStatusTagType: function(status) {
          const typeMap = {
            'PENDING': 'info',
            'PROCESSING': 'warning',
            'COMPLETED': 'success'
          }
          return typeMap[status] || 'info'
        }
      },
      mounted() {
        this.getList()
      }
    })
  </script>
</body>

</html>