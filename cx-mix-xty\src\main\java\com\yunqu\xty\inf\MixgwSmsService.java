package com.yunqu.xty.inf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.model.SystemErrorLog;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.LogUtil;
import com.yq.busi.common.util.http.HttpUtil;
import com.yq.busi.common.util.mq.MQBrokerUtil;
import com.yunqu.xty.base.CommonLogger;
import com.yunqu.xty.base.Constants;
import com.yunqu.xty.base.QueryFactory;
import com.yunqu.xty.service.SmsTemplateSysnService;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.crypt.MD5Util;
import org.easitline.common.utils.kit.RandomKit;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class MixgwSmsService extends IService {

	private Logger logger = CommonLogger.getLogger("sms");
	/**
	public static String SMS_URL 		= Constants.smsUrl();//短信平台 接口地址
	public static String SMS_PASSWORD 	= Constants.smsPassword();//短信平台密码
	public static String SMS_BUSI_CODE  = Constants.smsBusiCode();//短信业务代码
	public static String SMS_USER_ID  = Constants.smsUserId();//短信用户ID**/

	public final static String SMS_RETURN_FLAG  = "SMS_RETURN_FLAG_";

	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		String command = json.getString("command");
		if(ServiceCommand.SMS_SEND_EX.equals(command)){
			logger.info("[进入网关发送短信]"+json.toJSONString());
			return smsSendEx(json);
		}
		return null;
	}

	private JSONObject smsSendEx(JSONObject json) {
		JSONObject respJson = new JSONObject();
		JSONObject smsChannel=json.getJSONObject("smsChannel");
		String url=smsChannel.getString("URL");
		String userId=smsChannel.getString("ACCOUNT");
		String passwd=smsChannel.getString("PASSWD");
		String schema = json.getString("schema");
		if (StringUtils.isAnyBlank(url,userId,passwd)) {
			logger.info("[短信渠道参数为空]");
			respJson.put("state", 0);
			respJson.put("msg", "短信渠道参数为空！！！");
			return respJson;
		}
		String smsId = json.getString("smsId");
		JSONObject result = new JSONObject();
		result.put("smsId", smsId);
		logger.info("发送短信["+smsId+"].请求参数: " + json.toString());
		String mobile 	  = json.getString("receiver"); // 手机号码
		//判断有没有扩展字段,兼容
		String exJson = "";
		if(json.containsKey("exJson")){
			exJson = json.getString("exJson");
		}
		String content = json.getString("content");
		JSONObject qtJson=new JSONObject();
		qtJson.put("mobile", mobile);
		qtJson.put("exJson", exJson);
		qtJson.put("content", content);
		qtJson.put("smsId", smsId);
		qtJson.put("smsChannel", smsChannel);
		qtJson.put("serialId", RandomKit.randomStr());
		qtJson.put("timeStamp",System.currentTimeMillis());
		JSONObject param = new JSONObject() {{
			put("smsId", smsId);
			put("schema", schema);
			put("sendStatus", "3");
		}};
		try {
			JSONObject resultObj = MQBrokerUtil.sendQueueMsgAndGetResult(Constants.getSmsServiceBroker(), qtJson, 5);
			logger.info("[处理短信发送返回]"+resultObj);
			if (Objects.isNull(resultObj)) {
				String exist = CacheUtil.get("CACHE_SMS_" + smsId);
				// 检查是否已经返回结果,有返回结果说明已经提交成功
				if (Constants.MAGIC_01.equals(exist)) {
					resultObj.put("respCode", GWConstants.RET_CODE_SUCCESS);
					resultObj.put("respDesc", "发送成功");
				} else {
					String key = SMS_RETURN_FLAG + smsId;
					CacheUtil.put(key,"Y",3*60);
					param.remove("sendStatus");
					resultObj.put("respCode", "999");
					resultObj.put("respDesc", "发送失败,接口未返回结果或接口执行超时");

					SystemErrorLog log = new SystemErrorLog(smsChannel.getString("EP_CODE"), smsChannel.getString("BUSI_ORDER_ID"),Constants.APP_NAME,SystemErrorLog.ERROR_TYPE_SYSTEM, "XTY_T_GD_001","短信发送异常","系统有一条短信未正常发送，手机号码：" + mobile + "！");
					log.setErrorLevel("error");
					LogUtil.addSystemErrorLog(log,logger);
				}

			} else {
				if ("999".equals(resultObj.getString("respCode"))) {
					param.remove("sendStatus");
				}

				SystemErrorLog log = new SystemErrorLog(smsChannel.getString("EP_CODE"), smsChannel.getString("BUSI_ORDER_ID"),Constants.APP_NAME,SystemErrorLog.ERROR_TYPE_SYSTEM, "XTY_T_GD_001","短信发送异常","系统有一条短信未正常发送，手机号码：" + mobile + "！");
				log.setErrorLevel("error");
				LogUtil.addSystemErrorLog(log,logger);
			}
			return resultObj;
		} finally {
			logger.info("同步数据到es:" +param.toJSONString());
			SmsTemplateSysnService.getInstance().sendMessage(param.toJSONString());
		}

	}

	private JSONObject sendSms(String mobile,String exJsonStr,String content,JSONObject smsChannel,String smsId,String schema){
		JSONObject respJson = new JSONObject();
		respJson.put("respDesc", "发送失败");
		respJson.put("respCode", "999");
		respJson.put("msgId", RandomKit.randomStr());
		respJson.put("serialId",smsId);
		 PostMethod post = null;
		try {
			String url=smsChannel.getString("URL");
			String userId=smsChannel.getString("ACCOUNT");
			String passwd=smsChannel.getString("PASSWD");

			if (StringUtils.isAnyBlank(mobile,content)) {
				respJson.put("respDesc", "手机号、内容不能为空");
				return respJson;
			}
			JSONObject exJson= JSONObject.parseObject(exJsonStr);
			List<String> paramList = new ArrayList<String>();
			//获取变量
			getParam(content, paramList);
			if (exJson!=null) {
				content=replaceContent(paramList,content,exJson);
				logger.info("[MixgwSmsService替换后的内容]"+content);
			}
			logger.info("[MixgwSmsService] exJson:"+exJson+",content:"+content+",paramList:"+JSON.toJSONString(paramList));

			Map<String, Object>paramMap=new HashedMap();
			paramMap.put("id",userId);
			paramMap.put("MD5_td_code", MD5Util.getHexMD5(passwd));
			paramMap.put("mobile",  mobile);
			paramMap.put("msg_content",content);
			paramMap.put("msg_id",IDGenerator.getDefaultNUMID());
			paramMap.put("ext","");
			String result=HttpUtil.postHttp(url, paramMap);
			logger.info("[MixgwSmsService]result:"+result);
			if (StringUtils.isNotBlank(result) && result.contains("0#")) {
				respJson.put("respDesc", "发送成功");
				respJson.put("respCode", "000");
				updateSmsInfoEx(smsId,exJson.toJSONString(),schema);
			}
			respJson.put("data", result);

		} catch (Exception e) {
			logger.error("[MixgwSmsService] 异常:"+e.getMessage(),e);
		} finally {
            if(post != null) {
            	post.releaseConnection();
            }
        }
		return respJson;
	}

	public void updateSmsInfoEx(String smsRecordId, String exString,String schema) throws Exception {
		try {

			EasySQL sql = new EasySQL(" UPDATE " + schema + ".c_sms_send_record");
			sql.append(exString," SET EX_JSON = ?");
			sql.append(",DATA1 = '1' ");
			sql.append(smsRecordId," where ID = ? ");


			QueryFactory.getWriteQuery().execute(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 操作出错", e);
		}
	}

	public  List<String> getParam (String content,List<String> paramList){
		int startIndex = content.indexOf("${");
		if (startIndex>=0) {
			String temp = content.substring(content.indexOf("${"));
			int endIndex = temp.indexOf("}");
			String paramStr = temp.substring(2,endIndex);
			temp = temp.substring(paramStr.length()+3);
			paramList.add(paramStr);
			getParam(temp, paramList);
		}
		return paramList;
	}

	public static  String replaceContent(List<String> paramList,String templateContent,JSONObject exJson) {
		if (CommonUtil.listIsNotNull(paramList)) {
			for (String paramKeyStr:paramList) {
				String paramValueStr = exJson.getString(paramKeyStr);

				templateContent=templateContent.replace("${"+paramKeyStr+"}", paramValueStr);
			}
		}

		return templateContent;
	}

	public static void main(String[] args) {

		JSONObject exJson=new JSONObject();
		exJson.put("code", 123);
		exJson.put("url", 44);
		exJson.put("url11", 44);
		String templateContent = "尊敬的用户，你申请的视频会议，确认码为:${code}，请点击下方链接输入确认码进行视频会议:https://yc-136.yunqu-info.com${url}";
		List<String> paramList = new ArrayList<String>();
		//获取变量
		new MixgwSmsService().getParam(templateContent, paramList);
		System.out.println(paramList);
		if (paramList.size()>0) {
			for (String paramKeyStr:paramList) {
				String paramValueStr = exJson.getString(paramKeyStr);

				templateContent=templateContent.replace("${"+paramKeyStr+"}", paramValueStr);
			}
		}
		System.out.println(templateContent);
	}



}
