package com.yunqu.tariff.servlet;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.tariff.base.AppBaseServlet;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.utils.TariffPublicUtil;
import jodd.util.StringUtil;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.annotation.WebServlet;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 爬虫版本管理Servlet
 *
 * @ClassName TariffCrawlerVersionServlet
 * <AUTHOR> Copy This Tag)
 * @Description 爬虫版本管理相关HTTP接口
 * @Since create in 2025/1/28 10:30
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
@WebServlet("/servlet/crawlerVersion/*")
public class TariffCrawlerVersionServlet extends AppBaseServlet {

    private static final long serialVersionUID = 1L;

    /**
     * 版本作废操作
     */
    public EasyResult actionForInvalidVersion() {
        try {
            // 获取当前用户信息
            UserModel user = UserUtil.getUser(getRequest());
            if (user == null) {
                return EasyResult.fail("用户未登录");
            }

            // 获取请求参数
            String versionId = getJsonPara("versionId");
            String invalidReason = getJsonPara("invalidReason");
            String sql = "select * from " + Constants.getBusiTable("xty_crawler_version t") + " where t.id=?";
            JSONObject versionData = getQuery().queryForRow(sql, new Object[]{versionId}, new JSONMapperImpl());
            String versionNo = versionData.getString("VERSION_NO");
            String dateId = versionData.getString("BELONG_DATE_ID");

            // 参数验证
            if (StringUtil.isBlank(versionId)) {
                return EasyResult.fail("版本ID不能为空");
            }

            if (StringUtil.isBlank(invalidReason)) {
                return EasyResult.fail("作废原因不能为空");
            }

            // 直接在Servlet中执行版本作废操作
            EasyResult result = invalidVersionInServlet(versionId, invalidReason, user);

            if (result.isOk()) {
                //1：执行通知清理ES数据
                CompletableFuture.runAsync(() -> notifyVersionDataClean(versionNo, dateId));
                //2：执行通知重新稽核
                CompletableFuture.runAsync(() -> notifyVersionDataReAudit(dateId));
            }

            CommonLogger.getLogger().info("版本作废操作完成，版本ID: {}, 操作用户: {}", versionId, user.getUserAcc());
            return result;

        } catch (Exception e) {
            CommonLogger.getLogger().error("版本作废操作失败: {}", e.getMessage(), e);
            return EasyResult.fail("版本作废操作失败: " + e.getMessage());
        }
    }

    /**
     * 通知做ES数据清理
     */
    private void notifyVersionDataClean(String versionNo, String dateId) {
        TariffPublicUtil.delPublicVersionTariff(versionNo, dateId);
    }


    /**
     * 通知做重新稽核
     */
    private void notifyVersionDataReAudit(String dateId) {
        List<String> reAuditDateIds = getReAuditDateIds(dateId);
        TariffPublicUtil.addAuditTask(ArrayUtil.toArray(reAuditDateIds, String.class));
    }


    private List<String> getReAuditDateIds(String startDateId) {
        List<String> list = new ArrayList<>();
        list.add(startDateId);
        try {
            String sql = "select belong_date_id from " + Constants.getBusiTable("xty_crawler_version t") + " where t.belong_date_id>? and t.version_status='ACTIVE' order by create_time asc limit 1";
            String overDateId = getQuery().queryForString(sql, startDateId);
            if (StringUtil.isBlank(overDateId)) {
                return list;
            }

            if (overDateId != null && !overDateId.isEmpty()) {
                // 使用 SimpleDateFormat 解析日期
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

                // 解析开始日期和结束日期
                Date startDate = sdf.parse(startDateId);
                Date endDate = sdf.parse(overDateId);

                // 逐日增加并添加到列表中
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(startDate);

                // 循环添加日期直到结束日期
                while (calendar.getTime().before(endDate)) {
                    calendar.add(Calendar.DAY_OF_MONTH, 1);
                    String nextDate = sdf.format(calendar.getTime());
                    if (!nextDate.equals(startDateId)) {  // 避免重复添加开始日期
                        list.add(nextDate);
                    }
                }
            }
        } catch (Exception e) {
            CommonLogger.getLogger().error(e.getMessage(), e);
        }
        return list;
    }


    /**
     * 获取版本信息
     */
    public EasyResult actionForGetVersionInfo() {
        try {
            String versionId = getPara("versionId");
            String versionNo = getPara("versionNo");

            if (StringUtil.isBlank(versionId) && StringUtil.isBlank(versionNo)) {
                return EasyResult.fail("版本ID或版本号不能为空");
            }

            JSONObject param = new JSONObject();
            if (StringUtil.isNotBlank(versionId)) {
                param.put("VERSION_ID", versionId);
            }
            // 直接在Servlet中查询版本详情
            EasyResult result = getVersionDetailInServlet(versionId, versionNo);
            return result;

        } catch (Exception e) {
            CommonLogger.getLogger().error("获取版本信息失败: " + e.getMessage(), e);
            return EasyResult.fail("获取版本信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取版本影响范围分析
     */
    public EasyResult actionForGetVersionImpactAnalysis() {
        try {
            String versionId = getPara("versionId");

            if (StringUtil.isBlank(versionId)) {
                return EasyResult.fail("版本ID不能为空");
            }

            // 直接在Servlet中获取版本影响范围分析
            EasyResult result = getVersionImpactAnalysisInServlet(versionId);
            return result;

        } catch (Exception e) {
            CommonLogger.getLogger().error("获取版本影响范围分析失败: " + e.getMessage(), e);
            return EasyResult.fail("获取版本影响范围分析失败: " + e.getMessage());
        }
    }

    /**
     * 版本数据导出
     */
    public EasyResult actionForExportVersionData() {
        try {
            String versionId = getPara("versionId");
            String versionNo = getPara("versionNo");

            if (StringUtil.isBlank(versionId) && StringUtil.isBlank(versionNo)) {
                return EasyResult.fail("版本ID或版本号不能为空");
            }

            // 获取当前用户信息
            UserModel user = UserUtil.getUser(getRequest());
            if (user == null) {
                return EasyResult.fail("用户未登录");
            }

            // 构建导出参数
            JSONObject exportParam = new JSONObject();
            exportParam.put("versionId", versionId);
            exportParam.put("versionNo", versionNo);
            exportParam.put("exportUser", user.getUserAcc());
            exportParam.put("exportTime", System.currentTimeMillis());

            // 这里可以实现具体的导出逻辑
            // 例如：生成导出任务、异步处理、返回下载链接等

            CommonLogger.getLogger().info("版本数据导出请求，版本ID: " + versionId + ", 操作用户: " + user.getUserAcc());

            // 暂时返回成功响应，实际导出逻辑可以后续完善
            JSONObject result = new JSONObject();
            result.put("message", "导出任务已创建，请稍后查看导出结果");
            result.put("taskId", "EXPORT_" + System.currentTimeMillis());

            return EasyResult.ok(result);

        } catch (Exception e) {
            CommonLogger.getLogger().error("版本数据导出失败: " + e.getMessage(), e);
            return EasyResult.fail("版本数据导出失败: " + e.getMessage());
        }
    }

    /**
     * 批量版本操作
     */
    public EasyResult actionForBatchVersionOperation() {
        try {
            String operation = getPara("operation");
            String versionIds = getPara("versionIds");

            if (StringUtil.isBlank(operation)) {
                return EasyResult.fail("操作类型不能为空");
            }

            if (StringUtil.isBlank(versionIds)) {
                return EasyResult.fail("版本ID列表不能为空");
            }

            // 获取当前用户信息
            UserModel user = UserUtil.getUser(getRequest());
            if (user == null) {
                return EasyResult.fail("用户未登录");
            }

            // 权限检查
            if (!hasVersionManagePermission(user)) {
                return EasyResult.fail("您没有版本管理权限");
            }

            // 解析版本ID列表
            String[] versionIdArray = versionIds.split(",");

            JSONObject result = new JSONObject();
            int successCount = 0;
            int failCount = 0;

            // 根据操作类型执行批量操作
            for (String versionId : versionIdArray) {
                if (StringUtil.isNotBlank(versionId.trim())) {
                    try {
                        if ("invalid".equals(operation)) {
                            // 批量作废操作
                            EasyResult operationResult = invalidVersionInServlet(versionId.trim(), "批量作废操作", user);
                            if (operationResult != null && operationResult.isOk()) {
                                successCount++;
                            } else {
                                failCount++;
                            }
                        }
                    } catch (Exception e) {
                        CommonLogger.getLogger().error("批量操作版本失败，版本ID: " + versionId + ", 错误: " + e.getMessage(), e);
                        failCount++;
                    }
                }
            }

            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("message", "批量操作完成，成功: " + successCount + " 个，失败: " + failCount + " 个");

            CommonLogger.getLogger().info("批量版本操作完成，操作: " + operation + ", 成功: " + successCount + ", 失败: " + failCount);
            return EasyResult.ok(result);

        } catch (Exception e) {
            CommonLogger.getLogger().error("批量版本操作失败: " + e.getMessage(), e);
            return EasyResult.fail("批量版本操作失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户是否有版本管理权限
     * @param user 用户信息
     * @return 是否有权限
     */
    private boolean hasVersionManagePermission(UserModel user) {
        // 这里可以根据实际的权限管理系统实现具体的权限检查逻辑
        // 例如：检查用户角色、权限码等

        // 暂时简单实现：检查用户是否为管理员或特定角色
        try {
            String userAcc = user.getUserAcc();

            // 检查是否为管理员
            if (user.isAdmin()) {
                return true;
            }

            // 检查特定的用户账号
            if (StringUtils.isNotBlank(userAcc) &&
                    (userAcc.contains("admin") || userAcc.contains("manager"))) {
                return true;
            }

            // 检查用户角色
            if (user.getRoles() != null) {
                for (Object role : user.getRoles()) {
                    if (role.toString().contains("admin") ||
                            role.toString().contains("manager") ||
                            role.toString().contains("crawler")) {
                        return true;
                    }
                }
            }

            return false;

        } catch (Exception e) {
            CommonLogger.getLogger().error("权限检查失败: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 在Servlet中执行版本作废操作
     * @param versionId 版本ID
     * @param invalidReason 作废原因
     * @param user 当前用户
     * @return 操作结果
     */
    private EasyResult invalidVersionInServlet(String versionId, String invalidReason, UserModel user) {
        try {
            if (StringUtil.isBlank(versionId)) {
                return EasyResult.fail("版本ID不能为空");
            }

            if (StringUtil.isBlank(invalidReason)) {
                return EasyResult.fail("作废原因不能为空");
            }

            String currentUser = user.getUserAcc();
            String currentTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

            // 检查版本是否存在且状态为正常
            EasySQL checkSql = new EasySQL("SELECT VERSION_STATUS FROM " + getTableName("xty_crawler_version"));
            checkSql.append(versionId, "WHERE ID = ?");

            JSONObject statusRecords = getQuery().queryForRow(checkSql.getSQL(), checkSql.getParams(), new JSONMapperImpl());
            if (statusRecords == null || statusRecords.isEmpty()) {
                return EasyResult.fail("版本不存在");
            }

            String currentStatus = statusRecords.getString("VERSION_STATUS");
            if ("INVALID".equals(currentStatus)) {
                return EasyResult.fail("版本已经是作废状态");
            }

            EasyRecord record = new EasyRecord(Constants.getBusiTable("xty_crawler_version"), "id").setPrimaryValues(versionId);
            record.set("VERSION_STATUS", "INVALID");
            record.set("INVALID_TIME", currentTime);
            record.set("INVALID_USER", currentUser);
            record.set("INVALID_REASON", invalidReason);
            record.set("UPDATE_TIME", currentTime);
            QueryFactory.getTariffQuery().update(record);
            return EasyResult.ok("版本作废成功");
        } catch (Exception e) {
            CommonLogger.getLogger().error("版本作废操作失败: " + e.getMessage(), e);
            return EasyResult.fail("版本作废操作失败: " + e.getMessage());
        }
    }

    /**
     * 在Servlet中获取版本详情
     * @param versionId 版本ID
     * @param versionNo 版本号
     * @return 版本详情
     */
    private EasyResult getVersionDetailInServlet(String versionId, String versionNo) {
        try {
            if (StringUtil.isBlank(versionId) && StringUtil.isBlank(versionNo)) {
                return EasyResult.fail("版本ID或版本号不能为空");
            }

            EasySQL sql = new EasySQL("SELECT * FROM " + getTableName("xty_crawler_version") + " WHERE 1=1");

            if (StringUtil.isNotBlank(versionId)) {
                sql.append(versionId, "AND VERSION_ID = ?");
            } else {
                sql.append(versionNo, "AND VERSION_NO = ?");
            }

            CommonLogger.getLogger().info("查询版本详情SQL: " + sql.toFullSql());
            List<EasyRow> results = getQuery().queryForList(sql.getSQL(), sql.getParams());

            if (results == null || results.isEmpty()) {
                return EasyResult.fail("版本信息不存在");
            }

            // 将EasyRow转换为JSONObject
            EasyRow row = results.get(0);
            JSONObject result = new JSONObject();
            // 这里需要根据实际的字段名来获取数据
            result.put("VERSION_ID", row.getColumnValue("VERSION_ID"));
            result.put("VERSION_NO", row.getColumnValue("VERSION_NO"));
            result.put("VERSION_STATUS", row.getColumnValue("VERSION_STATUS"));
            result.put("VERSION_TABLE_NAME", row.getColumnValue("VERSION_TABLE_NAME"));
            result.put("DATA_COUNT", row.getColumnValue("DATA_COUNT"));
            result.put("CREATE_TIME", row.getColumnValue("CREATE_TIME"));
            result.put("CREATE_USER", row.getColumnValue("CREATE_USER"));
            result.put("CRAWL_DURATION", row.getColumnValue("CRAWL_DURATION"));
            result.put("INVALID_TIME", row.getColumnValue("INVALID_TIME"));
            result.put("INVALID_USER", row.getColumnValue("INVALID_USER"));
            result.put("INVALID_REASON", row.getColumnValue("INVALID_REASON"));

            return EasyResult.ok(result);

        } catch (Exception e) {
            CommonLogger.getLogger().error("查询版本详情失败: " + e.getMessage(), e);
            return EasyResult.fail("查询版本详情失败: " + e.getMessage());
        }
    }

    /**
     * 在Servlet中获取版本影响范围分析
     * @param versionId 版本ID
     * @return 影响范围分析
     */
    private EasyResult getVersionImpactAnalysisInServlet(String versionId) {
        try {
            if (StringUtil.isBlank(versionId)) {
                return EasyResult.fail("版本ID不能为空");
            }

            // 查询版本基本信息
            EasySQL versionSql = new EasySQL("SELECT VERSION_NO, VERSION_TABLE_NAME, DATA_COUNT, CREATE_TIME " +
                    "FROM " + getTableName("xty_crawler_version"));
            versionSql.append(versionId, "WHERE VERSION_ID = ?");

            List<EasyRow> versionResults = getQuery().queryForList(versionSql.getSQL(), versionSql.getParams());
            if (versionResults == null || versionResults.isEmpty()) {
                return EasyResult.fail("版本信息不存在");
            }

            EasyRow versionRow = versionResults.get(0);
            JSONObject versionInfo = new JSONObject();
            versionInfo.put("VERSION_NO", versionRow.getColumnValue("VERSION_NO"));
            versionInfo.put("VERSION_TABLE_NAME", versionRow.getColumnValue("VERSION_TABLE_NAME"));
            versionInfo.put("DATA_COUNT", versionRow.getColumnValue("DATA_COUNT"));
            versionInfo.put("CREATE_TIME", versionRow.getColumnValue("CREATE_TIME"));

            // 构建影响范围分析结果
            JSONObject result = new JSONObject();
            result.put("versionInfo", versionInfo);

            String dataCountStr = versionRow.getColumnValue("DATA_COUNT");
            int dataCount = 0;
            try {
                dataCount = Integer.parseInt(dataCountStr != null ? dataCountStr : "0");
            } catch (NumberFormatException e) {
                dataCount = 0;
            }

            result.put("impactDescription", "作废此版本将清理 " + dataCount + " 条爬取数据");
            result.put("tableName", versionRow.getColumnValue("VERSION_TABLE_NAME"));

            return EasyResult.ok(result);

        } catch (Exception e) {
            CommonLogger.getLogger().error("获取版本影响范围分析失败: " + e.getMessage(), e);
            return EasyResult.fail("获取版本影响范围分析失败: " + e.getMessage());
        }
    }

    /**
     * 在Servlet中记录操作日志
     * @param operationType 操作类型
     * @param targetId 目标ID
     * @param operationDesc 操作描述
     * @param operationUser 操作用户
     */
    private void recordOperationLogInServlet(String operationType, String targetId, String operationDesc, String operationUser) {
        try {
            String logId = UUID.randomUUID().toString().replace("-", "");
            String currentTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String clientIp = getRequest().getRemoteAddr();

            String logSql = "INSERT INTO " + getTableName("xty_crawler_operation_log") + " " +
                    "(ID, OPERATION_TYPE, OPERATION_DESC, TARGET_TYPE, TARGET_ID, " +
                    "OPERATION_USER, OPERATION_TIME, OPERATION_IP, CREATE_TIME) VALUES " +
                    "(?, ?, ?, 'VERSION', ?, ?, ?, ?, ?)";

            Object[] logParams = {logId, operationType, operationDesc, targetId,
                    operationUser, currentTime, clientIp, currentTime};

            getQuery().executeUpdate(logSql, logParams);

        } catch (Exception e) {
            CommonLogger.getLogger().error("记录操作日志失败: " + e.getMessage(), e);
        }
    }

    @Override
    protected EasyQuery getQuery() {
        return QueryFactory.getTariffQuery();
    }
}
