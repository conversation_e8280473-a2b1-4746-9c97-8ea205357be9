package com.yunqu.tariff.service;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.utils.TariffPublicUtil;
import com.yunqu.xty.commonex.kit.ElasticsearchKit;
import com.yunqu.xty.commonex.util.RedissonUtil;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.kit.RandomKit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 公示库资费字段核查服务
 * </p>
 *
 * @ClassName TariffCrawlCheckService
 * @Description 公示库资费字段核查服务，用于同步及检查爬虫数据
 * @Since create in 2024
 * @Version v1.0
 * @Company 广州云趣信息科技有限公司
 */
public class TariffCrawlCheckService {
    private static final Logger joblogger = LoggerFactory.getLogger(CommonLogger.getCrawLibJobLogger().getName());

    private static final Logger jobBuglogger = LoggerFactory.getLogger(CommonLogger.getcrawFindJobLogger().getName());

    // 缓存相关常量
    private static final String CACHE_KEY_PREFIX = "CRAWL_CHECK_RUNNING";
    public static final String CHECK_RUNNING_KEY = CACHE_KEY_PREFIX;
    private static final String ALL_RULES_CACHE_KEY = CACHE_KEY_PREFIX + "ALL_RULES";

    // 规则列表
    private List<JSONObject> ruleList;

    // 缓存任务信息的KEY前缀
    private static final String TASK_INFO_CACHE_PREFIX = "TASK_INFO_";
    // 任务信息缓存有效期（24小时）
    private static final int TASK_INFO_CACHE_EXPIRES = 24 * 60 * 60;

    // 添加全局线程池管理变量
    private static final List<ExecutorService> activeExecutorServices = Collections.synchronizedList(new ArrayList<>());

    // 应用关闭时的钩子方法
    static {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            joblogger.info("应用关闭钩子执行，关闭所有活动线程池");
            shutdownAllExecutorServices();
        }));
    }

    /**
     * 关闭所有活动的线程池
     */
    private static void shutdownAllExecutorServices() {
        synchronized (activeExecutorServices) {
            for (ExecutorService executor : activeExecutorServices) {
                try {
                    if (!executor.isShutdown()) {
                        executor.shutdownNow();
                        joblogger.info("强制关闭未终止的线程池");
                    }
                } catch (Exception e) {
                    joblogger.error("关闭线程池时发生异常: {}", e.getMessage(), e);
                }
            }
            activeExecutorServices.clear();
        }
    }

    /**
     * 创建并注册一个新的线程池
     * @param nThreads 线程数
     * @param poolName 线程池名称，用于日志
     * @return 创建的线程池
     */
    private ExecutorService createAndRegisterExecutorService(int nThreads, String poolName) {
        ThreadFactory threadFactory = new ThreadFactory() {
            private final AtomicInteger threadNumber = new AtomicInteger(1);

            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r, poolName + "-" + threadNumber.getAndIncrement());
                if (t.isDaemon()) {
                    t.setDaemon(false);  // 确保非守护线程，可以接收中断信号
                }
                if (t.getPriority() != Thread.NORM_PRIORITY) {
                    t.setPriority(Thread.NORM_PRIORITY);
                }
                return t;
            }
        };

        ExecutorService executorService = Executors.newFixedThreadPool(nThreads, threadFactory);
        synchronized (activeExecutorServices) {
            activeExecutorServices.add(executorService);
        }
        joblogger.info("创建并注册线程池: {}, 线程数: {}", poolName, nThreads);
        return executorService;
    }

    /**
     * 安全关闭线程池
     * @param executorService 要关闭的线程池
     * @param poolName 线程池名称，用于日志
     */
    private void safeShutdownExecutorService(ExecutorService executorService, String poolName, int timeoutHour) {
        if (executorService == null || executorService.isShutdown()) {
            return;
        }

        try {
            joblogger.info("开始关闭线程池: {}", poolName);
            executorService.shutdown();

            // 等待线程池终止
            boolean terminated = executorService.awaitTermination(timeoutHour, TimeUnit.HOURS);
            if (!terminated) {
                joblogger.warn("线程池 {} 在 {} 秒内未能正常关闭，执行强制关闭", poolName, timeoutHour);
                List<Runnable> pendingTasks = executorService.shutdownNow();
                joblogger.warn("线程池 {} 中有 {} 个任务未执行完成", poolName, pendingTasks.size());

                // 再次等待一小段时间
                terminated = executorService.awaitTermination(1, TimeUnit.HOURS);
                if (!terminated) {
                    joblogger.error("线程池 {} 无法完全关闭", poolName);
                }
            } else {
                joblogger.info("线程池 {} 已成功关闭", poolName);
            }
        } catch (InterruptedException e) {
            joblogger.error("等待线程池 {} 关闭时被中断: {}", poolName, e.getMessage(), e);
            Thread.currentThread().interrupt();
            executorService.shutdownNow();
        } finally {
            // 从活动线程池列表中移除
            synchronized (activeExecutorServices) {
                activeExecutorServices.remove(executorService);
            }
        }
    }

    /**
     * 检查是否有处理任务正在运行
     * @return 是否有处理任务正在运行
     */
    public static boolean isCheckRunning() {
        String cacheValue = RedissonUtil.get(CHECK_RUNNING_KEY);
        return StringUtils.isNotBlank(cacheValue);
    }

    /**
     * 设置处理任务运行状态
     * @param runningInfo 运行信息
     */
    private void setCheckRunning(JSONObject runningInfo) {
        RedissonUtil.setEx(CHECK_RUNNING_KEY, runningInfo.toJSONString(), 24 * 60 * 60);
        joblogger.info("设置处理任务运行状态，缓存键: {}", CHECK_RUNNING_KEY);
    }

    /**
     * 清除处理任务运行状态
     */
    public void clearCheckRunning() {
        RedissonUtil.del(CHECK_RUNNING_KEY);
        joblogger.info("清除处理任务运行状态，缓存键: {}", CHECK_RUNNING_KEY);
    }

    /**
     * 根据资费方案编号查找报送库记录
     * @param tariffNo 资费方案编号
     * @return 报送库记录，如果不存在则返回null
     */
    private JSONObject findReportRecordByTariffNo(String tariffNo) {
        if (StringUtils.isBlank(tariffNo)) {
            return null;
        }

        try {
            EasySQL sqlBuilder = new EasySQL();
            sqlBuilder.append("SELECT * FROM " + Constants.getBusiSchema() + ".xty_tariff_record WHERE 1=1");
            sqlBuilder.append(tariffNo, "AND REPORT_NO = ?");

            EasyQuery query = QueryFactory.getWriteQuery();
            List<JSONObject> reportList = query.queryForList(sqlBuilder.getSQL(), sqlBuilder.getParams(), new JSONMapperImpl());

            if (reportList != null && !reportList.isEmpty()) {
                return reportList.get(0);
            }
        } catch (Exception e) {
            joblogger.error("查找报送库记录时发生错误: {}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * 将爬虫记录转换为公示库记录
     */
    private JSONObject convertToLibRecord(JSONObject crawlRecord, JSONObject reportRecord) {
        JSONObject libRecord = new JSONObject();

        // 设置基础字段
        libRecord.put("id", RandomKit.uniqueStr());
        libRecord.put("tariff_record_id", crawlRecord.getString("TARIFF_RECORD_ID"));
        libRecord.put("task_id", crawlRecord.getString("TASK_ID"));
        libRecord.put("province_code", crawlRecord.getString("PROVINCE_CODE"));
        libRecord.put("province_name", crawlRecord.getString("PROVINCE_NAME"));
        libRecord.put("ent_code", crawlRecord.getString("ENT_CODE"));
        libRecord.put("ent_name", crawlRecord.getString("ENT_NAME"));
        libRecord.put("name", crawlRecord.getString("NAME"));
        libRecord.put("tariff_no", crawlRecord.getString("TARIFF_NO"));
        libRecord.put("fees", crawlRecord.getString("FEES"));
        libRecord.put("fees_unit", crawlRecord.getString("FEES_UNIT"));
        libRecord.put("call", crawlRecord.getString("CALL"));
        libRecord.put("data", crawlRecord.getString("DATA"));
        libRecord.put("data_unit", crawlRecord.getString("DATA_UNIT"));
        libRecord.put("sms", crawlRecord.getString("SMS"));
        libRecord.put("orient_traffic", crawlRecord.getString("ORIENT_TRAFFIC"));
        libRecord.put("orient_traffic_unit", crawlRecord.getString("ORIENT_TRAFFIC_UNIT"));
        libRecord.put("iptv", crawlRecord.getString("IPTV"));
        libRecord.put("bandwidth", crawlRecord.getString("BANDWIDTH"));
        libRecord.put("rights", crawlRecord.getString("RIGHTS"));
        libRecord.put("increment_business", crawlRecord.getString("INCREMENT_BUSINESS"));
        libRecord.put("other_content", crawlRecord.getString("OTHER_CONTENT"));
        libRecord.put("online_day", crawlRecord.getString("ONLINE_DAY"));
        libRecord.put("offline_day", crawlRecord.getString("OFFLINE_DAY"));
        libRecord.put("tariff_attr", crawlRecord.getString("TARIFF_ATTR"));

        // 根据 crawler_type 字段判断并设置 tariff_attr_type 字段
        String crawlerType = crawlRecord.getString("CRAWLER_TYPE");
        if (StringUtils.isNotBlank(crawlerType) && crawlerType.contains("全网")) {
            // 如果 crawler_type 包含"全网"，则 tariff_attr_type=0（全国）
            libRecord.put("tariff_attr_type", "0");
        } else {
            // 否则 tariff_attr_type=1（省内）
            libRecord.put("tariff_attr_type", "1");
        }

        libRecord.put("applicable_people", crawlRecord.getString("APPLICABLE_PEOPLE"));
        libRecord.put("applicable_area", crawlRecord.getString("APPLICABLE_AREA"));
        libRecord.put("valid_period", crawlRecord.getString("VALID_PERIOD"));
        libRecord.put("channel", crawlRecord.getString("CHANNEL"));
        libRecord.put("duration", crawlRecord.getString("DURATION"));
        libRecord.put("unsubscribe", crawlRecord.getString("UNSUBSCRIBE"));
        libRecord.put("responsibility", crawlRecord.getString("RESPONSIBILITY"));
        libRecord.put("others", crawlRecord.getString("OTHERS"));
        libRecord.put("extended_fields", crawlRecord.getString("EXTENDED_FIELDS"));

//        libRecord.put("other_fees", crawlRecord.getString("OTHER_FEE_CONTENT"));
//        libRecord.put("exceed_fees", crawlRecord.getString("OUT_FEE_CONTENT"));
//        libRecord.put("international_call", crawlRecord.getString("INTERNATIONAL_CALL"));
//        libRecord.put("international_call_unit", crawlRecord.getString("INTERNATIONAL_CALL_UNIT"));

        // 设置是否已报送
        if (reportRecord != null) {
            libRecord.put("reported", "1");

            // 新增：同步报送库的 STATUS 到公示库的 tariff_state
            libRecord.put("tariff_state", reportRecord.getString("STATUS"));

            // 新增：同步报送库的 reporter 到公示库的 tariff_reporter
            libRecord.put("tariff_reporter", reportRecord.getString("REPORTER"));

            libRecord.put("tariff_reporter_name", reportRecord.getString("REPORTER_NAME"));
        } else {
            libRecord.put("reported", "0");
            libRecord.put("tariff_state", "");
            libRecord.put("tariff_reporter", "");
            libRecord.put("tariff_reporter_name", "");

        }

        // 设置日期ID
        String dateId = crawlRecord.getString("DATE_ID");
        libRecord.put("date_id", dateId);

        // 设置月份ID
        if (StringUtils.isNotBlank(dateId) && dateId.length() >= 6) {
            String monthId = dateId.substring(0, 6);
            libRecord.put("month_id", monthId);
        }

        // 设置出现月份
        String createTime = crawlRecord.getString("CREATE_TIME");
        if (StringUtils.isNotBlank(createTime) && createTime.length() >= 7) {

            // 获取并设置版本号数组和出现日期数组
            String taskId = crawlRecord.getString("TASK_ID");
            if (StringUtils.isNotBlank(taskId)) {
                try {
                    JSONObject taskInfo = getTaskInfo(taskId);
                    if (taskInfo != null) {
                        // 设置版本号数组
                        String versionNo = taskInfo.getString("VERSION_NO");
                        if (StringUtils.isNotBlank(versionNo)) {
                            List<String> versionNos = new ArrayList<>();
                            versionNos.add(versionNo);
                            libRecord.put("version_nos", versionNos);
                        }

                        // 设置出现日期数组
                        String taskDateId = taskInfo.getString("DATE_ID");
                        if (StringUtils.isNotBlank(taskDateId)) {
                            List<String> dateIds = new ArrayList<>();
                            dateIds.add(taskDateId);
                            libRecord.put("date_ids", dateIds);
                        }

                        // 从创建时间提取年月，格式为yyyyMM
                        String appearMonth = taskDateId.substring(0, 6);
                        List<String> appearMonths = new ArrayList<>();

                        if(!appearMonths.contains(appearMonth)){
                            appearMonths.add(appearMonth);
                        }
                        libRecord.put("appear_months", appearMonths);

                    }
                } catch (Exception e) {
                    joblogger.error("获取任务信息失败: {}", e.getMessage(), e);
                }
            }
        } else {
            // 没有任务ID，使用创建时间作为备选
            joblogger.warn("记录中未找到任务ID，使用创建时间设置入库月份");
            setAppearMonthFromCreateTime(libRecord, crawlRecord);
        }

        libRecord.put("version_no", crawlRecord.getString("VERSION_NO"));
        libRecord.put("tariff_province_code", crawlRecord.getString("TARIFF_PROVINCE_CODE"));
        libRecord.put("tariff_province_name", crawlRecord.getString("TARIFF_PROVINCE_NAME"));
        libRecord.put("crawler_province_name", crawlRecord.getString("CRAWLER_PROVINCE_NAME"));
        libRecord.put("crawler_type", crawlRecord.getString("CRAWLER_TYPE"));
        libRecord.put("create_time", crawlRecord.getString("CREATE_TIME"));
        libRecord.put("update_time", crawlRecord.getString("UPDATE_TIME"));

        libRecord.put("bak1", crawlRecord.getString("CLASSIC_TYPE_ONE"));
        libRecord.put("bak2", crawlRecord.getString("CLASSIC_TYPE_TWO"));
        libRecord.put("tariff_type", crawlRecord.getString("TARIFF_TYPE"));


        // 如果有报送记录，使用报送记录中的分类信息
        if (reportRecord != null) {
            libRecord.put("classic_type_one", reportRecord.getString("TYPE1"));
            libRecord.put("classic_type_two", reportRecord.getString("TYPE2"));
            libRecord.put("is_telecom", reportRecord.getString("IS_TELECOM"));
            libRecord.put("tariff_record_id", reportRecord.getString("ID"));

            String taskId = crawlRecord.getString("TASK_ID");
            if (StringUtils.isNotBlank(taskId)) {
                try {
                    JSONObject taskInfo = getTaskInfo(taskId);
                    if (taskInfo != null) {

                        // 添加新的版本号
                        String versionNo = taskInfo.getString("VERSION_NO");
                        // 添加新的日期ID
                        String taskDateId = taskInfo.getString("DATE_ID");
                        String appearMonth = taskDateId.substring(0, 6);

                        if (reportRecord != null) {
                            String reportNo = reportRecord.getString("REPORT_NO");
                            String tariffName = reportRecord.getString("NAME");
                            String reporter = reportRecord.getString("REPORTER");

                            // 关联到 报送库的需要 更新对应报送库es索引
                            // 计算 serialId，使用与 getTariffSerialId 相同的逻辑
                            String singleKey = reportNo;
                            if (StringUtils.isBlank(singleKey)) {
                                singleKey = tariffName;
                            }
                            String serialId = getTariffSerialId(singleKey, reportNo, "1", reporter);

                            EasySQL sql = new EasySQL();
                            sql.append(reportRecord.getString("ID"), "UPDATE " + Constants.getBusiSchema() + ".xty_tariff_record SET IS_PUBLIC = 'Y' WHERE ID = ?");
                            QueryFactory.getWriteQuery().executeUpdate(sql.getSQL(), sql.getParams());

                            // 更新备份索引中的公示状态
                            try {
                                TariffPublicService.getInstance().updateBakEsIsPublic(reportNo, reportRecord.getString("ID"), "Y", versionNo, appearMonth);
                                joblogger.debug("更新备份索引中的公示状态成功，reportNo: {}, recordId: {}, version: {}, month: {}",
                                        reportNo, reportRecord.getString("ID"), versionNo, appearMonth);
                            } catch (Exception e) {
                                joblogger.error("更新备份索引中的公示状态失败: {}", e.getMessage(), e);
                            }
                            // 更新存储索引中的公示状态
                            try {
                                TariffPublicService.getInstance().updateStorageEsIsPublic(reportNo, serialId, "Y", versionNo, appearMonth);
                                joblogger.debug("更新存储索引中的公示状态成功，reportNo: {}, serialId: {}, version: {}, month: {}",
                                        reportNo, serialId, versionNo, appearMonth);
                            } catch (Exception e) {
                                joblogger.error("更新存储索引中的公示状态失败: {}", e.getMessage(), e);
                            }
                        }
                    }
                } catch (Exception e) {
                    joblogger.error("获取任务信息失败: {}", e.getMessage(), e);
                }
            }
        } else {
            // 没有报送记录，使用默认值
            libRecord.put("classic_type_one", "");
            libRecord.put("classic_type_two", "");
            libRecord.put("is_telecom", "0");
        }
        return libRecord;
    }


    /**
     * 清除任务信息缓存
     * 如果提供了特定的任务ID，则只清除该任务的缓存
     * 否则不进行任何操作（因为没有通配删除缓存的安全方式）
     *
     * @param taskId 可选的任务ID
     */
    public static void clearTaskInfoCache(String... taskId) {
        if (taskId != null && taskId.length > 0) {
            for (String id : taskId) {
                if (StringUtils.isNotBlank(id)) {
                    String cacheKey = TASK_INFO_CACHE_PREFIX + id;
                    RedissonUtil.del(cacheKey);
                    joblogger.info("已清除任务信息缓存，任务ID: {}", id);
                }
            }
        }
    }

    /**
     * 同步爬虫数据到公示库
     * 查询爬虫任务表中状态为2且未同步到公示库的任务
     * 按省份分组，批量处理同一省份的爬虫数据，每个省份内的任务按创建时间顺序处理
     */
    public void syncCrawlDataToPublic() {
        if (isCheckRunning()) {
            joblogger.info("同步爬虫数据到公示库的任务已在运行中，请稍后再试");
            return;
        }
        JSONObject runningInfo = new JSONObject();
        runningInfo.put("startTime", new Date());
        runningInfo.put("status", "running");
        runningInfo.put("message", "正在同步爬虫数据到公示库");
        setCheckRunning(runningInfo);

        ExecutorService executorService = null;

        try {
            // 查询待同步的爬虫任务，按省份分组
            List<JSONObject> groupedTaskList = findPendingSyncTasks();
            if (groupedTaskList.isEmpty()) {
                joblogger.info("没有待同步的爬虫任务");
                // 新增逻辑：查找最新版本表未同步数据并处理
                try {
                    String latestTableName = getLatestCrawlRecordTableName();
                    if (StringUtils.isNotBlank(latestTableName)) {
                        joblogger.info("尝试在最新版本表 {} 查找未同步的爬虫数据", latestTableName);
                        EasyQuery easyQuery = QueryFactory.getTariffQuery();
                        EasySQL sql = new EasySQL();
                        sql.append("SELECT * FROM " + Constants.getBusiSchema() + "." + latestTableName + " WHERE public_lib_id IS NULL");
                        List<JSONObject> unsyncedRecords = easyQuery.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
                        if (unsyncedRecords != null && !unsyncedRecords.isEmpty()) {
                            joblogger.info("最新版本表 {} 中有 {} 条未同步的爬虫数据，开始合并公示库", latestTableName, unsyncedRecords.size());
                            processBatchCrawlRecords(unsyncedRecords);
                        } else {
                            joblogger.info("最新版本表 {} 中没有未同步的爬虫数据", latestTableName);
                        }
                    } else {
                        joblogger.warn("未能获取最新的爬虫版本表名");
                    }
                } catch (Exception e) {
                    joblogger.error("处理最新版本表未同步数据时发生异常: {}", e.getMessage(), e);
                }
                clearCheckRunning();
                return;
            }
            joblogger.info("找到{}个省份的待同步爬虫任务", groupedTaskList.size());

            // 获取系统可用处理器数量，优化并行处理任务数
            int availableProcessors = Runtime.getRuntime().availableProcessors();
            int optimalThreadCount = Math.max(5, Math.min(availableProcessors, 10)); // 最少3个线程，最多10个线程
            joblogger.info("系统可用处理器数: {}, 优化后的线程数: {}", availableProcessors, optimalThreadCount);

            // 创建线程池，用于并行处理不同省份的任务
            executorService = createAndRegisterExecutorService(optimalThreadCount, "SyncCrawlData");
            List<Future<?>> futures = new ArrayList<>();

            // 为每个省份的任务组创建一个线程
            for (JSONObject groupedTask : groupedTaskList) {
                String provinceName = groupedTask.getString("PROVINCE_NAME");
                int taskCount = groupedTask.getIntValue("TASK_COUNT");
                List<JSONObject> tasks = (List<JSONObject>) groupedTask.get("TASKS");
                String taskIdsStr = groupedTask.getString("TASK_IDS");

                futures.add(executorService.submit(() -> {
                    try {
                        joblogger.info("开始处理省份{}的{}个爬虫任务，任务ID: {}", provinceName, taskCount, taskIdsStr);
                        processSingleTask(taskIdsStr);
                        joblogger.info("完成处理省份{}的爬虫任务", provinceName);
                    } catch (Exception e) {
                        joblogger.error("处理省份{}的爬虫任务时发生异常: {}", provinceName, e.getMessage(), e);
                    }
                }));

                // 减少任务之间的等待时间，但保留一定的间隔以避免资源争用
                Thread.sleep(1000); // 缩短任务间隔时间
            }

            // 等待所有任务完成，设置合理的超时时间
            for (Future<?> future : futures) {
                try {
                    // 设置最长等待时间为1小时，避免无限等待
                    future.get(24, TimeUnit.HOURS);
                } catch (TimeoutException e) {
                    joblogger.error("任务执行超时: {}", e.getMessage());
                    future.cancel(true); // 取消超时任务
                } catch (Exception e) {
                    joblogger.error("等待任务完成时发生异常: {}", e.getMessage(), e);
                }
            }

            joblogger.info("所有省份的爬虫任务同步完成");
        } catch (Exception e) {
            joblogger.error("同步爬虫数据到公示库时发生异常: {}", e.getMessage(), e);
            clearCheckRunning();

        } finally {
            if (executorService != null) {
                safeShutdownExecutorService(executorService, "SyncCrawlData", 12);
            }
            clearCheckRunning();
        }
    }


    /**
     * 批量处理爬虫数据，使用多线程并行处理
     * @param batchRecords 需要批量处理的爬虫记录
     */
    private void processBatchCrawlRecords(List<JSONObject> batchRecords) throws SQLException {
        if (batchRecords == null || batchRecords.isEmpty()) {
            return;
        }

        joblogger.info("开始多线程处理{}条爬虫记录", batchRecords.size());
        long startTime = System.currentTimeMillis();

        // 获取可用处理器数量，决定线程池大小
        int processors = Runtime.getRuntime().availableProcessors();
        // 线程数 = min(处理器数量*2, 10)，避免创建过多线程
        int threadCount = Math.min(processors * 2, 10);
        joblogger.info("创建处理线程池，线程数: {}", threadCount);

        // 使用计数器来跟踪处理进度
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);

        // 创建线程池
        ExecutorService executorService = createAndRegisterExecutorService(threadCount, "CrawlRecordProcessor");

        try {
            // 创建一个线程安全的记录集合
            List<JSONObject> recordsToProcess = Collections.synchronizedList(new ArrayList<>(batchRecords));

            // 创建一个并发安全的CompletionService，用于管理并行任务
            CompletionService<Boolean> completionService = new ExecutorCompletionService<>(executorService);

            // 记录所有提交的任务
            List<Future<Boolean>> futures = new ArrayList<>();

            // 为每条记录提交一个处理任务
            for (JSONObject crawlRecord : recordsToProcess) {
                Future<Boolean> future = completionService.submit(() -> {
                    String crawlId = crawlRecord.getString("ID");
                    try {
                        processSingleCrawlRecordForPublic(crawlRecord);
                        int current = successCount.incrementAndGet();

                        // 每处理50条记录打印一次进度
                        if (current % 50 == 0) {
                            long currentTime = System.currentTimeMillis();
                            double elapsed = (currentTime - startTime) / 1000.0;
                            double recordsPerSecond = current / elapsed;
                            joblogger.info("已处理{}条记录，耗时{}秒，处理速度{}条/秒",
                                    current, String.format("%.2f", elapsed), String.format("%.2f", recordsPerSecond));
                        }
                        return true;
                    } catch (Exception e) {
                        failCount.incrementAndGet();
                        joblogger.error("处理爬虫记录ID={}时发生异常: {}", crawlId, e.getMessage(), e);
                        // 为避免单条记录异常影响全部处理，返回false但不抛出异常
                        return false;
                    }
                });
                futures.add(future);
            }

            // 等待所有任务完成
            for (int i = 0; i < futures.size(); i++) {
                try {
                    // 设置超时时间，避免无限等待
                    Future<Boolean> future = completionService.poll(30, TimeUnit.MINUTES);
                    if (future == null) {
                        joblogger.warn("等待任务完成超时，可能有任务执行时间过长");
                        break;
                    }
                    // 获取结果，但不做特别处理，主要是为了确保等待任务完成
                    future.get();
                } catch (Exception e) {
                    joblogger.error("等待任务完成时发生异常: {}", e.getMessage(), e);
                }
            }

            // 计算总耗时
            long endTime = System.currentTimeMillis();
            double totalTime = (endTime - startTime) / 1000.0;
            joblogger.info("多线程处理完成，共{}条记录，成功{}条，失败{}条，总耗时{}秒，平均速度{}条/秒",
                    batchRecords.size(), successCount.get(), failCount.get(),
                    String.format("%.2f", totalTime),
                    String.format("%.2f", batchRecords.size() / totalTime));

        } finally {
            // 安全关闭线程池
            safeShutdownExecutorService(executorService, "CrawlRecordProcessor", 12);
        }
    }


    /**
     * 更新单条爬虫记录的公示库ID关联
     *
     * @param crawlRecordId 爬虫记录ID
     * @param publicLibId 公示库记录ID
     */
    private void updateCrawlRecordPublicLibId(String crawlRecordId, String publicLibId, String versionNo) {
        if (StringUtils.isBlank(crawlRecordId) || StringUtils.isBlank(publicLibId)) {
            return;
        }

        try {
            // 使用EasyRecord方式更新
            EasyRecord record = new EasyRecord(Constants.getBusiSchema() + "."+getCrawlRecordTableName(versionNo), "id");
            record.put("ID", crawlRecordId);
            record.put("PUBLIC_LIB_ID", publicLibId);

            // 执行更新
            QueryFactory.getTariffQuery().update(record);

//            joblogger.debug("成功更新爬虫记录的公示库ID关联，爬虫ID={}, 公示库ID={}", crawlRecordId, publicLibId);
        } catch (Exception e) {
            joblogger.error("更新爬虫记录的公示库ID关联失败: {}", e.getMessage(), e);
        }
    }


    /**
     * 查询待同步的爬虫任务，按省份分组
     *
     * @return 按省份分组的任务列表，每组包含省份名称和该省份的任务列表
     */
    private List<JSONObject> findPendingSyncTasks() throws SQLException {
        // 首先查询所有待同步的任务
        EasySQL sql = new EasySQL();
        sql.append("SELECT t.ID, t.PROVINCE_NAME, t.CREATE_TIME ");
        sql.append(" FROM " + Constants.getBusiSchema() + ".xty_crawler_task t");
        sql.append(" left join "+ Constants.getBusiSchema() + ".xty_crawler_version t2");
        sql.append(" on t.version_no = t2.version_no");
        sql.append(" WHERE t.STATUS = 2 AND (t.is_sync_to_public = 0 or t.is_sync_to_public is null) ");
        sql.append(Constants.CRAWLER_VERSION_STATUS_1, " and t2.VERSION_STATUS = ? ");
        sql.append(" ORDER BY t.PROVINCE_NAME, t.CREATE_TIME ASC");
        List<JSONObject> allTasks = QueryFactory.getTariffQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

        // 按省份分组
        Map<String, List<JSONObject>> provinceTasksMap = new HashMap<>();

        for (JSONObject task : allTasks) {
            String provinceName = task.getString("PROVINCE_NAME");

            // 如果provinceName为空，使用默认值
            if (StringUtils.isBlank(provinceName)) {
                provinceName = "未知省份";
            }

            if (!provinceTasksMap.containsKey(provinceName)) {
                provinceTasksMap.put(provinceName, new ArrayList<>());
            }

            provinceTasksMap.get(provinceName).add(task);
        }

        // 构建结果列表
        List<JSONObject> groupedTasks = new ArrayList<>();

        for (Map.Entry<String, List<JSONObject>> entry : provinceTasksMap.entrySet()) {
            String provinceName = entry.getKey();
            List<JSONObject> tasks = entry.getValue();

            // 构建省份任务组
            JSONObject groupedTask = new JSONObject();
            groupedTask.put("PROVINCE_NAME", provinceName);
            groupedTask.put("TASKS", tasks);
            groupedTask.put("TASK_COUNT", tasks.size());

            // 提取任务ID列表，用于日志记录
            List<String> taskIds = tasks.stream()
                    .map(task -> task.getString("ID"))
                    .collect(Collectors.toList());
            groupedTask.put("TASK_IDS", String.join(",", taskIds));

            groupedTasks.add(groupedTask);
        }

        joblogger.info("按省份分组查询到{}个分组的待同步爬虫任务", groupedTasks.size());
        return groupedTasks;
    }

    /**
     * 更新任务的同步状态
     */
    private void updateTaskSyncStatus(String taskId) throws SQLException {
        EasySQL sql = new EasySQL();
        sql.append(taskId, "UPDATE " + Constants.getBusiSchema() + ".xty_crawler_task SET is_sync_to_public = 1 WHERE ID = ?");
        int result = QueryFactory.getTariffQuery().executeUpdate(sql.getSQL(), sql.getParams());
        if (result > 0) {
            joblogger.info("成功更新爬虫任务{}的同步状态", taskId);
            // 清除该任务的缓存
            clearTaskInfoCache(taskId);
        } else {
            joblogger.warn("更新爬虫任务{}的同步状态失败", taskId);
        }
    }


    /**
     * 更新报送库的是否公示字段
     */
    private void updateTariffRecordIsPublic(String reportId, String currentMonth, String versionNum) {
        try {
            EasyQuery easyQuery = QueryFactory.getWriteQuery();
            EasySQL sql = new EasySQL();
            sql.append(reportId, "UPDATE " + Constants.getBusiSchema() + ".xty_tariff_record SET IS_PUBLIC = 'Y' WHERE ID = ?");
            int result = easyQuery.executeUpdate(sql.getSQL(), sql.getParams());
            if (result > 0) {
                joblogger.info("更新报送库的是否公示字段{}的同步状态", reportId);

                // 获取报送记录信息，包括报送编号等
                EasySQL sqlReport = new EasySQL();
                sqlReport.append(reportId, "SELECT ID, REPORT_NO, VERSION_NUM, NAME, REPORTER FROM " + Constants.getBusiSchema() + ".xty_tariff_record WHERE ID = ?");
                JSONObject reportRecord = easyQuery.queryForRow(sqlReport.getSQL(), sqlReport.getParams(), new JSONMapperImpl());

                if (reportRecord != null) {


                    String reportNo = reportRecord.getString("REPORT_NO");
                    String tariffName = reportRecord.getString("NAME");
                    String reporter = reportRecord.getString("REPORTER");

                    // 计算 serialId，使用与 getTariffSerialId 相同的逻辑

                    String singleKey = reportNo;
                    if (StringUtils.isBlank(singleKey)) {
                        singleKey = tariffName;
                    }
                    String serialId = getTariffSerialId(singleKey, reportNo, "1", reporter);



                    // 更新备份索引中的公示状态
                    try {
                        TariffPublicService.getInstance().updateBakEsIsPublic(reportNo, reportId, "Y", versionNum, currentMonth);
                        joblogger.debug("更新备份索引中的公示状态成功，reportNo: {}, recordId: {}, version: {}, month: {}",
                                reportNo, reportId, versionNum, currentMonth);
                    } catch (Exception e) {
                        joblogger.error("更新备份索引中的公示状态失败: {}", e.getMessage(), e);
                    }

                    // 更新存储索引中的公示状态
                    try {
                        TariffPublicService.getInstance().updateStorageEsIsPublic(reportNo, serialId, "Y", versionNum, currentMonth);
                        joblogger.debug("更新存储索引中的公示状态成功，reportNo: {}, serialId: {}, version: {}, month: {}",
                                reportNo, serialId, versionNum, currentMonth);
                    } catch (Exception e) {
                        joblogger.error("更新存储索引中的公示状态失败: {}", e.getMessage(), e);
                    }
                }
            } else {
                joblogger.warn("更新报送库的是否公示字段{}的同步状态失败", reportId);
            }
        } catch (Exception e) {
            joblogger.error(e.getMessage(), e);
        }
    }

    private String getTariffSerialId(String tariffName, String reportNo, String type, String reporter) {
        // 根据入参组合数据进行MD5生成唯一流水
        return SecureUtil.sha256(tariffName + reportNo + type + reporter);
    }

    public static void main(String[] args) {
        System.out.println(SecureUtil.sha256("24BJ102046" + "24BJ102046" + "1" + "BJ1"));
    }


    /**
     * 将爬虫记录插入公示库
     *
     * @param libRecord 公示库记录
     * @throws SQLException 数据库异常
     */
    private void insertPublicLibRecord(JSONObject libRecord) throws SQLException {
        try {
            // 获取当前时间
            Date now = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String currentTime = sdf.format(now);
            // 确保有ID字段，使用 RandomKit.uniqueStr() 生成唯一ID
            if (!libRecord.containsKey("id") || StringUtils.isBlank(libRecord.getString("id"))) {
                String uniqueId = RandomKit.uniqueStr();
                libRecord.put("id", uniqueId);
            }
            // 设置创建和更新时间
            libRecord.put("create_time", currentTime);
            libRecord.put("update_time", currentTime);
            // 构建插入记录，指定ID字段为主键
//            EasyRecord record = new EasyRecord(Constants.getBusiSchema() + ".xty_tariff_crawl_record_lib", "id");

            // 设置字段值
            for (Map.Entry<String, Object> entry : libRecord.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                // 跳过空值
                if (value == null) {
                    continue;
                }
                // 特殊处理appear_months字段，不保存到数据库
                if ("appear_months".equals(key)) {
                    continue;
                }
                // 特殊处理appear_months字段，不保存到数据库
                if ("version_nos".equals(key)) {
                    continue;
                }
                // 特殊处理appear_months字段，不保存到数据库
                if ("date_ids".equals(key)) {
                    continue;
                }
                // 转换字段名为大写，符合数据库命名规范
                String fieldName = key.toUpperCase();
                // 特殊处理可能导致SQL语法错误的关键字段，使用反引号包围
                if ("call".equalsIgnoreCase(key)) {
//                    record.put("`CALL`", value);
                } else {
//                    record.put(fieldName, value);
                }
            }

            // 执行插入
//            QueryFactory.getTariffQuery().save(record);
            // 获取ID（使用预先生成的ID）
            String id = libRecord.getString("id");
            joblogger.debug("爬虫记录已插入到公示库，ID={}", id);
            // 同步到ES索引，使用相同的ID
            try {
                // 添加到ES
                addDocumentToEs(libRecord);
                joblogger.debug("爬虫记录已同步到ES索引，ID={}", id);
            } catch (Exception e) {
                joblogger.error("同步爬虫记录到ES索引失败: {}", e.getMessage(), e);
                // 不抛出异常，允许数据库插入成功但ES同步失败的情况
            }
        } catch (Exception e) {
            joblogger.error("插入爬虫记录到公示库失败: {}", e.getMessage(), e);
            throw e;
        }
    }


    /**
     * 向ES添加文档
     * @param document 文档内容
     * @throws Exception 如果添加失败
     */
    public void addDocumentToEs(JSONObject document) throws Exception {
        try {
            // 获取文档ID
            String id = document.getString("id");
            if (StringUtils.isBlank(id)) {
                throw new Exception("文档ID不能为空");
            }

            // 使用ElasticsearchKit添加文档
            JSONObject response = ElasticsearchKit.addDoc(Constants.XTY_TARIFF_PUBLIC_LIB_INDEX, id, document);
            // 检查添加结果
            if (response == null || response.containsKey("error")) {
                String errorMsg = response != null ? response.toJSONString() : "响应为空";
                throw new Exception("添加文档失败: " + errorMsg);
            }
//            joblogger.debug("添加ES文档成功，ID: {}", id);
        } catch (Exception e) {
            joblogger.error("添加ES文档失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新ES中的文档
     * @param id 文档ID
     * @param document 更新的文档内容
     * @throws Exception 如果更新失败
     */
    public void updateDocumentInEs(String id, JSONObject document) throws Exception {
        try {
            if (StringUtils.isBlank(id)) {
                throw new Exception("文档ID不能为空");
            }

            // 精简文档，只保留必要字段
            JSONObject essentialDocument = document;

            // 构建 term 查询，根据ID查找文档
            JSONObject termQuery = new JSONObject()
                    .fluentPut("term", new JSONObject()
                            .fluentPut("id.keyword", id));

            // 构建更新脚本和参数
            JSONObject scriptParams = new JSONObject();
            for (String key : essentialDocument.keySet()) {
                scriptParams.put(key, essentialDocument.get(key));
            }

            // 构建更新脚本
            StringBuilder scriptBuilder = new StringBuilder();
            for (String key : essentialDocument.keySet()) {
                scriptBuilder.append("ctx._source.").append(key).append(" = params.").append(key).append("; ");
            }

            // 构建更新请求
            JSONObject updateRequest = new JSONObject()
                    .fluentPut("query", termQuery)
                    .fluentPut("script", new JSONObject()
                            .fluentPut("source", scriptBuilder.toString())
                            .fluentPut("lang", "painless")
                            .fluentPut("params", scriptParams));

            // 执行更新
            JSONObject response = ElasticsearchKit.updateByQuery(Constants.XTY_TARIFF_PUBLIC_LIB_INDEX, updateRequest);

            // 检查更新结果
            if (response == null || response.containsKey("error")) {
                String errorMsg = response != null ? response.toJSONString() : "响应为空";
                throw new Exception("更新文档失败: " + errorMsg);
            }
//            joblogger.debug("更新ES文档成功，ID: {}", id);
        } catch (Exception e) {
            joblogger.error("更新ES文档失败: {}", e.getMessage(), e);
            throw e;
        }
    }


    public void updateDocumentByTariffNo(String tariffNo, JSONObject document) throws Exception {
        try {
            if (StringUtils.isBlank(tariffNo)) {
                throw new Exception("tariffNo不能为空");
            }

            // 精简文档，只保留必要字段
            JSONObject essentialDocument = document;

            // 构建 term 查询，根据ID查找文档
            JSONObject termQuery = new JSONObject()
                    .fluentPut("term", new JSONObject()
                            .fluentPut("tariff_no.keyword", tariffNo));

            // 构建更新脚本和参数
            JSONObject scriptParams = new JSONObject();
            for (String key : essentialDocument.keySet()) {
                scriptParams.put(key, essentialDocument.get(key));
            }

            // 构建更新脚本
            StringBuilder scriptBuilder = new StringBuilder();
            for (String key : essentialDocument.keySet()) {
                scriptBuilder.append("ctx._source.").append(key).append(" = params.").append(key).append("; ");
            }

            // 构建更新请求
            JSONObject updateRequest = new JSONObject()
                    .fluentPut("query", termQuery)
                    .fluentPut("script", new JSONObject()
                            .fluentPut("source", scriptBuilder.toString())
                            .fluentPut("lang", "painless")
                            .fluentPut("params", scriptParams));

            // 执行更新
            JSONObject response = ElasticsearchKit.updateByQuery(Constants.XTY_TARIFF_PUBLIC_LIB_INDEX, updateRequest);

            // 检查更新结果
            if (response == null || response.containsKey("error")) {
                String errorMsg = response != null ? response.toJSONString() : "响应为空";
                throw new Exception("更新文档失败: " + errorMsg);
            }
//            joblogger.debug("更新ES文档成功，ID: {}", id);
        } catch (Exception e) {
            joblogger.error("更新ES文档失败: {}", e.getMessage(), e);
            throw e;
        }
    }


    /**
     * 获取ES中已存在的文档
     * @param id 文档ID
     * @return 文档内容，如果不存在则返回null
     */
    private JSONObject getExistingDocument(String id) {
        try {
            // 构建 term 查询，根据ID精确匹配
            JSONObject termQuery = new JSONObject();
            termQuery.put("term", new JSONObject().fluentPut("id.keyword", id));

            // 构建查询参数
            JSONObject queryParams = new JSONObject();
            queryParams.put("query", termQuery);

            // 使用 ElasticsearchKit.search 执行查询
            JSONObject esResult = ElasticsearchKit.search(Constants.XTY_TARIFF_PUBLIC_LIB_INDEX, queryParams);

            // 检查查询结果
            if (esResult != null && !esResult.containsKey("error")) {
                // 提取命中的文档
                JSONObject hits = esResult.getJSONObject("hits");
                if (hits != null && hits.getJSONArray("hits") != null && hits.getJSONArray("hits").size() > 0) {
                    // 获取第一个匹配的文档
                    JSONObject hit = hits.getJSONArray("hits").getJSONObject(0);
                    // 获取文档的_source部分，即文档的实际内容
                    JSONObject source = hit.getJSONObject("_source");

                    return source;
                }
            }
        } catch (Exception e) {
            joblogger.error("获取ES文档失败: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 根据ID查找报送库记录
     *
     * @param reportRecordId 报送库记录ID
     * @return 报送库记录，如果不存在则返回null
     */
    private JSONObject findReportRecordById(String reportRecordId) {
        if (StringUtils.isBlank(reportRecordId)) {
            return null;
        }
        try {
            EasySQL sqlBuilder = new EasySQL();
            sqlBuilder.append("SELECT * FROM " + Constants.getBusiSchema() + ".xty_tariff_record WHERE 1=1");
            sqlBuilder.append(reportRecordId, "AND ID = ?");
            EasyQuery query = QueryFactory.getWriteQuery();
            List<JSONObject> reportList = query.queryForList(sqlBuilder.getSQL(), sqlBuilder.getParams(), new JSONMapperImpl());

            if (reportList != null && !reportList.isEmpty()) {
                return reportList.get(0);
            }
        } catch (Exception e) {
            joblogger.error("根据ID查找报送库记录时发生错误: {}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * 修改公示库记录报送信息
     * 根据报送记录ID(TariffRecordId)查找并更新公示库记录的报送信息
     *
     * @param reportRecordId 报送记录ID
     * @return 操作结果，包含成功状态和消息
     */
    public JSONObject updatePublicLibRecord(String reportRecordId) {
        JSONObject result = new JSONObject();
        try {
            // 参数验证
            if (StringUtils.isBlank(reportRecordId)) {
                result.put("success", false);
                result.put("message", "报送记录ID不能为空");
                return result;
            }

            // 获取报送记录信息
            JSONObject reportRecord = findReportRecordById(reportRecordId);
            joblogger.info("查询要更新公示库对应报送库记录: {}",reportRecord.toJSONString());
            if (reportRecord == null) {
                result.put("success", false);
                result.put("message", "未找到对应的报送记录，ID: " + reportRecordId);
                return result;
            }
            String reportNo = reportRecord.getString("REPORT_NO");

            // 同步到ES索引
            try {

                // 构建更新文档
                JSONObject updateDoc = new JSONObject();
                updateDoc.put("reported", "1");
                updateDoc.put("classic_type_one", reportRecord.getString("TYPE1"));
                updateDoc.put("classic_type_two", reportRecord.getString("TYPE2"));
                updateDoc.put("is_telecom", reportRecord.getString("IS_TELECOM"));

                // 新增：同步报送库的 STATUS 到公示库的 tariff_state
                updateDoc.put("tariff_state", reportRecord.getString("STATUS"));

                // 新增：同步报送库的 reporter 到公示库的 tariff_reporter
                updateDoc.put("tariff_reporter", reportRecord.getString("REPORTER"));

                updateDoc.put("tariff_reporter_name", reportRecord.getString("REPORTER_NAME"));

                // 更新ES文档
                updateDocumentByTariffNo(reportNo, updateDoc);

                joblogger.debug("成功更新ES中公示库记录的信息，reportNo: {}, 文档；{}", reportNo, updateDoc.toJSONString());
            } catch (Exception e) {
                joblogger.error("更新ES中公示库记录的报送信息失败: {}", e.getMessage(), e);
                // 不影响主流程，记录日志后继续
            }

            result.put("success", true);
            result.put("message", "成功更新公示库记录的报送信息，报送ID: " + reportRecordId);
        } catch (Exception e) {
            joblogger.error("更新公示库记录的报送信息失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "更新公示库记录的报送信息失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 删除公示库记录报送信息
     * 根据报送记录ID(TariffRecordId)查找公示库记录，然后将报送相关字段置空
     *
     * @param tariffRecordId 报送记录ID
     * @return 操作结果，包含成功状态和消息
     */
    public JSONObject deletePublicLibRecord(String tariffRecordId) {
        JSONObject result = new JSONObject();
        try {
            // 参数验证
            if (StringUtils.isBlank(tariffRecordId)) {
                result.put("success", false);
                result.put("message", "报送记录ID不能为空");
                return result;
            }

            // 查找公示库记录
//            EasySQL sqlBuilder = new EasySQL();
//            sqlBuilder.append("SELECT ID FROM " + Constants.getBusiSchema() + ".xty_tariff_crawl_record_lib WHERE 1=1");
//            sqlBuilder.append(tariffRecordId, "AND TARIFF_RECORD_ID = ?");
//
//            EasyQuery query = QueryFactory.getTariffQuery();
//            List<JSONObject> records = query.queryForList(sqlBuilder.getSQL(), sqlBuilder.getParams(), new JSONMapperImpl());

            List<JSONObject> records = TariffPublicUtil.queryPublicLibRecordFromESByNo(tariffRecordId);

            if (records == null || records.isEmpty()) {
                result.put("success", false);
                result.put("message", "未找到对应的公示库记录，报送记录ID: " + tariffRecordId);
                return result;
            }

            String publicLibId = records.get(0).getString("ID");

//            // 获取当前时间
//            String currentTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
//
//            // 更新公示库记录，将报送相关字段置空
//            EasyRecord record = new EasyRecord(Constants.getBusiSchema() + ".xty_tariff_crawl_record_lib", "id");
//            record.set("ID", publicLibId);
//
//            // 清空报送相关字段
//            record.put("REPORTED", "0");
//            record.put("CLASSIC_TYPE_ONE", "");
//            record.put("CLASSIC_TYPE_TWO", "");
//            record.put("IS_TELECOM", "");
//            record.put("TARIFF_RECORD_ID", "");
//
//            // 新增：清空 tariff_state 和 tariff_reporter 字段
//            record.put("TARIFF_STATE", "");
//            record.put("TARIFF_REPORTER", "");
//            record.put("TARIFF_REPORTER_NAME", "");
//
//            // 执行更新
//            query.update(record);

            // 同步到ES索引
            try {
                // 构建更新文档
                JSONObject updateDoc = new JSONObject();
                updateDoc.put("id", publicLibId);
                updateDoc.put("reported", "0");
                updateDoc.put("classic_type_one", "");
                updateDoc.put("classic_type_two", "");
                updateDoc.put("is_telecom", "");
                updateDoc.put("tariff_record_id", "");

                // 新增：清空 tariff_state 和 tariff_reporter 字段
                updateDoc.put("tariff_state", "");
                updateDoc.put("tariff_reporter", "");
                updateDoc.put("tariff_reporter_name", "");

                // 更新ES文档
                updateDocumentInEs(publicLibId, updateDoc);
//                joblogger.debug("成功清空ES中公示库记录的报送信息，ID: {}", publicLibId);
            } catch (Exception e) {
                joblogger.error("清空ES中公示库记录的报送信息失败: {}", e.getMessage(), e);
                // 不影响主流程，记录日志后继续
            }

            result.put("success", true);
            result.put("message", "成功清空公示库记录的报送信息，ID: " + publicLibId);
            result.put("id", publicLibId);
        } catch (Exception e) {
            joblogger.error("清空公示库记录的报送信息失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "清空公示库记录的报送信息失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 根据方案编号、省份编码和企业编码查找报送库记录
     *
     * @param reportNo 方案编号
     * @param provinceCode 省份编码
     * @param entCode 企业编码
     * @return 报送库记录，如果不存在则返回null
     */
    private JSONObject findReportRecordByReportNoAndProvinceAndEnt(String reportNo, String provinceCode, String entCode) {
        if (StringUtils.isBlank(reportNo) || StringUtils.isBlank(provinceCode) || StringUtils.isBlank(entCode)) {
            return null;
        }

        try {
            EasySQL sqlBuilder = new EasySQL();
            sqlBuilder.append("SELECT ID, REPORT_NO, TYPE1, TYPE2, IS_TELECOM FROM " + Constants.getBusiSchema() + ".xty_tariff_record WHERE 1=1");
            sqlBuilder.append(reportNo, "AND REPORT_NO = ?");
            sqlBuilder.append(provinceCode, "AND PROVINCE = ?");
            sqlBuilder.append(entCode, "AND ENT = ?");
            sqlBuilder.append("ORDER BY UPDATE_TIME DESC");

            EasyQuery query = QueryFactory.getWriteQuery();
            List<JSONObject> reportList = query.queryForList(sqlBuilder.getSQL(), sqlBuilder.getParams(), new JSONMapperImpl());

            if (reportList != null && !reportList.isEmpty()) {
                return reportList.get(0);
            }
        } catch (Exception e) {
            joblogger.error("根据方案编号、省份和企业查找报送库记录时发生错误: {}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * 根据任务ID获取任务信息，增加缓存机制避免重复查询数据库
     *
     * @param taskId 任务ID
     * @return 任务信息
     */
    private JSONObject getTaskInfo(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            return null;
        }
        // 构造缓存键
        String cacheKey = TASK_INFO_CACHE_PREFIX + taskId;
        // 先从缓存获取
        String cacheValue = RedissonUtil.get(cacheKey);
        if (StringUtils.isNotBlank(cacheValue)) {
            try {
                // 解析缓存的JSON字符串为JSONObject
                return JSON.parseObject(cacheValue);
            } catch (Exception e) {
                joblogger.error("解析任务信息缓存失败: {}", e.getMessage(), e);
                // 缓存解析失败，继续查询数据库
            }
        }
        // 缓存不存在或解析失败，查询数据库
        try {
            EasySQL sqlBuilder = new EasySQL();
            sqlBuilder.append("SELECT ID, DATE_ID, VERSION_NO FROM " + Constants.getBusiSchema() + ".xty_crawler_task WHERE 1=1");
            sqlBuilder.append(taskId, "AND ID = ?");
            List<JSONObject> taskList = QueryFactory.getTariffQuery().queryForList(sqlBuilder.getSQL(), sqlBuilder.getParams(), new JSONMapperImpl());
            if (taskList != null && !taskList.isEmpty()) {
                JSONObject taskInfo = taskList.get(0);
                // 将查询结果存入缓存
                if (taskInfo != null) {
                    RedissonUtil.setEx(cacheKey, taskInfo.toJSONString(), TASK_INFO_CACHE_EXPIRES);
                    joblogger.debug("任务信息已缓存，任务ID: {}", taskId);
                }

                return taskInfo;
            }
        } catch (Exception e) {
            joblogger.error("根据任务ID获取任务信息失败: {}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * 从创建时间中设置入库月份
     *
     * @param libRecord 公示库记录
     * @param crawlRecord 爬虫记录
     */
    private void setAppearMonthFromCreateTime(JSONObject libRecord, JSONObject crawlRecord) {
        String dateId = crawlRecord.getString("DATE_ID");
        if (StringUtils.isNotBlank(dateId) && dateId.length() >= 6) {
            // 从创建时间提取年月，格式为yyyyMM
            String appearMonth = dateId.substring(0, 6);
            List<String> appearMonths = new ArrayList<>();
            appearMonths.add(appearMonth);
            libRecord.put("appear_months", appearMonths);
//            joblogger.debug("根据创建时间设置入库月份: {}", appearMonth);
        }
    }

    private JSONObject findExistingPublicLibRecord(JSONObject crawlRecord) {
        if (crawlRecord == null) {
            return null;
        }

        try {
            String crawlId = crawlRecord.getString("ID");
            String tariffNo = crawlRecord.getString("TARIFF_NO");
            String name = crawlRecord.getString("NAME");
            String provinceCode = crawlRecord.getString("PROVINCE_CODE");
            String entCode = crawlRecord.getString("ENT_CODE");
            String currentVersion = crawlRecord.getString("VERSION_NO");

            if (StringUtils.isBlank(tariffNo) || StringUtils.isBlank(name) ||
                    StringUtils.isBlank(provinceCode) || StringUtils.isBlank(entCode)) {
                joblogger.debug("爬虫记录关键字段缺失，无法查找匹配记录: ID={}", crawlId);
                return null;
            }

            String cacheKey = "crawlibkey:" + String.join("#", tariffNo, name, provinceCode, entCode);

            // 缓存命中直接跳过
            if (RedissonUtil.exist(cacheKey)) {
                joblogger.debug("Redisson缓存命中，跳过ES查询: {}", cacheKey);
                JSONObject processedMarker = new JSONObject();
                processedMarker.put("PROCESSED", "YES");
                return processedMarker;
            }

            // 构建ES查询条件
            JSONObject queryParams = new JSONObject();
            JSONObject boolQuery = new JSONObject();
            JSONArray mustArray = new JSONArray();

            addTermQueryForES(mustArray, "tariff_no.keyword", tariffNo);
            addTermQueryForES(mustArray, "name.keyword", name);
            addTermQueryForES(mustArray, "province_code.keyword", provinceCode);
            addTermQueryForES(mustArray, "ent_code.keyword", entCode);

            boolQuery.put("must", mustArray);
            queryParams.put("query", new JSONObject().fluentPut("bool", boolQuery));

            JSONArray sortArray = new JSONArray();
            JSONObject sortItem = new JSONObject();
            sortItem.put("update_time.keyword", new JSONObject().fluentPut("order", "desc"));
            sortArray.add(sortItem);
            queryParams.put("sort", sortArray);
            queryParams.put("size", 1);

//            joblogger.debug("ES查询匹配记录参数: {}", queryParams.toJSONString());
            JSONObject esResult = ElasticsearchKit.search(Constants.XTY_TARIFF_PUBLIC_LIB_INDEX, queryParams);

            if (esResult != null && esResult.containsKey("hits")) {
                JSONObject hits = esResult.getJSONObject("hits");
                JSONArray hitArray = hits.getJSONArray("hits");

                if (hitArray != null && !hitArray.isEmpty()) {
                    JSONObject hit = hitArray.getJSONObject(0);
                    JSONObject source = hit.getJSONObject("_source");

                    if (source != null) {
                        JSONObject record = new JSONObject();
                        record.put("ID", source.getString("id"));
                        String existingId = source.getString("id");

                        Object versionNosObj = source.get("version_nos");
                        List<String> versionNos = new ArrayList<>();
                        boolean versionExists = false;

                        if (versionNosObj instanceof List) {
                            versionNos = (List<String>) versionNosObj;
                            if (StringUtils.isNotBlank(currentVersion)) {
                                versionExists = versionNos.contains(currentVersion);
                            }
                        }

                        if (versionExists) {
                            joblogger.debug("找到完全匹配的公示库记录，ID={}, 版本数组包含当前版本: {}", existingId, currentVersion);
                        } else {
                            joblogger.debug("找到匹配的公示库记录，但版本数组不包含当前版本，ID={}, 当前版本: {}", existingId, currentVersion);
                        }

                        if (!versionExists) {
                            // 转换为公示库记录，保留原ID
                            JSONObject libRecord = convertToLibRecord(crawlRecord, findReportRecordByTariffNo(crawlRecord.getString("TARIFF_NO")));
                            // 使用已存在记录的ID，确保数据库和ES使用相同的ID
                            libRecord.put("id", existingId);
                            // 始终更新数组字段
//                            joblogger.debug("更新数组字段1:"+libRecord.toJSONString()+"---,existingId:"+existingId);
                            updateArrayFieldsFromExistingDocument(libRecord, existingId, crawlRecord);
                            // 更新记录
                            updateLibRecord(libRecord);
                            // 更新爬虫记录中的公示库ID关联
                            updateCrawlRecordPublicLibId(crawlId, existingId, currentVersion);
                        } else {
                            // 仍然更新爬虫记录中的公示库ID关联，确保关联正确
                            if(Constants.isForceReportIndexUpdate()){
                                JSONObject libRecord = convertToLibRecord(crawlRecord, findReportRecordByTariffNo(crawlRecord.getString("TARIFF_NO")));
                                // 使用已存在记录的ID，确保数据库和ES使用相同的ID
                                libRecord.put("id", existingId);
                                // 始终更新数组字段
                                updateArrayFieldsFromExistingDocument(libRecord, existingId, crawlRecord);
                                // 更新记录
                                updateLibRecord(libRecord);
                            }
                            updateCrawlRecordPublicLibId(crawlId, existingId, currentVersion);
                        }

//                        JSONObject libRecord = convertToLibRecord(crawlRecord, findReportRecordByTariffNo(tariffNo));
//                        libRecord.put("id", existingId);
//                        updateArrayFieldsFromExistingDocument(libRecord, existingId, crawlRecord);
//                        updateLibRecord(libRecord);
//                        updateCrawlRecordPublicLibId(crawlId, existingId, currentVersion);

                        // 缓存命中
                        RedissonUtil.setEx(cacheKey, "1", 5);

                        JSONObject processedMarker = new JSONObject();
                        processedMarker.put("PROCESSED", "YES");
                        return processedMarker;
                    }
                }
            }

            joblogger.debug("未找到匹配的公示库记录，爬虫记录ID={}, tariffNo={}", crawlId, tariffNo);
            return null;
        } catch (Exception e) {
            joblogger.error("ES查找公示库记录时发生错误: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 添加ES查询的精确匹配条件
     *
     * @param mustArray 必须匹配条件数组
     * @param field 字段名
     * @param value 字段值
     */
    private void addTermQueryForES(JSONArray mustArray, String field, String value) {
        if (StringUtils.isNotBlank(value)) {
            JSONObject termQuery = new JSONObject();
            termQuery.put("term", new JSONObject().fluentPut(field, value));
            mustArray.add(termQuery);
        }
    }

    /**
     * 处理单条爬虫数据，将其同步到公示库
     */
    private void processSingleCrawlRecordForPublic(JSONObject crawlRecord) throws SQLException {

        jobBuglogger.info("处理这条数据："+crawlRecord.getString("ID"));

        String crawlId = crawlRecord.getString("ID");
        // 检查是否已存在匹配的公示库记录，并在查找过程中处理更新逻辑
        JSONObject existingRecord = findExistingPublicLibRecord(crawlRecord);

        // 如果返回null，说明没有找到匹配记录，需要新增
        // 如果返回的对象包含PROCESSED标记，说明已在findExistingPublicLibRecord方法中处理了更新逻辑
        if (existingRecord == null) {
            // 没有找到匹配的记录，需要新增
            joblogger.debug("爬虫记录{}在公示库中不存在，进行新增", crawlId);
            // 查找报送库中对应的记录
            JSONObject reportRecord = null;
            String tariffNo = crawlRecord.getString("TARIFF_NO");
            if (StringUtils.isNotBlank(tariffNo)) {
                reportRecord = findReportRecordByTariffNo(tariffNo);
            }
            // 转换为公示库记录，使用RandomKit.uniqueStr()生成唯一ID
            JSONObject libRecord = convertToLibRecord(crawlRecord, reportRecord);
            // 保存到公示库
            insertPublicLibRecord(libRecord);
            // 获取新插入记录的ID
            String newLibId = libRecord.getString("id");
            // 更新爬虫记录中的公示库ID关联
            if (StringUtils.isNotBlank(newLibId)) {
                updateCrawlRecordPublicLibId(crawlId, newLibId, crawlRecord.getString("VERSION_NO"));
            }
            joblogger.debug("爬虫记录{}已同步到公示库，新记录ID={}", crawlId, newLibId);
        } else if (!"YES".equals(existingRecord.getString("PROCESSED"))) {
            // 这种情况不应该出现，因为findExistingPublicLibRecord方法已经处理了所有更新逻辑
            // 但为了健壮性，添加一个警告日志
            joblogger.warn("爬虫记录{}的查找结果异常，返回的对象没有PROCESSED标记", crawlId);
        }
        // 如果existingRecord不为null且包含PROCESSED标记，说明已在findExistingPublicLibRecord方法中处理了更新逻辑，这里无需再做处理
    }

    /**
     * 从现有ES文档中获取并更新数组字段
     *
     * @param libRecord 要更新的记录
     * @param existingId 现有记录ID
     * @param crawlRecord 爬虫记录
     */
    private void updateArrayFieldsFromExistingDocument(JSONObject libRecord, String existingId, JSONObject crawlRecord) {
        // 获取已有记录的出现月份、版本号数组和出现日期数组
        List<String> appearMonths = new ArrayList<>();
        List<String> versionNos = new ArrayList<>();
        List<String> dateIds = new ArrayList<>();

        // 查询现有ES文档，获取已有的数组
        try {
            JSONObject existingDoc = getExistingDocument(existingId);
            if (existingDoc != null) {
                // 获取已有的出现月份
                Object existingMonths = existingDoc.get("appear_months");
                if (existingMonths instanceof List) {
                    appearMonths.addAll((List<String>) existingMonths);
                }

                // 获取已有的版本号数组
                Object existingVersionNos = existingDoc.get("version_nos");
                if (existingVersionNos instanceof List) {
                    versionNos.addAll((List<String>) existingVersionNos);
                }

                // 获取已有的出现日期数组
                Object existingDateIds = existingDoc.get("date_ids");
                if (existingDateIds instanceof List) {
                    dateIds.addAll((List<String>) existingDateIds);
                }
            }
        } catch (Exception e) {
            joblogger.error("获取已有ES文档失败: {}", e.getMessage(), e);
        }

        // 获取任务信息，添加新的版本号和日期ID
        String taskId = crawlRecord.getString("TASK_ID");
        if (StringUtils.isNotBlank(taskId)) {
            try {
                JSONObject taskInfo = getTaskInfo(taskId);
                if (taskInfo != null) {
                    // 添加新的版本号
                    String versionNo = taskInfo.getString("VERSION_NO");

                    // 版本号不同，需要更新记录
                    joblogger.debug("爬虫记录{}在公示库中已存在(ID={}), 但版本不同(已存在版本:{}, 当前版本:{}), 进行更新",
                            crawlRecord.getString("ID"), existingId, versionNos, versionNo);

                    if (StringUtils.isNotBlank(versionNo) && !versionNos.contains(versionNo)) {
                        versionNos.add(versionNo);
                    }

                    // 添加新的日期ID
                    String taskDateId = taskInfo.getString("DATE_ID");
                    if (StringUtils.isNotBlank(taskDateId) && !dateIds.contains(taskDateId)) {
                        dateIds.add(taskDateId);
                        // 从日期ID提取年月，格式为yyyyMM
                        if (taskDateId.length() >= 6) {
                            String appearMonth = taskDateId.substring(0, 6);
                            if (!appearMonths.contains(appearMonth)) {
                                appearMonths.add(appearMonth);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                joblogger.error("获取任务信息失败: {}", e.getMessage(), e);
            }
        }

        // 更新数组，即使数组未变化也更新，确保每次都写入ES
        libRecord.put("appear_months", appearMonths);
        libRecord.put("version_nos", versionNos);
        libRecord.put("date_ids", dateIds);

        joblogger.debug("更新记录ID={}的数组字段，appear_months={}, version_nos={}, date_ids={}",
                existingId, appearMonths, versionNos, dateIds);
    }

    /**
     * 更新公示库记录
     * @param libRecord 要更新的公示库记录
     * @throws SQLException SQL异常
     */
    private void updateLibRecord(JSONObject libRecord) throws SQLException {
        if (libRecord == null || !libRecord.containsKey("id")) {
            return;
        }

        try {
            String id = libRecord.getString("id");

            // 获取当前时间
            Date now = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String currentTime = sdf.format(now);

            // 设置更新时间
            libRecord.put("update_time", currentTime);

            // 构建更新记录
//            EasyRecord record = new EasyRecord(Constants.getBusiSchema() + ".xty_tariff_crawl_record_lib", "id");
//            record.put("id", id);

            // 设置字段值
            for (Map.Entry<String, Object> entry : libRecord.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                // 跳过ID字段和空值
                if ("id".equals(key) || value == null) {
                    continue;
                }
                // 特殊处理数组字段，不保存到数据库
                if ("appear_months".equals(key) || "version_nos".equals(key) || "date_ids".equals(key)) {
                    continue;
                }
                // 转换字段名为大写，符合数据库命名规范
                String fieldName = key.toUpperCase();
//                // 特殊处理可能导致SQL语法错误的关键字段，使用反引号包围
//                if ("call".equalsIgnoreCase(key)) {
//                    record.put("`CALL`", value);
//                } else {
//                    record.put(fieldName, value);
//                }
            }

            // 执行更新
//            QueryFactory.getTariffQuery().update(record);

//            joblogger.debug("成功更新公示库记录，ID={}", id);

            // 同时更新ES文档
            try {
                updateDocumentInEs(id, libRecord);
//                joblogger.debug("成功更新ES文档，ID={}", id);
            } catch (Exception e) {
                joblogger.error("更新ES文档失败: {}", e.getMessage(), e);
                // 数据库更新成功但ES更新失败，记录日志但不影响主流程
            }
        } catch (Exception e) {
            joblogger.error("更新公示库记录失败: {}", e.getMessage(), e);
            throw new SQLException("更新公示库记录失败", e);
        }
    }

    /**
     * 处理一组爬虫任务（同一省份的多个任务）
     * 按版本号顺序处理数据，确保一个版本的数据完全处理完成后才开始处理下一个版本
     * 每个版本内部按1000条一批进行处理
     *
     * @param taskIdsStr 任务ID字符串，多个ID用逗号分隔
     */
    private void processSingleTask(String taskIdsStr) {
        joblogger.info("开始处理爬虫任务组: {}", taskIdsStr);

        if (StringUtils.isBlank(taskIdsStr)) {
            joblogger.warn("任务ID为空，无法处理");
            return;
        }

        // 将逗号分隔的任务ID转换为列表
        String[] taskIdArray = taskIdsStr.split(",");
        List<String> taskIdList = new ArrayList<>(Arrays.asList(taskIdArray));

        try {
            // 构建IN子句
            StringBuilder taskInClause = new StringBuilder();
            for (int i = 0; i < taskIdList.size(); i++) {
                if (i > 0) {
                    taskInClause.append(",");
                }
                taskInClause.append("'").append(taskIdList.get(i)).append("'");
            }

            // 首先查询所有相关任务的版本号
            EasyQuery easyQuery = QueryFactory.getTariffQuery();
            EasySQL versionSql = new EasySQL();
            versionSql.append("SELECT DISTINCT t.VERSION_NO,t.PROVINCE_NAME FROM " + Constants.getBusiSchema() + ".xty_crawler_task t ")
                    .append("WHERE t.ID IN ("+taskInClause.toString()+") ")
                    .append("ORDER BY t.VERSION_NO ASC");

            List<JSONObject> versionList = easyQuery.queryForList(versionSql.getSQL(), versionSql.getParams(), new JSONMapperImpl());
            joblogger.info("任务组 {} 共有 {} 个不同版本需要处理", taskIdsStr, versionList.size());

            // 按版本号顺序处理数据
            for (JSONObject versionObj : versionList) {
                String version = versionObj.getString("VERSION_NO");
                String provinceCode = versionObj.getString("PROVINCE_NAME");

                if (StringUtils.isBlank(version)) {
                    joblogger.warn("跳过空版本号");
                    continue;
                }

                joblogger.info("开始处理版本 {} 的数据", version);
                String tableName = getCrawlRecordTableName(version);

                // 查询该版本下的所有任务ID
                EasySQL taskSql = new EasySQL();
                taskSql.append("SELECT ID FROM " + Constants.getBusiSchema() + ".xty_crawler_task ")
                        .append("WHERE ID IN ("+taskInClause.toString()+") ")
                        .append(version, " AND VERSION_NO = ?")
                        .append("ORDER BY CREATE_TIME ASC");

                List<JSONObject> versionTaskList = easyQuery.queryForList(taskSql.getSQL(), taskSql.getParams(), new JSONMapperImpl());

                if (versionTaskList.isEmpty()) {
                    joblogger.warn("版本 {} 下没有找到任务", version);
                    continue;
                }

                // 构建该版本的任务ID IN子句
                StringBuilder versionTaskInClause = new StringBuilder();
                for (int i = 0; i < versionTaskList.size(); i++) {
                    if (i > 0) {
                        versionTaskInClause.append(",");
                    }
                    versionTaskInClause.append("'").append(versionTaskList.get(i).getString("ID")).append("'");
                }

                // 查询该版本下的记录总数
                EasySQL countSql = new EasySQL();
                countSql.append("SELECT COUNT(1) FROM " + Constants.getBusiSchema() + "."+tableName)
                        .append("WHERE task_id IN ("+versionTaskInClause.toString()+") ");
                countSql.append(version, "  AND VERSION_NO = ?");

                int totalRecords = easyQuery.queryForInt(countSql.getSQL(), countSql.getParams());
                joblogger.info("版本 {} 共有 {} 条爬虫数据需要处理", version, totalRecords);

                if (totalRecords == 0) {
                    joblogger.warn("版本 {} 下没有找到爬虫记录", version);
                    continue;
                }

                // 分批处理该版本的记录，每批1000条
                final int BATCH_SIZE = 2000;
                int totalBatches = (totalRecords + BATCH_SIZE - 1) / BATCH_SIZE; // 向上取整

                for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                    int offset = batchIndex * BATCH_SIZE;

                    EasySQL recordSql = new EasySQL();
                    recordSql.append("")
                            .append("  SELECT * FROM " + Constants.getBusiSchema() + "." + tableName + " ")
                            .append("  WHERE TASK_ID IN (" + versionTaskInClause.toString() + ") ");
                    recordSql.append(version, "  AND VERSION_NO = ? ")
//                    recordSql.append("  ORDER BY DATE_ID ASC ")
                            .append("  LIMIT ").append(String.valueOf(BATCH_SIZE))
                            .append(" OFFSET ").append(String.valueOf(offset));

                    List<JSONObject> batchRecords = easyQuery.queryForList(recordSql.getSQL(), recordSql.getParams(), new JSONMapperImpl());

                    joblogger.info("版本 {} 的第 {}/{} 批次，获取到 {} 条记录", version, batchIndex + 1, totalBatches, batchRecords.size());

                    if (!batchRecords.isEmpty()) {
                        try {
                            // 处理当前批次的记录
                            processBatchCrawlRecords(batchRecords);
                            joblogger.info("版本 {} 的第 {}/{} 批次处理完成", version, batchIndex + 1, totalBatches);
                        } catch (Exception e) {
                            joblogger.error("处理版本 {} 的第 {}/{} 批次时发生异常: {}", version, batchIndex + 1, totalBatches, e.getMessage(), e);
                        } finally {
                            // 清理资源，帮助GC
                            batchRecords.clear();
                        }
                    }
                }

                joblogger.info("版本 {} 的所有数据处理完成", version);

                // 每个版本处理完成后执行GC，释放内存
                System.gc();
            }

            // 更新所有任务的同步状态
            for (String taskId : taskIdList) {
                try {
                    updateTaskSyncStatus(taskId);
                    joblogger.info("更新任务 {} 的同步状态为已同步", taskId);
                } catch (Exception e) {
                    joblogger.error("更新任务 {} 的同步状态时发生异常: {}", taskId, e.getMessage(), e);
                }
            }

            joblogger.info("完成处理爬虫任务组: {}", taskIdsStr);
        } catch (Exception e) {
            joblogger.error("处理爬虫任务组 {} 时发生异常: {}", taskIdsStr, e.getMessage(), e);
        }
    }

    /**
     * 根据版本号获取爬虫记录表名
     * @param versionNo 版本号
     * @return 爬虫记录表名
     */
    private String getCrawlRecordTableName(String versionNo) {
        if (StringUtils.isBlank(versionNo)) {
            // 默认返回原表名
            return "xty_tariff_crawl_record";
        }

        // 构造缓存键
        String cacheKey = "CRAWL_CHECK_RUNNING_TABLE_NAME_" + versionNo;
        // 先从缓存获取
        String cachedTableName = RedissonUtil.get(cacheKey);
        if (StringUtils.isNotBlank(cachedTableName)) {
            return cachedTableName;
        }

        try {
            EasySQL sql = new EasySQL();
            sql.append("SELECT VERSION_TABLE_NAME FROM " + Constants.getBusiSchema() + ".xty_crawler_version WHERE 1=1");
            sql.append(versionNo, "AND VERSION_NO = ?");

            EasyQuery query = QueryFactory.getTariffQuery();
            String tableName = query.queryForString(sql.getSQL(), sql.getParams());

            if (StringUtils.isNotBlank(tableName)) {
                // 缓存表名，30分钟有效期
                RedissonUtil.setEx(cacheKey, tableName, 30 * 60);
                return tableName;
            } else {
                joblogger.debug("未找到版本{}对应的表名，使用默认表", versionNo);
                return "xty_tariff_crawl_record";
            }
        } catch (Exception e) {
            joblogger.error("查询版本{}对应的表名时发生错误: {}", versionNo, e.getMessage(), e);
            return "xty_tariff_crawl_record";
        }
    }

    /**
     * 获取xty_crawler_version表中最新的VERSION_TABLE_NAME
     * @return 最新版本表名，若无则返回null
     */
    private String getLatestCrawlRecordTableName() {
        try {
            EasyQuery query = QueryFactory.getTariffQuery();
            EasySQL sql = new EasySQL();
            sql.append("SELECT VERSION_TABLE_NAME FROM " + Constants.getBusiSchema() + ".xty_crawler_version  where VERSION_STATUS = 'ACTIVE' ORDER BY CREATE_TIME DESC LIMIT 1");
            String tableName = query.queryForString(sql.getSQL(), sql.getParams());
            return StringUtils.isNotBlank(tableName) ? tableName : null;
        } catch (Exception e) {
            joblogger.error("获取最新爬虫版本表名失败: {}", e.getMessage(), e);
            return null;
        }
    }

}
