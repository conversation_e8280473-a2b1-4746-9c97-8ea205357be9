package com.yunqu.cc.expand.listener;

import com.yunqu.cc.expand.base.Constants;
import com.yunqu.cc.expand.service.OrderClearService;
import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceContextListener;

import javax.servlet.annotation.WebListener;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * AppServiceContextListener
 * </p>
 *
 * @ClassName AppServiceContextListener
 * <AUTHOR> Copy This Tag)
 * @Description AppServiceContextListener
 * @Since create in 5/14/24 8:22 PM
 * @Version v1.0
 * @Copyright Copyright (c) 2024
 * @Company 广州云趣信息科技有限公司
 */
@WebListener
public class AppServiceContextListener extends ServiceContextListener {
    @Override
    protected List<ServiceResource> serviceResourceCatalog() {
        List<ServiceResource> list = new ArrayList<>();

        ServiceResource serviceResource = new ServiceResource();
        serviceResource.appName = Constants.APP_NAME;
        serviceResource.serviceId = "XTY_EVT_ES_ORDER_OPERATE";
        serviceResource.description = "ES操作服务";
        serviceResource.serviceName = "ES操作服务";
        serviceResource.className = "com.yunqu.cc.expand.service.EsEventService";
        list.add(serviceResource);

        ServiceResource serviceResource1 = new ServiceResource();
        serviceResource1.appName = Constants.APP_NAME;
        serviceResource1.serviceId = "XTY_EVT_ES_ORDER_QUERY";
        serviceResource1.description = "ES查询服务";
        serviceResource1.serviceName = "ES查询服务";
        serviceResource1.className = "com.yunqu.cc.expand.service.EsQueryService";
        list.add(serviceResource1);


        ServiceResource serviceResource2 = new ServiceResource();
        serviceResource2.appName = Constants.APP_NAME;
        serviceResource2.serviceId = "XTY_EVT_ES_ORDER_STAT_QUERY";
        serviceResource2.description = "ES查询统计服务";
        serviceResource2.serviceName = "ES查询统计服务";
        serviceResource2.className = "com.yunqu.cc.expand.service.EsStatQueryService";
        list.add(serviceResource2);

        ServiceResource serviceResource3 = new ServiceResource();
        serviceResource3.appName = Constants.APP_NAME;
        serviceResource3.serviceId = OrderClearService.SERVICE_ID;
        serviceResource3.description = "工单引擎数据清理服务";
        serviceResource3.serviceName = "工单引擎数据清理服务";
        serviceResource3.className = "com.yunqu.cc.expand.service.OrderClearService";
        list.add(serviceResource3);

        ServiceResource serviceResource4 = new ServiceResource();
        serviceResource4.appName = Constants.APP_NAME;
        serviceResource4.serviceId = "XTY_EXPAND_SERVICE";
        serviceResource4.description = "信通院扩展包通用服务接口";
        serviceResource4.serviceName = "信通院扩展包通用服务接口";
        serviceResource4.className = "com.yunqu.cc.expand.inf.XtyExpandService";
        list.add(serviceResource4);

        ServiceResource serviceResource5 = new ServiceResource();
        serviceResource5.appName = Constants.APP_NAME;
        serviceResource5.className = "com.yunqu.cc.expand.inf.RegInitDataService"	;
        serviceResource5.description = "注册模块初始化数据接口-cx-mix-expand";
        serviceResource5.serviceId = "CC-REG-INIT-DATA-" + Constants.APP_NAME;
        serviceResource5.serviceName = "注册模块初始化数据接口-cx-mix-expand";
        list.add(serviceResource5);

        return list;
    }
}
