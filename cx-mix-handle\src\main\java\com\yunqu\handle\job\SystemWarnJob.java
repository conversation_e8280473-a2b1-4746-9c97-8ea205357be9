package com.yunqu.handle.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.handle.base.CommonLogger;
import com.yunqu.handle.base.Constants;
import com.yunqu.handle.base.QueryFactory;
import com.yunqu.handle.service.OrderFetchService;
import com.yunqu.handle.service.RedisWarnService;
import com.yunqu.handle.service.SystemWarnService;
import com.yunqu.handle.util.RedisLockUtil;
import com.yunqu.handle.util.SmsMsgUtil;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import com.yunqu.handle.util.XtyModuleRuntimeDataCollector;
import org.apache.commons.collections4.CollectionUtils;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 申诉工单创建定时任务
 * 定时查询未创建工单的申诉记录并调用OrderSubmitInfService发起工单
 */
public class SystemWarnJob implements Job {
    
    private static final Logger logger = LoggerFactory.getLogger(CommonLogger.getLogger("warn").getName());
    private static final String LOCK_KEY = "system_warn_job_lock";
    private static final int LOCK_EXPIRE_TIME = 60 * 60; // 锁过期时间，60秒
    private static final String LAST_SYNC_TIME = "last_sync_time";

    private static final String[] FREE_TIMES = new String[]{"00","01","02","03","04","05","06","07","12","13","19","20","21","22","23"};

    
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        logger.info("开始执行系统预警定时任务");
        
        // 使用分布式锁确保在集群环境中只有一个实例执行任务
        boolean locked = false;
        try {
            // 尝试获取锁
            locked = RedisLockUtil.lock(LOCK_KEY, LOCK_EXPIRE_TIME);
            if (!locked) {
                logger.info("未能获取到分布式锁，跳过本次执行");
                return;
            }

            //获取当前小时
            String currentHour = DateUtil.getCurrentDateStr("HH");

           
            this.errrWarn();

            this.redisWarn();

            //上报运行状态
            XtyModuleRuntimeDataCollector.report(Constants.APP_NAME, "SystemWarnJob", "定时检查公众端状态、稽核信息", 5000);

            //判断是否是空闲时间
            if (!Arrays.stream(FREE_TIMES).anyMatch(currentHour::equals)) {
                logger.info("当前时间[{}]不在空闲时间范围内，跳过本次执行",currentHour);
                return;
            }

            this.syncWarn();
           
            this.checkFiles();
            
            logger.info("系统预警定时任务执行完成");
            
        } catch (Exception e) {
            logger.error("执行系统预警定时任务异常: " + e.getMessage(), e);
            throw new JobExecutionException(e);
        } finally {
            // 释放锁
            if (locked) {
                RedisLockUtil.unlock(LOCK_KEY);
                logger.debug("已释放分布式锁");
            }
        }
    }

    private JSONObject errrWarn () {
        SystemWarnService service = new SystemWarnService();
        JSONObject execute;
        try {
            execute = service.execute();
            if (execute != null && execute.getBooleanValue("success")) {
                JSONObject data = execute.getJSONObject("data");
                if (data != null) {
                    StringBuilder sb = new StringBuilder();
                    sb.append("申诉错误队列存在").append(data.getIntValue("errQueueSize")).append("条；");
                    sb.append("申诉截止当前时间入库").append(data.getIntValue("appealSize")).append("条；");
                    sb.append("绿通截止当前时间入库").append(data.getIntValue("complainSize")).append("条；");
                    sb.append("调解截止当前时间入库").append(data.getIntValue("mediateSize")).append("条；");
                    String mString = sb.toString();
                    logger.info("入口系统预警数据：" + mString);
                    if (data.getIntValue("errQueueSize") > 0) {
                        SmsMsgUtil.sendMsg( mString,false);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("执行系统预警定时任务异常: " + e.getMessage(), e);
        }
        return null;
    }


      private JSONObject checkFiles () {
        OrderFetchService service = new OrderFetchService();
        try {
            service.checkFiles();
        } catch (Exception e) {
            logger.error("执行系统预警定时任务异常: " + e.getMessage(), e);
        }
        return null;
    }

    private JSONObject redisWarn () {
        RedisWarnService service = new RedisWarnService();
        JSONObject execute;
        try {
            for (int i = 0; i < 5; i++) {
                execute = service.execute();
                 if (!execute.getBooleanValue("success")) {
                    logger.info("redis链接[{}]正常....",i);
                    return null;
                }
                CommonUtil.sleep(3);
            }
            SmsMsgUtil.sendMsg( String.format("公信云redis链接异常,已重试3次"),false);
        } catch (Exception e) {
            logger.error("执行公信云redis预警定时任务异常: " + e.getMessage(), e);
        }
        return null;
    }

    private JSONObject syncWarn () {
        OrderFetchService service = new OrderFetchService();
        // 分页拉取工单数据
        JSONObject data = new JSONObject();
        boolean hasMoreData = true;
        int pageIndex = 0;
        int pageSize = 50;
        String endTime = CacheUtil.get(LAST_SYNC_TIME);
        String startTime = DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD)+" 00:00:00" ;
        //获取当前小时
        String currentHour = DateUtil.getCurrentDateStr("HH");
        endTime = Optional.ofNullable(endTime).orElse(DateUtil.getCurrentDateStr());
        if (Arrays.asList(new String[]{"00","01","02","03","04","05","06"}).contains(currentHour)) {
            startTime = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, DateUtil.getCurrentDateStr(), -1) + " 00:00:00";
        }
        while (hasMoreData) {
            logger.info("拉取第{}页工单数据，每页{}条",  pageIndex, pageSize);
            // 调用服务拉取数据 DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD)
            JSONObject result = service.fetchOrders(pageIndex, pageSize,true,  startTime,endTime);
            if (result != null && result.getBooleanValue("success")) {
                List<JSONObject> dataList = JSONArray.parseArray(result.getString("data"),JSONObject.class);
                if (dataList != null && !dataList.isEmpty()) {
                    logger.info("本页获取到{}条工单数据", dataList.size());
                    
                    try {
                        List<JSONObject> unSyncList = this.getUnSyncIds(dataList);
                        if (unSyncList != null && !unSyncList.isEmpty()) {
                            logger.info("本页获取到{}条未同步工单数据,开始自动同步未同步数据：{}" ,unSyncList.size(),JSONObject.toJSONString(unSyncList));
                            service.saveOrdersToDatabase(unSyncList);
                            logger.info("结束自动同步未同步数据");
                        }
                        if ("00".equals(currentHour)) {
                            String lastDay = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, DateUtil.getCurrentDateStr(), -1);
                            int appealSize = dataList.stream()
                                .filter(item -> "01".equals(item.getString("isAppeal")) && lastDay.equals(item.getString("appealTime").substring(0, 10))).collect(Collectors.toList()).size();
                            logger.info("本页获取到{}条申诉工单数据", appealSize);
                            int complainSize = dataList.stream()
                                .filter(item -> "02".equals(item.getString("isAppeal")) && lastDay.equals(item.getString("appealTime").substring(0, 10))).collect(Collectors.toList()).size();
                            logger.info("本页获取到{}条申诉工单数据", complainSize);
                            data.put("appealSize", data.getIntValue("appealSize") + appealSize);
                            data.put("complainSize", data.getIntValue("complainSize") + complainSize);
                        }
                    } catch (SQLException e) {
                        logger.info("查询未同步工单ID异常: " + e.getMessage(), e);
                    }
                    // 如果返回的数据少于pageSize，说明没有更多数据了
                    if (dataList.size() < pageSize) {
                        hasMoreData = false;
                    }  else {
                        pageIndex++;
                    }
                } else {
                    logger.info("本页没有获取到工单数据，结束拉取");
                    hasMoreData = false;
                }
            } else {
                logger.error("拉取工单数据失败: {}", result != null ? result.getString("message") : "未知错误");
                hasMoreData = false;
            }
        }
        if ("00".equals(currentHour)) { 
            StringBuilder sb = new StringBuilder();
            sb.append("申诉截止当前时间入库").append(data.getIntValue("appealSize")).append("条；");
            sb.append("绿通截止当前时间入库").append(data.getIntValue("complainSize")).append("条；");
            String mString = sb.toString();
            logger.info("入口系统预警数据：" + mString);
            try {
                SmsMsgUtil.sendMsg( mString,true);
            } catch (Exception e) {
                logger.error("发送短信异常: " + e.getMessage(), e);
            }
        }
        return null;
    }

    /**
     * 获取未同步的ID列表
     * 从输入列表中筛选出在数据库中不存在的ID
     * 
     * @param list 包含id字段的JSONObject列表
     * @return 未同步的ID列表，如果没有则返回null
     * @throws SQLException 数据库查询异常
     */
    private List<JSONObject> getUnSyncIds(List<JSONObject> list) throws SQLException {
        // 提取所有ID
        List<String> idList = list.stream()
                .map(item -> item.getString("id"))
                .collect(Collectors.toList());
        
        if (idList.isEmpty()) {
            return null;
        }
        
        // 查询数据库中已存在的ID
        EasySQL sql = new EasySQL();
        sql.append(" select ID from " + Constants.getSysSchema() + ".c_box_appeal");
        sql.append(" where 1=1 ");
        sql.appendIn(idList.toArray(new String[]{}), " and id  ");
        
        List<JSONObject> appealIds = QueryFactory.getQuery().queryForList(
                sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        
        if (CollectionUtils.isEmpty(appealIds)) {
            // 如果数据库中没有任何匹配的ID，说明所有ID都是未同步的
            return list;
        }
        
        // 使用HashSet提高查找效率，时间复杂度从O(n²)降低到O(n)
        Set<String> existingIds = appealIds.stream()
                .map(item -> item.getString("ID"))
                .collect(Collectors.toSet());
        
        // 筛选出未同步的ID
        return list.stream()
                .filter(item -> !existingIds.contains(item.getString("id")))
                .collect(Collectors.toList());
    }
}