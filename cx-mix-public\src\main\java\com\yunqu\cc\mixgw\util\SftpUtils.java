package com.yunqu.cc.mixgw.util;

import com.jcraft.jsch.*;
import com.yunqu.cc.mixgw.base.CommonLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <p>
 * 现代化SFTP工具类
 * 提供完整的SFTP文件操作功能，支持连接管理、文件上传下载、目录操作等
 * </p>
 *
 * @ClassName SftpUtils
 * <AUTHOR> Copy This Tag)
 * @Description 功能完整的SFTP工具类，支持多种认证方式和完善的错误处理
 * @Since create in 2025/7/24 15:20
 * @Version v2.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
public class SftpUtils implements AutoCloseable {

    private static final Logger logger = LoggerFactory.getLogger(CommonLogger.getLogger("sftp").getName());

    // 默认配置常量
    private static final int DEFAULT_PORT = 22;
    private static final int DEFAULT_TIMEOUT = 30000; // 30秒
    private static final String DEFAULT_CHARSET = "UTF-8";

    // 连接相关
    private Session session;
    private ChannelSftp channelSftp;
    private SftpConfig config;
    private volatile boolean connected = false;

    // 连接池管理（静态，所有实例共享）
    private static final Map<String, SftpUtils> connectionPool = new ConcurrentHashMap<>();
    private static final AtomicLong connectionCounter = new AtomicLong(0);

    /**
     * SFTP连接配置类
     */
    public static class SftpConfig {
        private String host;
        private int port = DEFAULT_PORT;
        private String username;
        private String password;
        private String privateKeyPath;
        private String privateKeyPassphrase;
        private int timeout = DEFAULT_TIMEOUT;
        private String charset = DEFAULT_CHARSET;
        private boolean strictHostKeyChecking = false;
        private Map<String, String> sessionConfig = new HashMap<>();

        // 构造函数
        public SftpConfig(String host, String username) {
            this.host = host;
            this.username = username;
        }

        public SftpConfig(String host, int port, String username) {
            this.host = host;
            this.port = port;
            this.username = username;
        }

        // Builder模式的方法
        public SftpConfig password(String password) {
            this.password = password;
            return this;
        }

        public SftpConfig privateKey(String privateKeyPath) {
            this.privateKeyPath = privateKeyPath;
            return this;
        }

        public SftpConfig privateKey(String privateKeyPath, String passphrase) {
            this.privateKeyPath = privateKeyPath;
            this.privateKeyPassphrase = passphrase;
            return this;
        }

        public SftpConfig timeout(int timeout) {
            this.timeout = timeout;
            return this;
        }

        public SftpConfig charset(String charset) {
            this.charset = charset;
            return this;
        }

        public SftpConfig strictHostKeyChecking(boolean strict) {
            this.strictHostKeyChecking = strict;
            return this;
        }

        public SftpConfig sessionConfig(String key, String value) {
            this.sessionConfig.put(key, value);
            return this;
        }

        // Getters
        public String getHost() {
            return host;
        }

        public int getPort() {
            return port;
        }

        public String getUsername() {
            return username;
        }

        public String getPassword() {
            return password;
        }

        public String getPrivateKeyPath() {
            return privateKeyPath;
        }

        public String getPrivateKeyPassphrase() {
            return privateKeyPassphrase;
        }

        public int getTimeout() {
            return timeout;
        }

        public String getCharset() {
            return charset;
        }

        public boolean isStrictHostKeyChecking() {
            return strictHostKeyChecking;
        }

        public Map<String, String> getSessionConfig() {
            return sessionConfig;
        }

        /**
         * 生成连接的唯一标识
         */
        public String getConnectionKey() {
            return String.format("%s:%d@%s", username, port, host);
        }
    }

    /**
     * 文件传输进度回调接口
     */
    public interface ProgressCallback {
        /**
         * 进度回调
         * @param transferred 已传输字节数
         * @param total 总字节数
         * @param percentage 传输百分比
         */
        void onProgress(long transferred, long total, double percentage);
    }

    /**
     * 文件信息类
     */
    public static class FileInfo {
        private String name;
        private String path;
        private long size;
        private boolean isDirectory;
        private long lastModified;
        private String permissions;

        public FileInfo(String name, String path, long size, boolean isDirectory, long lastModified, String permissions) {
            this.name = name;
            this.path = path;
            this.size = size;
            this.isDirectory = isDirectory;
            this.lastModified = lastModified;
            this.permissions = permissions;
        }

        // Getters
        public String getName() {
            return name;
        }

        public String getPath() {
            return path;
        }

        public long getSize() {
            return size;
        }

        public boolean isDirectory() {
            return isDirectory;
        }

        public long getLastModified() {
            return lastModified;
        }

        public String getPermissions() {
            return permissions;
        }

        @Override
        public String toString() {
            return String.format("FileInfo{name='%s', path='%s', size=%d, isDirectory=%s, lastModified=%d, permissions='%s'}",
                    name, path, size, isDirectory, lastModified, permissions);
        }
    }

    // ==================== 构造函数 ====================

    /**
     * 私有构造函数，通过静态方法创建实例
     */
    private SftpUtils(SftpConfig config) {
        this.config = config;
        this.connect();
    }

    /**
     * 创建SFTP连接实例（密码认证）
     */
    public static SftpUtils create(String host, String username, String password) {
        SftpConfig config = new SftpConfig(host, username).password(password);
        return new SftpUtils(config);
    }

    /**
     * 创建SFTP连接实例（密码认证，指定端口）
     */
    public static SftpUtils create(String host, int port, String username, String password) {
        SftpConfig config = new SftpConfig(host, port, username).password(password);
        return new SftpUtils(config);
    }

    /**
     * 创建SFTP连接实例（私钥认证）
     */
    public static SftpUtils createWithPrivateKey(String host, String username, String privateKeyPath) {
        SftpConfig config = new SftpConfig(host, username).privateKey(privateKeyPath);
        return new SftpUtils(config);
    }

    /**
     * 创建SFTP连接实例（私钥认证，带密码）
     */
    public static SftpUtils createWithPrivateKey(String host, String username, String privateKeyPath, String passphrase) {
        SftpConfig config = new SftpConfig(host, username).privateKey(privateKeyPath, passphrase);
        return new SftpUtils(config);
    }

    /**
     * 创建SFTP连接实例（使用配置对象）
     */
    public static SftpUtils create(SftpConfig config) {
        return new SftpUtils(config);
    }

    /**
     * 从连接池获取或创建连接实例
     */
    public static SftpUtils getPooledConnection(SftpConfig config) {
        String key = config.getConnectionKey();
        return connectionPool.computeIfAbsent(key, k -> {
            logger.info("创建新的连接池连接: {}", key);
            return new SftpUtils(config);
        });
    }

    // ==================== 连接管理 ====================

    /**
     * 建立SFTP连接
     */
    public synchronized boolean connect() {
        if (connected && isConnected()) {
            logger.info("SFTP连接已存在: {}:{}", config.getHost(), config.getPort());
            return true;
        }

        try {
            logger.info("开始建立SFTP连接: {}@{}:{}", config.getUsername(), config.getHost(), config.getPort());

            // 创建JSch实例
            JSch jsch = new JSch();

            // 设置私钥
            if (config.getPrivateKeyPath() != null) {
                if (config.getPrivateKeyPassphrase() != null) {
                    jsch.addIdentity(config.getPrivateKeyPath(), config.getPrivateKeyPassphrase());
                } else {
                    jsch.addIdentity(config.getPrivateKeyPath());
                }
                logger.info("已设置私钥认证: {}", config.getPrivateKeyPath());
            }

            // 创建会话
            session = jsch.getSession(config.getUsername(), config.getHost(), config.getPort());

            // 设置密码
            if (config.getPassword() != null) {
                session.setPassword(config.getPassword());
                logger.info("已设置密码认证");
            }

            // 设置会话配置
            Properties sessionConfig = new Properties();
            sessionConfig.put("StrictHostKeyChecking", config.isStrictHostKeyChecking() ? "yes" : "no");
            sessionConfig.putAll(config.getSessionConfig());
            session.setConfig(sessionConfig);

            // 设置超时
            session.setTimeout(config.getTimeout());

            // 连接会话
            session.connect();
            logger.info("SSH会话连接成功");

            // 打开SFTP通道
            Channel channel = session.openChannel("sftp");
            channel.connect();
            channelSftp = (ChannelSftp) channel;

            connected = true;
            long connectionId = connectionCounter.incrementAndGet();
            logger.info("SFTP连接建立成功: {}@{}:{} (连接ID: {})",
                    config.getUsername(), config.getHost(), config.getPort(), connectionId);

            return true;

        } catch (Exception e) {
            logger.error("SFTP连接失败: {}@{}:{}", config.getUsername(), config.getHost(), config.getPort(), e);
            disconnect();
            return false;
        }
    }

    /**
     * 检查连接是否有效
     */
    public boolean isConnected() {
        return connected &&
                session != null && session.isConnected() &&
                channelSftp != null && channelSftp.isConnected();
    }

    /**
     * 断开SFTP连接
     */
    public synchronized void disconnect() {
        try {
            if (channelSftp != null && channelSftp.isConnected()) {
                channelSftp.disconnect();
                logger.info("SFTP通道已断开");
            }
        } catch (Exception e) {
            logger.warn("断开SFTP通道时发生异常", e);
        }

        try {
            if (session != null && session.isConnected()) {
                session.disconnect();
                logger.info("SSH会话已断开");
            }
        } catch (Exception e) {
            logger.warn("断开SSH会话时发生异常", e);
        }

        connected = false;
        channelSftp = null;
        session = null;

        logger.info("SFTP连接已断开: {}@{}:{}", config.getUsername(), config.getHost(), config.getPort());
    }

    /**
     * 重新连接
     */
    public boolean reconnect() {
        logger.info("尝试重新连接SFTP: {}@{}:{}", config.getUsername(), config.getHost(), config.getPort());
        disconnect();
        return connect();
    }

    /**
     * 确保连接有效，如果无效则重新连接
     */
    private boolean ensureConnected() {
        if (!isConnected()) {
            logger.info("连接无效，尝试重新连接");
            return connect();
        }
        return true;
    }

    /**
     * 实现AutoCloseable接口
     */
    @Override
    public void close() {
        disconnect();
    }

    // ==================== 目录操作 ====================

    /**
     * 获取当前工作目录
     */
    public String getCurrentDirectory() throws SftpException {
        if (!ensureConnected()) {
            throw new SftpException(0, "SFTP连接失败");
        }

        try {
            String pwd = channelSftp.pwd();
            logger.info("当前工作目录: {}", pwd);
            return pwd;
        } catch (Exception e) {
            logger.error("获取当前目录失败", e);
            throw new SftpException(0, "获取当前目录失败: " + e.getMessage());
        }
    }

    /**
     * 切换到指定目录
     */
    public boolean changeDirectory(String path) {
        if (!ensureConnected()) {
            logger.error("SFTP连接失败，无法切换目录");
            return false;
        }

        try {
            channelSftp.cd(path);
            logger.info("切换到目录: {}", path);
            return true;
        } catch (Exception e) {
            logger.error("切换目录失败: {}", path, e);
            return false;
        }
    }

    /**
     * 创建目录（支持递归创建）
     */
    public boolean createDirectory(String path) {
        if (!ensureConnected()) {
            logger.error("SFTP连接失败，无法创建目录");
            return false;
        }

        try {
            // 尝试直接创建目录
            channelSftp.mkdir(path);
            logger.info("目录创建成功: {}", path);
            return true;
        } catch (SftpException e) {
            // 如果失败，尝试递归创建
            logger.error(e.getMessage(), e);
            return createDirectoryRecursively(path);
        }
    }

    /**
     * 递归创建目录
     */
    private boolean createDirectoryRecursively(String path) {
        try {
            String[] dirs = path.split("/");
            StringBuilder currentPath = new StringBuilder();

            for (String dir : dirs) {
                if (dir.isEmpty()) continue;

                if (currentPath.length() > 0) {
                    currentPath.append("/");
                }
                currentPath.append(dir);

                try {
                    channelSftp.mkdir(currentPath.toString());
                    logger.info("创建目录: {}", currentPath.toString());
                } catch (SftpException e) {
                    logger.error(e.getMessage(), e);
                    // 目录可能已存在，继续
                    if (e.id != ChannelSftp.SSH_FX_FAILURE) {
                        logger.info("目录可能已存在: {}", currentPath.toString());
                    }
                }
            }

            logger.info("递归创建目录成功: {}", path);
            return true;
        } catch (Exception e) {
            logger.error("递归创建目录失败: {}", path, e);
            return false;
        }
    }

    /**
     * 删除目录（空目录）
     */
    public boolean removeDirectory(String path) {
        if (!ensureConnected()) {
            logger.error("SFTP连接失败，无法删除目录");
            return false;
        }

        try {
            channelSftp.rmdir(path);
            logger.info("目录删除成功: {}", path);
            return true;
        } catch (Exception e) {
            logger.error("删除目录失败: {}", path, e);
            return false;
        }
    }

    /**
     * 检查路径是否存在
     */
    public boolean exists(String path) {
        if (!ensureConnected()) {
            return false;
        }

        try {
            channelSftp.stat(path);
            return true;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查是否为目录
     */
    public boolean isDirectory(String path) {
        if (!ensureConnected()) {
            return false;
        }

        try {
            SftpATTRS attrs = channelSftp.stat(path);
            return attrs.isDir();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查是否为文件（非目录）
     */
    public boolean isFile(String path) {
        if (!ensureConnected()) {
            return false;
        }

        try {
            SftpATTRS attrs = channelSftp.stat(path);
            return !attrs.isDir();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return false;
        }
    }

    /**
     * 验证文件是否存在（专门用于文件验证）
     * @param remoteFilePath 远程文件路径
     * @return true如果文件存在且是文件（非目录），false如果文件不存在或是目录
     */
    public boolean fileExists(String remoteFilePath) {
        if (!ensureConnected()) {
            logger.info("SFTP连接失败，无法验证文件存在性");
            return false;
        }

        if (remoteFilePath == null || remoteFilePath.trim().isEmpty()) {
            logger.info("文件路径为空，验证失败");
            return false;
        }

        try {
            SftpATTRS attrs = channelSftp.stat(remoteFilePath);
            boolean isFile = !attrs.isDir();
            logger.info("文件存在性验证: {} -> 存在: {}, 是文件: {}", remoteFilePath, true, isFile);
            return isFile;
        } catch (SftpException e) {
            if (e.id == ChannelSftp.SSH_FX_NO_SUCH_FILE) {
                logger.info("文件不存在: {}", remoteFilePath);
            } else {
                logger.info("验证文件存在性时发生异常: {}, 错误: {}", remoteFilePath, e.getMessage());
            }
            return false;
        } catch (Exception e) {
            logger.info("验证文件存在性时发生未知异常: {}, 错误: {}", remoteFilePath, e.getMessage());
            return false;
        }
    }

    /**
     * 验证文件是否存在并获取详细信息
     * @param remoteFilePath 远程文件路径
     * @return FileExistenceInfo 包含存在性和详细信息的对象
     */
    public FileExistenceInfo checkFileExistence(String remoteFilePath) {
        if (!ensureConnected()) {
            return new FileExistenceInfo(false, "SFTP连接失败");
        }

        if (remoteFilePath == null || remoteFilePath.trim().isEmpty()) {
            return new FileExistenceInfo(false, "文件路径为空");
        }

        try {
            SftpATTRS attrs = channelSftp.stat(remoteFilePath);
            boolean isFile = !attrs.isDir();

            if (isFile) {
                FileInfo fileInfo = new FileInfo(
                    getFileName(remoteFilePath),
                    remoteFilePath,
                    attrs.getSize(),
                    false,
                    attrs.getMTime() * 1000L,
                    attrs.getPermissionsString()
                );
                return new FileExistenceInfo(true, "文件存在", fileInfo);
            } else {
                return new FileExistenceInfo(false, "路径存在但是目录，不是文件");
            }

        } catch (SftpException e) {
            logger.error(e.getMessage(), e);
            if (e.id == ChannelSftp.SSH_FX_NO_SUCH_FILE) {
                return new FileExistenceInfo(false, "文件不存在");
            } else {
                return new FileExistenceInfo(false, "验证失败: " + e.getMessage());
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new FileExistenceInfo(false, "验证异常: " + e.getMessage());
        }
    }

    /**
     * 批量验证文件是否存在
     * @param remoteFilePaths 远程文件路径列表
     * @return Map<String, Boolean> 文件路径和存在性的映射
     */
    public Map<String, Boolean> batchFileExists(List<String> remoteFilePaths) {
        Map<String, Boolean> results = new HashMap<>();

        if (remoteFilePaths == null || remoteFilePaths.isEmpty()) {
            logger.info("文件路径列表为空");
            return results;
        }

        if (!ensureConnected()) {
            logger.error("SFTP连接失败，无法批量验证文件存在性");
            // 所有文件都标记为不存在
            for (String path : remoteFilePaths) {
                results.put(path, false);
            }
            return results;
        }

        logger.info("开始批量验证文件存在性，共{}个文件", remoteFilePaths.size());

        for (String filePath : remoteFilePaths) {
            boolean exists = fileExists(filePath);
            results.put(filePath, exists);
            logger.info("批量验证: {} -> {}", filePath, exists);
        }

        long existsCount = results.values().stream().mapToLong(b -> b ? 1 : 0).sum();
        logger.info("批量文件存在性验证完成: 总数={}, 存在={}, 不存在={}",
                   remoteFilePaths.size(), existsCount, remoteFilePaths.size() - existsCount);

        return results;
    }

    /**
     * 文件存在性信息类
     */
    public static class FileExistenceInfo {
        private boolean exists;
        private String message;
        private FileInfo fileInfo;

        public FileExistenceInfo(boolean exists, String message) {
            this.exists = exists;
            this.message = message;
        }

        public FileExistenceInfo(boolean exists, String message, FileInfo fileInfo) {
            this.exists = exists;
            this.message = message;
            this.fileInfo = fileInfo;
        }

        public boolean isExists() { return exists; }
        public String getMessage() { return message; }
        public FileInfo getFileInfo() { return fileInfo; }

        @Override
        public String toString() {
            return String.format("FileExistenceInfo{exists=%s, message='%s', fileInfo=%s}",
                               exists, message, fileInfo);
        }
    }

    /**
     * 列出目录内容
     */
    @SuppressWarnings("unchecked")
    public List<FileInfo> listFiles(String path) {
        List<FileInfo> files = new ArrayList<>();

        if (!ensureConnected()) {
            logger.error("SFTP连接失败，无法列出文件");
            return files;
        }

        try {
            Vector<ChannelSftp.LsEntry> entries = channelSftp.ls(path);

            for (ChannelSftp.LsEntry entry : entries) {
                String name = entry.getFilename();
                if (".".equals(name) || "..".equals(name)) {
                    continue;
                }

                SftpATTRS attrs = entry.getAttrs();
                String fullPath = path.endsWith("/") ? path + name : path + "/" + name;

                FileInfo fileInfo = new FileInfo(
                        name,
                        fullPath,
                        attrs.getSize(),
                        attrs.isDir(),
                        attrs.getMTime() * 1000L, // 转换为毫秒
                        attrs.getPermissionsString()
                );

                files.add(fileInfo);
            }

            logger.info("列出目录内容: {} (共{}个文件)", path, files.size());

        } catch (Exception e) {
            logger.error("列出目录内容失败: {}", path, e);
        }

        return files;
    }

    // ==================== 文件操作 ====================

    /**
     * 上传文件
     */
    public boolean uploadFile(String localFilePath, String remoteFilePath) {
        return uploadFile(localFilePath, remoteFilePath, null);
    }

    /**
     * 上传文件（带进度回调）
     */
    public boolean uploadFile(String localFilePath, String remoteFilePath, ProgressCallback callback) {
        File localFile = new File(localFilePath);
        if (!localFile.exists() || !localFile.isFile()) {
            logger.error("本地文件不存在或不是文件: {}", localFilePath);
            return false;
        }

        return uploadFile(localFile, remoteFilePath, callback);
    }

    /**
     * 上传文件（File对象）
     */
    public boolean uploadFile(File localFile, String remoteFilePath) {
        return uploadFile(localFile, remoteFilePath, null);
    }

    /**
     * 上传文件（File对象，带进度回调）
     */
    public boolean uploadFile(File localFile, String remoteFilePath, ProgressCallback callback) {
        if (!ensureConnected()) {
            logger.error("SFTP连接失败，无法上传文件");
            return false;
        }

        if (!localFile.exists() || !localFile.isFile()) {
            logger.error("本地文件不存在或不是文件: {}", localFile.getAbsolutePath());
            return false;
        }

        // 确保远程目录存在
        String remoteDir = getParentPath(remoteFilePath);
        if (remoteDir != null && !exists(remoteDir)) {
            createDirectory(remoteDir);
        }

        try (FileInputStream fis = new FileInputStream(localFile)) {
            long fileSize = localFile.length();
            logger.info("开始上传文件: {} -> {} (大小: {} bytes)", localFile.getAbsolutePath(), remoteFilePath, fileSize);

            if (callback != null) {
                // 使用带进度的上传
                uploadWithProgress(fis, remoteFilePath, fileSize, callback);
            } else {
                // 直接上传
                channelSftp.put(fis, remoteFilePath);
            }

            logger.info("文件上传成功: {} -> {}", localFile.getAbsolutePath(), remoteFilePath);
            return true;

        } catch (Exception e) {
            logger.error("文件上传失败: {} -> {}", localFile.getAbsolutePath(), remoteFilePath, e);
            return false;
        }
    }

    /**
     * 上传输入流
     */
    public boolean uploadStream(InputStream inputStream, String remoteFilePath) {
        return uploadStream(inputStream, remoteFilePath, -1, null);
    }

    /**
     * 上传输入流（带进度回调）
     */
    public boolean uploadStream(InputStream inputStream, String remoteFilePath, long totalSize, ProgressCallback callback) {
        if (!ensureConnected()) {
            logger.error("SFTP连接失败，无法上传流");
            return false;
        }

        // 确保远程目录存在
        String remoteDir = getParentPath(remoteFilePath);
        if (remoteDir != null && !exists(remoteDir)) {
            createDirectory(remoteDir);
        }

        try {
            logger.info("开始上传流: {}", remoteFilePath);

            if (callback != null && totalSize > 0) {
                uploadWithProgress(inputStream, remoteFilePath, totalSize, callback);
            } else {
                channelSftp.put(inputStream, remoteFilePath);
            }

            logger.info("流上传成功: {}", remoteFilePath);
            return true;

        } catch (Exception e) {
            logger.error("流上传失败: {}", remoteFilePath, e);
            return false;
        }
    }

    /**
     * 带进度的上传实现
     */
    private void uploadWithProgress(InputStream inputStream, String remoteFilePath, long totalSize, ProgressCallback callback) throws SftpException {
        // 创建进度监控器
        SftpProgressMonitor monitor = new SftpProgressMonitor() {
            private long transferred = 0;

            @Override
            public void init(int op, String src, String dest, long max) {
                transferred = 0;
            }

            @Override
            public boolean count(long count) {
                transferred += count;
                double percentage = totalSize > 0 ? (double) transferred / totalSize * 100 : 0;
                callback.onProgress(transferred, totalSize, percentage);
                return true;
            }

            @Override
            public void end() {
                callback.onProgress(totalSize, totalSize, 100.0);
            }
        };

        channelSftp.put(inputStream, remoteFilePath, monitor);
    }

    /**
     * 下载文件
     */
    public boolean downloadFile(String remoteFilePath, String localFilePath) {
        return downloadFile(remoteFilePath, localFilePath, null);
    }

    /**
     * 下载文件（带进度回调）
     */
    public boolean downloadFile(String remoteFilePath, String localFilePath, ProgressCallback callback) {
        if (!ensureConnected()) {
            logger.error("SFTP连接失败，无法下载文件");
            return false;
        }

        if (!exists(remoteFilePath)) {
            logger.error("远程文件不存在: {}", remoteFilePath);
            return false;
        }

        // 确保本地目录存在
        File localFile = new File(localFilePath);
        File parentDir = localFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }

        try (FileOutputStream fos = new FileOutputStream(localFile)) {
            logger.info("开始下载文件: {} -> {}", remoteFilePath, localFilePath);

            if (callback != null) {
                // 获取文件大小
                SftpATTRS attrs = channelSftp.stat(remoteFilePath);
                long fileSize = attrs.getSize();
                downloadWithProgress(remoteFilePath, fos, fileSize, callback);
            } else {
                channelSftp.get(remoteFilePath, fos);
            }

            logger.info("文件下载成功: {} -> {}", remoteFilePath, localFilePath);
            return true;

        } catch (Exception e) {
            logger.error("文件下载失败: {} -> {}", remoteFilePath, localFilePath, e);
            // 删除可能不完整的文件
            if (localFile.exists()) {
                localFile.delete();
            }
            return false;
        }
    }

    /**
     * 下载文件到输出流
     */
    public boolean downloadToStream(String remoteFilePath, OutputStream outputStream) {
        return downloadToStream(remoteFilePath, outputStream, null);
    }

    /**
     * 下载文件到输出流（带进度回调）
     */
    public boolean downloadToStream(String remoteFilePath, OutputStream outputStream, ProgressCallback callback) {
        if (!ensureConnected()) {
            logger.error("SFTP连接失败，无法下载文件");
            return false;
        }

        if (!exists(remoteFilePath)) {
            logger.error("远程文件不存在: {}", remoteFilePath);
            return false;
        }

        try {
            logger.info("开始下载文件到流: {}", remoteFilePath);

            if (callback != null) {
                SftpATTRS attrs = channelSftp.stat(remoteFilePath);
                long fileSize = attrs.getSize();
                downloadWithProgress(remoteFilePath, outputStream, fileSize, callback);
            } else {
                channelSftp.get(remoteFilePath, outputStream);
            }

            logger.info("文件下载到流成功: {}", remoteFilePath);
            return true;

        } catch (Exception e) {
            logger.error("文件下载到流失败: {}", remoteFilePath, e);
            return false;
        }
    }

    /**
     * 带进度的下载实现
     */
    private void downloadWithProgress(String remoteFilePath, OutputStream outputStream, long totalSize, ProgressCallback callback) throws SftpException {
        SftpProgressMonitor monitor = new SftpProgressMonitor() {
            private long transferred = 0;

            @Override
            public void init(int op, String src, String dest, long max) {
                transferred = 0;
            }

            @Override
            public boolean count(long count) {
                transferred += count;
                double percentage = totalSize > 0 ? (double) transferred / totalSize * 100 : 0;
                callback.onProgress(transferred, totalSize, percentage);
                return true;
            }

            @Override
            public void end() {
                callback.onProgress(totalSize, totalSize, 100.0);
            }
        };

        channelSftp.get(remoteFilePath, outputStream, monitor);
    }

    /**
     * 删除文件
     */
    public boolean deleteFile(String remoteFilePath) {
        if (!ensureConnected()) {
            logger.error("SFTP连接失败，无法删除文件");
            return false;
        }

        try {
            channelSftp.rm(remoteFilePath);
            logger.info("文件删除成功: {}", remoteFilePath);
            return true;
        } catch (Exception e) {
            logger.error("文件删除失败: {}", remoteFilePath, e);
            return false;
        }
    }

    /**
     * 重命名文件
     */
    public boolean renameFile(String oldPath, String newPath) {
        if (!ensureConnected()) {
            logger.error("SFTP连接失败，无法重命名文件");
            return false;
        }

        try {
            channelSftp.rename(oldPath, newPath);
            logger.info("文件重命名成功: {} -> {}", oldPath, newPath);
            return true;
        } catch (Exception e) {
            logger.error("文件重命名失败: {} -> {}", oldPath, newPath, e);
            return false;
        }
    }

    /**
     * 获取文件信息
     */
    public FileInfo getFileInfo(String remoteFilePath) {
        if (!ensureConnected()) {
            logger.error("SFTP连接失败，无法获取文件信息");
            return null;
        }

        try {
            SftpATTRS attrs = channelSftp.stat(remoteFilePath);
            String fileName = getFileName(remoteFilePath);

            return new FileInfo(
                    fileName,
                    remoteFilePath,
                    attrs.getSize(),
                    attrs.isDir(),
                    attrs.getMTime() * 1000L,
                    attrs.getPermissionsString()
            );
        } catch (Exception e) {
            logger.error("获取文件信息失败: {}", remoteFilePath, e);
            return null;
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 获取路径的父目录
     */
    private String getParentPath(String path) {
        if (path == null || path.isEmpty()) {
            return null;
        }

        int lastSlash = path.lastIndexOf('/');
        if (lastSlash <= 0) {
            return null;
        }

        return path.substring(0, lastSlash);
    }

    /**
     * 获取路径的文件名
     */
    private String getFileName(String path) {
        if (path == null || path.isEmpty()) {
            return "";
        }

        int lastSlash = path.lastIndexOf('/');
        if (lastSlash < 0) {
            return path;
        }

        return path.substring(lastSlash + 1);
    }

    /**
     * 标准化路径
     */
    public static String normalizePath(String path) {
        if (path == null || path.isEmpty()) {
            return "/";
        }

        // 替换反斜杠为正斜杠
        path = path.replace('\\', '/');

        // 确保以正斜杠开头
        if (!path.startsWith("/")) {
            path = "/" + path;
        }

        // 移除重复的斜杠
        path = path.replaceAll("/+", "/");

        // 移除末尾的斜杠（除非是根目录）
        if (path.length() > 1 && path.endsWith("/")) {
            path = path.substring(0, path.length() - 1);
        }

        return path;
    }

    /**
     * 连接路径
     */
    public static String joinPath(String... paths) {
        if (paths == null || paths.length == 0) {
            return "/";
        }

        StringBuilder result = new StringBuilder();
        for (String path : paths) {
            if (path == null || path.isEmpty()) {
                continue;
            }

            path = path.replace('\\', '/');

            if (result.length() == 0) {
                result.append(path);
            } else {
                if (!result.toString().endsWith("/") && !path.startsWith("/")) {
                    result.append("/");
                }
                if (result.toString().endsWith("/") && path.startsWith("/")) {
                    result.append(path.substring(1));
                } else {
                    result.append(path);
                }
            }
        }

        return normalizePath(result.toString());
    }

    // ==================== 静态工具方法 ====================

    /**
     * 清理连接池
     */
    public static void clearConnectionPool() {
        logger.info("清理SFTP连接池，当前连接数: {}", connectionPool.size());

        for (SftpUtils sftp : connectionPool.values()) {
            try {
                sftp.close();
            } catch (Exception e) {
                logger.warn("关闭连接池中的连接时发生异常", e);
            }
        }

        connectionPool.clear();
        logger.info("SFTP连接池已清理");
    }

    /**
     * 获取连接池状态
     */
    public static Map<String, Object> getConnectionPoolStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("totalConnections", connectionPool.size());
        status.put("activeConnections", connectionPool.values().stream()
                .mapToInt(sftp -> sftp.isConnected() ? 1 : 0).sum());
        status.put("connectionCounter", connectionCounter.get());

        return status;
    }

    /**
     * 测试连接
     */
    public static boolean testConnection(SftpConfig config) {
        try (SftpUtils sftp = new SftpUtils(config)) {
            return sftp.connect();
        } catch (Exception e) {
            logger.error("测试SFTP连接失败", e);
            return false;
        }
    }
}
