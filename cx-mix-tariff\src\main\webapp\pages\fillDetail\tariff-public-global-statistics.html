<!DOCTYPE html>
<html>

<head>
  <title>公示资费整体统计</title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
  <!-- 基础的 css js 资源 -->
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.4">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.3">
  <link rel="stylesheet" href="./common.css?v=20241127">
  <!-- 表头样式覆盖 -->
  <link rel="stylesheet" href="/cx-mix-report/static/css/tableModify.css?v=1.0.0">
  <link rel="stylesheet" href="/cx-mix-tariff/static/css/searchForm.css">
  <style>
    .el-form-item__content {
      display: flex;
      align-items: center;
      height: 100%;
    }

    #tariff-public-global-statistics .search-form.grid-4 {
      grid-template-columns: 2fr 2fr 2fr 250px !important;
      gap: 16px 4px !important;
    }
  </style>
</head>

<body class="yq-page-full vue-box">
  <div id="tariff-public-global-statistics" class="flex yq-table-page" v-loading="loading" element-loading-text="加载中..."
    v-cloak v-auth:[permissions]="'cx-xty-tariff-province-whole-export'">
    <div class="yq-card">
      <div class="card-header">
        <div class="head-title">公示资费整体统计</div>
        <div class="yq-table-control">
          <el-button type="primary" plain size="small"
            @click="handleExportFront($refs.excelTable.$el, {title: '公示资费整体统计'})"
            v-if="permissions['cx-xty-tariff-province-whole-export']">
            <i class="el-icon-download"></i>导出
          </el-button>
        </div>
      </div>
      <div class="card-content">
        <el-form class="search-form grid-4" :model="searchForm" :rules="rules" ref="searchForm" size="small"
          label-width="75px">
          <el-form-item label="版本" prop="version">
            <el-select v-model="searchForm.version" placeholder="请选择">
              <el-option v-for="(label, value) in versionOptions" :key="value" :label="label.VERSION_NO"
                :value="label.VERSION_NO"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="省份" prop="provinceCode">
            <el-select v-model="searchForm.provinceCode" @change="handleChange($event, 'provinceCode')"
              placeholder="请选择" filterable clearable multiple collapse-tags>
              <el-option v-for="item in provinces" :key="item.CODE" :label="item.NAME" :value="item.CODE"></el-option>
            </el-select>
            <el-form-item label="全选" label-width="50px">
              <el-checkbox v-model="provinceCodeCheckAll"
                @change="handleChangeCheckAll($event, 'provinceCode')"></el-checkbox>
            </el-form-item>
          </el-form-item>
          <el-form-item label="运营商" prop="entType">
            <el-select v-model="searchForm.entType" @change="handleChange($event, 'entType')" placeholder="请选择"
              :disabled="roleInfo.userAccType === '3' || roleInfo.userAccType === '5'" filterable clearable multiple
              collapse-tags>
              <el-option v-for="(label, value) in companys" :key="value" :label="label" :value="value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label-width="0px" style="justify-self: flex-end; grid-column: 4;">
            <el-button type="primary" icon="el-icon-search" @click="getList(1)">搜索</el-button>
            <el-button type="primary" plain icon="el-icon-refresh" @click="handleReset"
              style="margin-left: 4px;">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="yq-table">
          <el-table ref="table" :data="tableData" height="100%" stripe border fit style="width: 100%"
            :span-method="spanMethod" @sort-change="handleSortChange">
            <el-table-column label="省份" prop="provinceName" min-width="100"></el-table-column>
            <el-table-column label="运营商" prop="entName" min-width="120">
              <template slot-scope="scope">
                {{ getReporterName(scope.row) }}
              </template>
            </el-table-column>
            <el-table-column label="资费总数" prop="tariffCount" min-width="150">
              <template slot-scope="scope">
                <template v-if="scope.row.provinceCode !== '999999'">
                  <el-link @click="openTab(scope.row)" type="primary"
                    :underline="false">{{scope.row.tariffCount}}</el-link>
                  <i class="el-icon-caret-bottom" @click="handleToggle(scope, 'expandTariffCount')"
                    style="cursor: pointer;"></i>
                  <template v-if="scope.row.expandTariffCount">
                    <el-link @click="openTab(scope.row, {classicTypeOne: '1'})" type="primary"
                      :underline="false">公众：{{scope.row.publicTariffCount}}</el-link>
                    <el-link @click="openTab(scope.row, {classicTypeOne: '2'})" type="primary"
                      :underline="false">政企：{{scope.row.enterpriseTariffCount}}</el-link>
                    <el-link @click="openTab(scope.row, {reported: '0'})" type="primary"
                      :underline="false">未报送：{{scope.row.notReportedCount}}</el-link>
                  </template>
                </template>
                <template v-else>
                  <span style="margin-right: 16px;">{{scope.row.tariffCount}}</span>
                  <i class="el-icon-caret-bottom" @click="handleToggle(scope, 'expandTariffCount')"
                    style="cursor: pointer;"></i>
                  <template v-if="scope.row.expandTariffCount">
                    <p>公众：{{scope.row.publicTariffCount}}</p>
                    <p>政企：{{scope.row.enterpriseTariffCount}}</p>
                    <p>未报送：{{scope.row.notReportedCount}}</p>
                  </template>
                </template>
              </template>
            </el-table-column>
            <el-table-column label="公众资费" prop="publicTariffCount" min-width="150">
              <template slot-scope="scope">
                <template v-if="scope.row.provinceCode !== '999999'">
                  <el-link @click="openTab(scope.row, { classicTypeOne: '1' })" type="primary"
                    :underline="false">{{scope.row.publicTariffCount}}</el-link>
                  <i class="el-icon-caret-bottom" @click="handleToggle(scope, 'expandPublicTariffCount')"
                    style="cursor: pointer;"></i>
                  <template v-if="scope.row.expandPublicTariffCount">
                    <el-link @click="openTab(scope.row, {classicTypeOne: '1', tariffState: '1'})" type="primary"
                      :underline="false">在售：{{scope.row.publicForSaleCount}}</el-link>
                    <el-link @click="openTab(scope.row, {classicTypeOne: '1', tariffState: '3'})" type="primary"
                      :underline="false">下架：{{scope.row.publicOfflineCount}}</el-link>
                    <el-link @click="openTab(scope.row, {classicTypeOne: '1', tariffState: '4'})" type="primary"
                      :underline="false">未售：{{scope.row.publicNotForSaleCount}}</el-link>
                  </template>
                </template>
                <template v-else>
                  <span style="margin-right: 16px;">
                    {{scope.row.publicTariffCount}}</span>
                  <i class="el-icon-caret-bottom" @click="handleToggle(scope, 'expandPublicTariffCount')"
                    style="cursor: pointer;"></i>
                  <template v-if="scope.row.expandPublicTariffCount">
                    <p>在售：{{scope.row.publicForSaleCount}}</p>
                    <p>下架：{{scope.row.publicOfflineCount}}</p>
                    <p>未售：{{scope.row.publicNotForSaleCount}}</p>
                  </template>
                </template>
              </template>
            </el-table-column>
            <el-table-column label="公众在售" prop="publicForSaleCount" min-width="120">
              <template slot-scope="scope">
                <el-link v-if="scope.row.provinceCode !== '999999'"
                  @click="openTab(scope.row, {classicTypeOne: '1', tariffState: '1'})" type="primary"
                  :underline="false">{{scope.row.publicForSaleCount}}</el-link>
                <span v-else>{{scope.row.publicForSaleCount}}</span>
              </template>
            </el-table-column>
            <el-table-column label="公众在售国际即港澳台" prop="publicForSaleIntlCount" min-width="150">
              <template slot-scope="scope">
                <el-link v-if="scope.row.provinceCode !== '999999'"
                  @click="openTab(scope.row, {classicTypeOne: '1', tariffState: '1', classicTypeTwo: '4,6'})"
                  type="primary" :underline="false">{{scope.row.publicForSaleIntlCount}}</el-link>
                <span v-else>{{scope.row.publicForSaleIntlCount}}</span>
              </template>
            </el-table-column>
            <el-table-column label="公众在售国内数量（不含港澳台）" prop="pfsdCount" min-width="150">
              <template slot-scope="scope">
                <template v-if="scope.row.provinceCode !== '999999'">
                  <el-link
                    @click="openTab(scope.row, {classicTypeOne: '1', tariffState: '1', classicTypeTwo: '1,2,3,5,7'})"
                    type="primary" :underline="false">{{scope.row.pfsdCount}}</el-link>
                  <i class="el-icon-caret-bottom" @click="handleToggle(scope, 'expandPfsdCount')"
                    style="cursor: pointer;"></i>
                  <template v-if="scope.row.expandPfsdCount">
                    <el-link
                      @click="openTab(scope.row, {classicTypeOne: '1', tariffState: '1', classicTypeTwo: '1,2,3,5,7', isTelecom: '1'})"
                      type="primary" :underline="false">通信类：{{scope.row.pfsdTelecomCount}}</el-link>
                    <el-link
                      @click="openTab(scope.row, {classicTypeOne: '1', tariffState: '1', classicTypeTwo: '1,2,3,5,7', isTelecom: '2'})"
                      type="primary" :underline="false">非通信类：{{scope.row.pfsdNonTelecomCount}}</el-link>
                  </template>
                </template>
                <template v-else>
                  <span style="margin-right: 16px;">
                    {{scope.row.pfsdCount}}</span>
                  <i class="el-icon-caret-bottom" @click="handleToggle(scope, 'expandPfsdCount')"
                    style="cursor: pointer;"></i>
                  <template v-if="scope.row.expandPfsdCount">
                    <p>通信类：{{scope.row.pfsdTelecomCount}}</p>
                    <p>非通信类：{{scope.row.pfsdNonTelecomCount}}</p>
                  </template>
                </template>
              </template>
            </el-table-column>
            <el-table-column label="公众在售国内（不含港澳台）全国" prop="pfsdNationalCount" min-width="150">
              <template slot-scope="scope">
                <el-link v-if="scope.row.provinceCode !== '999999'"
                  @click="openTab(scope.row, {classicTypeOne: '1', tariffState: '1', classicTypeTwo: '1,2,3,5,7', label_1: '34', label_2: scope.row.entCode})"
                  type="primary" :underline="false">{{scope.row.pfsdNationalCount}}</el-link>
                <span v-else>{{scope.row.pfsdNationalCount}}</span>
              </template>
            </el-table-column>
            <el-table-column label="公众在售国内（不含港澳台）省内" prop="pfsdProvincialCount" min-width="150">
              <template slot-scope="scope">
                <el-link v-if="scope.row.provinceCode !== '999999'"
                  @click="openTab(scope.row, {classicTypeOne: '1', tariffState: '1', classicTypeTwo: '1,2,3,5,7', label_1_id: scope.row.provinceCode, label_2: scope.row.entCode})"
                  type="primary" :underline="false">{{scope.row.pfsdProvincialCount}}</el-link>
                <span v-else>{{scope.row.pfsdProvincialCount}}</span>
              </template>
            </el-table-column>
            <el-table-column label="公众在售国内（不含港澳台）通信类" prop="pfsdTelecomCount" min-width="150">
              <template slot-scope="scope">
                <el-link v-if="scope.row.provinceCode !== '999999'"
                  @click="openTab(scope.row, {classicTypeOne: '1', tariffState: '1', classicTypeTwo: '1,2,3,5,7', isTelecom: '1'})"
                  type="primary" :underline="false">{{scope.row.pfsdTelecomCount}}</el-link>
                <span v-else>{{scope.row.pfsdTelecomCount}}</span>
              </template>
            </el-table-column>
            <el-table-column label="公众在售国内（不含港澳台）通信类" align="center">
              <el-table-column label="全国" prop="pfsdTelecomNationalCount" min-width="100">
                <template slot-scope="scope">
                  <el-link v-if="scope.row.provinceCode !== '999999'"
                    @click="openTab(scope.row, {classicTypeOne: '1', tariffState: '1', classicTypeTwo: '1,2,3,5,7', isTelecom: '1', label_1: '34', label_2: scope.row.entCode})"
                    type="primary" :underline="false">{{scope.row.pfsdTelecomNationalCount}}</el-link>
                  <span v-else>{{scope.row.pfsdTelecomNationalCount}}</span>
                </template>
              </el-table-column>
              <el-table-column label="省内" prop="pfsdTelecomProvincialCount" min-width="100">
                <template slot-scope="scope">
                  <el-link v-if="scope.row.provinceCode !== '999999'"
                    @click="openTab(scope.row, {classicTypeOne: '1', tariffState: '1', classicTypeTwo: '1,2,3,5,7', isTelecom: '1', label_1_id: scope.row.provinceCode, label_2: scope.row.entCode})"
                    type="primary" :underline="false">{{scope.row.pfsdTelecomProvincialCount}}</el-link>
                  <span v-else>{{scope.row.pfsdTelecomProvincialCount}}</span>
                </template>
              </el-table-column>
              <el-table-column label="套餐" prop="pfsdTelecomPackageCount" min-width="100">
                <template slot-scope="scope">
                  <el-link v-if="scope.row.provinceCode !== '999999'"
                    @click="openTab(scope.row, {classicTypeOne: '1', tariffState: '1', classicTypeTwo: '1', isTelecom: '1'})"
                    type="primary" :underline="false">{{scope.row.pfsdTelecomPackageCount}}</el-link>
                  <span v-else>{{scope.row.pfsdTelecomPackageCount}}</span>
                </template>
              </el-table-column>
              <el-table-column label="加装包" prop="pfsdTelecomAddonCount" min-width="100">
                <template slot-scope="scope">
                  <el-link v-if="scope.row.provinceCode !== '999999'"
                    @click="openTab(scope.row, {classicTypeOne: '1', tariffState: '1', classicTypeTwo: '2', isTelecom: '1'})"
                    type="primary" :underline="false">{{scope.row.pfsdTelecomAddonCount}}</el-link>
                  <span v-else>{{scope.row.pfsdTelecomAddonCount}}</span>
                </template>
              </el-table-column>
              <el-table-column label="营销活动" prop="pfsdTelecomMarketingCount" min-width="120">
                <template slot-scope="scope">
                  <el-link v-if="scope.row.provinceCode !== '999999'"
                    @click="openTab(scope.row, {classicTypeOne: '1', tariffState: '1', classicTypeTwo: '3', isTelecom: '1'})"
                    type="primary" :underline="false">{{scope.row.pfsdTelecomMarketingCount}}</el-link>
                  <span v-else>{{scope.row.pfsdTelecomMarketingCount}}</span>
                </template>
              </el-table-column>
              <el-table-column label="国内（不含港澳台）标准资费" prop="pfsdTelecomStandardCount" min-width="150">
                <template slot-scope="scope">
                  <el-link v-if="scope.row.provinceCode !== '999999'"
                    @click="openTab(scope.row, {classicTypeOne: '1', tariffState: '1', classicTypeTwo: '5', isTelecom: '1'})"
                    type="primary" :underline="false">{{scope.row.pfsdTelecomStandardCount}}</el-link>
                  <span v-else>{{scope.row.pfsdTelecomStandardCount}}</span>
                </template>
              </el-table-column>
            </el-table-column>
            <el-empty slot="empty" description="暂无信息"></el-empty>
          </el-table>
          <!-- 用于导出 -->
          <el-table v-show="false" ref="excelTable" :data="tableData" height="100%" stripe border fit
            style="width: 100%" :span-method="spanMethod" @sort-change="handleSortChange">
            <el-table-column label="省份" prop="provinceName" min-width="100"></el-table-column>
            <el-table-column label="运营商" prop="entName" min-width="120">
              <template slot-scope="scope">
                {{ getReporterName(scope.row) }}
              </template>
            </el-table-column>
            <el-table-column label="资费总数" prop="tariffCount" min-width="120">
            </el-table-column>
            <el-table-column label="政企资费" prop="enterpriseTariffCount" min-width="120">
            </el-table-column>
            <el-table-column label="未报送资费" prop="notReportedCount" min-width="120">
            </el-table-column>
            <el-table-column label="公众资费" prop="publicTariffCount" min-width="120">
            </el-table-column>
            <el-table-column label="公众在售" prop="publicForSaleCount" min-width="120">
            </el-table-column>
            <el-table-column label="公众下架" prop="publicOfflineCount" min-width="120">
            </el-table-column>
            <el-table-column label="公众未售" prop="publicNotForSaleCount" min-width="120">
            </el-table-column>
            <el-table-column label="公众在售国际即港澳台" prop="publicForSaleIntlCount" min-width="150"></el-table-column>
            <el-table-column label="公众在售国内（不含港澳台）" prop="pfsdCount" min-width="150"></el-table-column>
            <el-table-column label="公众在售国内（不含港澳台）全国" prop="pfsdNationalCount" min-width="150"></el-table-column>
            <el-table-column label="公众在售国内（不含港澳台）省内" prop="pfsdProvincialCount" min-width="150"></el-table-column>
            <el-table-column label="公众在售国内（不含港澳台）非通信类" prop="pfsdNonTelecomCount" min-width="150"></el-table-column>
            <el-table-column label="公众在售国内（不含港澳台）通信类" prop="pfsdTelecomCount" min-width="150"></el-table-column>
            <el-table-column label="公众在售国内（不含港澳台）通信类" align="center">
              <el-table-column label="全国" prop="pfsdTelecomNationalCount" min-width="100"></el-table-column>
              <el-table-column label="省内" prop="pfsdTelecomProvincialCount" min-width="100"></el-table-column>
              <el-table-column label="套餐" prop="pfsdTelecomPackageCount" min-width="100"></el-table-column>
              <el-table-column label="加装包" prop="pfsdTelecomAddonCount" min-width="100"></el-table-column>
              <el-table-column label="营销活动" prop="pfsdTelecomMarketingCount" min-width="120"></el-table-column>
              <el-table-column label="国内（不含港澳台）标准资费" prop="pfsdTelecomStandardCount" min-width="150"></el-table-column>
            </el-table-column>
            <el-empty slot="empty" description="暂无信息"></el-empty>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</body>
<script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
<script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
<script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
<script src="/cc-base/static/js/my_i18n.js?v=202111"></script>
<script src="/cc-base/static/js/i18n.js?v=1"></script>
<script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.1"></script>
<script src="/cx-mix-tariff/pages/fillDetail/mixins/statisticMixins.js?v=20241224"></script>
<script type="text/javascript" src="/cx-mix-tariff/static/js/time.js"></script>
<script>
  var appPage = new Vue({
    el: '#tariff-public-global-statistics',
    mixins: [statisticMixins],
    data: function () {
      return {
        initialFetch: false,
        searchForm: {
          version: '',
          provinceCode: [],
          entType: [],
        },
        rules: {
          provinceCode: [],
          entType: [],
        },
        provinceCodeCheckAll: false,
        versionOptions: [],
        provinceAndGroup: [],
      }
    },
    created() {
      this.getProvinceAndGroup()
      this.getVersions()
        .then((res) => {
          this.getList()
        })
    },
    methods: {
      handleReset() {
        let version = this.searchForm.version
        this.$refs['searchForm'].resetFields();
        this.searchForm.version = version
        this.provinceCodeCheckAll = true
        this.getList()
      },
      getList() {
        this.$refs.searchForm?.validate?.(valid => {
          if (!valid) {
            return false
          }

          this.loading = true
          const payload = {
            version: this.searchForm.version,
            provinceCode: this.searchForm.provinceCode.join(),
            entCode: this.searchForm.entType.join(),
          }

          yq.remoteCall('/cx-mix-tariff/webcall?action=tariffPublicLibStatDao.publicTariffWholeStat', payload)
            .then(res => {
              if (res.data) {
                this.tableData = (res.data.statistics || [])
                  .sort((a, b) => a.provinceCode - b.provinceCode)
                  .sort((a, b) => a.provinceCode === b.provinceCode ? (a.entCode - b.entCode) : 0)
              }
            })
            .finally(() => this.loading = false)
        })
      },
      getReporterName(row) {
        if (row.provinceCode === '999999') {
          return row.entName
        } else {
          return row.provinceName + row.entName
        }
      },
      // 获取字典数据
      getVersions() {
        return yq.remoteCall(
          "/cx-mix-tariff/webcall?action=crawlerTariffData.getVersionList"
        ).then((res) => {
          this.versionOptions = res.data || [];
          this.searchForm.version = this.versionOptions[0] && this.versionOptions[0].VERSION_NO
        });
      },
      getCommonDict() {
        let data = {
          "controls": [
            "common.getDict(XTY_TARIFF_ENT)"
          ],
          "params": {}
        }
        return Promise.all([
          yq.remoteCall('/cx-mix-report/webcall', data).then(res => {
            this.companys = res["common.getDict(XTY_TARIFF_ENT)"].data || {}
            // this.companys[999999] = '全行业'
          }),
          yq.remoteCall('/cx-mix-report/webcall?action=dutyReport.provinceDict').then(res => {
            this.provinces = res.data || []
            let idx = this.provinces.findIndex(i => i.CODE == 0)
            this.provinces.splice(idx, 1)
          }),
        ])
      },
      // 获取报送主体的省份
      getProvinceAndGroup() {
        return yq
          .remoteCall(
            "/cx-mix-tariff/webcall?action=common.queryTariffProvinceTree"
          )
          .then((res) => {
            if (res.state == 1) {
              this.provinceAndGroup = res.data || [];
            }
          });
      },
      handleToggle(scope, field) {
        if (!this.tableData[scope.$index]) {
          return
        }
        let flag = !!this.tableData[scope.$index][field]
        this.tableData = this.tableData.map((i, idx) => scope.$index == idx ? { ...i, [field]: !flag } : i)
      },
      openTab(row, payload) {
        const data = {
          sourceChannel: 'tariff-public-global-statistics',
          versionNos: this.searchForm.version,
          provinceName: row.provinceCode,
          entName: row.entCode === '999999' ? this.searchForm.entType.join() : row.entCode,
          ...payload,
        }

        if (data.label_1_id) {
          let find = this.findProvince(data.label_1_id, 'provinceCode')
          find && (data.label_1 = find.id)
          delete data.label_1_id
        }

        if (data.label_2 === '999999') {
          data.label_2 = ''
        }

        top.popup.openTab({
          url: '/cx-mix-tariff/pages/undisclosed-tariff-statistics/check.html',
          title: '公示字段检查（全行业）',
          data,
          id: 'cx-xty-public-tariff-list-crawl-check',
        });
      },
    }
  });
</script>

</html>