package com.yunqu.society.servlet;

import java.util.Arrays;
import java.util.Objects;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;

import com.yunqu.society.util.CaptchaGenerator;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.crypt.MD5Util;
import org.easitline.common.utils.kit.RandomKit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.society.base.AppBaseServlet;
import com.yunqu.society.base.CommonLogger;
import com.yunqu.society.base.Constants;
import com.yunqu.society.model.FieldValidExcutor;
import com.yunqu.society.model.BoxAppeal;
import com.yunqu.society.service.AppealService;
import com.yunqu.society.util.RedisLockUtil;

/**
 * 申诉管理Servlet
 * 实现申诉信息的CRUD操作
 * 包含以下功能:
 * 1. 添加申诉记录
 * 2. 更新申诉信息
 * 3. 查询申诉列表
 * 4. 删除申诉记录
 */
@WebServlet("/service/appeal")
public class AppealServlet extends AppBaseServlet {

    private static Logger logger = LoggerFactory.getLogger(CommonLogger.getLogger("appeal").getName());

    private static Logger infoLogger = LoggerFactory.getLogger(CommonLogger.getLogger("info").getName());


    private static final long serialVersionUID = 1L;

    private String SUBMIT_LIMIT_KEY = "appeal:submit:limit:";

    private static AppealService service = new AppealService();

    /**
     * 添加申诉记录
     * 实现功能:
     * - 创建申诉记录
     * - 设置记录字段值
     * - 生成唯一ID
     * - 保存记录并处理结果
     */
    public JSONObject actionForAdd() {
        String id = RandomKit.uniqueStr();
        JSONObject result = new JSONObject();
        JSONObject param = getJSONObject();
        infoLogger.info("添加申诉记录【{}】:{}" ,id, param.toJSONString());
        try {
            JSONObject appealInfo = param.getJSONObject("appeal");
            Boolean continueSubmit = param.getBooleanValue("continueSubmit");
            if (Objects.isNull(appealInfo)) {
                return EasyResult.fail("申诉信息不能为空");
            }
            String md5 = MD5Util.getHexMD5(appealInfo.toJSONString());
            return RedisLockUtil.executeLocked(md5, 5, 1 * 1000, ()-> {
                // 重复提交校验
                if (service.isRepeatSubmit(md5,result)) {
                    return result;
                }
                BoxAppeal appeal = JSONObject.parseObject(appealInfo.toJSONString(), BoxAppeal.class);
                int submit = Objects.isNull(CacheUtil.get( getKeyByYMD(SUBMIT_LIMIT_KEY)+ appeal.getPhone())) ? 0 : CacheUtil.get(getKeyByYMD(SUBMIT_LIMIT_KEY) + appeal.getPhone());
                if (submit >= 5) {
                    return EasyResult.fail("您当日提交次数过于频繁，请稍后再试!");
                }
                if (!service.checkDept(appeal.getEntDeptCode())) {
                    return EasyResult.fail("被申诉主投企业有误，请刷新页面后重试！");
                }
                if (Constants.IS_APPEAL_YES.equals(appeal.getIsAppeal())) {
                    // 申诉信息入参校验
                    FieldValidExcutor.valid(appeal);
                    String verifyFlag = "";
                    String msg = "";
                    int appealPhoneVerify = 1;
                    if (isValidPhoneNumber(appeal.getAppealPhone())) {
                        appealPhoneVerify = verifyThreeElements(appeal.getCardType(),appeal.getIdCard(),appeal.getAppealName(),appeal.getAppealPhone());
                        if ( appealPhoneVerify != 1) {
                            verifyFlag = "1";
                            if (appealPhoneVerify == 2) {
                                verifyFlag = "4";
                                msg = "<span style=\"font-weight: bold; color: red;\">申诉问题涉及号码</span>为空号，请填写正确的信息后再进行申诉。";
                            } else {
                                msg = "姓名、证件号码与<span style=\"font-weight: bold; color: red;\">申诉问题涉及号码入网实名登记信息</span>不一致，请填写正确的信息后再进行申诉。";
                            }
                            logger.error("【申诉问题涉及号码】三要素校验校验失败：" + JSONObject.toJSONString(result));
                        }

                    }

                    if (!appeal.getAppealPhone().equals(appeal.getPhone())) {
                        int contactPhoneVerify = verifyThreeElements(StringUtils.isBlank(appeal.getConcactCardType()) ?
                                appeal.getCardType() : appeal.getConcactCardType(), StringUtils.isBlank(appeal.getConcactIdCard()) ?
                                appeal.getIdCard() : appeal.getConcactIdCard(), StringUtils.isBlank(appeal.getConcactName()) ?
                                appeal.getAppealName() : appeal.getConcactName(),appeal.getPhone());
                        if (contactPhoneVerify != 1) {
                            verifyFlag = StringUtils.isBlank(verifyFlag) ? "2" : "3";
                            if (contactPhoneVerify == 2) {
                                if (appealPhoneVerify == 2) {
                                    msg = "<span style=\"font-weight: bold; color: red;\">申诉问题涉及号码及联系电话</span>为空号，请填写正确的信息后再进行申诉。";
                                } else if (appealPhoneVerify == 0) {
                                    msg = "<span style=\"font-weight: bold; color: red;\">联系电话</span>为空号，申诉人的姓名、证件号码与<span style=\"font-weight: bold; color: red;\">申诉问题涉及号码入网实名登记信息</span>均不一致。请填写正确的信息后再进行申诉。";
                                } else {
                                    msg = "<span style=\"font-weight: bold; color: red;\">联系电话</span>为空号，请填写正确的信息后再进行申诉。";

                                }
                            } else {
                                if (appealPhoneVerify == 2) {
                                    msg = "<span style=\"font-weight: bold; color: red;\">申诉问题涉及号码</span>为空号，申诉人的姓名、证件号码与<span style=\"font-weight: bold; color: red;\">联系电话入网实名登记信息</span>均不一致。请填写正确的信息后再进行申诉。";

                                } else if (appealPhoneVerify == 0) {
                                    msg = "申诉人的姓名、证件号码与<span style=\"font-weight: bold; color: red;\">申诉问题涉及号码、联系电话入网实名登记信息</span>均不一致，请填写正确的信息后再进行申诉。";
                                } else {
                                    msg = "姓名、证件号码与<span style=\"font-weight: bold; color: red;\">联系电话入网实名登记信息</span>不一致，请确认联系电话是否登记在申诉人名下。";
                                }
                            }
                            logger.error("【联系号码】三要素校验校验失败：" + JSONObject.toJSONString(result));
                        }

                    }
                    if (StringUtils.isNotBlank(verifyFlag) && !("4".equals(verifyFlag) && continueSubmit)) {
                        EasyResult fail = EasyResult.fail(msg);
                        fail.put("verifyFlag", verifyFlag);
                        return fail;
                    }
                }
                appeal.setId(id);
                appeal.setCreateTime(DateUtil.getCurrentDateStr());
                appeal.setAppealTime(DateUtil.getCurrentDateStr());
                appeal.setServiceStatus(Constants.MEDIATION_STATUS_00);
                appeal.setOrgCode("000000");
                appeal.setOrgName("部中心");
                appeal.setIpAddress(getClientIP(this.getRequest()));
                appeal.setExistAttachment(existAttachment(appeal.getAppealAttachment()) ? Constants.EXIST_ATTACHMENT_YES : Constants.EXIST_ATTACHMENT_NO);

                // 保存申诉信息
                service.saveAppeal(appeal,md5);

                CacheUtil.put(getKeyByYMD(SUBMIT_LIMIT_KEY) + appeal.getPhone(), submit + 1,24*60*60);
                return EasyResult.ok();
            });
        } catch (Exception e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail("提交失败:" + e.getMessage());
        }
    }



    private boolean existAttachment (String attachmentId)  {
        if (StringUtils.isBlank(attachmentId)) {
            return false;
        }
        return service.existAttachment(attachmentId);
    }

    /*
     * 修改申诉信息
     * 实现功能:
     * - 获取申诉ID
     * - 根据ID查询申诉信息
     * - 更新申诉信息
     */
    public JSONObject actionForCancel() {
        String id = this.getJsonPara("id");
        if (StringUtils.isBlank(id)) {
            return EasyResult.fail("申诉ID不能为空");
        }
        try {
            return service.cancelAppeal(id);
        } catch (Exception e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail("取消失败:" + e.getMessage());
        }
    }



    /**
     * 三要素校验
     * @param idType
     * @param idNumber
     * @param name
     * @param phone
     * @return
     */
    private int verifyThreeElements (String idType,String idNumber,String name,String phone) {
        JSONObject result = service.threeFactorVerification( idType, idNumber, name, phone);
        if (result != null && ("0".equals(result.getString("result")) || "497".equals(result.getString("code")))) {
            if ("497".equals(result.getString("code"))) {
                return 2;
            }
            return 0;
        }
        return 1;
    }


    /**
     * 生成验证码
     * 实现功能:
     * - 生成4位随机字母数字验证码图片
     * - 将验证码存入Redis
     * - 返回Base64编码的验证码图片
     */
    public EasyResult actionForGenerateCaptcha() {
        try {
            HttpServletRequest request = this.getRequest();

            JSONObject param = this.getJSONObject();
            String phone = param.getString("phone");

            String random = RandomKit.randomMD5Str();
            String ip = getClientOneIP(request);
            logger.info("ip:{}获取图形验证码", ip);

            // 生成验证码图片
            CaptchaGenerator.CaptchaResult captchaResult = CaptchaGenerator.generateCaptcha();
            String captcha = captchaResult.getCode();
            String imageBase64 = captchaResult.getImageBase64();

            // 生成唯一key
            String captchaKey = "appeal:captcha:" + ip + ":" + random;
            logger.info("图形验证码key:{}", captchaKey);
            // 存入Redis,有效期10分钟
            CacheUtil.put(captchaKey, captcha, 10 * 60);

            String phoneRandomKey = "appeal:phone:random:" + ip + ":" + random;
            CacheUtil.put(phoneRandomKey, phone, 10 * 60);

            logger.info("生成验证码:{}", captcha);
            EasyResult ok = EasyResult.ok(imageBase64);

            ok.put("random", random);
            return ok;
        } catch (Exception e) {
            logger.error("生成验证码失败:", e);
            return EasyResult.fail("生成验证码失败:" + e.getMessage());
        }
    }

    /**
     * 校验验证码
     * 实现功能:
     * - 校验用户输入的验证码
     * - 验证码10分钟内有效
     * - 校验成功后清除Redis中的验证码
     */
    public EasyResult actionForVerifyCaptcha() {
        HttpServletRequest request = this.getRequest();
        try {
            JSONObject param = this.getJSONObject();
            String inputCaptcha = param.getString("captcha");
            String random = param.getString("random");
            if (StringUtils.isBlank(inputCaptcha)) {
                return EasyResult.fail("图形验证码不能为空");
            }
            String captchaKey = "appeal:captcha:" + getClientOneIP(request) + ":" + random;
            logger.info("图形验证码key:{}", captchaKey);

            String captcha = CacheUtil.get(captchaKey);

            if (StringUtils.isBlank(captcha)) {
                return EasyResult.fail("图形验证码已失效,请重新获取");
            }

            // 验证码比对忽略大小写
            if (!captcha.equalsIgnoreCase(inputCaptcha)) {
                return EasyResult.fail("图形验证码错误");
            }

            // 验证通过后清除Redis中的验证码
            CacheUtil.delete(captchaKey);

            return EasyResult.ok();
        } catch (Exception e) {
            logger.error("校验验证码失败:", e);
            return EasyResult.fail("图形验证码校验失败:" + e.getMessage());
        }
    }

    /*
     * 归属地校验
     * 实现功能:
     * - 校验用户输入的手机号
     * - 根据手机号查询归属地信息
     * - 返回归属地信息
     */
    public JSONObject actionForPlaceVerification() {
        try {
            JSONObject jsonObject = this.getJSONObject();
            String phone = jsonObject.getString("phone");// 申诉手机号
            if (StringUtils.isBlank(phone)) {
                return EasyResult.fail("申诉手机号不能为空");
            }
            return EasyResult.ok(service.placeVerification(phone));
        } catch (Exception e) {
            logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
            return EasyResult.fail();
        }

    }

    /**
     * 号码归属信息查询接口V2
     * 新版接口，按照码号服务平台接口规范实现
     * 实现功能:
     * - 校验用户输入的手机号码格式
     * - 根据手机号查询归属地信息
     * - 返回详细的归属地信息，包括运营商、省份、城市等
     * - 提供更完善的错误处理和日志记录
     */
    public JSONObject actionForPlaceVerificationV2() {
        try {
            JSONObject jsonObject = this.getJSONObject();
            BoxAppeal appeal = JSONObject.parseObject(jsonObject.getString("appeal"), BoxAppeal.class);

            String phone = appeal.getAppealPhone();// 查询手机号
            String entType = appeal.getEntType();

            int submit = Objects.isNull(CacheUtil.get(getKeyByYMD(SUBMIT_LIMIT_KEY) + appeal.getPhone())) ? 0 : CacheUtil.get(getKeyByYMD(SUBMIT_LIMIT_KEY) + appeal.getPhone());
            if (submit > 5) {
                return EasyResult.error(500,"您当日提交次数过于频繁，请稍后再试!");
            }
            // 参数校验
            if (StringUtils.isBlank(phone)) {
                return EasyResult.error(500,"申诉问题涉及号码不能为空");
            }

            // 手机号格式校验
            if (!isValidPhoneNumber(phone)) {
                logger.info("号码归属信息查询V2 - 手机号格式校验失败, 手机号: {}", phone);
                return EasyResult.ok("非手机号码格式不校验");
            }

            if( Arrays.asList("99010501","990104").indexOf(entType) == -1) {
                logger.info("号码归属信息查询V2 - 企业类型校验失败, 企业类型: {}", entType);
                return EasyResult.ok("非基础运营商及移动转售商不校验");
            }

            logger.info("号码归属信息查询V2 - 查询手机号: {}", phone);

            // 调用归属地查询服务
            JSONObject result = service.queryPhoneLocationByStandard(phone);

            if (Objects.nonNull(result) && "200".equals(result.getString("code"))) {
                String errType = "";
                String deptName = appeal.getEntDeptName().replaceAll("（", "(").replaceAll("）", ")");
                if (!deptName.equals(result.getString("carrierName"))) {
                    errType = "1";
                }
                if ((!appeal.getProvinceName().contains(result.getString("province"))
                        && !result.getString("province").contains(appeal.getProvinceName()))
                        || (!appeal.getCityName().contains(result.getString("city"))
                        && !result.getString("city").contains(appeal.getCityName()))) {
                    errType = "1".equals(errType) ? "3" : "2";
                }
                if (StringUtils.isNotBlank(errType)) {
                    EasyResult fail = EasyResult.fail("1".equals(errType) ? "您填写的<span style=\"font-weight: bold; color: red;\">申诉问题涉及号码不属于被申诉企业</span>，是否继续提交？"
                            : ("2".equals(errType) ? "您填写的<span style=\"font-weight: bold; color: red;\">申诉问题涉及号码归属地与被申诉企业所属地区不一致</span>，是否继续提交？"
                            : "您填写的<span style=\"font-weight: bold; color: red;\">申诉问题涉及号码的归属企业与被申诉企业名称及所属地区不一致</span>，是否继续提交？"));
                    fail.put("errType", errType);
                    return fail;
                }
            }

            logger.info("号码归属信息查询V2 - 查询成功, 手机号: {}, 结果: {}", phone, JSONObject.toJSONString(result));

            return EasyResult.ok();
        } catch (Exception e) {
            logger.error("号码归属信息查询V2异常 - error: " + e.getMessage(), e);
            return EasyResult.ok();
        }
    }

    /**
     * 校验手机号码格式
     * @param phone 手机号码
     * @return 是否为有效的手机号码
     */
    private boolean isValidPhoneNumber(String phone) {
        if (StringUtils.isBlank(phone)) {
            return false;
        }
        // 支持11位手机号码（1开头）
        if (phone.length() == 11 && phone.startsWith("1")) {
            return phone.matches("^1[3-9]\\d{9}$");
        }
        // 支持12位手机号码（01开头）
        if (phone.length() == 12 && phone.startsWith(Constants.PHONE_PREFIX_CHINA_TELECOM)) {
            return phone.matches("^01[3-9]\\d{9}$");
        }
        return false;
    }


    private String getClientIP(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if (StringUtils.isNotBlank(ip) && ip.length() > 100) {
            ip = ip.substring(0, 100);
        }
        return ip;
    }


    private String getClientOneIP(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if (StringUtils.isNotBlank(ip) ) {
            ip = ip.split(",")[0];
        }
        return ip;
    }

}