package com.yunqu.tariff.servlet;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.tariff.base.AppBaseServlet;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.QueryFactory;
import jodd.util.StringUtil;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;

import javax.servlet.annotation.WebServlet;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.EasyRow;
import java.util.Map;
import java.util.UUID;

/**
 * 爬虫版本生成配置管理Servlet
 * 负责版本生成配置的数据操作
 *
 * @ClassName TariffCrawlerVersionConfigServlet
 * <AUTHOR> Copy This Tag)
 * @Description 爬虫版本生成配置相关HTTP接口
 * @Since create in 2025/1/28 16:00
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
@WebServlet("/servlet/crawlerVersionConfig/*")
public class TariffCrawlerVersionConfigServlet extends AppBaseServlet {

    /**
     * 新增配置
     * POST /servlet/crawlerVersionConfig?action=addConfig
     */
    public EasyResult actionForAddConfig() {
        try {
            // 权限检查
            UserModel user = getCurrentUser();
            if (!hasConfigManagePermission(user)) {
                return EasyResult.fail("没有配置管理权限");
            }

            JSONObject jsonObject = getJSONObject();

            // 获取参数
            String configName = jsonObject.getString("configName");
            String configType = jsonObject.getString("configType");

            String ruleValue = jsonObject.getString("ruleValue");
            String isEnabled = jsonObject.getString("isEnabled");
            Integer effectiveType = jsonObject.getInteger("effectiveType");
            String effectiveStartDate = jsonObject.getString("effectiveStartDate");
            String effectiveEndDate = jsonObject.getString("effectiveEndDate");
            String description = jsonObject.getString("description");
            String remark = jsonObject.getString("remark");

            // 参数验证
            if (StringUtil.isBlank(configName)) {
                return EasyResult.fail("配置名称不能为空");
            }
            if (StringUtil.isBlank(ruleValue)) {
                return EasyResult.fail("执行日期不能为空");
            }
            if (effectiveType == null) {
                return EasyResult.fail("有效期类型不能为空");
            }
            if (effectiveType != 1 && effectiveType != 2) {
                return EasyResult.fail("有效期类型值无效");
            }
            // 如果是指定有效期，必须提供开始和结束日期
            if (effectiveType == 2) {
                if (StringUtil.isBlank(effectiveStartDate) || StringUtil.isBlank(effectiveEndDate)) {
                    return EasyResult.fail("指定有效期时，开始日期和结束日期不能为空");
                }
            }

            // 验证规则值格式（执行日期必须是1-31的数字）
            EasyResult validateResult = validateRuleValue(ruleValue);
            if (!validateResult.isOk()) {
                return validateResult;
            }

            // 检查日期冲突
            EasyResult conflictResult = checkDateConflict(ruleValue, null);
            if (!conflictResult.isOk()) {
                return conflictResult;
            }

            // 系统配置只能由系统创建，用户不能创建系统配置
            if ("SYSTEM".equals(configType)) {
                return EasyResult.fail("不能创建系统配置，只能创建自定义配置");
            }

            String currentTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String currentUser = user.getUserAcc();
            String configId = UUID.randomUUID().toString().replace("-", "");

            // 设置默认值
            if (StringUtil.isBlank(configType)) {
                configType = "CUSTOM";
            }
            if (StringUtil.isBlank(isEnabled)) {
                isEnabled = "Y";
            }

            // 使用EasyRecord进行数据插入
            EasyRecord record = new EasyRecord(getTableName("xty_crawler_version_config"), "ID");
            record.set("ID", configId);
            record.set("CONFIG_NAME", configName);
            record.set("CONFIG_TYPE", configType);
            record.set("RULE_VALUE", ruleValue);
            record.set("IS_ENABLED", isEnabled);
            record.set("EFFECTIVE_TYPE", effectiveType);
            record.set("EFFECTIVE_START_DATE", effectiveType == 2 ? effectiveStartDate : null);
            record.set("EFFECTIVE_END_DATE", effectiveType == 2 ? effectiveEndDate : null);
            record.set("DESCRIPTION", description);
            record.set("CREATE_USER", currentUser);
            record.set("CREATE_TIME", currentTime);
            record.set("REMARK", remark);

            boolean result = this.getQuery().save(record);
            if (result) {
                JSONObject resultData = new JSONObject();
                resultData.put("configId", configId);
                resultData.put("message", "配置创建成功");
                return EasyResult.ok(resultData);
            } else {
                return EasyResult.fail("配置创建失败");
            }

        } catch (Exception e) {
            CommonLogger.getLogger().error("新增配置失败: " + e.getMessage(), e);
            return EasyResult.fail("新增配置失败: " + e.getMessage());
        }
    }

    /**
     * 修改配置
     * POST /servlet/crawlerVersionConfig?action=updateConfig
     */
    public EasyResult actionForUpdateConfig() {
        try {
            // 权限检查
            UserModel user = getCurrentUser();
            if (!hasConfigManagePermission(user)) {
                return EasyResult.fail("没有配置管理权限");
            }

            JSONObject jsonObject = getJSONObject();
            String configId = jsonObject.getString("ID");

            if (StringUtil.isBlank(configId)) {
                return EasyResult.fail("配置ID不能为空");
            }

            // 检查配置是否存在
            EasyRecord existingRecord = new EasyRecord(getTableName("xty_crawler_version_config"), "ID");
            existingRecord.setPrimaryValues(configId);
            try {
                Map<String, String> result = this.getQuery().findById(existingRecord);
                if (result == null || result.isEmpty()) {
                    return EasyResult.fail("配置不存在");
                }
                // 将查询结果设置回record中
                for (Map.Entry<String, String> entry : result.entrySet()) {
                    existingRecord.set(entry.getKey(), entry.getValue());
                }
            } catch (Exception e) {
                return EasyResult.fail("查询配置失败: " + e.getMessage());
            }

            // 系统配置不允许修改
            if ("SYSTEM".equals(existingRecord.getString("CONFIG_TYPE"))) {
                return EasyResult.fail("系统配置不允许修改");
            }

            // 获取更新参数
            String configName = jsonObject.getString("configName");
            String ruleValue = jsonObject.getString("ruleValue");
            String isEnabled = jsonObject.getString("isEnabled");
            Integer effectiveType = jsonObject.getInteger("effectiveType");
            String effectiveStartDate = jsonObject.getString("effectiveStartDate");
            String effectiveEndDate = jsonObject.getString("effectiveEndDate");
            String description = jsonObject.getString("description");
            String remark = jsonObject.getString("remark");

            // 验证规则值格式（如果提供了新的执行日期）
            if (StringUtil.isNotBlank(ruleValue)) {
                EasyResult validateResult = validateRuleValue(ruleValue);
                if (!validateResult.isOk()) {
                    return validateResult;
                }

                // 检查日期冲突（排除当前配置）
                EasyResult conflictResult = checkDateConflict(ruleValue, configId);
                if (!conflictResult.isOk()) {
                    return conflictResult;
                }
            }

            // 验证有效期类型
            if (effectiveType != null) {
                if (effectiveType != 1 && effectiveType != 2) {
                    return EasyResult.fail("有效期类型值无效");
                }
                // 如果是指定有效期，必须提供开始和结束日期
                if (effectiveType == 2) {
                    if (StringUtil.isBlank(effectiveStartDate) || StringUtil.isBlank(effectiveEndDate)) {
                        return EasyResult.fail("指定有效期时，开始日期和结束日期不能为空");
                    }
                }
            }

            String currentTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String currentUser = user.getUserAcc();

            // 更新记录
            if (StringUtil.isNotBlank(configName)) {
                existingRecord.set("CONFIG_NAME", configName);
            }
            if (StringUtil.isNotBlank(ruleValue)) {
                existingRecord.set("RULE_VALUE", ruleValue);
            }
            if (StringUtil.isNotBlank(isEnabled)) {
                existingRecord.set("IS_ENABLED", isEnabled);
            }
            if (effectiveType != null) {
                existingRecord.set("EFFECTIVE_TYPE", effectiveType);
                // 根据有效期类型设置日期字段
                if (effectiveType == 1) {
                    // 长期有效时清空日期
                    existingRecord.set("EFFECTIVE_START_DATE", null);
                    existingRecord.set("EFFECTIVE_END_DATE", null);
                } else if (effectiveType == 2) {
                    // 指定有效期时设置日期
                    existingRecord.set("EFFECTIVE_START_DATE", effectiveStartDate);
                    existingRecord.set("EFFECTIVE_END_DATE", effectiveEndDate);
                }
            } else {
                // 如果没有提供有效期类型，但提供了日期，则单独更新日期
                if (StringUtil.isNotBlank(effectiveStartDate)) {
                    existingRecord.set("EFFECTIVE_START_DATE", effectiveStartDate);
                }
                if (StringUtil.isNotBlank(effectiveEndDate)) {
                    existingRecord.set("EFFECTIVE_END_DATE", effectiveEndDate);
                }
            }
            if (StringUtil.isNotBlank(description)) {
                existingRecord.set("DESCRIPTION", description);
            }
            if (StringUtil.isNotBlank(remark)) {
                existingRecord.set("REMARK", remark);
            }

            existingRecord.set("UPDATE_USER", currentUser);
            existingRecord.set("UPDATE_TIME", currentTime);

            boolean result = this.getQuery().save(existingRecord);
            if (result) {
                return EasyResult.ok("配置更新成功");
            } else {
                return EasyResult.fail("配置更新失败");
            }

        } catch (Exception e) {
            CommonLogger.getLogger().error("修改配置失败: " + e.getMessage(), e);
            return EasyResult.fail("修改配置失败: " + e.getMessage());
        }
    }

    /**
     * 删除配置
     * POST /servlet/crawlerVersionConfig?action=deleteConfig
     */
    public EasyResult actionForDeleteConfig() {
        try {
            // 权限检查
            UserModel user = getCurrentUser();
            if (!hasConfigManagePermission(user)) {
                return EasyResult.fail("没有配置管理权限");
            }

            JSONObject jsonObject = getJSONObject();
            String configId = jsonObject.getString("ID");

            if (StringUtil.isBlank(configId)) {
                return EasyResult.fail("配置ID不能为空");
            }

            // 检查配置是否存在
            EasyRecord existingRecord = new EasyRecord(getTableName("xty_crawler_version_config"), "ID");
            existingRecord.setPrimaryValues(configId);
            try {
                Map<String, String> result = this.getQuery().findById(existingRecord);
                if (result == null || result.isEmpty()) {
                    return EasyResult.fail("配置不存在");
                }

                // 系统配置不允许删除
                if ("SYSTEM".equals(result.get("CONFIG_TYPE"))) {
                    return EasyResult.fail("系统配置不允许删除");
                }

                boolean deleteResult = this.getQuery().deleteById(existingRecord);
                if (deleteResult) {
                    CommonLogger.getLogger().info("删除配置成功，配置名称: " + result.get("CONFIG_NAME"));
                    return EasyResult.ok("配置删除成功");
                } else {
                    return EasyResult.fail("配置删除失败");
                }
            } catch (Exception e) {
                CommonLogger.getLogger().error("删除配置失败: " + e.getMessage(), e);
                return EasyResult.fail("删除配置失败: " + e.getMessage());
            }

        } catch (Exception e) {
            CommonLogger.getLogger().error("删除配置失败: " + e.getMessage(), e);
            return EasyResult.fail("删除配置失败: " + e.getMessage());
        }
    }

    /**
     * 记录版本生成执行日志
     * POST /servlet/crawlerVersionConfig/logExecute
     */
    public EasyResult actionForLogExecute() {
        try {
            JSONObject jsonObject = getJSONObject();

            String configId = jsonObject.getString("configId");
            String executeDate = jsonObject.getString("executeDate");
            String executeTime = jsonObject.getString("executeTime");
            String executeResult = jsonObject.getString("executeResult");
            String executeMessage = jsonObject.getString("executeMessage");

            if (StringUtil.isBlank(configId)) {
                return EasyResult.fail("配置ID不能为空");
            }

            String currentTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String logId = UUID.randomUUID().toString().replace("-", "");

            // 使用EasyRecord进行数据插入
            EasyRecord record = new EasyRecord(getTableName("xty_crawler_version_generate_log"), "ID");
            record.set("ID", logId);
            record.set("CONFIG_ID", configId);
            record.set("EXECUTE_DATE", executeDate);
            record.set("EXECUTE_TIME", executeTime);
            record.set("EXECUTE_RESULT", executeResult);
            record.set("EXECUTE_MESSAGE", executeMessage);
            record.set("CREATE_TIME", currentTime);

            boolean result = this.getQuery().save(record);
            if (result) {
                return EasyResult.ok("执行日志记录成功");
            } else {
                return EasyResult.fail("执行日志记录失败");
            }

        } catch (Exception e) {
            CommonLogger.getLogger().error("记录执行日志失败: " + e.getMessage(), e);
            return EasyResult.fail("记录执行日志失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户是否有配置管理权限
     */
    private boolean hasConfigManagePermission(UserModel user) {
        return true;
    }

    /**
     * 获取当前用户
     */
    private UserModel getCurrentUser() {
        try {
            return UserUtil.getUser(getRequest());
        } catch (Exception e) {
            CommonLogger.getLogger().error("获取当前用户失败: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 验证规则值格式（执行日期）
     */
    private EasyResult validateRuleValue(String ruleValue) {
        try {
            // 验证是否为数字
            int day = Integer.parseInt(ruleValue);

            // 验证日期范围
            if (day < 1 || day > 31) {
                return EasyResult.fail("执行日期必须在1-31之间");
            }

            return EasyResult.ok("验证通过");

        } catch (NumberFormatException e) {
            return EasyResult.fail("执行日期必须是数字");
        } catch (Exception e) {
            return EasyResult.fail("执行日期格式错误：" + e.getMessage());
        }
    }

    /**
     * 检查日期冲突
     */
    private EasyResult checkDateConflict(String ruleValue, String excludeConfigId) {
        try {
            // 直接在Servlet中查询数据库检查冲突
            EasySQL sql = new EasySQL("SELECT ID, CONFIG_NAME, RULE_VALUE " +
                "FROM " + getTableName("xty_crawler_version_config") +
                " WHERE IS_ENABLED = 'Y' ");
            sql.append(ruleValue, "AND RULE_VALUE = ?");

            if (StringUtil.isNotBlank(excludeConfigId)) {
                sql.append(excludeConfigId, " AND ID != ?");
            }
/*  */
            List<EasyRow> conflictRecords = getQuery().queryForList(sql.getSQL(), sql.getParams());

            if (conflictRecords != null && !conflictRecords.isEmpty()) {
                StringBuilder conflictMsg = new StringBuilder("存在日期冲突：");
                for (EasyRow record : conflictRecords) {
                    conflictMsg.append("\n- 执行日期").append(record.getColumnValue("RULE_VALUE"))
                              .append(" 与配置 '").append(record.getColumnValue("CONFIG_NAME")).append("' 冲突");
                }
                return EasyResult.fail(conflictMsg.toString());
            }

            return EasyResult.ok("无冲突");

        } catch (Exception e) {
            CommonLogger.getLogger().error("检查日期冲突失败: " + e.getMessage(), e);
            return EasyResult.fail("检查日期冲突失败：" + e.getMessage());
        }
    }



    @Override
    protected EasyQuery getQuery() {
        return QueryFactory.getTariffQuery();
    }
}
