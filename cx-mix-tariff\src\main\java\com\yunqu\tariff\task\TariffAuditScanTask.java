package com.yunqu.tariff.task;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.enums.AuditTaskStatusEnum;
import com.yunqu.tariff.service.TariffAuditTaskService;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 稽核任务扫描定时任务
 *
 * <AUTHOR>
 * @date 2024-12-21
 */
public class TariffAuditScanTask {

    private static final Logger logger = CommonLogger.getLogger("audit-scan-task");

    // 线程池，用于并发处理任务
    private static volatile ExecutorService executor;

    // 线程池状态标识
    private static final AtomicBoolean isShutdown = new AtomicBoolean(false);

    // 每次扫描的最大任务数量
    private static final int MAX_SCAN_COUNT = 5;

    // 静态初始化块
    static {
        initializeExecutor();
        // 添加JVM关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(TariffAuditScanTask::shutdown));
    }

    /**
     * 初始化线程池
     */
    private static synchronized void initializeExecutor() {
        if (executor == null || executor.isShutdown()) {
            executor = Executors.newFixedThreadPool(Constants.getAuditThreadPoolSize(), r -> {
                Thread t = new Thread(r, "TariffAuditScanTask-" + System.currentTimeMillis());
                t.setDaemon(true); // 设置为守护线程
                return t;
            });
            isShutdown.set(false);
            logger.info("TariffAuditScanTask线程池已初始化，线程数: {}", Constants.getAuditThreadPoolSize());
        }
    }

    /**
     * 优雅关闭线程池
     */
    public static void shutdown() {
        if (isShutdown.compareAndSet(false, true)) {
            logger.info("开始关闭TariffAuditScanTask线程池");
            if (executor != null && !executor.isShutdown()) {
                executor.shutdown();
                try {
                    // 等待30秒让任务完成
                    if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                        logger.warn("线程池未能在30秒内正常关闭，强制关闭");
                        executor.shutdownNow();
                        // 再等待10秒
                        if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                            logger.error("线程池强制关闭失败");
                        }
                    }
                    logger.info("TariffAuditScanTask线程池已关闭");
                } catch (InterruptedException e) {
                    logger.warn("等待线程池关闭时被中断", e);
                    executor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
        }
    }

    public void run() {
        logger.info("开始扫描待执行的稽核任务");

        try {
            // 检查线程池状态
            if (isShutdown.get() || (executor != null && executor.isShutdown())) {
                logger.warn("线程池已关闭，重新初始化");
                initializeExecutor();
            }

            // 查询待执行的任务
            List<JSONObject> pendingTasks = getPendingTasks();

            if (pendingTasks.isEmpty()) {
                logger.info("没有待执行的稽核任务");
                return;
            }

            logger.info("发现 {} 个待执行的稽核任务", pendingTasks.size());

            // 提交任务到线程池处理
            for (JSONObject task : pendingTasks) {
                if (!isShutdown.get() && executor != null && !executor.isShutdown()) {
                    executor.submit(new TaskProcessor(task));
                } else {
                    logger.warn("线程池不可用，跳过任务: {}", task.getString("ID"));
                }
            }

        } catch (Exception e) {
            logger.error("扫描稽核任务失败", e);
        }
    }

    /**
     * 查询待执行的任务
     */
    private List<JSONObject> getPendingTasks() {
        try {
            EasySQL sql = new EasySQL();
            sql.append("SELECT * FROM " + Constants.getBusiTable(Constants.AUDIT_TASK_TABLE));
            sql.append(AuditTaskStatusEnum.PENDING.getCode(), " WHERE STATUS = ?");
            sql.append(" ORDER BY CREATE_TIME ASC");
            sql.append(MAX_SCAN_COUNT, " LIMIT ?");

            return QueryFactory.getTariffQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

        } catch (Exception e) {
            logger.error("查询待执行任务失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 任务处理器
     */
    private static class TaskProcessor implements Runnable {

        private final JSONObject task;

        public TaskProcessor(JSONObject task) {
            this.task = task;
        }

        @Override
        public void run() {
            String taskId = task.getString("ID");
            String taskName = task.getString("TASK_NAME");

            logger.info("开始处理稽核任务: {} - {}", taskId, taskName);

            try {
                // 检查任务是否已被其他线程处理
                if (isTaskProcessing(taskId)) {
                    logger.warn("任务 {} 正在被其他线程处理，跳过", taskId);
                    return;
                }

                // 调用任务处理服务
                EasyResult result = TariffAuditTaskService.processTask(taskId);

                logger.info("稽核任务处理完成: {} - {} - {}", taskId, taskName, result);

            } catch (Exception e) {
                logger.error("处理稽核任务失败: {} - {}", taskId, taskName, e);

                // 更新任务状态为失败
                try {
                    TariffAuditTaskService.updateTaskStatus(taskId, AuditTaskStatusEnum.FAILED,
                            "任务处理异常: " + e.getMessage());
                } catch (Exception updateEx) {
                    logger.error("更新任务失败状态失败: {}", taskId, updateEx);
                }
            }
        }

        /**
         * 检查任务是否正在处理中
         */
        private boolean isTaskProcessing(String taskId) {
            try {
                String sql = "SELECT STATUS FROM " + Constants.getBusiTable(Constants.AUDIT_TASK_TABLE) + " WHERE ID = ?";
                JSONObject result = QueryFactory.getTariffQuery().queryForRow(sql, new Object[]{taskId}, new JSONMapperImpl());

                if (result != null) {
                    int status = result.getIntValue("STATUS");
                    return status != AuditTaskStatusEnum.PENDING.getCode();
                }

                return false;

            } catch (Exception e) {
                logger.error("检查任务状态失败: {}", taskId, e);
                return true; // 出错时认为正在处理，避免重复处理
            }
        }
    }



    /**
     * 获取线程池状态信息
     */
    public static JSONObject getThreadPoolStatus() {
        JSONObject status = new JSONObject();

        if (executor instanceof java.util.concurrent.ThreadPoolExecutor) {
            java.util.concurrent.ThreadPoolExecutor tpe = (java.util.concurrent.ThreadPoolExecutor) executor;

            status.put("corePoolSize", tpe.getCorePoolSize());
            status.put("maximumPoolSize", tpe.getMaximumPoolSize());
            status.put("activeCount", tpe.getActiveCount());
            status.put("taskCount", tpe.getTaskCount());
            status.put("completedTaskCount", tpe.getCompletedTaskCount());
            status.put("queueSize", tpe.getQueue().size());
            status.put("isShutdown", tpe.isShutdown());
            status.put("isTerminated", tpe.isTerminated());
        }

        return status;
    }
}
