<!DOCTYPE html>
<html>

<head>
  <title>报送资费内容情况变化</title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
  <!-- 基础的 css js 资源 -->
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.4">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.3">
  <link rel="stylesheet" href="./common.css?v=20241127">
  <link rel="stylesheet" href="/cx-mix-tariff/static/css/searchForm.css">
  <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
  <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
  <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
  <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.1"></script>
  <script src="/cc-base/static/js/my_i18n.js?v=202111"></script>
  <script src="/cc-base/static/js/i18n.js?v=1"></script>
  <script src="/cc-base/static/cdn/axios@0.26.1/axios.min.js"></script>
  <style>
    #tariff-report-content-statistics .search-form.grid-5 {
      gap: 16px 4px !important;
    }
  </style>
</head>

<body class="yq-page-full vue-box">
  <div id="tariff-report-content-statistics" class="flex yq-table-page"
    v-auth:[permissions]="'cx-xty-tariff-report-chk-export'">
    <div class="yq-card">
      <div class="card-header">
        <div class="title">{{ getI18nValue('报送资费内容情况变化') }}</div>
        <div class="yq-table-control">
          <el-button type="primary" plain size="small" @click="handleExport"
            v-if="permissions['cx-xty-tariff-report-chk-export']">
            <i class="el-icon-download"></i>导出
          </el-button>
        </div>
      </div>
      <div class="card-content">
        <div class="search-box">
          <senior-search :show.sync="moreSearch">
            <el-form :inline="false" :model="searchForm" ref="form" class="search-form grid-5" label-width="80px"
              size="small">
              <!-- 默认搜索条件 -->
              <el-form-item label="适用地区" prop="provinceCode">
                <el-cascader v-model="tree" @change="handleCasader" :options="options" :props="{multiple: true}"
                  clearable collapse-tags placeholder=" 请选择">
                </el-cascader>
              </el-form-item>
              <el-form-item label="运营商" prop="endCode">
                <el-select v-model="searchForm.endCode" placeholder="请选择" filterable clearable>
                  <el-option v-for="(label, value) in XTY_REPORTER_ENT" :key="value" :label="label"
                    :value="value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="一级分类" prop="type1">
                <el-select v-model="searchForm.type1" placeholder="请选择" multiple collapse-tags clearable>
                  <el-option v-for="(label, value) in XTY_TARIFF_ONE_TYPE" :key="value" :label="label"
                    :value="value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="资费状态" prop="status">
                <el-select v-model="searchForm.status" placeholder="请选择" multiple collapse-tags clearable>
                  <el-option v-for="(label, value) in XTY_TARIFF_STATUS" :key="value" :label="label"
                    :value="value"></el-option>
                </el-select>
              </el-form-item>
              <!-- 高级搜索条件 -->
              <template v-if="moreSearch">
                <el-form-item label="是否公示" prop="isPublic">
                  <el-select v-model="searchForm.isPublic" placeholder="请选择" clearable>
                    <el-option v-for="(label, value) in is_public_list" :key="value" :label="label"
                      :value="value"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="是否通信类" prop="isTelecom">
                  <el-select v-model="searchForm.isTelecom" placeholder="请选择" multiple collapse-tags clearable>
                    <el-option v-for="(label, value) in XTY_TARIFF_TELECOM" :key="value" :label="label"
                      :value="value"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="报送主体" prop="reporter">
                  <div class="baseflex">
                    <el-cascader v-model="searchForm.label_1" :options="provinceAndGroup" :props="provinceCascaderProps"
                      :show-all-levels="false" placeholder="请选择" clearable style="margin-right: 2px"></el-cascader>
                    <el-select v-model="searchForm.label_2" placeholder="请选择" filterable clearable>
                      <el-option v-for="(label, value) in XTY_REPORTER_ENT" :key="value" :label="label"
                        :value="value"></el-option>
                    </el-select>
                  </div>
                </el-form-item>
                <!-- <el-form-item label="二级分类" prop="type2">
                  <el-select v-model="searchForm.type2" placeholder="请选择" multiple collapse-tags clearable>
                    <el-option v-for="(label, value) in XTY_TARIFF_TWO_TYPE" :key="value" :label="label"
                      :value="value"></el-option>
                  </el-select>
                </el-form-item> -->
                <el-form-item label="公示版本" prop="versionNos">
                  <el-select v-model="searchForm.versionNos" placeholder="请选择" multiple collapse-tags clearable>
                    <el-option v-for="(item, index) in versionOptions" :key="index" :label="item.VERSION_NO"
                      :value="item.VERSION_NO"></el-option>
                  </el-select>
                </el-form-item>
              </template>
              <el-form-item class="btns" label-width="16px">
                <el-button type="primary" plain size="small" icon="el-icon-refresh"
                  @click="handleReset">{{getI18nValue('重置')}}</el-button>
                <el-button type="primary" size="small" icon="el-icon-search"
                  @click="getList">{{getI18nValue('搜索')}}</el-button>
                <el-button type="primary" plain size="small" @click.stop="moreSearch = !moreSearch">
                  <img src="/easitline-cdn/vue-yq/static/imgs/filter.png" alt="">高级搜索
                </el-button>
              </el-form-item>
            </el-form>
          </senior-search>
        </div>
        <!-- 统计表格 -->
        <div class="yq-table">
          <el-table :data="tableData" style="width: 100%" v-loading="loading" border stripe>
            <!-- 版本列 -->
            <el-table-column prop="version" label="公示版本" min-width="120">
            </el-table-column>
            <el-table-column label="资费总数" prop="total" min-width="120">
              <template slot-scope="scope">
                <el-link type="primary" :underline="false" @click="openTab(scope.row)">
                  {{ scope.row.total }}
                </el-link>
              </template>
            </el-table-column>
            <!-- 检查项目列 - 根据字典动态生成 -->
            <el-table-column v-for="(rule, ruleCode) in ruleDict" :key="ruleCode" :prop="ruleCode" :label="rule"
              min-width="120">
              <template slot-scope="scope">
                <el-link v-if="scope.row.version !== '平均'" type="primary" :underline="false"
                  @click="openTab(scope.row, ruleCode)">
                  {{ formatNumber(scope.row[ruleCode]) }}
                </el-link>
                <span v-else>{{ formatNumber(scope.row[ruleCode]) }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
  <script type="text/javascript" src="/cx-mix-tariff/static/js/time.js"></script>
  <script src="./mixins.js?v=20250723"></script>
  <script>
    var tariffReportContentStatistics = new Vue({
      el: '#tariff-report-content-statistics',
      mixins: [mixins],
      data: function () {
        return {
          searchForm: {
            provinceCode: '',     // 省份代码
            areaCode: '',
            endCode: '',          // 运营商
            type1: [],            // 一级分类
            isPublic: '',         // 是否公示
            isTelecom: [],        // 是否通信类
            type2: [],            // 二级分类
            versionNos: [],       // 版本号
            label_1: '',          // 报送主体
            label_2: '',          // 报送主体
          },
          tableData: [],
          loading: false,
          entOptions: [],
          versionOptions: [],
          ruleDict: {},
          provinceCascaderProps: {
            value: "id",
            label: "name",
            children: "children",
            emitPath: false,
          },
          XTY_TARIFF_ONE_TYPE: {},
          XTY_TARIFF_TWO_TYPE: {},
          XTY_TARIFF_TELECOM: {},
          XTY_REPORTER_ENT: {},
          XTY_TARIFF_STATUS: {},
          is_public_list: {
            Y: "公示",
            N: "未公示",
          },
        }
      },
      methods: {
        getPayload() {
          const data = {
            provinceCode: this.searchForm.provinceCode,
            areaCode: this.searchForm.areaCode,
            endCode: [this.searchForm.endCode],
            type1: this.searchForm.type1,
            type2: this.searchForm.type2,
            isPublic: [this.searchForm.isPublic],
            isTelecom: this.searchForm.isTelecom,
            versionNos: this.searchForm.versionNos,
            status: this.searchForm.status,
          };

          let label_1 = this.findProvince(this.searchForm.label_1, 'id')
          data.reporter = [label_1 ? (label_1.tariffProvinceCode + this.searchForm.label_2) : this.searchForm.label_2]
          return data
        },
        // 获取列表数据
        getList() {
          this.loading = true;
          yq.remoteCall(
            "/cx-mix-tariff/webcall?action=tariffPublicLibStatDao.tariffReportChkStat",
            this.getPayload()
          ).then((res) => {
            if (res.state == 1) {
              this.tableData = res.data || [];
            } else {
              this.$message.error(res.msg || '获取数据失败');
              this.tableData = [];
            }
          }).finally(() => {
            this.loading = false;
          })
        },
        handleExport() {
          const data = this.getPayload()
          const url = '/cx-mix-tariff/servlet/tariffPublicLib?action=tariffReportChkStat&' + $.param(data);
          yq.remoteCall(
            url,
            {},
            (res) => {
              this.$message({
                type: res.state == 1 ? 'success' : 'error',
                message: res.msg
              })
            }
          )
        },
        // 重置搜索条件
        handleReset() {
          this.$refs.form.resetFields();
          this.searchForm.areaCode = ''
          this.searchForm.label_1 = ''
          this.searchForm.label_2 = ''
          this.moreSearch = false;
          this.getList()
        },
        // 格式化数字显示
        formatNumber(num) {
          if (num === null || num === undefined || num === '') {
            return '0';
          }
          return num
        },
        // 获取报送主体的省份
        getProvinceAndGroup() {
          return yq
            .remoteCall(
              "/cx-mix-tariff/webcall?action=common.queryTariffProvinceTree"
            )
            .then((res) => {
              if (res.state == 1) {
                this.provinceAndGroup = res.data || [];
              }
            });
        },
        // 获取检查规则字典
        getRuleDict() {
          return yq
            .remoteCall(
              "/cx-mix-tariff/webcall?action=common.tariffRuleDict"
            )
            .then((res) => {
              this.ruleDict = res.data || {};
            });
        },
        // 获取字典数据
        getDict() {
          yq.daoCall(
            {
              controls: [
                "common.getDict(XTY_TARIFF_ONE_TYPE)",
                "common.getDict(XTY_TARIFF_TWO_TYPE)",
                "common.getDict(XTY_TARIFF_TELECOM)",
                "common.getDict(XTY_TARIFF_STATUS)",
              ],
              params: {},
            },
            (data) => {
              this.XTY_TARIFF_ONE_TYPE =
                data["common.getDict(XTY_TARIFF_ONE_TYPE)"].data;
              this.XTY_TARIFF_TWO_TYPE =
                data["common.getDict(XTY_TARIFF_TWO_TYPE)"].data;
              this.XTY_TARIFF_TELECOM =
                data["common.getDict(XTY_TARIFF_TELECOM)"].data;
              this.XTY_TARIFF_STATUS =
                data["common.getDict(XTY_TARIFF_STATUS)"].data;
            },
            { contextPath: "cx-mix-tariff" }
          );

          yq.remoteCall(
            "/cx-mix-tariff/webcall?action=crawlerTariffData.getVersionList"
          ).then((res) => {
            this.versionOptions = res.data || [];
          });
        },
        openTab(row, ruleCode) {
          const data = {
            sourceChannel: 'tariff-report-content-statistics',
            ent: this.searchForm.endCode,
            isPublic: this.searchForm.isPublic,
            is_telecom: this.searchForm.isTelecom.join(),
            label_1: this.searchForm.label_1,
            label_2: this.searchForm.label_2,
            TYPE1: this.searchForm.type1.join(),
            TYPE2: this.searchForm.type2.join(),
            publicVersions: row.version,
            fieldCheckNo: ruleCode,
            status: this.searchForm.status.join(),
          }
          localStorage.setItem("areaTree", JSON.stringify(this.tree));
          top.popup.openTab({
            url: '/cx-mix-tariff/pages/fillDetail/china-all-ent-check.html',
            title: '报送字段检查（全行业）',
            data,
            id: 'cx-xty-tariff-list-qhyjc',
          });
        },
      },
      mounted() {
        this.getRuleDict()
      },
    })
  </script>
</body>

</html>