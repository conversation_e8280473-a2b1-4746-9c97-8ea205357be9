<template>
  <div
    id="tariff-common"
    class="yq-card"
    v-auth:[permissions]="
      'cx-xty-tariff-edit;cx-xty-tariff-delete;cx-xty-tariff-export;cx-xty-tariff-exportPdf;cx-xty-tariff-relate'
    "
  >
    <div class="card-header" style="justify-content: space-between">
      <div class="head-title">
        {{ getI18nValue(model === "stat" ? "公示资费" : "公示字段检查（全行业）") }}
      </div>
      <div class="export">
        <columns
          :columns="tableConfig"
          query-id="disclosedOrderList"
          @handlehide="handlehide"
        ></columns>
        <template v-if="permissions['cx-xty-tariff-export']">
          <el-button plain size="small" type="primary" @click="handleExport">
            <i class="el-icon-download"></i>导出
          </el-button>
          <el-button
            v-if="model === 'check'"
            :disabled="selectedList.length < 1"
            style="margin-left: 0"
            plain
            size="small"
            type="primary"
            :loading="checkResultLoading"
            @click="handleCheckResult"
            >比对</el-button
          >
        </template>
      </div>
    </div>

    <div class="card-content">
      <div class="search-box">
        <senior-search :show.sync="moreSearch">
          <el-form
            class="search-form grid-5"
            :inline="false"
            :model="searchForm"
            ref="searchForm"
            size="small"
            label-width="70px"
          >
            <el-form-item :label="getI18nValue('方案编号')" prop="TARIFF_NO">
              <el-input
                v-model="searchForm.tariffNo"
                :placeholder="getI18nValue('请输入')"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item :label="getI18nValue('资费名称')" prop="NAME">
              <el-input
                v-model="searchForm.name"
                :placeholder="getI18nValue('请输入')"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item :label="getI18nValue('资费来源')">
              <el-select v-model="searchForm.tariffAttr" clearable>
                <el-option
                  v-for="(label, index) in dict.XTY_TARIFF_CRAWL_TYPE"
                  :key="index"
                  :label="label"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="getI18nValue('运营商')">
              <el-select
                v-model="searchForm.entName"
                style="margin-right: 2px"
                placeholder="请选择"
                :disabled="['01', '02'].includes(groupType)"
                filterable
                multiple
                collapse-tags
                clearable
              >
                <el-option
                  v-for="(label, value) in dict.XTY_REPORTER_ENT"
                  :key="value"
                  :label="label"
                  :value="value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="getI18nValue('省份')">
              <el-select
                v-model="searchForm.provinceName"
                clearable
                :disabled="groupType === '01'"
              >
                <el-option
                  v-for="item in provinceAndGroup"
                  :key="item.provinceCode"
                  :value="item.provinceCode"
                  :label="item.name"
                ></el-option>
              </el-select>
            </el-form-item>

            <template v-if="moreSearch">
                <!-- <template> -->
                <el-form-item v-if="model === 'check'" label="一级分类" prop="classicTypeOne">
                  <el-select
                    v-model="searchForm.classicTypeOne"
                    placeholder="请选择"
                    filterable
                    clearable
                    multiple
                    collapse-tags
                  >
                    <el-option
                      v-for="(label, value) in dict.XTY_TARIFF_ONE_TYPE"
                      :key="value"
                      :label="label"
                      :value="value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item v-if="model === 'check'" label="二级分类" prop="classicTypeTwo">
                  <el-select
                    v-model="searchForm.classicTypeTwo"
                    placeholder="请选择"
                    filterable
                    clearable
                    multiple
                    collapse-tags
                  >
                    <el-option
                      v-for="(label, value) in dict.XTY_TARIFF_TWO_TYPE"
                      :key="value"
                      :label="label"
                      :value="value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                  :label="getI18nValue('是否通信类')"
                  v-if="model === 'check'"
                  prop="isTelecom"
                  label-width="80px"
                >
                  <el-select
                    v-model="searchForm.isTelecom"
                    placeholder="请选择"
                    multiple
                    collapse-tags
                    filterable
                    clearable
                  >
                    <el-option
                      v-for="(label, value) in dict.XTY_TARIFF_TELECOM"
                      :key="value"
                      :label="label"
                      :value="value"
                    ></el-option>
                  </el-select>
                </el-form-item>

              <el-form-item :label="getI18nValue('是否报送')" v-if="model === 'check'" prop="reported">
                <el-select
                  v-model="searchForm.reported"
                  placeholder="请选择"
                  filterable
                  clearable
                >
                  <el-option
                    v-for="(label, value) in dict.XTY_TARIFF_CRAWL_REPORT"
                    :key="value"
                    :label="label"
                    :value="value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="资费分类" prop="TARIFF_TYPE">
                <el-input
                  v-model="searchForm.tariffType"
                  placeholder="请输入"
                  clearable
                ></el-input>
                <!-- <el-select
                  v-model="searchForm.CRAWLER_TYPE"
                  placeholder="请选择"
                  filterable
                  clearable
                  multiple
                  collapse-tags
                > -->
                <!-- <el-option></el-option> -->
                <!-- </el-select> -->
              </el-form-item>
              <el-form-item label="资费标准" prop="fees">
                <el-input
                  v-model="searchForm.fees"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="超出资费" prop="exceedFees">
                <el-input
                  v-model="searchForm.exceedFees"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="其他费用" prop="otherFees">
                <el-input
                  v-model="searchForm.otherFees"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item
                :label="getI18nValue('其他服务内容')"
                label-width="98px"
              >
                <el-input
                  v-model="searchForm.otherContent"
                  :placeholder="getI18nValue('请输入')"
                  clearable
                ></el-input>
              </el-form-item>
              <!-- <el-form-item :label="getI18nValue('适用范围')">
                <el-input
                  v-model="searchForm.APPLICABLE_PEOPLE"
                  :placeholder="getI18nValue('请输入')"
                  clearable
                ></el-input>
              </el-form-item> -->
              <el-form-item :label="getI18nValue('有效期限')">
                <el-input
                  v-model="searchForm.validPeriod"
                  :placeholder="getI18nValue('请输入')"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item :label="getI18nValue('销售渠道')">
                <el-input
                  v-model="searchForm.channel"
                  :placeholder="getI18nValue('请输入')"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item :label="getI18nValue('在网要求')">
                <el-input
                  v-model="searchForm.duration"
                  :placeholder="getI18nValue('请输入')"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item :label="getI18nValue('退订方式')">
                <el-input
                  v-model="searchForm.unsubscribe"
                  :placeholder="getI18nValue('请输入')"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item :label="getI18nValue('其他事项')">
                <el-input
                  v-model="searchForm.others"
                  :placeholder="getI18nValue('请输入')"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item :label="getI18nValue('上线日期')">
                <el-date-picker
                  v-model="searchForm.onlineDay"
                  type="datetimerange"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  clearable
                  :unlink-panels="true"
                  :append-to-body="false"
                  popper-class="left-popper-430"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item :label="getI18nValue('下线日期')">
                <el-date-picker
                  v-model="searchForm.offlineDay"
                  type="datetimerange"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  clearable
                  :unlink-panels="true"
                  :append-to-body="false"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item :label="getI18nValue('版本号')">
                <el-select
                  v-model="searchForm.versionNos"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="(label, value) in dict.versionOptions"
                    :key="value"
                    :label="label.VERSION_NO"
                    :value="label.VERSION_NO"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="getI18nValue('出现月份')">
                <el-date-picker
                  v-model="searchForm.appearMonths"
                  type="month"
                  value-format="yyyyMM"
                  start-placeholder="开始月份"
                  end-placeholder="结束月份"
                  clearable
                  :unlink-panels="true"
                  :append-to-body="false"
                >
                </el-date-picker>
              </el-form-item>

              <el-form-item label="资费状态" v-if="model === 'check'" prop="tariffState">
                <el-select v-model="searchForm.tariffState" placeholder="请选择" filterable multiple collapse-tags clearable>
                  <el-option v-for="(label, value) in dict.XTY_TARIFF_STATUS" :key="value" :label="label"
                    :value="value"></el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="getI18nValue('报送主体')" v-if="model === 'check'" prop="tariffReporter">
                <div class="baseflex">
                  <el-cascader v-model="searchForm.label_1" :options="provinceAndGroup" :props="provinceCascaderProps" :show-all-levels="false" placeholder="请选择" clearable style="margin-right: 2px"></el-cascader>
                  <el-select v-model="searchForm.label_2" placeholder="请选择" filterable clearable style="margin-right: 2px">
                    <el-option v-for="(label, value) in dict.XTY_REPORTER_ENT" :key="value" :label="label"
                      :value="value"></el-option>
                  </el-select>
                </div>
              </el-form-item>

              <template v-if="model === 'check'">
                <el-form-item
                  label="字段检查结果"
                  label-width="100px">
                  <el-select
                    v-model="searchForm.fieldCheckNo"
                    placeholder="请选择"
                    collapse-tags
                    multiple
                    clearable>
                    <el-option
                      v-for="(val, key) in dict.fieldCheckResultDict"
                      :key="key"
                      :label="val"
                      :value="key"></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item
                  :label="getI18nValue('检查时间')">
                  <el-date-picker
                    v-model="searchForm.fieldCheckTime"
                    type="datetimerange"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :default-time="['00:00:00', '23:59:59']"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    clearable
                    :unlink-panels="true"
                    :append-to-body="false">
                  </el-date-picker>
                </el-form-item>
              </template>
                 

              <!-- <el-form-item label="适用地区" prop="provinceCode">
                <el-cascader v-model="tree" :options="options" :props="props" clearable collapse-tags placeholder="请选择"
                  @change="handleCasader" style="width: 100%" size="small">
                </el-cascader>
              </el-form-item>
              <el-form-item :label="getI18nValue('是否通信类')" prop="is_telecom" label-width="80px">
                <el-select v-model="searchForm.is_telecom" placeholder="请选择" filterable clearable>
                  <el-option v-for="(label, value) in XTY_TARIFF_TELECOM" :key="value" :label="label"
                    :value="value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="getI18nValue('资费标准')">
                <div style="display: flex">
                  <el-input v-model="searchForm.feesBegin" :placeholder="getI18nValue('请输入')" clearable
                    @input="handleInput(1)"></el-input>
                  <div>~</div>
                  <el-input v-model="searchForm.feesEnd" :placeholder="getI18nValue('请输入')" clearable
                    @input="handleInput(2)"></el-input>
                </div>
              </el-form-item>
              <el-form-item :label="getI18nValue('资费单位')" prop="fees_unit">
                <el-input v-model="searchForm.fees_unit" :placeholder="getI18nValue('请输入')" clearable></el-input>
              </el-form-item>
              <el-form-item :label="getI18nValue('超出资费')">
                <el-input v-model="searchForm.extra_fees" :placeholder="getI18nValue('请输入')" clearable></el-input>
              </el-form-item>
              <el-form-item :label="getI18nValue('其他费用')">
                <el-input v-model="searchForm.other_fees" :placeholder="getI18nValue('请输入')" clearable></el-input>
              </el-form-item>
              <el-form-item label="资费属性" prop="tariff_attr">
                <el-select v-model="searchForm.tariff_attr" placeholder="请选择" filterable clearable multiple collapse-tags
                  :disabled="tabIndex == '1' ? true : false">
                  <el-option v-for="(label, value) in XTY_TARIFF_ATTRIBUTES" :key="value" :label="label" :value="value"
                    :disabled="tabIndex == '2' ? (label == '全国' ? true : false) : false"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="资费状态" prop="status">
                <el-select v-model="searchForm.status" placeholder="请选择" filterable clearable multiple collapse-tags>
                  <el-option v-for="(label, value) in XTY_TARIFF_STATUS" :key="value" :label="label"
                    :value="value" :disabled="value === '5'"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="getI18nValue('违约责任')">
                <el-input v-model="searchForm.responsibility" :placeholder="getI18nValue('请输入')" clearable></el-input>
              </el-form-item>
              <el-form-item :label="getI18nValue('上线日期开始时间')" prop="onlineDayStart" label-width="120px">
                <el-date-picker v-model="searchForm.onlineDayStart" type="date" format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd" :placeholder="getI18nValue('请选择开始时间')" clearable
                  :append-to-body="false" popper-class="left-popper-220">
                </el-date-picker>
              </el-form-item>
              <el-form-item :label="getI18nValue('上线日期结束时间')" prop="onlineDayEnd" label-width="120px">
                <el-date-picker v-model="searchForm.onlineDayEnd" type="date" format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd" :placeholder="getI18nValue('请选择结束时间')" clearable
                  :append-to-body="false">
                </el-date-picker>
              </el-form-item>
              <el-form-item :label="getI18nValue('下线日期开始时间')" prop="offlineDayStart" label-width="120px">
                <el-date-picker v-model="searchForm.offlineDayStart" type="date" format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd" :placeholder="getI18nValue('请选择开始时间')" clearable
                  :append-to-body="false">
                </el-date-picker>
              </el-form-item>
              <el-form-item :label="getI18nValue('下线日期结束时间')" prop="offlineDayEnd" label-width="120px">
                <el-date-picker v-model="searchForm.offlineDayEnd" type="date" format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd" :placeholder="getI18nValue('请选择结束时间')" clearable
                  :append-to-body="false">
                </el-date-picker>
              </el-form-item>
              <el-form-item :label="getI18nValue('创建时间')">
                <el-date-picker v-model="searchForm.createTime" type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss" 
                  :default-time="['00:00:00', '23:59:59']" start-placeholder="开始时间" end-placeholder="结束时间" clearable
                  :unlink-panels="true" :append-to-body="false" popper-class="left-popper-430">
                </el-date-picker>
              </el-form-item>
              <el-form-item :label="getI18nValue('别名')">
                <el-input v-model="searchForm.tariff_another_name" :placeholder="getI18nValue('请输入')" clearable></el-input>
              </el-form-item>
              <el-form-item label="版本号">
                <el-select v-model="searchForm.versionNum" placeholder="请选择" clearable>
                  <el-option label="未更改" value="0"></el-option>
                  <el-option label="已更改" value="1"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="getI18nValue('修改时间')">
                <el-date-picker v-model="searchForm.updateTime" type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss" 
                  :default-time="['00:00:00', '23:59:59']" start-placeholder="开始时间" end-placeholder="结束时间" clearable
                  :unlink-panels="true" :append-to-body="false" popper-class="left-popper-220">
                </el-date-picker>
              </el-form-item>
              <el-form-item :label="getI18nValue('统计日期')" v-if="sourceChannel == 'total02'">
                <el-date-picker v-model="searchForm.statTime" type="daterange" format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd" start-placeholder="开始时间" end-placeholder="结束时间" clearable
                  :unlink-panels="true" :append-to-body="false">
                </el-date-picker>
              </el-form-item>
              <el-form-item :label="getI18nValue('是否公示')">
                <el-select v-model="searchForm.isPublic">
                  <el-option v-for="(value, key) in is_public_list" :key="key" :label="value" :value="key"></el-option>
                </el-select>
              </el-form-item>
              <template v-if="allentCheck">
                <el-form-item label="字段检查结果" label-width="100px">
                  <el-select v-model="searchForm.fieldCheckNo" placeholder="请选择" collapse-tags multiple clearable>
                    <el-option
                      v-for="(val, key) in dict.fieldCheckResultDict"
                      :key="key"
                      :label="val"
                      :value="key"
                    ></el-option>
                  </el-select>
                </el-form-item>

                 <el-form-item :label="getI18nValue('公示字段检查时间')" label-width="120px">
                   <el-date-picker v-model="searchForm.fieldCheckTime" type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss"
                                  :default-time="['00:00:00', '23:59:59']" start-placeholder="开始时间" end-placeholder="结束时间" clearable
                                  :unlink-panels="true" :append-to-body="false" popper-class="left-popper-430">
                    </el-date-picker>
                 </el-form-item>
              </template> -->
            </template>
            <el-form-item class="btns" label-width="0px">
              <el-button
                type="primary"
                plain
                icon="el-icon-refresh"
                @click="handleReset"
                >重置</el-button
              >
              <el-button
                type="primary"
                icon="el-icon-search"
                @click="getList(1)"
                >搜索</el-button
              >
              <el-button
                type="primary"
                plain
                size="small"
                @click.stop="moreSearch = !moreSearch"
              >
                <img
                  src="/easitline-cdn/vue-yq/static/imgs/filter.png"
                  alt=""
                />高级搜索
              </el-button>
            </el-form-item>
          </el-form>
        </senior-search>
      </div>
      <div class="yq-table">
        <el-table
          ref="table"
          :data="tableData.data"
          style="width: 100%"
          height="100%"
          v-loading="tableData.loading"
          stripe
          border
          row-key="id"
          @select="handleSelect"
        >
          <el-table-column
            label=""
            type="selection"
            reserve-selection
            width="60px"
            fixed
            v-if="
              model === 'check' &&
              (permissions['cx-xty-tariff-delete'] ||
                permissions['cx-xty-tariff-export'])
            "
          ></el-table-column>
          <template v-for="(label, value) in tableConfig">
            <el-table-column
              :key="value"
              :prop="value"
              :label="label"
              :min-width="
                value == 'REPORT_NO'
                  ? 200
                  : value == 'AREA_DESC' || value == 'NAME'
                  ? 400
                  : 180
              "
              :show-overflow-tooltip="value == 'AREA_DESC' ? false : true"
              :fixed="value == 'REPORT_NO' ? true : false"
              v-if="columns.indexOf(value) != -1"
            >
              <template slot-scope="scope">
                <template v-if="value == 'classicTypeOne'">
                  <span>{{ dict.XTY_TARIFF_ONE_TYPE[scope.row[value]] }}</span>
                </template>
                <template v-else-if="value == 'classicTypeTwo'">
                  <span>{{ dict.XTY_TARIFF_TWO_TYPE[scope.row[value]] }}</span>
                </template>
                <template v-else-if="value == 'isTelecom'">
                  <span>{{ dict.XTY_TARIFF_TELECOM[scope.row[value]] }}</span>
                </template>
                <template v-else-if="value == 'tariffAttrType'">
                  <span>{{
                    dict.XTY_TARIFF_CRAWL_TYPE[scope.row[value]]
                  }}</span>
                </template>
                <template v-else-if="value == 'tariffState'">
                  <span>{{ dict.XTY_TARIFF_STATUS[scope.row[value]] }}</span>
                </template>
                <template v-else-if="value == 'reported'">
                  <span>{{
                    dict.XTY_TARIFF_CRAWL_REPORT[scope.row[value]]
                  }}</span>
                </template>
                <template
                  v-else-if="
                    ['versionNos', 'dateIds', 'appearMonths'].includes(value)
                  "
                >
                  {{ scope.row[value].join("，") }}
                </template>
                <template v-else>
                  <span>{{ scope.row[value] }}</span>
                </template>
              </template>
            </el-table-column>
          </template>
          <el-table-column
            prop="opt"
            :label="getI18nValue('操作')"
            :width="180"
            fixed="right"
          >
            <template slot-scope="scope">


              <el-link
                class="custlink"
                @click="handleEdit(scope.row, 'detail')"
                >{{ getI18nValue("详情") }}</el-link
              >
              <el-link class="custlink" @click="handleHistory(scope.row)">{{
                getI18nValue("历史")
              }}</el-link>
              <el-link v-if="model === 'check'" class="custlink" @click="handleOpenCheckDialog(scope.row)">{{ getI18nValue("修正")}}</el-link>

            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          background
          @current-change="onPageChange"
          @size-change="onPageSizeChange"
          :current-page="tableData.pageIndex"
          :page-size="tableData.pageSize"
          :page-sizes="[15, 30, 50, 100]"
          layout="total, prev, pager, next, jumper,sizes"
          :total="tableData.totalRow"
        >
        </el-pagination>
      </div>
    </div>

    <!-- 详情 -->
    <el-drawer
      :title="getI18nValue('资费信息')"
      :visible.sync="configDrawer"
      :size="1200"
      :wrapper-closable="false"
    >
      <div class="drawer-content">
        <tariff-detail :config-form="configForm" :model="model"></tariff-detail>
      </div>
      <div class="drawer-footer">
        <!-- <el-button
          type="primary"
          plain
          @click="handleExportPDF"
          v-if="permissions['cx-xty-tariff-exportPdf']"
          >{{ getI18nValue("导出") }}</el-button
        > -->
        <el-button type="primary" plain @click="configDrawer = false">{{
          getI18nValue("取消")
        }}</el-button>
      </div>
    </el-drawer>

    <!-- 历史 -->
    <time-line :drawer.sync="historyDrawer" :list="historyList"></time-line>
    
    <!-- 修正对话框 -->
    <check-again-dialog
      ref="checkAgainDialogRef"
      :dict="dict.fieldCheckResultDict"
      :dialog.sync="checkAgainDialogVisible"
      :values="checkAgainDialogData"
      @update="getList()"
    ></check-again-dialog>
  </div>
</template>

<script>
module.exports = {
  components: {
    columns: httpVueLoader("../../fillDetail/components/columns.vue"),
    "time-line": httpVueLoader("./TimeLine.vue"),
    "drawer-edit": httpVueLoader("./DetailDrawer.vue"),
    "tariff-detail": httpVueLoader("./Detail.vue"),
    'check-again-dialog': httpVueLoader('./checkAgainDialog.vue'),
  },
  props: {
    model: {
      type: String,
      default: "stat", // stat: 统计; check: 检查
    },
  },
  data() {
    return {
      sourceChannel: "",
      tabIndex: "all",
      moreSearch: false,
      searchForm: {
        tariffNo: "",
        name: "",
        tariffAttr: "",
        entName: "",
        provinceName: "",
        classicTypeOne: "",
        classicTypeTwo: "",
        isTelecom: "",
        reported: "",
        tariffType: "",
        fees: "",
        otherContent: "",
        validPeriod: "",
        channel: "",
        duration: "",
        unsubscribe: "",
        others: "",
        onlineDay: [],
        offlineDay: [],
        versionNos: "",
        appearMonths: "",
        exceedFees: "",
        otherFees: "",
        label_1: '',
        label_2: '',
        tariffState: '',
      },
      configForm: {
        SEQ_NO: "",
        REPORT_NO: "",
        REPORT_OBJ: "",
        APPLICABLE_AREA: "",
        REPORTER: "",
        REPORTER_NAME: "",
        IS_PUBLIC: "",
        REASON_NO_PUBLIC: "",
        TYPE1: "",
        TYPE2: "",
        NAME: "",
        UNSUBSCRIBE: "",
        FEES: "",
        FEES_UNIT: "",
        CALL_NUM: "",
        DATA_NUM: "",
        DATA_UNIT: "",
        SMS_NUM: "",
        INTERNATIONAL_CALL: "",
        INTERNATIONAL_ROAMING_DATA: "",
        INTERNATIONAL_SMS: "",
        ORIENT_TRAFFIC: "",
        ORIENT_TRAFFIC_UNIT: "",
        IPTV: "",
        BANDWIDTH: "",
        RIGHTS: "",
        PLAN: "",
        OTHER_CONTENT: "",
        APPLICABLE_PEOPLE: "",
        VALID_PERIOD: "",
        CHANNEL: "",
        DURATION: "",
        RESPONSIBILITY: "",
        RESTRICTIONS: "",
        STATUS: "",
        TARIFF_ATTR: "",
        TARIFF_ANOTHER_NAME: "",
        STATUS_2: "",
        ONLINE_DAY: "",
        OFFLINE_DAY: "",
        UPDATE_TIME: "",
        OTHERS: "",
        CLEAR_CAUSE: "",
        AGENT_RELEASE: "",
        DEL_ACC: "",
        DEL_TIME: "",
        REASON: "",
        CREATE_ACC: "",
        CREATE_TIME: "",
        AREA_SELECT_TYPE: "",
        APPLICABLE_PROVINCE: "",
        areaTree: "",
        AREA_DESC: "",
        VERSION_NUM: "",
        VERSION_NO: "",
        PROVINCE_NAME: "",
        FIELD_CHECK_RESULT: "",
        FIELD_CHECK_TIME: "",
        IS_TELECOM: "",
        EXTRA_FEES: "",
        OTHER_FEES: "",
      },
      rules: [{ required: true, message: getI18nValue("必填") }],

      reportObj_b: [],

      tableData: {
        pageIndex: 1,
        pageSize: 15,
        totalRow: 0,
        loading: false,
        data: [],
      },
      historyList: [], // 历史列表
      columns: [], // 显示列
      tableConfig: [],
      reporter_list: [],
      options: [],
      options_2: [],
      tree: [],
      provinceCode: "",
      groupType: "", //账号类型：01-省企业，02-集团，03-省管局，04-工信部,
      area_list: {},
      areaSelectType_list: {
        1: "全国",
        2: "指定省市",
        3: "排除省市",
      },
      province_list: [],
      configForm_edit: {},
      configForm_areaTree: [],
      ids: "",
      permissions: {},
      provinceAndGroup: [],
      provinceCascaderProps: {
        value: 'id',
        label: 'name',
        children: 'children',
        emitPath: false,
      },
      configurID: "", //当前详情页的id，用于内部导出

      configDrawer: false, // 资费详情
      historyDrawer: false, // 历史详情
      configDrawer_edit: false, //资费修改

      // 字典
      dict: {
        XTY_TARIFF_ACTION_TYPE: {},
        XTY_TARIFF_ONE_TYPE: {},
        XTY_TARIFF_TWO_TYPE: {},
        XTY_TARIFF_ENT: {},
        XTY_TARIFF_STATUS: {},
        XTY_TARIFF_ATTRIBUTES: {},
        XTY_TARIFF_TELECOM: {},
        XTY_REPORTER_ENT: {},
        XTY_TARIFF_CRAWL_TYPE: {},
        XTY_TARIFF_CRAWL_REPORT: {},
        fieldCheckResultDict: [], // 报送字段检查字典
        versionOptions: [],
      },

      checkResultLoading: false,
      selectedList: [], // 已选择的列表数据
      
      checkAgainDialogVisible: false,
      checkAgainDialogData: {}
    };
  },
  created() {
    this.ent && (this.searchForm.label_2 = this.ent);
  },
  mounted() {
    this.getDict();
    this.getProvinceAndGroup().then(() => {
      this.getParams();
      this.handleGetProvinces();
    });
    this.getTariffRecordFields();
    this.getProvince();
    this.getProvince2();
  },
  methods: {
    handleResetList() {
      this.historyList = [];
    },
    // 列表增删改查 分页查询
    onPageChange: function (page) {
      this.tableData.pageIndex = page;
      this.getList();
    },
    onPageSizeChange: function (size) {
      this.tableData.pageSize = size;
      this.getList();
    },
    handleInput(num) {
      if (num == "1") {
        // 使用正则表达式判断输入是否为正整数
        if (!/^\d+$/.test(this.searchForm.feesBegin)) {
          this.searchForm.feesBegin = this.searchForm.feesBegin.replace(
            /\D/g,
            ""
          );
        }
      }
      if (num == "2") {
        if (!/^\d+$/.test(this.searchForm.feesEnd)) {
          this.searchForm.feesEnd = this.searchForm.feesEnd.replace(/\D/g, "");
        }
      }
    },
    //查询列表
    getList(page) {
      if (this.groupType == "01" && this.tree.length === 0) {
        this.$message.warning("请选择适用地区");
        return;
      }

      this.tableData.loading = true;

      this.tableData.pageIndex = page || this.tableData.pageIndex;
      const data = JSON.parse(JSON.stringify(this.searchForm));

      // data.pageType = "3";
      data.pageIndex = this.tableData.pageIndex;
      data.pageSize = this.tableData.pageSize;
      // data.tariff_attr = this.searchForm.tariff_attr
      //   ? this.searchForm.tariff_attr.length > 0
      //     ? this.searchForm.tariff_attr.join(",")
      //     : ""
      //   : "";
      // data.updateTime = this.searchForm.updateTime
      //   ? this.searchForm.updateTime[0] + "~" + this.searchForm.updateTime[1]
      //   : "";
      // data.createTime = this.searchForm.createTime
      //   ? this.searchForm.createTime[0] + "~" + this.searchForm.createTime[1]
      //   : "";

      // data.fieldCheckTime = this.searchForm.fieldCheckTime
      //   ? this.searchForm.fieldCheckTime[0] +
      //     "~" +
      //     this.searchForm.fieldCheckTime[1]
      //   : "";

      // 跳转过来的需要传的参数
      data.sourceChannel = yq.q("sourceChannel")
        ? decodeURIComponent(yq.q("sourceChannel"))
        : "";
      data.channelDateType = yq.q("channelDateType")
        ? decodeURIComponent(yq.q("channelDateType"))
        : "";
      data.channelDate = yq.q("channelDate")
        ? decodeURIComponent(yq.q("channelDate"))
        : "";
      data.totalStatus = yq.q("totalStatus")
        ? decodeURIComponent(yq.q("totalStatus"))
        : "";
      // data.stType = yq.q("stType") ? decodeURIComponent(yq.q("stType")) : "";
      // data.stime = yq.q("stime") ? decodeURIComponent(yq.q("stime")) : "";
      // data.etime = yq.q("etime") ? decodeURIComponent(yq.q("etime")) : "";

      // if (data.sourceChannel == "total02") {
      //   data.statTime = this.searchForm.statTime
      //     ? this.searchForm.statTime[0] + "~" + this.searchForm.statTime[1]
      //     : "";
      // }

      let label_1 = this.findProvince(data.label_1, 'id')
      data.tariffReporter = label_1 ? (label_1.tariffProvinceCode + data.label_2) : data.label_2

      // if (Array.isArray(data.label_1) && data.label_1.length > 0) {
      //   data.reporter = data.label_1
      //     .map((i) => this.findProvince(i, "id"))
      //     .filter(Boolean)
      //     .map((i) => i.tariffProvinceCode + data.label_2)
      //     .join(",");
      // } else {
      //   data.reporter = data.label_2;
      // }
      // this.provinceCode && (data.provinceId = this.provinceCode);

      // data.fieldCheckNo = (data.fieldCheckNo || []).join(",");

      // delete data.label_1;
      // delete data.label_2;
      // delete data.label_3;
      // delete data.TYPE1;
      // delete data.TYPE2;

 // 获取报送字段检查字典
      yq.remoteCall("/cx-mix-tariff/webcall?action=common.tariffRuleDict").then(
        (res) => {
          if (res.data) {
            this.dict.fieldCheckResultDict = res.data;
          }
        }
      );

      yq.remoteCall(
        "/cx-mix-tariff/webcall?action=tariffCrawlRecord.publicLibRecordList",
        data,
        (res) => {
          // res.rows.forEach((el) => {
          // el.classicTypeOne = this.dict.XTY_TARIFF_ONE_TYPE[el.classicTypeOne];
          // el.classicTypeTwo = this.dict.XTY_TARIFF_TWO_TYPE[el.classicTypeTwo];
          // el.isTelecom = this.dict.XTY_TARIFF_TELECOM[el.isTelecom];
          // el.reported = this.dict.XTY_TARIFF_REPORTED[el.reported]
          // this.reportObj_b.forEach((val) => {
          //   if (el.REPORT_OBJ == val.PINYIN) {
          //     el.REPORT_OBJ = val.PROVINCE_NAME;
          //   }
          // });
          // });

          this.tableData.data = res.rows;
          this.tableData.totalRow = res.total;
          this.$refs.table.doLayout();
          this.tableData.loading = false;
          // if (res.state == 1) {

          // } else {
          //   this.$message.error(res.msg);
          //   this.tableData.loading = false;
          // }
        }
      );
    },

    handleReset: function () {
      // 清空搜索条件
      for (var key in this.searchForm) {
        if (key === "entName" && ["01", "02"].includes(this.groupType)) {
          continue;
        } else if (
          key === "provinceName" &&
          ["01", "03"].includes(this.groupType)
        ) {
          continue;
        } else {
          this.searchForm[key] = "";
        }
      }

      // 清空选择项
      this.selectedList = [];
      this.$refs["table"] && this.$refs["table"]?.clearSelection();

      this.getList();
    },
    handleEdit(item, label) {
      if (label == "detail") {
        this.configDrawer = true;
        this.configurID = item.ID;
        this.configForm = {
          ...item,
          tariffAttr: this.dict.XTY_TARIFF_CRAWL_TYPE[item.tariffAttr],
        };
      } else {
        this.configDrawer_edit = true;
      }
      // const data = { id: item.ID };
      // this.configForm_edit = item;
      // yq.remoteCall(
      //   "/cx-mix-tariff/webcall?action=tariffRecord.feeFillingInfo",
      //   data,
      //   (res) => {
      //     // 49991208 表示永久
      //     res.data.OFFLINE_DAY === "49991208" && (res.data.OFFLINE_DAY = "");
      //     if (
      //       res.data.AREA_SELECT_TYPE == "2" ||
      //       res.data.AREA_SELECT_TYPE == ""
      //     ) {
      //       this.configForm_areaTree = res.areaTree;
      //     }
      //     this.configForm_edit = res.data;
      //     this.configForm_edit.action_type = "M";
      //     this.configForm_edit.isMagUp = "Y";

      //     for (var key in this.configForm) {
      //       this.configForm[key] = res.data[key];
      //     }
      //     if (res.data.REPORTER_NAME) {
      //       this.configForm[
      //         "REPORTER_NAME"
      //       ] = `${res.data.REPORTER_NAME}（${res.data.REPORTER}）`;
      //     }
      //     this.configForm.IS_PUBLIC = this.is_public_list[res.data.IS_PUBLIC];
      //     this.configForm.AREA_SELECT_TYPE =
      //       this.areaSelectType_list[res.data.AREA_SELECT_TYPE];
      //     this.configForm.APPLICABLE_AREA = res.data.APPLICABLE_AREA.split(",");
      //     this.reportObj_b.forEach((val) => {
      //       if (this.configForm.REPORT_OBJ == val.PINYIN) {
      //         this.configForm.REPORT_OBJ = val.PROVINCE_NAME;
      //       }
      //     });
      //     this.configForm.TYPE1 = item.TYPE1;
      //     this.configForm.TYPE2 = item.TYPE2;
      //     this.configForm.STATUS_2 = item.STATUS;
      //     this.configForm.STATUS =
      //       this.dict.XTY_TARIFF_STATUS[this.configForm.STATUS];
      //     this.configForm.TARIFF_ATTR =
      //       this.dict.XTY_TARIFF_ATTRIBUTES[this.configForm.TARIFF_ATTR];
      //     this.configForm.areaTree = res.areaTree;
      //     this.configForm.IS_TELECOM =
      //       this.dict.XTY_TARIFF_TELECOM[item.IS_TELECOM];
      //   }
      // );
    },
    // 删除
    handleDelete(label, item) {
      if (label == "all") {
        var ids = this.$refs["table"].selection.map((item) => item.ID);
        var versionList = this.$refs["table"].selection.map(
          (item) => item.VERSION_NUM
        );
        if (ids.length == 0) {
          this.$message.error(getI18nValue("请选择要删除的备案！"));
          return;
        } else {
          if (versionList.every((i) => i === "V0")) {
            this.ids = ids.join(",");
            this.handleDeleteOk(this.ids);
          } else {
            this.$message.error(getI18nValue("不能选择已修改的备案"));
          }
        }
      } else {
        this.ids = item.ID;
        this.handleDeleteOk(this.ids);
      }
    },
    handleDeleteOk(ids) {
      this.$confirm("是否删除选择的备案?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const data = {
            ids: ids,
          };
          yq.remoteCall(
            "/cx-mix-tariff/servlet/tariff?action=mangerDirectDelInfos",
            data,
            (res) => {
              console.log(res);
              if (res.state == 1) {
                this.$message.success(res.msg);
                this.getList(1);
              } else {
                this.$message.error(res.msg);
              }
            }
          );
        })
        .catch(function () {});
    },
    handleHistory(item) {
      this.historyList = [];
      const data = {
        publicLibId: item.id,
      };

      yq.remoteCall(
        "/cx-mix-tariff/servlet/tariffPublicLib?action=getCrawlRecordsByPublicLibId",
        data,
        (res) => {
          // const mock = {
          //   ID: "324295",
          //   TASK_ID: "1927314034697965570",
          //   PROVINCE_CODE: "340000",
          //   PROVINCE_NAME: "安徽",
          //   ENT_CODE: "2",
          //   ENT_NAME: "中国移动",
          //   NAME: "新入网79元（P库）和包保理信用分期购机36月营销",
          //   TARIFF_NO: "25AH202519",
          //   FEES: "",
          //   FEES_UNIT: "",
          //   CALL: "",
          //   DATA: "0",
          //   DATA_UNIT: "MB",
          //   SMS: "",
          //   ORIENT_TRAFFIC: "0",
          //   ORIENT_TRAFFIC_UNIT: "MB",
          //   IPTV: "—",
          //   BANDWIDTH: "—",
          //   RIGHTS: "—",
          //   INCREMENT_BUSINESS: "",
          //   OTHER_CONTENT:
          //     "1.自办理次月起，客户每月享受主套餐直降16元优惠，同时按月从客户话费账户代为充值等额的和包红包至客户和包账户，以便替客户偿还每月分期款，其中客户享受的月套餐直降额度与每月代充值和包红包金额相等。2.客户需签订全网信用购业务分期协议。3.客户需开通和包支付账户并实名，和包红包为每月1日起开始代充值，如有欠费或者话费账户余额不满足条件，则会导致代充值失败，可能影响客户的分期还款。4.分期还款相关：（1）和包红包代充值及发票开具说明1）客户每月按时足额缴费后，根据约定，安徽移动将从其话费现金账户余额中代转相应费用以和包红包形式充值至客户和包账户（有价卡充值、空中充值、集团统付、未关联营销活动、赠送类话费等余额不支持和包红包代充值，客户可使用办理信用购业务的手机号码发送“和包红包代充值”或“hbdcz”至10086查询当前话费账户中支持和包红包代充值的余额情况），以便替客户偿还信用购业务分期账单。客户可通过和包APP账单或中国移动APP查看和包红包代充值及还款情况。2）代充值和包红包属于我公司代收代付款项，因此无法开具我公司的通信费发票，如客户需开具发票，可联系代充值金额的具体使用商户或单位开具。（2）还款方式说明：1）和包红包代充值自动还款（推荐）：需在每月10日（账单日）前足额缴清其移动套餐费用，以便安徽移动从其话费账户代转相应费用至客户和包账户，用于偿还分期款。2）自行主动偿还分期款：如无法确保在账单日前缴纳满足条件的套餐费用，需在账单日前确保相应代扣款渠道（和包账户或绑定银行卡等）余额充足，或主动通过和包app选择相应账单还款。（3）分期扣款:和包将在每月7-9日从客户办理信用购业务的移动手机号对应的和包账户自动代扣本月分期款。（4）逾期扣款:若每月10日(含)扣款失败的，即发生逾期。和包将从客户授权的账户中进行代扣，优先代扣和包红包，不足的部分将从客户的和包余额或绑定的银行卡进行代扣。仅支持整期付款金额扣款，不支持部分金额扣款。不同模式下的逾期扣款规则如下所示：1）余额宝分期模式下，如在每月7日-25日均扣款失败，根据约定将在当月直接从客户余额宝账户剩余未解冻金额中扣除与“月还款金额”一致的资金。2）花呗先享模式下，如在每月7日-25日均扣款失败，根据约定将形成花呗账单，需要客户自行根据花呗账单还款。3）移动惠享模式下，如在每月7日-25日均扣款失败，根据约定将从客户授权签约的支付宝账户代扣相应费用偿还分期款。5.提前还款或终止合约 如客户仅需要提前还款，可通过和包APP自主发起提前一次性结清剩余分期款并缴纳相应提前结清手续费。如客户需要提前终止套餐合约，请前往移动营业厅办理相应解约手续，并依据约定缴纳相应违约金。6.其他提示（1）客户可通过和包APP账单查看分期合作方、和包签署的服务协议及每月还款金额、账单日、账单等订单信息（2）若客户违约，根据业务受理单及服务协议约定，客户将承担相应的违约责任，并可能会收到电话、短信等不同形式的通知。具体事项以客户签订的相关协议为准。（3）若客户未在办理业务规定时间内取货，则该笔和包信用购业务事宜不成立。",
          //   ONLINE_DAY: "20241212",
          //   OFFLINE_DAY: "20260110",
          //   TARIFF_ATTR: "",
          //   APPLICABLE_PEOPLE:
          //     "安徽移动新入网客户，年龄22岁-65岁，以身份证年龄为准，办理时需绑定银行卡完成实名认证。",
          //   APPLICABLE_AREA: "",
          //   VALID_PERIOD: "36个月",
          //   CHANNEL: "线下",
          //   DURATION: "合约期内应承诺在网。",
          //   UNSUBSCRIBE: "线下营业厅",
          //   RESPONSIBILITY:
          //     "根据营销活动办理约定，甲方承诺在合约期内在网，合约期内如办理退订/降档、过户、预销、销户、退费、停机、营销活动中断等业务视为违约。对于合约期内尚未享受的优惠将不再享受，同时甲方需按合约期内已享受的资源价值缴纳违约金（ 违约金的标准为乙方给予甲方的补贴和优惠金额，包括终端补贴、礼品价值、赠送的话费、已减免的费用等）。合约到期后未退订的业务将按原价收费。",
          //   OTHERS: "",
          //   EXTENDED_FIELDS: "",
          //   DATE_ID: "20250527",
          //   VERSION_NO: "V20250527_2",
          //   CRAWL_URL: "",
          //   COMPARE_TIME: "",
          //   DIFF_FIELDS: "",
          //   TARIFF_PROVINCE_CODE: "AH",
          //   TARIFF_PROVINCE_NAME: "安徽",
          //   CRAWLER_PROVINCE_NAME: "安徽省",
          //   CRAWLER_TYPE: "安徽资费",
          //   CRAWLER_CALLBACK_FILE_NAME: "安徽省_20250528_680637.zip",
          //   CRAWLER_DATA_BELONG_FILE_NAME:
          //     "安徽省_20250528_680637_安徽资费.xlsx",
          //   CREATE_BY: "",
          //   CREATE_TIME: "2025-05-30 10:12:15.0",
          //   UPDATE_BY: "",
          //   CREATE_DEPT: "",
          //   UPDATE_TIME: "2025-05-30 10:12:15.0",
          //   CLASSIC_TYPE_ONE: "",
          //   CLASSIC_TYPE_TWO: "",
          //   TARIFF_TYPE: "营销活动",
          //   PUBLIC_LIB_ID: "82491541474996999381934",
          // };
          // const newData = [...res.data, mock]
          const newData = res.data;
          newData.forEach((element, idx, arr) => {
            if (element.OFFLINE_DAY) {
              element.OFFLINE_DAY = element.OFFLINE_DAY.replace(
                /(\d{4})(\d{2})(\d{2})/,
                "$1年$2月$3日"
              );
            }
            if (element.ONLINE_DAY) {
              element.ONLINE_DAY = element.ONLINE_DAY.replace(
                /(\d{4})(\d{2})(\d{2})/,
                "$1年$2月$3日"
              );
            }

            if (element.DEL_ACC) {
              element.OPTION_CONTENT = "删除";
              element.OPERATION_ACC = element.DEL_ACC;
              element.OPERATION_TIME = element.DEL_TIME;
            } else {
              if (idx === arr.length - 1) {
                element.OPTION_CONTENT = "创建";
                element.OPERATION_ACC = element.CREATE_ACC;
                element.OPERATION_TIME = element.CREATE_TIME;
              } else {
                element.OPTION_CONTENT = "修改";
                element.OPERATION_ACC = element.UPDATE_ACC;
                element.OPERATION_TIME = element.UPDATE_TIME;
              }
            }

            element.SELECT = false;
            element.DISABLE = false;
            element.TYPE1 = this.dict.XTY_TARIFF_ONE_TYPE[element.TYPE1];
            element.TYPE2 = this.dict.XTY_TARIFF_TWO_TYPE[element.TYPE2];
            element.STATUS = this.dict.XTY_TARIFF_STATUS[element.STATUS];
            // this.reportObj.forEach((item) => {
            //   if (item.PROVINCE_CODE == element.PROVINCE) {
            //     element.PROVINCE_NAME = item.PROVINCE_NAME;
            //   }
            // });
            // const array = element.PROVINCE_NAME.split(",");
            // const array2 = element.AREA_NAME.split(",");
            // element.PROVINCE_NAME = this.handleSort([...new Set(array)]);
            // element.AREA_NAME = this.handleSort([...new Set(array2)]);
            // element.IS_PUBLIC = this.is_public_list[element.IS_PUBLIC];
          });
          this.historyList = newData;
        }
      );

      this.historyDrawer = true;
    },
    //排序
    handleSort(arr) {
      arr.sort(function (a, b) {
        return a.localeCompare(b, "zh-Hans-CN", { sensitivity: "accent" });
      });
      return arr.join(",");
    },
    // 确认
    handleConfirm() {
      var ids = this.$refs["table"].selection.map((item) => item.OBJ_ID);
      if (ids.length == 0) {
        this.$message.error(getI18nValue("请选择要确认的数据！"));
        return;
      }

      this.$confirm(getI18nValue("是否确认选中数据？"), getI18nValue("提示"), {
        confirmButtonText: getI18nValue("确定"),
        cancelButtonText: getI18nValue("取消"),
        type: "warning",
      })
        .then(() => {
          var data = { ids: ids };
          yq.remoteCall(
            "/cc-quality/servlet/QcTaskObj?action=ReleaseAll",
            data,
            (result) => {
              if (result.state == 1) {
                this.$message.success(result.msg);
                // this.doSearch()
              } else {
                this.$message.error(result.msg);
              }
            }
          );
        })
        .catch(() => {});
    },

    getProvince() {
      yq.remoteCall(
        "/cx-mix-tariff/webcall?action=common.provinces",
        {},
        (res) => {
          this.reportObj_b = JSON.parse(JSON.stringify(res.data));
          this.reportObj_b.push({
            PROVINCE_NAME: "集团",
            PINYIN: "JT",
          });
        }
      );
    },
    getProvince2() {
      yq.remoteCall(
        "/cx-mix-tariff/webcall?action=common.provincesAndAll",
        {},
        (res) => {
          this.province_list = JSON.parse(JSON.stringify(res.data));
        }
      );
    },

    getDict() {
      yq.daoCall(
        {
          controls: [
            "common.getDict(XTY_TARIFF_ACTION_TYPE)",
            "common.getDict(XTY_TARIFF_ONE_TYPE)",
            "common.getDict(XTY_TARIFF_TWO_TYPE)",
            "common.getDict(XTY_TARIFF_ENT)",
            "common.getDict(XTY_TARIFF_STATUS)",
            "common.getDict(XTY_TARIFF_ATTRIBUTES)",
            "common.getDict(XTY_TARIFF_TELECOM)",
            "common.getDict(XTY_TARIFF_CRAWL_TYPE)",
            "common.getDict(XTY_TARIFF_CRAWL_REPORT)",
          ],
          params: {},
        },
        (data) => {
          this.dict.XTY_TARIFF_ACTION_TYPE =
            data["common.getDict(XTY_TARIFF_ACTION_TYPE)"].data;
          this.dict.XTY_TARIFF_ONE_TYPE =
            data["common.getDict(XTY_TARIFF_ONE_TYPE)"].data;
          this.dict.XTY_TARIFF_TWO_TYPE =
            data["common.getDict(XTY_TARIFF_TWO_TYPE)"].data;
          this.dict.XTY_TARIFF_ENT =
            data["common.getDict(XTY_TARIFF_ENT)"].data;
          this.dict.XTY_TARIFF_STATUS =
            data["common.getDict(XTY_TARIFF_STATUS)"].data;
          this.dict.XTY_TARIFF_ATTRIBUTES =
            data["common.getDict(XTY_TARIFF_ATTRIBUTES)"].data;
          this.dict.XTY_TARIFF_TELECOM =
            data["common.getDict(XTY_TARIFF_TELECOM)"].data;
          this.dict.XTY_TARIFF_CRAWL_TYPE =
            data["common.getDict(XTY_TARIFF_CRAWL_TYPE)"].data;
          this.dict.XTY_TARIFF_CRAWL_REPORT =
            data["common.getDict(XTY_TARIFF_CRAWL_REPORT)"].data;

          if (!yq.q("sourceChannel") && !yq.q("status")) {
            // 默认显示 "资费状态"为"在售"
            this.searchForm.status = ["1"];
          }
        },
        { contextPath: "cx-mix-tariff" }
      );

      yq.remoteCall("/cx-mix-tariff/webcall?action=common.ents", {}, (res) => {
        this.dict.XTY_REPORTER_ENT = res.data;
      });

      // 获取报送字段检查字典
      yq.remoteCall("/cx-mix-tariff/webcall?action=common.tariffRuleDict").then(
        (res) => {
          if (res.data) {
            this.dict.fieldCheckResultDict = res.data;
          }
        }
      );

      yq.remoteCall(
        "/cx-mix-tariff/webcall?action=crawlerTariffData.getVersionList"
      ).then((res) => {
        this.dict.versionOptions = res.data || [];

        if (Array.isArray(res.data) && res.data.length > 0 && !this.searchForm.versionNos) {
          this.searchForm.versionNos = res.data[0]?.VERSION_NO
        }
      });
    },
    // 获取当前是什么账号类型
    getMessage() {
      yq.remoteCall(
        "/cx-mix-tariff/webcall?action=common.queryUserGroupType",
        {},
        (res) => {
          if (res.state == 1) {
            const {
              provinceCode = "",
              groupType = "",
              entCode = "",
            } = res.data;

            this.provinceCode = provinceCode;
            this.groupType = groupType;

            // 企业账号
            if (groupType) {
              if (groupType === "01" && entCode) {
                this.searchForm.entName = [entCode];
                this.searchForm.provinceName = provinceCode;
              } else if (groupType === "03") {
                this.searchForm.provinceName = provinceCode;
              }
            }

            this.getTree();
            this.getList();
          }
        }
      );
    },
    getTree() {
      // 1. 根据省份编码生成树形结构（角色默认适用地区）
      this.setTreeByRole();

      // 2. 从本地存储获取树形结构（下穿），如果有，则覆盖角色默认适用地区
      this.setTreeByStore();

      // 根据tree生成搜索条件（provinceCode, areaCode）
      this.handleCasader();
    },
    setTreeByRole() {
      if (!this.provinceCode) {
        return;
      }
      const provinceNode = this.options.find(
        (item) => item.value === this.provinceCode
      );
      if (provinceNode) {
        this.tree = provinceNode.children.map((child) => [
          provinceNode.value,
          child.value,
        ]);
      }
    },
    setTreeByStore() {
      if (!this.sourceChannel) {
        return;
      }
      let areaTree = localStorage.getItem("areaTree");
      console.log("[areaTree]=============>>", areaTree);
      if (!areaTree) {
        return;
      }
      try {
        this.tree = JSON.parse(areaTree);
      } catch (e) {
        console.error("[解析areaTree失败]:", e);
      }
      localStorage.removeItem("areaTree");
    },
    // 获取table字段名字
    getTariffRecordFields() {
      let url =
        "/cx-mix-tariff/webcall?action=tariffCrawlRecord.crawlRecordFields";
      if (this.model === "check")
        url =
          "/cx-mix-tariff/webcall?action=tariffCrawlRecord.crawlRecordFieldsByCheck";
      yq.remoteCall(url, {}, (res) => {
        if (res.state == 1) {
          this.tableConfig = res?.data || {};
        }
      });
    },
    handlehide(data) {
      this.columns = data;
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      });
    },
    // 导出
    handleExport() {
      const data = { ...this.searchForm };

      const table_columns = this.$refs["table"].store.states.columns.map(
        (item) => {
          return item.property;
        }
      );

      data.fields = table_columns.slice(this.model === 'check' ? 1 : 0, table_columns.length - 1).join(",");

      yq.remoteCall(
        "/cx-mix-tariff/servlet/tariffPublicLib?action=exportCrawl",
        data,
        (res) => {
          this.$message({
            type: res.state == 1 ? "success" : "error",
            message: res.msg,
          });
        }
      );
    },
    handleExportPDF() {
      var id = this.$refs["table"].selection.map((item) => item.ID);
      if (!this.configDrawer) {
        if (id.length == 0) {
          this.$message.error(getI18nValue("请选择要导出的备案！"));
          return;
        } else if (id.length > 1) {
          this.$message.error(getI18nValue("只能导出单个备案"));
          return;
        }
      } else if (this.configurID) {
        id[0] = this.configurID;
      }

      yq.confirm("本信息不代表资费备案信息，仅供参考！", "注意", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          window.open(
            `/cx-mix-tariff/servlet/tariff?action=exportPdf&id=${id[0]}`
          );
        })
        .catch(() => {
          yq.message({
            message: "已取消",
            type: "info",
          });
        });
    },
    // 获取报送主体的省份
    getProvinceAndGroup() {
      return yq
        .remoteCall(
          "/cx-mix-tariff/webcall?action=common.queryTariffProvinceTree"
        )
        .then((res) => {
          if (res.state == 1) {
            this.provinceAndGroup = res.data || [];
          }
        });
    },
    findProvince(value, prop = "provinceCode", unique = true) {
      let find = [];
      function search(children) {
        for (let i = 0; i < children.length; i++) {
          if (children[i][prop] === value) {
            find.push(children[i]);
          }
          if (children[i].children && children[i].children.length > 0) {
            search(children[i].children);
          }
        }
      }
      search(this.provinceAndGroup);
      return unique ? find[find.length - 1] : find;
    },
    //获取省份树结构
    handleGetProvinces() {
      yq.remoteCall(
        "/cx-mix-tariff/webcall?action=common.queryAreaTree",
        {},
        (res) => {
          this.options = res.data;
          this.options_2 = res.data;
          //获取当前是什么账号类型
          this.getMessage();
        }
      );
    },
    handleCasader() {
      const label1 = [];
      const label2 = [];
      this.tree.forEach((item) => {
        label1.push(item[0]);
        label2.push(item[1]);
      });
      this.searchForm.provinceCode = [...new Set(label1)].join(",");
      this.searchForm.areaCode = [...new Set(label2)].join(",");
    },
    // 获取资费统计界面跳转过来传来的参数
    getParams() {
      let sourceChannel = yq.q("sourceChannel");

      if (!sourceChannel) {
        return;
      }

      console.log("[sourceChannel]=============>>", sourceChannel);
      this.sourceChannel = sourceChannel;

      if (sourceChannel == "tariff-report-statistics") {
        let status = decodeURIComponent(yq.q("status"));
        this.searchForm.status = status ? status.split(",") : [];
        let createTime = decodeURIComponent(yq.q("createTime", "")).replaceAll(
          "+",
          " "
        );
        this.searchForm.createTime = createTime ? createTime.split(",") : "";
        let label_1 = decodeURIComponent(yq.q("label_1", "")).split(",");
        this.searchForm.label_1 = label_1
          .map((i) => this.findProvince(i, "tariffProvinceCode"))
          .filter(Boolean)
          .map((i) => i.id);
      } else {
        const keys = Object.keys(this.searchForm);
        keys.forEach((item) => {
          if (yq.q(item)) {
            this.searchForm[item] = decodeURIComponent(yq.q(item))
              ? decodeURIComponent(yq.q(item))
              : "";
          }
        });
        this.searchForm.TYPE1 = this.searchForm.TYPE1
          ? this.searchForm.TYPE1.split(",")
          : "";
        this.searchForm.TYPE2 = this.searchForm.TYPE2
          ? this.searchForm.TYPE2.split(",")
          : "";
        this.searchForm.status = this.searchForm.status
          ? this.searchForm.status.split(",")
          : "";
        if (yq.q("sourceChannel") == "total02") {
          this.searchForm.statTime = this.searchForm.statTime
            ? this.searchForm.statTime.split(",")
            : "";
          this.searchForm.onlineDay = "";
        } else {
          this.searchForm.onlineDay = this.searchForm.onlineDay
          ? Array.isArray(this.searchForm.onlineDay)
            ? this.searchForm.onlineDay
            : this.searchForm.onlineDay.split(",")
          : [];
        }

        this.searchForm.offlineDay = this.searchForm.offlineDay
          ? Array.isArray(this.searchForm.offlineDay)
            ? this.searchForm.offlineDay
            : this.searchForm.offlineDay.split(",")
          : [];

        this.searchForm.tariff_attr = this.searchForm.tariff_attr
          ? this.searchForm.tariff_attr.split(",")
          : "";
        this.searchForm.classicTypeOne = this.searchForm.classicTypeOne
          ? this.searchForm.classicTypeOne.split(",")
          : "";
        this.searchForm.classicTypeTwo = this.searchForm.classicTypeTwo
          ? this.searchForm.classicTypeTwo.split(",")
          : "";
        this.searchForm.tariffState = this.searchForm.tariffState
          ? this.searchForm.tariffState.split(",")
          : "";
        this.searchForm.isTelecom = this.searchForm.isTelecom
          ? this.searchForm.isTelecom.split(",")
          : "";
        this.searchForm.entName = this.searchForm.entName
          ? Array.isArray(this.searchForm.entName)
            ? this.searchForm.entName
            : this.searchForm.entName.split(",")
          : [];

        console.log("获取资费统计界面跳转过来传来的参数", this.searchForm);
      }
    },

    // 比对
    handleCheckResult() {
      this.checkResultLoading = true;

      const data = {
        publicLibIds: this.selectedList.map((i) => i.id),
      };

      yq.remoteCall(
        "/cx-mix-tariff/servlet/tariffPublicLib?action=checkPublicLibReportRelation",
        data
      )
        .then((res) => {
          if (res.state == 1) {
            this.$message.success(res?.msg || "比对成功");
          } else {
            this.$message.error(res?.msg || "比对失败");
          }
        })
        .catch((err) => {
          this.$message.error(err?.msg || "比对失败");
        })
        .finally(() => (this.checkResultLoading = false));
    },
    handleSelect(selection, _) {
      this.selectedList = selection;
    },

    handleOpenCheckDialog(row) {
      this.checkAgainDialogVisible = true;
      this.checkAgainDialogData = row;
      this.$nextTick(() => this.$refs['checkAgainDialogRef'] && this.$refs['checkAgainDialogRef'].init());
    },
  },
};
</script>

<style scoped>
.title {
  font-size: 16px;
  font-weight: bold;
  width: 6%;
  white-space: nowrap;
}

.export span .el-button {
  padding: 7px 16px;
}

/* el-date-picker popper修正 */
#tariff-common.yq-card {
  overflow: auto;
}

#tariff-common.yq-card .card-content {
  overflow: auto;
}

#tariff-common .left-popper-220 {
  left: -220px !important;
}

#tariff-common .left-popper-430 {
  left: -430px !important;
}
</style>
