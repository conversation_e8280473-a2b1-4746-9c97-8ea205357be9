package com.yunqu.xty.inf;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.xty.base.CommonLogger;
import com.yunqu.xty.base.Constants;
import com.yunqu.xty.service.*;
import com.yunqu.xty.utils.ClassTools;
import com.yunqu.xty.utils.XtyModuleRuntimeDataCollector;

import org.apache.log4j.Logger;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;

/**
 * 自动受理任务
 */
public class AutoDutyDealService extends IService {

    public final static String DUTY_AUTO_SERVICE = "XTY_DUTY_AUTO_SERVICE";

    private Logger logger = CommonLogger.getLogger("duty");

    @Override
    public JSONObject invoke(JSONObject param) throws ServiceException {
        AutoDealAppendService appendService = ClassTools.getClass(AutoDealAppendService.class);
        appendService.invoke();
        AutoDealJudgeService judgeService = ClassTools.getClass(AutoDealJudgeService.class);
        judgeService.invoke();
        AutoDealAffirmService affirmService = ClassTools.getClass(AutoDealAffirmService.class);
        affirmService.invoke();
        // 05.07沈岑腾讯会议确认去除超时任务
        //AutoDealApproveService approveService = ClassTools.getClass(AutoDealApproveService.class);
        //approveService.invoke();
        StartDutyService startDutyService = ClassTools.getClass(StartDutyService.class);
        startDutyService.invoke();
        /*// TODO 升级时删除此任务，触发交由申辩配置过期定时任务执行
        GeneratePleadApproveTaskService getDutyService = ClassTools.getClass(GeneratePleadApproveTaskService.class);
        getDutyService.invoke(null);
        GenerateSupplementPleadApproveTaskService generateSupplementPleadApproveTaskService = ClassTools.getClass(GenerateSupplementPleadApproveTaskService.class);
        generateSupplementPleadApproveTaskService.invoke(null);*/

        // 抽查工单判断"提交材料环节"是否超时
        ExaminationOverdueService examinationOverdueService = ClassTools.getClass(ExaminationOverdueService.class);
        examinationOverdueService.executeOverdueCheckTask();
        //上报运行状态
        XtyModuleRuntimeDataCollector.report(Constants.APP_NAME, "AutoDutyDealService", "定责自动处理任务", 25 * 60 * 60);
        return null;
    }

 }
