<!DOCTYPE html>
<html>

<head>
  <title>版本任务列表</title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
  <!-- 基础的 css js 资源 -->
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.4">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.3">
  <link rel="stylesheet" href="./common.css?v=20241104">
  <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
  <script src="/easitline-cdn/vue-yq/libs/httpVueLoader.js"></script>
  <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
  <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
  <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.1"></script>
  <script src="/cc-base/static/js/my_i18n.js?v=202111"></script>
  <script src="/cc-base/static/js/i18n.js?v=1"></script>
  <style>
    .task-status-tag {
      font-weight: bold;
    }
    .task-count-info {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .count-item {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      background-color: #f5f7fa;
    }
  </style>
</head>

<body class="yq-page-full vue-box">
  <div id="versionTaskList" class="flex yq-table-page" v-loading="loading" element-loading-text="加载中..." v-cloak>
    <!-- 页面标题 -->
    <div class="yq-card">
      <div class="card-header">
        <div class="head-title">{{ getI18nValue('版本任务列表') }} - {{ versionNo }}</div>
        <div class="yq-table-control">
          <!-- <el-button type="primary" plain size="small" icon="el-icon-refresh" 
            @click="refreshList">{{getI18nValue('刷新')}}</el-button> -->
        </div>
      </div>
      <div class="card-content">

        <!-- 搜索区域 -->
        <div class="yq-search">
          <el-form :model="searchForm" ref="searchForm" :inline="true" size="small">
            <el-form-item :label="getI18nValue('省份名称')" prop="provinceName">
              <el-input v-model="searchForm.provinceName" :placeholder="getI18nValue('请输入省份名称')" clearable></el-input>
            </el-form-item>
            <el-form-item :label="getI18nValue('运营商')" prop="operatorName">
              <el-input v-model="searchForm.operatorName" :placeholder="getI18nValue('请输入运营商名称')" clearable></el-input>
            </el-form-item>
            <el-form-item :label="getI18nValue('任务状态')" prop="status">
              <el-select v-model="searchForm.status" :placeholder="getI18nValue('请选择任务状态')" clearable>
                <el-option label="待执行" value="0"></el-option>
                <el-option label="执行中" value="1"></el-option>
                <el-option label="执行完成" value="2"></el-option>
                <el-option label="执行失败" value="3"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="search" icon="el-icon-search">{{getI18nValue('查询')}}</el-button>
              <el-button @click="resetSearch" icon="el-icon-refresh">{{getI18nValue('重置')}}</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 表格区域 -->
        <div class="yq-table">
          <el-table ref="table" :data="tableData.data" style="width: 100%" height="100%" v-loading="tableData.loading"
            border stripe>
            <el-table-column prop="PROVINCE_NAME" :label="getI18nValue('省份名称')" min-width="100"></el-table-column>
            <el-table-column prop="OPERATOR_NAME" :label="getI18nValue('运营商')" min-width="100"></el-table-column>
            <el-table-column prop="STATUS" :label="getI18nValue('任务状态')" width="90">
              <template slot-scope="scope">
                <el-tag size="small" :type="getTaskStatusTagType(scope.row.STATUS)" class="task-status-tag">
                  {{formatTaskStatus(scope.row.STATUS)}}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column :label="getI18nValue('资费数量')" min-width="180">
              <template slot-scope="scope">
                <div class="task-count-info">
                  <span class="count-item">本省: {{scope.row.LOCAL_PROVINCE_COUNT || 0}}</span>
                  <span class="count-item">集团: {{scope.row.GROUP_COUNT || 0}}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="RUN_SERVER" :label="getI18nValue('运行服务')" min-width="120"></el-table-column>
            <el-table-column prop="CRAWLER_BEGIN_TIME" :label="getI18nValue('爬取开始时间')" min-width="160">
              <template slot-scope="scope">
                {{formatDateTime(scope.row.CRAWLER_BEGIN_TIME)}}
              </template>
            </el-table-column>
            <el-table-column prop="CRAWLER_ENT_TIME" :label="getI18nValue('爬取结束时间')" min-width="160">
              <template slot-scope="scope">
                {{formatDateTime(scope.row.CRAWLER_ENT_TIME)}}
              </template>
            </el-table-column>
            <el-table-column prop="opt" :label="getI18nValue('操作')" width="100" fixed="right">
              <template slot-scope="scope">
                <el-link v-if="scope.row.STATUS === '2' || scope.row.STATUS === '3'"
                  class="custlink" type="primary" @click="recrawlTask(scope.row)">
                  {{getI18nValue('重新爬取')}}
                </el-link>
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>

  <script>
    var versionTaskList = new Vue({
      el: '#versionTaskList',
      data: function () {
        return {
          loading: false,
          versionNo: '',
          versionId: '',
          searchForm: {
            provinceName: '',
            operatorName: '',
            status: ''
          },
          tableData: {
            loading: false,
            data: []
          }
        }
      },
      mounted: function () {
        this.init()
      },
      methods: {
        // 初始化
        init: function() {
          // 获取URL参数
          const urlParams = new URLSearchParams(window.location.search)
          this.versionNo = urlParams.get('versionNo') || ''
          this.versionId = urlParams.get('versionId') || ''
          
          if (!this.versionNo) {
            this.$message.error('版本号参数缺失')
            return
          }
          
          this.getList()
        },
        // 获取任务列表
        getList: function (page) {
          this.tableData.loading = true
          const data = JSON.parse(JSON.stringify(this.searchForm))
          data.versionNo = this.versionNo
          
          yq.daoCall({
            controls: ["crawlerTariffData.getCrawlerTaskPage"],
            params: data
          }, (res) => {
            if (res && res['crawlerTariffData.getCrawlerTaskPage']) {
              const result = res['crawlerTariffData.getCrawlerTaskPage']
              if (result.state === 1) {
                this.tableData.data = result.data || []
                this.tableData.totalRow = result.totalRow || 0
              } else {
                this.$message.error(result.msg || '获取任务列表失败')
              }
            }
            this.tableData.loading = false
          }, {
            contextPath: '/cx-mix-tariff'
          })
        },
        // 搜索
        search: function() {
          this.getList(1)
        },
        // 重置搜索
        resetSearch: function() {
          this.$refs['searchForm'].resetFields()
          this.getList(1)
        },
        // 刷新列表
        refreshList: function() {
          this.getList()
        },
        // 分页变化
        onPageChange: function(page) {
          this.getList(page)
        },
        onPageSizeChange: function(size) {
          this.tableData.pageSize = size
          this.getList(1)
        },
        // 重新爬取任务
        recrawlTask: function(row) {
          this.$confirm(`确定要重新爬取任务 "${row.ID}" 吗？`, '重新爬取确认', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            yq.remoteCall("/cx-mix-tariff/servlet/crawlerTariffData?action=recrawler", {
              taskId: row.ID
            }, (res) => {
              if (res.state === 1) {
                this.$message.success('重新爬取任务已提交')
                this.getList()
              } else {
                this.$message.error(res.msg || '重新爬取失败')
              }
            })
          }).catch(() => {
            // 用户取消
          })
        },
        // 格式化任务状态
        formatTaskStatus: function(status) {
          const statusMap = {
            0: '待执行',
            1: '执行中', 
            2: '执行完成',
            3: '执行失败'
          }
          return statusMap[status] || '未知'
        },
        // 获取任务状态标签类型
        getTaskStatusTagType: function(status) {
          const typeMap = {
            0: 'info',
            1: 'warning',
            2: 'success', 
            3: 'danger'
          }
          return typeMap[status] || 'info'
        },
        // 格式化时间
        formatDateTime: function(dateTime) {
          if (!dateTime) return '-'
          if (dateTime.includes('.0')) {
            return dateTime.replace('.0', '')
          }
          return dateTime
        }
      }
    })
  </script>
</body>
</html>
