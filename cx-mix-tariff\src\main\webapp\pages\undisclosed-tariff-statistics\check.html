<!DOCTYPE html>
<html>
  <head>
    <title>公示字段检查（全行业）</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"
    />
    <!-- 基础的 css js 资源 -->
    <link
      rel="stylesheet"
      href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css"
    />
    <link
      rel="stylesheet"
      href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.4"
    />
    <link
      rel="stylesheet"
      href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.3"
    />
    <link rel="stylesheet" href="../fillDetail/common.css?v=20241104" />
    <link rel="stylesheet" href="/cx-mix-tariff/static/css/searchForm.css" />
    <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
    <script src="/easitline-cdn/vue-yq/libs/httpVueLoader.js"></script>
    <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
    <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
    <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.1"></script>
    <script src="/cc-base/static/js/my_i18n.js?v=202111"></script>
    <script src="/cc-base/static/js/i18n.js?v=1"></script>
    <script src="/cc-base/static/cdn/axios@0.26.1/axios.min.js"></script>
    <style>
      .el-tabs__nav-wrap::after {
        background-color: #ffffff;
      }
      .el-popover {
        background: black !important;
        color: #ffffff;
      }
    </style>
  </head>

  <body class="yq-page-full vue-box">
    <div id="tariff-statistics-check" class="flex yq-table-page">
      <common model="check"></common>
    </div>
    <script>
      var qualityTask = new Vue({
        el: "#tariff-statistics-check",
        components: {
          common: httpVueLoader("./components/Common.vue?_t=" + Date.now()),
        },
        data: function () {
          return {};
        },
        methods: {},
        mounted() {},
      });
    </script>
  </body>
</html>
