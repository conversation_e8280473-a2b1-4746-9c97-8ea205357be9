package com.yunqu.cc.mixgw.servlet;

import cn.hutool.core.io.FileUtil;
import com.yunqu.cc.mixgw.auth.RSAAuth;
import com.yunqu.cc.mixgw.auth.RSAAuthInterceptor;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.util.SftpUtils;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.core.web.render.Render;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletException;
import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.Part;
import java.io.*;
import java.util.Arrays;
import java.util.HashSet;

/**
 * <p>
 * 稽核任务文件上传Servlet
 * 支持RSA鉴权验证
 * </p>
 *
 * @ClassName TariffAuditTaskFileServlet
 * <AUTHOR> Copy This Tag)
 * @Description 稽核任务文件上传接口，支持RSA签名验证
 * @Since create in 2025/7/23 22:53
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
@RSAAuth(keyName = "default", expireTime = 300L, allowReplay = false, required = false)
@MultipartConfig(maxFileSize = 50 * 1024 * 1024) // 50MB
@WebServlet("/tariff/report/file")
public class TariffReportFileServlet extends HttpServlet {

    private static final Logger logger = LoggerFactory.getLogger(CommonLogger.getLogger("report-file").getName());

    // 支持的文件类型
    private static final HashSet<String> ALLOWED_FILE_TYPES = new HashSet<>(Arrays.asList(
            "pdf", "doc", "docx", "xls", "xlsx", "txt", "zip", "rar", "jpg", "jpeg", "png", "gif"
    ));

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        Render.renderJson(req, resp, EasyResult.fail("暂不支持GET请求"));
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        long startTime = System.currentTimeMillis();
        String requestId = String.valueOf(System.currentTimeMillis());
        String clientIp = getClientIpAddress(req);

        logger.info("[{}] 开始处理稽核任务文件上传请求 - 客户端IP: {}", requestId, clientIp);

        try {
            // 设置请求编码
            req.setCharacterEncoding("UTF-8");
            logger.debug("[{}] 设置请求编码为UTF-8", requestId);

            // RSA鉴权验证
            logger.info("[{}] 开始RSA鉴权验证", requestId);
            if (!RSAAuthInterceptor.preHandle(req, resp, this.getClass())) {
                logger.warn("[{}] RSA鉴权验证失败，请求被拒绝", requestId);
                return;
            }

            // 获取已验证的客户端ID
            String clientId = RSAAuthInterceptor.getAuthenticatedClientId(req);
            logger.info("[{}] RSA鉴权验证成功 - 客户端ID: {}", requestId, clientId);

            // 检查是否为multipart请求
            String contentType = req.getContentType();
            logger.debug("[{}] 检查请求Content-Type: {}", requestId, contentType);
            if (contentType == null || !contentType.toLowerCase().contains("multipart/form-data")) {
                logger.error("[{}] 请求格式错误 - Content-Type: {}, 需要multipart/form-data", requestId, contentType);
                Render.renderJson(req, resp, EasyResult.fail("请求格式错误，需要multipart/form-data"));
                return;
            }

            // 获取上传的文件
            logger.info("[{}] 开始获取上传文件", requestId);
            Part filePart = req.getPart("file");
            if (filePart == null) {
                logger.error("[{}] 未找到上传文件，请求中缺少file参数", requestId);
                Render.renderJson(req, resp, EasyResult.fail("未找到上传文件"));
                return;
            }

            // 获取文件名和大小
            String fileName = getFileName(filePart);
            long fileSize = filePart.getSize();
            logger.info("[{}] 获取文件信息 - 文件名: {}, 文件大小: {} bytes", requestId, fileName, fileSize);

            if (fileName == null || fileName.trim().isEmpty()) {
                logger.error("[{}] 文件名验证失败 - 文件名为空", requestId);
                Render.renderJson(req, resp, EasyResult.fail("文件名不能为空"));
                return;
            }

            if (fileSize <= 0) {
                logger.error("[{}] 文件大小验证失败 - 文件大小: {} bytes", requestId, fileSize);
                Render.renderJson(req, resp, EasyResult.fail("文件内容为空"));
                return;
            }

            // 验证文件类型
            if (!isValidFileType(fileName)) {
                logger.error("[{}] 文件类型验证失败 - 文件名: {}, 支持的类型: {}", requestId, fileName, ALLOWED_FILE_TYPES);
                Render.renderJson(req, resp, EasyResult.fail("不支持的文件类型，仅支持：" + String.join(", ", ALLOWED_FILE_TYPES)));
                return;
            }
            logger.info("[{}] 文件类型验证通过 - 文件扩展名: {}", requestId, FileUtil.extName(fileName));

            // 创建上传目录
            logger.info("[{}] 开始创建本地上传目录", requestId);
            String baseDir = AppContext.getContext(Constants.APP_NAME).getProperty("SFTP_LOCAL_BASE_PATH", "");

            String date = req.getParameter("date");
            logger.info("[{}] 获取请求参数 - date: {}", requestId, date);
            String year = date.substring(0, 4);
            String month = date.substring(4, 6);
            String day = date.substring(6, 8);
            String uploadBaseDir = baseDir + File.separator + "report" + File.separator + year + File.separator + month + File.separator + day;
            logger.debug("[{}] 本地上传目录路径: {}", requestId, uploadBaseDir);

            File uploadDir = new File(uploadBaseDir);
            if (!uploadDir.exists()) {
                boolean mkdirResult = uploadDir.mkdirs();
                logger.info("[{}] 创建目录结果: {} - 路径: {}", requestId, mkdirResult ? "成功" : "失败", uploadBaseDir);
            } else {
                logger.debug("[{}] 目录已存在: {}", requestId, uploadBaseDir);
            }

            // 生成文件路径
            String filePath = uploadBaseDir + File.separator + fileName;
            logger.info("[{}] 生成本地文件路径: {}", requestId, filePath);

            // 保存文件
            logger.info("[{}] 开始保存文件到本地", requestId);
            long saveStartTime = System.currentTimeMillis();
            boolean saveResult = saveUploadedFile(filePart, filePath, requestId);
            long saveEndTime = System.currentTimeMillis();

            if (!saveResult) {
                logger.error("[{}] 文件保存失败 - 路径: {}, 耗时: {}ms", requestId, filePath, saveEndTime - saveStartTime);
                Render.renderJson(req, resp, EasyResult.fail("文件保存失败"));
                return;
            }
            logger.info("[{}] 文件保存成功 - 路径: {}, 耗时: {}ms", requestId, filePath, saveEndTime - saveStartTime);

            // 文件上传SFTP
            logger.info("[{}] 开始上传文件到SFTP服务器", requestId);
            long sftpStartTime = System.currentTimeMillis();
            String remotePath = uploadFileToSFTP(filePath, date, requestId);
            long sftpEndTime = System.currentTimeMillis();

            if (StringUtils.isBlank(remotePath)) {
                logger.error("[{}] SFTP文件上传失败 - 本地路径: {}, 耗时: {}ms", requestId, filePath, sftpEndTime - sftpStartTime);
                Render.renderJson(req, resp, EasyResult.fail("文件上传失败"));
                return;
            }
            logger.info("[{}] SFTP文件上传成功 - 远程路径: {}, 耗时: {}ms", requestId, remotePath, sftpEndTime - sftpStartTime);

            long totalTime = System.currentTimeMillis() - startTime;
            logger.info("[{}] 稽核任务文件上传处理完成 - 日期: {}, 文件名: {}, 文件大小: {} bytes, 远程路径: {}, 客户端ID: {}, 总耗时: {}ms",
                    requestId, date, fileName, fileSize, remotePath, clientId, totalTime);

            Render.renderJson(req, resp, EasyResult.ok(remotePath));
        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            logger.error("[{}] 稽核任务文件上传处理失败 - 客户端IP: {}, 总耗时: {}ms, 异常信息: {}",
                    requestId, clientIp, totalTime, e.getMessage(), e);
            Render.renderJson(req, resp, EasyResult.fail("文件上传失败: " + e.getMessage()));
        }
    }

    /**
     * 获取上传文件的原始文件名
     */
    private String getFileName(Part filePart) {
        String fileName = filePart.getSubmittedFileName();
        if (fileName != null) {
            // 处理可能的编码问题
            try {
                if (isMessyCode(fileName)) {
                    fileName = new String(fileName.getBytes("ISO-8859-1"), "UTF-8");
                }
            } catch (UnsupportedEncodingException e) {
                logger.warn("文件名编码转换失败，使用原始文件名: " + fileName, e);
            }
        }
        return fileName;
    }

    /**
     * 检查字符串是否为乱码
     */
    private boolean isMessyCode(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        // 简单的乱码检测：如果包含大量的问号或特殊字符，可能是乱码
        long questionMarkCount = str.chars().filter(ch -> ch == '?').count();
        return questionMarkCount > str.length() * 0.3;
    }

    /**
     * 验证文件类型是否允许
     */
    private boolean isValidFileType(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return false;
        }
        String extension = FileUtil.extName(fileName).toLowerCase();
        return ALLOWED_FILE_TYPES.contains(extension);
    }

    /**
     * 保存上传的文件
     */
    private boolean saveUploadedFile(Part filePart, String filePath, String requestId) {
        logger.debug("[{}] 开始写入文件流 - 目标路径: {}", requestId, filePath);

        try (InputStream inputStream = filePart.getInputStream();
             FileOutputStream outputStream = new FileOutputStream(filePath)) {

            byte[] buffer = new byte[8192];
            int bytesRead;
            long totalBytesWritten = 0;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
                totalBytesWritten += bytesRead;
            }

            outputStream.flush();
            logger.debug("[{}] 文件流写入完成 - 写入字节数: {}, 路径: {}", requestId, totalBytesWritten, filePath);

            // 验证文件是否成功保存
            File savedFile = new File(filePath);
            if (savedFile.exists() && savedFile.length() > 0) {
                logger.debug("[{}] 文件保存验证成功 - 文件大小: {} bytes", requestId, savedFile.length());
                return true;
            } else {
                logger.error("[{}] 文件保存验证失败 - 文件不存在或大小为0", requestId);
                return false;
            }

        } catch (IOException e) {
            logger.error("[{}] 保存文件IO异常 - 路径: {}, 异常: {}", requestId, filePath, e.getMessage(), e);
            return false;
        } catch (Exception e) {
            logger.error("[{}] 保存文件未知异常 - 路径: {}, 异常: {}", requestId, filePath, e.getMessage(), e);
            return false;
        }
    }


    private String uploadFileToSFTP(String localFilePath, String date, String requestId) {
        String remotePath = "";
        // 获取SFTP连接配置
        String ftpIp = Constants.getSftpIp();
        String ftpPort = Constants.getSftpPort();
        String ftpUser = Constants.getSftpUsername();
        String ftpPwd = Constants.getSftpPassword();
        String ftpBasePath = Constants.REPORT_SFTP_REMOTE_BASE_FOLDER;


        try (SftpUtils sftpUtils = SftpUtils.create(ftpIp, Integer.parseInt(ftpPort), ftpUser, ftpPwd)) {
            if (!sftpUtils.isConnected()) {
                logger.error("[{}] SFTP连接失败", requestId);
                return null;
            }
            remotePath = ftpBasePath + File.separator + date;

            File file = new File(localFilePath);
            String remoteFileName = remotePath + File.separator + file.getName();
            if (sftpUtils.uploadFile(file, remoteFileName)) {
                logger.info("[{}] 文件上传成功 - 本地文件: {}, 远程路径: {}, 文件名: {}, 文件大小: {} bytes",
                        requestId, localFilePath, remotePath, file.getName(), file.length());
                return remotePath;
            }
        } catch (Exception e) {
            logger.error("[{}] 文件上传失败 - 本地文件: {}, 远程路径: {}, 异常: {}",
                    requestId, localFilePath, remotePath, e.getMessage(), e);
        }
        return remotePath;
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 处理多个IP的情况，取第一个非unknown的有效IP
        if (ip != null && ip.contains(",")) {
            String[] ips = ip.split(",");
            for (String strIp : ips) {
                if (!"unknown".equalsIgnoreCase(strIp.trim())) {
                    ip = strIp.trim();
                    break;
                }
            }
        }

        return ip;
    }

}
