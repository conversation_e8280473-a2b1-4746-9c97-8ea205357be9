package com.yunqu.cc.mixgw.listener;
import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.mq.MQBrokerUtil;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.job.ThreadMgr;
import com.yunqu.cc.mixgw.mqclient.ConsumerBroker;
import com.yunqu.cc.mixgw.mqclient.handler.*;


@WebListener()
public class InitListener implements ServletContextListener {

	@Override
	public void contextDestroyed(ServletContextEvent arg0) {
		CommonLogger.logger.info("contextDestroyed----");
		//MQTopicUtil.removeTopic(Constants.getSmsServiceBroker());
		ThreadMgr.getInstance().shutDown();
		MQBrokerUtil.clearBrokerCache();
		CommonLogger.logger.info(CommonUtil.getClassNameAndMethod(this)+" 停止MQ主题消费端"+Constants.getSmsServiceBroker());
	}

	@Override
	public void contextInitialized(ServletContextEvent arg0) {
		CommonLogger.logger.info("contextInitialized----");
		ThreadMgr.getInstance().start();
		ThreadMgr.getInstance().startNewThread(new ConsumerBroker(Constants.getSmsServiceBroker(), new SyncHandler()));
		ThreadMgr.getInstance().startNewThread(new ConsumerBroker(Constants.getTranServiceBroker(), new TranSyncHandler()));
		ThreadMgr.getInstance().startNewThread(new ConsumerBroker(Constants.getTraiffAuditLoadBroker(), new TariffAuditResultHandler()));
		ThreadMgr.getInstance().startNewThread(new ConsumerBroker(Constants.getTraiffBakBroker(), new TariffBakHandler()));
		ThreadMgr.getInstance().startNewThread(new ConsumerBroker(Constants.getTaskBroker(), new TaskHandler()));
		ThreadMgr.getInstance().startNewThread(new ConsumerBroker(Constants.getReqForwardBroker(), new ReqForwardHandler()));
		ThreadMgr.getInstance().startNewThread(new ConsumerBroker(Constants.getTraiffOrderQueryBroker(), new TariffOrderQueryHandler()));
		ThreadMgr.getInstance().startNewThread(new ConsumerBroker(Constants.getTraiffDelMsgBroker(), new TariffDelMsgHandler()));
		ThreadMgr.getInstance().startNewThread(new ConsumerBroker(Constants.getTraiffExeMessageBroker(), new TariffExeMessageHandler()));

		ThreadMgr.getInstance().startNewThread(new ConsumerBroker(Constants.getTraiffExeAuditMessageBroker(), new TariffExeAuditMessageHandler()));

		ThreadMgr.getInstance().startNewThread(new ConsumerBroker(Constants.getPhoneVerifyBroker(), new XtyPhoneVerifyHandler()));
		ThreadMgr.getInstance().startNewThread(new ConsumerBroker(Constants.TARIFF_INFO_ANALYSIS_BROKER, new TariffInfoAnalysisHandler()));
		ThreadMgr.getInstance().startNewThread(new ConsumerBroker(Constants.ALARM_EMAIL_QUEUE, new AlarmSendEmailHandler()));
	}

}
