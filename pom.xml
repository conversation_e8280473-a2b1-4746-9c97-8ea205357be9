<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>com.yunqu.mars</groupId>
        <artifactId>mars-framework</artifactId>
        <version>3.4</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>cc-xty</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <name>cc-xty</name>

    <properties>
        <java-version>1.8</java-version>
        <compiler-version>1.8</compiler-version>
        <project.build.sourceVersion>1.8</project.build.sourceVersion>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <!--   解决maven命令执行时控制台出现中文乱码     -->
        <argLine>-Dfile.encoding=UTF-8</argLine>
    </properties>


    <modules>
        <module>cx-mix-notice</module>
        <module>cx-mix-expand</module>
        <module>cx-mix-report</module>
        <module>cx-mix-public</module>
        <module>cx-mix-tariff</module>
        <module>cx-mix-xty</module>
        <module>cx-mix-commonext</module>
        <module>cx-mix-data</module>
        <module>alarm-mgr</module>
        <module>cc-portal</module>
        <module>easitline-cdn</module>
        <module>cx-mix-alarm</module>
        <module>cx-mix-login</module>
        <module>web-public</module>
        <!--<module>mars-cloud</module>-->
        <module>cx-mix-handle</module>
        <module>cx-mix-data</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.34</version>
        </dependency>
        <dependency>
            <groupId>org.xerial</groupId>
            <artifactId>sqlite-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yunqu.cc</groupId>
            <artifactId>cc-commonext</artifactId>
            <version>2.0</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.yunqu.cc</groupId>
                <artifactId>cc-common-oss</artifactId>
                <version>3.3</version>
            </dependency>
            <dependency>
                <groupId>com.yunqu.cc</groupId>
                <artifactId>cc-commonext</artifactId>
                <version>2.0</version>
            </dependency>
            <dependency>
                <groupId>com.yunqu.mars</groupId>
                <artifactId>cx-mix-commonext</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
