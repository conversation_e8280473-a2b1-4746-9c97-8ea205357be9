package com.yunqu.tariff.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.container.XtyTariffContainer;
import com.yunqu.tariff.service.TariffReportStatSyncService;
import com.yunqu.xty.commonex.util.RedissonUtil;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.slf4j.Logger;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

public class BusiUtil {
    private static Logger logger = CommonLogger.getLogger();
    // 集团
    private static Map<String, String> GEMap = new HashMap<>();
    // 省企业
    private static Map<String, String> PEMap = new HashMap<>();

    /**
     * 获取接口外部的企业编号
     */
    public static String getOutEntCode(String entCode) {
        if (Constants.ENT_TYPE_004.equals(entCode)) {
            return "5";
        }
        return entCode;
    }

    /**
     * 根据企业编号获取企业信息
     */
    public static String getEntByEntCode(String entCode) {
        String entName = null;
        if (Constants.ENT_TYPE_001.equals(entCode)) {
            entName = "电信";
        } else if (Constants.ENT_TYPE_002.equals(entCode)) {
            entName = "移动";
        } else if (Constants.ENT_TYPE_003.equals(entCode)) {
            entName = "联通";
        } else if (Constants.ENT_TYPE_004.equals(entCode) || Constants.ENT_TYPE_005.equals(entCode)) {
            entName = "广电";
        }
        return entName;
    }

    /**
     * 根据部门类型获取企业信息
     */
    public static JSONObject getEnt(String reporter) {
        String reporterType = null;
        String entName = null;
        String ent = getEntCode(reporter);
        if ("5".equals(ent)) {
            ent = Constants.ENT_TYPE_004;
        }
        String reportObj = getReportObj(reporter);
        if ("JT".equals(reportObj)) {
            reporterType = "1";
        } else {
            reporterType = "2";
        }
        if (Constants.ENT_TYPE_001.equals(ent)) {
            ent = Constants.ENT_TYPE_001;
            entName = "电信";
        } else if (Constants.ENT_TYPE_002.equals(ent)) {
            ent = Constants.ENT_TYPE_002;
            entName = "移动";
        } else if (Constants.ENT_TYPE_003.equals(ent)) {
            ent = Constants.ENT_TYPE_003;
            entName = "联通";
        } else if (Constants.ENT_TYPE_004.equals(ent)) {
            ent = Constants.ENT_TYPE_004;
            entName = "广电";
        }
        JSONObject result = new JSONObject();
        result.put("reporterType", reporterType);
        result.put("ent", ent);
        result.put("entName", entName);
        return result;
    }

	public static List<JSONObject> getEnts(Set<String> reporters) {
		return reporters.stream().map(BusiUtil::getEnt).collect(Collectors.toList());
	}

	/**
	 * 根据部门类型获取企业信息
     */
	public static JSONObject getEntByTypeCode(String groupTypeCode) {
		String reporterType = null;
		String ent = null;
		String entName = null;
		String geEntType = GEMap.get(groupTypeCode);
		String peEntType = PEMap.get(groupTypeCode);
		if (StringUtils.isNotBlank(geEntType)) {
			reporterType = "1";
		} else {
			reporterType = "2";
		}
		if (Constants.ENT_TYPE_001.equals(geEntType) || Constants.ENT_TYPE_001.equals(peEntType)) {
			ent = Constants.ENT_TYPE_001;
			entName = "电信";
		} else if (Constants.ENT_TYPE_002.equals(geEntType) || Constants.ENT_TYPE_002.equals(peEntType)) {
			ent = Constants.ENT_TYPE_002;
			entName = "移动";
		} else if (Constants.ENT_TYPE_003.equals(geEntType) || Constants.ENT_TYPE_003.equals(peEntType)) {
			ent = Constants.ENT_TYPE_003;
			entName = "联通";
		} else if (Constants.ENT_TYPE_004.equals(geEntType) || Constants.ENT_TYPE_004.equals(peEntType)) {
			ent = Constants.ENT_TYPE_004;
			entName = "广电";
		}
		JSONObject result = new JSONObject();
		result.put("reporterType", reporterType);
		result.put("ent", ent);
		result.put("entName", entName);
		return result;
	}

	public static Set<String> getReporterByProvinceCode(String provinceCode, String groupTypeCode) {
		Set<String> provincePinYinSimple = getProvincePinYinSimple2(provinceCode);
		if (provincePinYinSimple==null || provincePinYinSimple.isEmpty()) {
            logger.error("通过省份编码[{}]获取不到拼音信息", provinceCode);
            provincePinYinSimple = new HashSet<>(Collections.singleton("JT"));
        }
		JSONObject entJson = getEntByTypeCode(groupTypeCode);
		String entCode = entJson.getString("ent");
		if (StringUtils.isBlank(entCode)) {
            logger.error("通过部门类型编号[{}]获取不到企业信息", groupTypeCode);
			return null;
		}
		entCode = getOutEntCode(entCode);
		String finalEntCode = entCode;
		return provincePinYinSimple.stream().map(o-> o+ finalEntCode).collect(Collectors.toSet());
	}

    /**
     * 获取公示备案号
     */
    public static String getPublicReportNo(String reporter, String date) {
        String provincePinYinSimple = getReportObj(reporter);
        if (StringUtils.isBlank(provincePinYinSimple)) {
            logger.error("通过主体[{}]获取不到拼音信息", reporter);
            return null;
        }
        String entCode = getEntCode(reporter);
        if (StringUtils.isBlank(entCode)) {
            logger.error("通过主体[{}]获取不到企业信息", reporter);
            return null;
        }
        if (StringUtils.isNotBlank(date) && date.length() >= 4) {
            date = date.substring(2, 4);
        }
        String dateYY = StringUtils.isNotBlank(date) ? date : DateUtil.getCurrentDateStr("yy");
//		String dateYY = ;
        int nextSeq = nextSeq(dateYY + provincePinYinSimple + entCode);
        String result = dateYY + provincePinYinSimple + entCode + String.format("%05d", nextSeq);
        return result;
    }

    /**
     * 获取非公示备案号
     */
    public static String getPrivateReportNo(String reporter, String date) {
        return "N" + getPublicReportNo(reporter, date);
    }

    /**
     * 通过省份获取拼音简写
     */
    public static String getProvincePinYinSimple(String provinceCode) {
        try {
            List<JSONObject> provinces = getProvinces();
            Set<String> provincePy = provinces.stream().filter(o -> StringUtils.equals(o.getString("PROVINCE_CODE"), provinceCode)).map(o -> o.getString("TARIFF_PROVINCE_CODE")).collect(Collectors.toSet());
            if (provincePy == null || provincePy.isEmpty()) return null;
            return CollUtil.get(provincePy, 0);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }


    /**
     * 通过省份获取拼音简写
     */
    public static Set<String> getProvincePinYinSimple2(String provinceCode) {
        try {
            List<JSONObject> provinces = getProvinces();
            Set<String> provincePy = provinces.stream().filter(o -> StringUtils.equals(o.getString("PROVINCE_CODE"), provinceCode)).map(o -> o.getString("TARIFF_PROVINCE_CODE")).collect(Collectors.toSet());
            if(provincePy == null || provincePy.isEmpty()) return null;
            return provincePy;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 通过地区编码获取省份编码
     */
    public static String getProvinceByAreaCode(String[] areaCodeArray) {
        try {
            List<JSONObject> provinces = getTariffArea();
            Set<String> provinceCodeSet = provinces.stream().filter(o -> CollUtil.contains(Arrays.asList(areaCodeArray), o.getString("TARIFF_AREA_CODE"))).map(o -> o.getString("PROVINCE_CODE")).collect(Collectors.toSet());
            if (CollUtil.isEmpty(provinceCodeSet)) return null;
            return CollUtil.join(provinceCodeSet, ",");

			/* EasyQuery query = QueryFactory.getWriteQuery();
			EasySQL sql = new EasySQL("select GROUP_CONCAT( DISTINCT t1.PROVINCE_CODE SEPARATOR ',' ) PROVINCE_CODE ");
    		sql.append("from "+Constants.getBusiSchema()+".xty_tariff_area t1");
    		sql.append("where 1=1");
    		sql.appendIn(areaCodeArray, "and t1.TARIFF_AREA_CODE");
    		// sql.append("Y", "and t1.BUSI_ORDER = ?");
    		return query.queryForString(sql.getSQL(), sql.getParams()); */
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }


    /**
     * <AUTHOR>
     * @Description 添加报送数据到ES
     * @Param: [o]
     * @Return: void
     * @Since create in 2024/10/18 16:48
     * @Company 广州云趣信息科技有限公司
     */
    public static void sendToEsForAdd(JSONObject o) throws Exception {
        JSONObject reqParam = new JSONObject();
        reqParam.put("data", o);
        reqParam.put("primary", o.getString("id"));
        reqParam.put("indexName", Constants.XTY_TARIFF_INFO_INDEX);
        reqParam.put("command", Constants.ES_OPERATE_CREATE_DOC);
        ;
        // 请求XTY事件ES操作服务
        IService service = ServiceContext.getService("XTY_EVT_ES_ORDER_OPERATE");
        CommonLogger.logger.info("[addDataToEs]请求soa服务创建es文档参数：{}", reqParam.toJSONString());
        JSONObject result = service.invoke(reqParam);
        CommonLogger.logger.info("[addDataToEs]请求soa服务创建es文档结果:{}", result.toJSONString());
    }


    /**
     * <AUTHOR>
     * @Description 修改报送数据到ES
     * @Param: [o]
     * @Return: void
     * @Since create in 2024/10/18 16:48
     * @Company 广州云趣信息科技有限公司
     */
    public static void sendToEsForUpdate(String id, JSONObject o) throws Exception {
        JSONObject reqParam = new JSONObject();
        reqParam.put("data", o);
        reqParam.put("primary", id);
        reqParam.put("indexName", Constants.XTY_TARIFF_INFO_INDEX);
        reqParam.put("command", Constants.ES_OPERATE_UPDATE_DOC);
        // 请求XTY事件ES操作服务
        IService service = ServiceContext.getService("XTY_EVT_ES_ORDER_OPERATE");
        CommonLogger.logger.info("[addDataToEs]请求soa服务修改es文档参数：{}", reqParam.toJSONString());
        JSONObject result = service.invoke(reqParam);
        CommonLogger.logger.info("[addDataToEs]请求soa服务修改es文档结果:{}", result.toJSONString());
    }


    /**
     * <AUTHOR>
     * @Description 更新es数据
     * @Param: [fileName]
     * @Return: void
     * @Since create in 2024/10/18 17:33
     * @Company 广州云趣信息科技有限公司
     */
    public static void sendToEsForDelete(String reportNo) throws Exception {
        JSONObject json = new JSONObject();
        json.put("query", new JSONObject() {{
            put("term", new JSONObject() {{
                put("reportNo.keyword", reportNo);
            }});
        }});

        JSONObject reqParam = new JSONObject() {{
            put("data", json);
            put("primary", IdUtil.getSnowflakeNextIdStr());
            put("indexName", Constants.XTY_TARIFF_INFO_INDEX);
            put("command", Constants.ES_OPERATE_COMMAND_DELETE_BY_QUERY_DOC);
        }};

        // 请求XTY事件ES操作服务
        IService service = ServiceContext.getService("XTY_EVT_ES_ORDER_OPERATE");
        CommonLogger.logger.info("[analyzeReported]请求soa服务删除es文档参数：{}", reqParam.toJSONString());
        JSONObject result = service.invoke(reqParam);
        CommonLogger.logger.info("[analyzeReported]请求soa服务删除es文档结果:{}", result.toJSONString());
    }


    /**
     * 从备份索引删除资费数据
     */
    public static void deleteTariffFromBakIndex(String reportNo) {
        try {
            JSONObject json = new JSONObject();
            json.put("query", new JSONObject() {{
                put("term", new JSONObject() {{
                    put("REPORT_NO", reportNo);
                }});
            }});

            JSONObject reqParam = new JSONObject() {{
                put("data", json);
                put("primary", IdUtil.getSnowflakeNextIdStr());
                put("indexName", Constants.XTY_TARIFF_BAK_INFO_INDEX);
                put("command", Constants.ES_OPERATE_COMMAND_DELETE_BY_QUERY_DOC);
            }};

            // 请求XTY事件ES操作服务
            IService service = ServiceContext.getService("XTY_EVT_ES_ORDER_OPERATE");
            CommonLogger.logger.info("[analyzeReported]请求soa服务删除es文档参数：{}", reqParam.toJSONString());
            JSONObject result = service.invoke(reqParam);
            CommonLogger.logger.info("[analyzeReported]请求soa服务删除es文档结果:{}", result.toJSONString());

            TariffReportStatSyncService tariffReportStatSyncService = new TariffReportStatSyncService();
            tariffReportStatSyncService.deleteDocumentsByTariffNo(reportNo);
        } catch (Exception e) {
            logger.error("从备份索引删除资费失败, reportNo: {}", reportNo, e);
        }
    }

    /**
     * 通过地区拼音获取省份编码
     */
    public static String getAreaCodeBySimplePinyin(String pinyinSimple) {
        try {
            EasyQuery query = QueryFactory.getWriteQuery();

            EasySQL sql = new EasySQL("select t1.PROVINCE_CODE");
            sql.append("from CC_PROVINCE t1");
            sql.append("where 1=1");
            sql.append(pinyinSimple, "and t1.TARIFF_PROVINCE_CODE = ?", false);
            String provinceCode = query.queryForString(sql.getSQL(), sql.getParams());
            if (StringUtils.isNotBlank(provinceCode)) {
                sql = new EasySQL("select GROUP_CONCAT(t1.TARIFF_AREA_CODE) AREA_CODE");
                sql.append("from " + Constants.getBusiSchema() + ".xty_tariff_area t1");
                sql.append("where 1=1");
                sql.append(provinceCode, "and t1.PROVINCE_CODE = ?", false);
                // sql.append("Y", "and t1.BUSI_ORDER = ?");
                return query.queryForString(sql.getSQL(), sql.getParams());
            }
            return "";
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return "";
        }
    }

    /**
     * 获取地市名称
     */
    public static  String getAreaNameByCode(String areaCode) {
        List<JSONObject> tariffArea = getTariffArea();
        return tariffArea.stream().filter(o -> StringUtils.equals(o.getString("TARIFF_AREA_CODE"), areaCode)).map(o -> o.getString("TARIFF_AREA_NAME")).findFirst().orElse(null);
    }

    /**
     * 获取地市名称
     */
    public static  String getAreaNameByCodes(String areaCodes) {
        List<String> areaCodeList = StrUtil.split(areaCodes, ",");
        return areaCodeList.stream().map(BusiUtil::getAreaNameByCode).collect(Collectors.joining(","));
    }


    private static List<JSONObject> getTariffArea() {
        return XtyTariffContainer.TARIFF_CAFFEINE.get("TARIFF_AREA", k -> getAreas("XTY:TARIFF:AREAS", "select * from " + Constants.getBusiSchema() + ".xty_tariff_area"));
    }

    private static List<JSONObject> getAreas(String cachePrefix,String sql) {
        Set<JSONObject> members = RedissonUtil.smembers(cachePrefix);
        if(members!=null && !members.isEmpty()) return new ArrayList<>(members);

        try {
            List<JSONObject> result = QueryFactory.getReadQuery().queryForList(sql, new Object[]{}, new JSONMapperImpl());
            if(result!=null) RedissonUtil.sadd(cachePrefix, result.toArray());
            return result;
        } catch (SQLException e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    public static String getAreaName(String areaCode) {
        JSONObject area = getArea(areaCode);
        if(area==null) return null;
        return area.getString("TARIFF_AREA_NAME");
    }

//    private static List<JSONObject> getProvinces() throws SQLException {
//        return XtyTariffContainer.TARIFF_CAFFEINE.get("PROVINCES", k->{
//            String sql = "select * from "+Constants.getBusiSchema()+".XTY_TARIFF_PROVINCE";
//            List<JSONObject> jsonObjects = null;
//            try {
//                jsonObjects = QueryFactory.getReadQuery().queryForList(sql, new Object[]{}, new JSONMapperImpl());
//            } catch (SQLException e) {
//                throw new RuntimeException(e);
//            }
//            return jsonObjects;
//        });
//    }

    public static JSONObject getArea(String areaCode) {
        return XtyTariffContainer.TARIFFAREACAFFEINE.get(areaCode, (key)->{
            JSONObject value = RedissonUtil.hget("tariff:area", key);
            if(value!=null) return value;
            try {
                JSONObject jsonObject = QueryFactory.getWriteQuery().queryForRow("select * from " + Constants.getBusiSchema() + ".XTY_TARIFF_AREA where TARIFF_AREA_CODE = ?", new Object[]{key}, new JSONMapperImpl());
                if(jsonObject!=null) RedissonUtil.hset("tariff:area", key, jsonObject);
                return jsonObject;
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
            return null;
        });
    }




    public static List<JSONObject> getProvinces() throws SQLException {
        return XtyTariffContainer.TARIFF_CAFFEINE.get("PROVINCES", (k) -> getAreas("XTY:TARIFF:PROVINCES", "select * from "+Constants.getBusiSchema()+".XTY_TARIFF_PROVINCE"));
    }

    public static String getAreaCodeBytariffProvinceSimple(String tariffProvinceSimple) {
        try {
            List<JSONObject> provinces = getProvinces();
            Set<String> provinceCodes = provinces.stream().filter(o -> StringUtils.equals(o.getString("TARIFF_PROVINCE_CODE"), tariffProvinceSimple)).map(o -> o.getString("PROVINCE_CODE")).collect(Collectors.toSet());
            if (CollUtil.isEmpty(provinceCodes)) return "";
            String provinceByReporter = CollUtil.get(provinceCodes, 0);
            List<JSONObject> tariffArea = getTariffArea();
            Set<String> collect = tariffArea.stream().filter(o -> StringUtils.equals(provinceByReporter, o.getString("PROVINCE_CODE"))).map(o -> o.getString("TARIFF_AREA_CODE")).collect(Collectors.toSet());
            return CollUtil.join(collect, ",");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return "";
        }
    }

    /**
     * 通过省份拼音获取省份名称
     */
    public static String getProvinceNameBySimplePinyin(String pinyinSimple) {
        try {
            List<JSONObject> provinces = getProvinces();
            JSONObject province = provinces.stream().filter(o -> StringUtils.equals(o.getString("TARIFF_PROVINCE_CODE"), pinyinSimple)).findFirst().orElse(null);
            if (province == null) return "";
            return province.getString("PROVINCE_NAME");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return "";
        }
    }

    /**
     * 通过省份拼音获取省份名称-广电
     */
    public static String getProvinceNameBySimplePinyinGd(String pinyinSimple) {
        try {
            List<JSONObject> provinces = getProvinces();
            JSONObject province = provinces.stream().filter(o -> StringUtils.equals(o.getString("TARIFF_PROVINCE_CODE"), pinyinSimple)).findFirst().orElse(null);
            if (province == null) return "";
            return province.getString("TARIFF_PROVINCE_NAME");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return "";
        }
    }

    /**
     * 根据备案主体获取省份编码
     */
    public static String getProvinceByReporter(String reporter) {
        try {
            String reportObj = getReportObj(reporter);
            if ("JT".equals(reportObj)) {
                return null;
            }
            List<JSONObject> provinces = getProvinces();
            JSONObject province = provinces.stream().filter(o -> StringUtils.equals(o.getString("TARIFF_PROVINCE_CODE"), reportObj)).findFirst().orElse(null);
            if (province == null) return "";
            return province.getString("PROVINCE_CODE");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

	/**
	 * 获取部门序号配置信息
     */
	public static List<JSONObject> getDeptIndex(String ent, String provinceCode,String tariffProvinceCode, String schema) {
		try {
			EasyQuery query = QueryFactory.getWriteQuery();
			EasySQL sql = new EasySQL();
			sql.append("select t1.ID,t1.ENT_ID,t1.ENT,t1.PROVINCE_NAME,t1.PROVINCE_CODE, ");
            sql.append("t1.CREATE_DEPT,t1.CREATE_TIME, t1.TARIFF_PROVINCE_CODE PROVINCE_PINYIN ");
            sql.append("from "+schema +".XTY_DEPT_INDEX_CONFIG t1 where 1=1");
            sql.append(ent, "and t1.ENT=?", false);
            if (StringUtils.isNotBlank(provinceCode)) {
                sql.append(provinceCode, "and t1.PROVINCE_CODE=?", false);
            } else {
                sql.append("and (t1.PROVINCE_CODE is null or t1.PROVINCE_CODE='')");
            }
//            sql.append(tariffProvinceCode, "and t1.TARIFF_PROVINCE_CODE=?");
            List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
            return list;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取部门序号配置信息
     */
    public static List<JSONObject> getDeptIndexByTariffProvinceCode(String ent, String tariffProvinceCode, String schema) {
        try {
            EasyQuery query = QueryFactory.getWriteQuery();
            EasySQL sql = new EasySQL();
            sql.append("select t1.ID,t1.ENT_ID,t1.ENT,t1.PROVINCE_NAME,t1.PROVINCE_CODE, ");
            sql.append("t1.CREATE_DEPT,t1.CREATE_TIME, t1.TARIFF_PROVINCE_CODE PROVINCE_PINYIN ");
            sql.append("from "+schema +".XTY_DEPT_INDEX_CONFIG t1 where 1=1");
            sql.append(ent, "and t1.ENT=?", false);
            sql.append(tariffProvinceCode, "and t1.TARIFF_PROVINCE_CODE=?",false);
            List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
            return list;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取user所在资费部门序号配置信息--用于资费填报权限配置
     */
    public static List<JSONObject> getDeptIndexByUserId(String ent, String provinceCode, String schema, String userId) {
        try {
            EasyQuery query = QueryFactory.getWriteQuery();
            EasySQL sql = new EasySQL();
            sql.append("select * from " + schema + ".XTY_DEPT_INDEX_CONFIG where 1=1");
            sql.append("and  id in (select TARIFF_DEPT_ID from " + schema + ".xty_dept_user where 1=1 ");
            sql.append(userId, "and USER_ID = ?  )");
            logger.info("查询的sql为：{}", sql.getFullSq());
            List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
            return list;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取部门序号配置信息
     */
    public static String getDeptName(String ent, String provinceCode, String deptIndex, String schema) {
        try {
            EasyQuery query = QueryFactory.getWriteQuery();
            EasySQL sql = new EasySQL();
            sql.append("select DEPT_NAME from " + schema + ".XTY_DEPT_INDEX_CONFIG where 1=1");
            sql.append(ent, "and ENT=?", false);
            sql.append(deptIndex, "and DEPT_INDEX=?", false);
            if (StringUtils.isNotBlank(provinceCode) && !"JT".equals(provinceCode)) {
                EasySQL sql2 = new EasySQL("SELECT pinyin  FROM cc_province ");
                sql2.append(" WHERE 1=1 ");
                sql2.append(provinceCode, " and TARIFF_PROVINCE_CODE = ? ");
                String provincePinYin = query.queryForString(sql2.getSQL(), sql2.getParams());
                sql.append(provincePinYin, "and PROVINCE_PINYIN=?", false);
            } else {
                sql.append("and (PROVINCE_PINYIN is null or PROVINCE_PINYIN='')");
            }
            logger.info("sqlwei::{}", sql.getFullSq());
            return query.queryForString(sql.getSQL(), sql.getParams());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据部门编码获取部门序号
     */
    public static String getDeptCode(String deptIndex, String provinceCode, String schema) {
        try {
            EasyQuery query = QueryFactory.getWriteQuery();
            EasySQL sql = new EasySQL();
            sql.append("select ENT from " + schema + ".XTY_DEPT_INDEX_CONFIG where 1=1");
            sql.append(deptIndex, "and DEPT_INDEX=?", false);
            if (StringUtils.isNotBlank(provinceCode)) {
                sql.append(provinceCode, "and PROVINCE_CODE=?", false);
            } else {
                sql.append("and (PROVINCE_CODE is null or PROVINCE_CODE='')");
            }
            String deptCode = query.queryForString(sql.getSQL(), sql.getParams());
            return deptCode;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取备案主体名称
     */
    public static String getReporterName(String reporter, String schema) {
        try {
            String reportObj = getReportObj(reporter); // 获取拼音简写

            String provinceName = "JT".equals(reportObj) ? "集团" : getProvinceNameBySimplePinyinGd(reportObj);
            JSONObject entJson = getEnt(reporter);
            String ent = entJson.getString("ent");
            String entName = entJson.getString("entName");

//			String deptIndex = getDeptIndex(reporter);
//			String deptName = getDeptName(ent, reportObj, deptIndex, schema);
            String reporterName = provinceName + entName; // + deptName;
            logger.info("sb报送主体名称：{}", reporterName);
            return reporterName;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }


    /**
     * 格式化资费名称
     */
    public static String formatFeeName(String name) {
        return name
                .replace("\r", "")   // 移除回车符
                .replace("\n", "").trim() ;  // 移除换行符 去掉前后空格
//                .replace("（", "(")   // 中文左括号转英文
//                .replace("）", ")");  // 中文右括号转英文
    }

    /**
     * 获取序号
     */
    public static synchronized int nextSeq(String tableName) {
        EasyQuery query = QueryFactory.getWriteQuery();
        tableName = tableName.toUpperCase();
        String sql = "select SEQ_ID from CC_SEQ  where TABLE_ID = ?";
        int seq = 0;
        try {
            String seqIdStr = query.queryForString(sql, new Object[]{tableName});
            if (StringUtils.isNotBlank(seqIdStr)) {
                seq = Integer.parseInt(seqIdStr);
            }
            if (seq <= 0) {
                sql = "insert into CC_SEQ(TABLE_ID,SEQ_ID) values (?,?)";
                query.execute(sql, new Object[]{tableName, 1});
            }
        } catch (Exception ex) {
            logger.error("nextSeq() error->cause:{}", ex.getMessage(), ex);
        }
        seq = seq + 1;
        sql = "update CC_SEQ set SEQ_ID = ? where TABLE_ID = ?  ";
        try {
            query.executeUpdate(sql, new Object[]{seq, tableName});
        } catch (Exception ex) {
            logger.error("nextSeq() error->cause:{}", ex.getMessage(), ex);
        }
        return seq;
    }

    /**
     * 根据备案主体获取备案者
     */
    public static String getReportObj(String reporter) {
        if (StringUtils.isBlank(reporter)) {
            return null;
        }
        String reportObj = reporter.substring(0, reporter.length() - 1);
        return reportObj;
    }

    public static String getEntCode(String reporter) {
        if (StringUtils.isBlank(reporter)) {
            return null;
        }
        String reportObj = reporter.substring(2, 3);
        return reportObj;
    }

//	public static String getClientId(String reporter) {
//		if (StringUtils.isBlank(reporter)) {
//			return null;
//		}
//		String reportObj = reporter.substring(0, reporter.length()-2);
//		return reportObj;
//	}

//	public static String getDeptIndex(String reporter) {
//		if (StringUtils.isBlank(reporter)) {
//			return null;
//		}
//		String reportObj = reporter.substring(reporter.length()-2);
//		return reportObj;
//	}

    static {
        GEMap.put(Constants.GROUP_TYPE_GE_TELECOM, Constants.ENT_TYPE_001);
        GEMap.put(Constants.GROUP_TYPE_GE_MOBILE, Constants.ENT_TYPE_002);
        GEMap.put(Constants.GROUP_TYPE_GE_UNICOM, Constants.ENT_TYPE_003);
        GEMap.put(Constants.GROUP_TYPE_GE_BROAD, Constants.ENT_TYPE_004);
        // PEMap.put(Constants.GROUP_TYPE_PE, Constants.ENT_TYPE_001);
        PEMap.put(Constants.GROUP_TYPE_PE_TELECOM, Constants.ENT_TYPE_001);
        PEMap.put(Constants.GROUP_TYPE_PE_MOBILE, Constants.ENT_TYPE_002);
        PEMap.put(Constants.GROUP_TYPE_PE_UNICOM, Constants.ENT_TYPE_003);
        PEMap.put(Constants.GROUP_TYPE_PE_BROAD, Constants.ENT_TYPE_004);
        // PEMap.put(Constants.GROUP_TYPE_PE_LOCAL, Constants.ENT_TYPE_001);
    }

    public static JSONObject FieldZhMap = new JSONObject(true);

    static {
        // 字段中文映射
        FieldZhMap.put("SEQ_NO", "序列号");
        FieldZhMap.put("REPORTER_NAME", "报送主体");
        FieldZhMap.put("TYPE1", "一级分类");
        FieldZhMap.put("TYPE2", "二级分类");
        FieldZhMap.put("IS_TELECOM", "是否通信类");
        FieldZhMap.put("NAME", "资费名称");
        FieldZhMap.put("FEES", "资费标准");
        FieldZhMap.put("FEES_UNIT", "资费单位");
        FieldZhMap.put("EXTRA_FEES", "超出资费");
        FieldZhMap.put("OTHER_FEES", "其他费用");
        FieldZhMap.put("CALL_NUM", "语音");
        FieldZhMap.put("DATA_NUM", "通用流量");
        FieldZhMap.put("DATA_UNIT", "流量单位");
        FieldZhMap.put("SMS_NUM", "短信");
        // FieldZhMap.put("INTERNATIONAL_CALL", "国际语音");
        // FieldZhMap.put("INTERNATIONAL_ROAMING_DATA", "国际漫游流量");
        // FieldZhMap.put("INTERNATIONAL_SMS", "国际短信");
        FieldZhMap.put("ORIENT_TRAFFIC", "定向流量");
        FieldZhMap.put("ORIENT_TRAFFIC_UNIT", "定向流量单位");
        FieldZhMap.put("IPTV", "IPTV");
        FieldZhMap.put("BANDWIDTH", "带宽");
        FieldZhMap.put("RIGHTS", "权益");
        FieldZhMap.put("OTHER_CONTENT", "其他服务内容");
        FieldZhMap.put("TARIFF_ATTR", "资费属性");
        FieldZhMap.put("APPLICABLE_PEOPLE", "适用范围");
        FieldZhMap.put("AREA_DESC", "适用地区");
        FieldZhMap.put("VALID_PERIOD", "有效期限");
        FieldZhMap.put("CHANNEL", "销售渠道");
        FieldZhMap.put("DURATION", "在网要求");
        FieldZhMap.put("UNSUBSCRIBE", "退订方式");
        FieldZhMap.put("RESPONSIBILITY", "违约责任");
        FieldZhMap.put("ONLINE_DAY", "上线日期");
        FieldZhMap.put("OFFLINE_DAY", "下线日期");
        FieldZhMap.put("OTHERS", "其他事项");
//		FieldZhMap.put("REPORT_OBJ", "备案者");
//		FieldZhMap.put("REASON_NO_PUBLIC", "不公示原因");
        // FieldZhMap.put("RESTRICTIONS", "限制条件");
        FieldZhMap.put("REPORT_NO", "方案编号");
        FieldZhMap.put("STATUS", "资费状态");
        FieldZhMap.put("ENT_NAME", "企业名称");
        FieldZhMap.put("VERSION_NUM", "版本号");
        FieldZhMap.put("TARIFF_ANOTHER_NAME", "别名");// 别名


        // 修改
        // FieldZhMap.put("PLAN", "存量订购用户服务预案");

        // 删除
//		FieldZhMap.put("REASON", "删除原因");
        FieldZhMap.put("DEL_TIME", "删除时间");
        FieldZhMap.put("DEL_ACC", "删除人账号");
        FieldZhMap.put("CREATE_TIME", "创建时间");
        FieldZhMap.put("CREATE_ACC", "创建人账号");


        // FieldZhMap.put("UPDATE_ACC", "修改人账号"); // 用户要求不展示
        // FieldZhMap.put("PROVINCE_NAME", "适用省份"); // 已没用
        // FieldZhMap.put("APPLICABLE_AREA", "适用地区"); // 已没用
//
//        FieldZhMap.put("FIELD_CHECK_RESULT", "报送字段检查");
//        FieldZhMap.put("FIELD_CHECK_TIME", "报送字段检查时间");
    }


    /**
     * 根据版本号获取爬虫记录表名
     * @param versionNo 版本号
     * @return 爬虫记录表名
     */
    public static String getCrawlRecordTableName(String versionNo) {
        if (StringUtils.isBlank(versionNo)) {
            // 默认返回原表名
            return "xty_tariff_crawl_record";
        }

        // 构造缓存键
        String cacheKey = "CRAWL_CHECK_RUNNING_TABLE_NAME_" + versionNo;
        // 先从缓存获取
        String cachedTableName = RedissonUtil.get(cacheKey);
        if (StringUtils.isNotBlank(cachedTableName)) {
            return cachedTableName;
        }

        try {
            EasySQL sql = new EasySQL();
            sql.append("SELECT VERSION_TABLE_NAME FROM " + Constants.getBusiSchema() + ".xty_crawler_version WHERE 1=1");
            sql.append(versionNo, "AND VERSION_NO = ?");

            EasyQuery query = QueryFactory.getTariffQuery();
            String tableName = query.queryForString(sql.getSQL(), sql.getParams());

            if (StringUtils.isNotBlank(tableName)) {
                // 缓存表名，30分钟有效期
                RedissonUtil.setEx(cacheKey, tableName, 30 * 60);
                return tableName;
            } else {
                logger.info("未找到版本{}对应的表名，使用默认表", versionNo);
                return "xty_tariff_crawl_record";
            }
        } catch (Exception e) {
            logger.error("查询版本{}对应的表名时发生错误: {}", versionNo, e.getMessage(), e);
            return "xty_tariff_crawl_record";
        }
    }

}
