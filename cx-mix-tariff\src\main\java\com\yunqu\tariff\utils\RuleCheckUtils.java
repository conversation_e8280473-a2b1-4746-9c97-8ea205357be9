package com.yunqu.tariff.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.model.tariffReportStat.ProvinceAreaBean;
import com.yunqu.tariff.model.tariffReportStat.TariffReportedStatCell;
import com.yunqu.tariff.model.tariffReportStat.TariffReportedStatParam;
import com.yunqu.tariff.service.TariffReportStatSyncService;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.EasyQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;


/**
 * <p>
 * 资费规则检查工具类
 * </p>
 *
 * @ClassName RuleCheckUtils
 * @Description 资费规则检查工具类，提供各种规则检查方法
 * @Since create in 2024
 * @Version v1.0
 * @Company 广州云趣信息科技有限公司
 */
public class RuleCheckUtils {

    private static final Logger joblogger = LoggerFactory.getLogger(CommonLogger.getcheckJobLogger().getName());

    // 预编译的正则表达式模式缓存
    private static final Map<String, Pattern> REGEX_PATTERN_CACHE = new ConcurrentHashMap<>();

    /**
     * 更新资费报送统计单元并同步到索引
     * @param tariff 资费信息
     * @param checkNo 检查编号
     * @return 是否更新成功
     */
    public static boolean updateTariffReportedStatCellAndSync(JSONObject tariff, String checkNo) {
        try {
            // 构造TariffReportedStatCell数据
            TariffReportedStatCell cell = new TariffReportedStatCell();
            cell.setId(tariff.getString("ID"));
            cell.setTariffNo(tariff.getString("REPORT_NO"));
            cell.setEntCode(tariff.getString("ENT"));
            cell.setBusiOrderId(tariff.getString("BUSI_ORDER_ID"));
            cell.setReporter(tariff.getString("REPORTER"));
            cell.setType1(tariff.getString("TYPE1"));
            cell.setType2(tariff.getString("TYPE2"));
            cell.setIsPublic(tariff.getString("IS_PUBLIC"));
            cell.setIsTelecom(tariff.getString("IS_TELECOM"));
            cell.setStatus(tariff.getString("STATUS"));

            // 设置版本号数组
            String versionNo = tariff.getString("PUBLIC_VERSIONS");

            if (StringUtils.isNotBlank(versionNo)) {
                versionNo = versionNo.replace("[", "").replace("]", "")
                        .replaceAll("\"","");
                String[] versionNoArray = versionNo.split(",");
                cell.setVersionNos(versionNoArray);
            }
            
            // 根据检查结果设置规则检查状态
            String checkNoStr = checkNo;
            if (StringUtils.isNotBlank(checkNoStr)) {
                String[] ruleNos = checkNoStr.split(",");
                for (String ruleNo : ruleNos) {
                    int ruleNoInt = Integer.parseInt(ruleNo.trim());
                    switch (ruleNoInt) {
                        case 1:
                            cell.setTariffNameDuplication(1);
                            break;
                        case 2:
                            cell.setTariffStandardMissing(1);
                            break;
                        case 3:
                            cell.setServiceContentMissing(1);
                            break;
                        case 4:
                            cell.setScopeInconsistent(1);
                            break;
                        case 5:
                            cell.setValidityInconsistent(1);
                            break;
                        case 6:
                            cell.setSalesChannelInconsistent(1);
                            break;
                        case 7:
                            cell.setFreeServiceTerm(1);
                            break;
                        case 8:
                            cell.setUnsubscribeInconsistent(1);
                            break;
                        case 9:
                            cell.setLiabilityInconsistent(1);
                            break;
                        case 10:
                            cell.setOfflineTimeInconsistent(1);
                            break;
                        case 11:
                            cell.setContentVerbose(1);
                            break;
                        case 12:
                            cell.setContentDuplicateOrConflict(1);
                            break;
                        case 13:
                            cell.setDataQuotaInconsistent(1);
                            break;
                        case 14:
                            cell.setAddonPackageRestriction(1);
                            break;
                        case 15:
                            cell.setMarketingCampaignRestriction(1);
                            break;
                        case 16:
                            cell.setPackageRestriction(1);
                            break;
                    }
                }
            }

            // 设置省份区域数据
            List<ProvinceAreaBean> provinceDatas = new ArrayList<>();
            JSONArray areaTreeArray = tariff.getJSONArray("AREA_TREE");
            if (areaTreeArray != null && !areaTreeArray.isEmpty()) {
                for (int i = 0; i < areaTreeArray.size(); i++) {
                    JSONObject provinceNode = areaTreeArray.getJSONObject(i);
                    String provinceCode = provinceNode.getString("value");
                    JSONArray childrenArray = provinceNode.getJSONArray("children");

                    if (StringUtils.isNotBlank(provinceCode) && childrenArray != null && !childrenArray.isEmpty()) {
                        String[] areaCodes = new String[childrenArray.size()];
                        for (int j = 0; j < childrenArray.size(); j++) {
                            JSONObject areaNode = childrenArray.getJSONObject(j);
                            areaCodes[j] = areaNode.getString("value");
                        }
                        provinceDatas.add(ProvinceAreaBean.of(provinceCode, areaCodes));
                    }
                }
            }

            // 构造TariffReportedStatParam参数
            TariffReportedStatParam param = new TariffReportedStatParam(provinceDatas, cell);
            joblogger.info("同步到报送库检查索引的入参：" + param.toString());
            TariffReportStatSyncService tariffReportStatSyncService = new TariffReportStatSyncService();
            try {
                tariffReportStatSyncService.updateDocumentsByTariffNo(param);
                return true;
            } catch (Exception e) {
                joblogger.error("调用tariffReportStatSyncService.updateDocumentsByTariffNo 出现问题：" + e.getMessage(), e);
                return false;
            }
        } catch (Exception e) {
            joblogger.error("更新资费报送统计单元失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取预编译的正则表达式模式
     * @param regex 正则表达式
     * @return 编译后的Pattern对象
     */
    public static Pattern getCompiledPattern(String regex) {
        return REGEX_PATTERN_CACHE.computeIfAbsent(regex, Pattern::compile);
    }

    /**
     * 1. 资费名称重复检查
     * @param tariffId 资费ID
     * @param tariffName 资费名称
     * @param reporter 报告者
     * @return 是否存在问题
     */
    public static boolean checkNameDuplicate(String tariffId, String tariffName, String reporter) {
        joblogger.info("开始检查资费名称重复，资费ID: {}, 资费名称: {}, 报告者: {}", tariffId, tariffName, reporter);

        if (StringUtils.isBlank(tariffName) || StringUtils.isBlank(reporter)) {
            joblogger.info("资费名称或报告者为空，跳过检查，资费ID: {}", tariffId);
            return false;
        }

        try {
            // 构建SQL查询，检查是否存在相同NAME和REPORTER的其他记录
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT COUNT(1) FROM "+ Constants.getBusiSchema()+".xty_tariff_record ");
            sqlBuilder.append("WHERE NAME = ? AND REPORTER = ? AND ID != ? AND STATUS = '1'");

            joblogger.info("执行资费名称重复检查SQL: {}, 参数: [{}, {}, {}]",
                    sqlBuilder.toString(), tariffName, reporter, tariffId);

            // 执行查询
            EasyQuery query = QueryFactory.getWriteQuery();
            int count = query.queryForInt(sqlBuilder.toString(), new Object[]{tariffName, reporter, tariffId});

            joblogger.info("资费名称重复检查结果，资费ID: {}, 重复数量: {}", tariffId, count);

            // 如果存在相同NAME和REPORTER的其他记录，则返回true表示存在重复
            return count > 0;
        } catch (Exception e) {
            joblogger.error("检查资费名称重复时发生错误: {}, 资费ID: {}", e.getMessage(), tariffId, e);
            // 发生异常时，保守返回false
            return false;
        }
    }

    /**
     * 2. 资费标准缺失检查 - 报送库版本
     * @param tariffId 资费ID
     * @param type2 二级分类
     * @param fees 资费标准
     * @return 是否存在问题
     */
    public static boolean checkFeesMissing(String tariffId, String type2, String fees) {
        joblogger.info("开始检查资费标准缺失，资费ID: {}, 二级分类: {}, 资费标准: {}", tariffId, type2, fees);

        boolean result = ("1".equals(type2) || "2".equals(type2)) && StringUtils.isBlank(fees);

        if (result) {
            joblogger.info("资费标准缺失检查结果: 存在问题，资费ID: {}, 二级分类: {}", tariffId, type2);
        } else {
            joblogger.info("资费标准缺失检查结果: 无问题，资费ID: {}", tariffId);
        }

        return result;
    }

    /**
     * 2. 资费标准缺失检查 - 报送库版本（改进版，同时检查资费标准、超出资费、其他费用）
     * @param tariffId 资费ID
     * @param type2 二级分类
     * @param fees 资费标准
     * @param exceedFees 超出资费
     * @param otherFees 其他费用
     * @return 是否存在问题
     */
    public static boolean checkFeesMissingWithAll(String tariffId, String type2, String fees, String exceedFees, String otherFees) {
        joblogger.info("开始检查资费标准缺失（增强版），资费ID: {}, 二级分类: {}, 资费标准: {}, 超出资费: {}, 其他费用: {}",
                tariffId, type2, fees, exceedFees, otherFees);

        // 判断是否为空或为"-"
        boolean isFeesMissing = isBlankOrDash(fees);
        boolean isExceedFeesMissing = isBlankOrDash(exceedFees);
        boolean isOtherFeesMissing = isBlankOrDash(otherFees);

        // 满足套餐或加装包 且 三项都缺失
        boolean result = ("1".equals(type2) || "2".equals(type2))
                && isFeesMissing
                && isExceedFeesMissing
                && isOtherFeesMissing;

        if (result) {
            joblogger.info("资费标准缺失检查结果（增强版）: 存在问题，资费ID: {}, 二级分类: {}", tariffId, type2);
        } else {
            joblogger.info("资费标准缺失检查结果（增强版）: 无问题，资费ID: {}", tariffId);
        }

        return result;
    }

    // 判断空值或为"-"
    private static boolean isBlankOrDash(String value) {
        return StringUtils.isBlank(value) || "-".equals(value.trim());
    }


    /**
     * 3. 服务内容缺失检查
     * @param tariffId 资费ID
     * @param contentFields 要检查的字段内容数组
     * @param configFields 配置的字段数组
     * @param ex2Array 缺失关键词数组（可配置）
     * @return 是否存在问题
     */
    public static boolean checkContentMissing(String tariffId, String[] contentFields, String[] configFields, String[] ex2Array) {
        joblogger.info("开始检查服务内容缺失，资费ID: {}", tariffId);
        joblogger.info("传入的缺失关键词(ex2Array): {}", ex2Array == null ? "null" : java.util.Arrays.toString(ex2Array));
        joblogger.info("传入的内容字段(contentFields): {}", contentFields == null ? "null" : java.util.Arrays.toString(contentFields));
        joblogger.info("传入的配置字段(configFields): {}", configFields == null ? "null" : java.util.Arrays.toString(configFields));

        // 如果内容字段为空，直接返回有问题
        if (contentFields == null || contentFields.length == 0) {
            joblogger.info("服务内容字段为空，资费ID: {}", tariffId);
            return true;
        }

        // 定义视为缺失的关键词
        String[] missingKeywords = ex2Array;
        if (missingKeywords == null || missingKeywords.length == 0) {
            missingKeywords = new String[]{"无", "否", "不包含", "不涉及", "-", "0"};
            joblogger.info("缺失关键词为空，使用默认缺失关键词: {}", java.util.Arrays.toString(missingKeywords));
        } else {
            joblogger.info("实际使用的缺失关键词: {}", java.util.Arrays.toString(missingKeywords));
        }

        // 如果没有配置，使用默认字段
        String[] fields = configFields;
        if (fields == null || fields.length == 0) {
            joblogger.info("使用默认字段列表检查服务内容缺失，资费ID: {}", tariffId);
            // 默认字段索引: 0=CALL_NUM, 1=DATA_NUM, 2=SMS_NUM, 3=ORIENT_TRAFFIC, 4=IPTV, 5=BANDWIDTH, 6=RIGHTS, 7=OTHER_CONTENT
            
            // 检查所有字段是否都缺失
            boolean allMissing = true;
            for (String content : contentFields) {
                // 如果有一个字段不是缺失的，则标记为false
                if (!isFieldMissing(content, missingKeywords)) {
                    allMissing = false;
                    break;
                }
            }
            
            if (allMissing) {
                joblogger.info("服务内容缺失检查结果: 存在问题，资费ID: {}, 所有字段均为缺失", tariffId);
            } else {
                joblogger.info("服务内容缺失检查结果: 无问题，资费ID: {}", tariffId);
            }
            joblogger.info("最终判定结果 allMissing = {}", allMissing);
            return allMissing;
        } else {
            joblogger.info("使用配置字段列表检查服务内容缺失，资费ID: {}, 字段列表长度: {}", tariffId, fields.length);
            
            // 检查是否所有字段都缺失
            boolean allMissing = true;
            
            // 如果内容字段数组长度小于配置字段数组长度，则可能有字段缺失
            if (contentFields.length < fields.length) {
                joblogger.info("内容字段数量小于配置字段数量，资费ID: {}", tariffId);
                // 仍需检查现有字段是否都缺失
                for (String content : contentFields) {
                    if (!isFieldMissing(content, missingKeywords)) {
                        allMissing = false;
                        break;
                    }
                }
            } else {
                // 检查所有字段是否都缺失
                for (String content : contentFields) {
                    if (!isFieldMissing(content, missingKeywords)) {
                        allMissing = false;
                        break;
                    }
                }
            }

            if (allMissing) {
                joblogger.info("服务内容缺失检查结果: 存在问题，资费ID: {}, 所有字段均为缺失", tariffId);
            } else {
                joblogger.info("服务内容缺失检查结果: 无问题，资费ID: {}", tariffId);
            }
            joblogger.info("最终判定结果 allMissing = {}", allMissing);
            return allMissing;
        }
    }
    
    /**
     * 检查字段是否缺失
     * @param fieldValue 字段值
     * @param missingKeywords 视为缺失的关键词数组
     * @return 是否缺失
     */
    /**
     * 检查字段是否缺失
     * @param fieldValue 字段值
     * @param missingKeywords 视为缺失的关键词数组
     * @return 是否缺失
     */
    private static boolean isFieldMissing(String fieldValue, String[] missingKeywords) {
        // 如果字段为空，则视为缺失
        if (StringUtils.isBlank(fieldValue)) {
            return true;
        }

        // 如果字段值等于任一缺失关键词，则视为缺失
        for (String keyword : missingKeywords) {
            if (fieldValue.trim().equals(keyword)) {
                return true;
            }
        }

        return false;
    }


    public static void main(String[] args) {
        System.out.println(isFieldMissing("标准资费：0元/月。当月累计国内主叫语音通话达到一定时长即赠送国内语音包，每月最高可累计获赠100分钟；当月累计通用流量使用达到一定流量即赠送通用流量包，每月最高可累计获赠30GB", new String[]{"无", "否", "不包含", "不涉及", "-", "0"}));
    }

    /**
     * 4. 适用范围填写不规范检查
     * @param tariffId 资费ID
     * @param applicablePeople 适用范围
     * @param regexPatterns 配置的正则表达式数组
     * @return 是否存在问题
     */
    public static boolean checkApplicablePeopleIrregular(String tariffId, String applicablePeople, String[] regexPatterns) {
        joblogger.info("开始检查适用范围填写不规范，资费ID: {}", tariffId);

        if (StringUtils.isBlank(applicablePeople)) {
            joblogger.info("适用范围为空，跳过检查，资费ID: {}", tariffId);
            return false;
        }

        // 如果没有配置，使用默认正则表达式
        String[] patterns = regexPatterns;
        if (patterns == null || patterns.length == 0) {
            patterns = new String[]{
                "指定.*用户", "指定.*客户", "特定.*用户", "特定.*客户", "维系", "邀约", "受邀", "特邀",
                "打标", "目标客户", "目标用户", "重点用户", "重点客户", "精准营销"
            };
            joblogger.info("使用默认正则表达式列表检查适用范围，资费ID: {}", tariffId);
        } else {
            joblogger.info("使用配置正则表达式列表检查适用范围，资费ID: {}, 正则表达式列表长度: {}", tariffId, patterns.length);
        }

        // 使用正则表达式匹配不规范的适用范围描述
        for (String pattern : patterns) {
            try {
                if (getCompiledPattern(pattern).matcher(applicablePeople).find()) {
                    joblogger.info("适用范围填写不规范，资费ID: {}, 适用范围: {}, 匹配正则: {}", tariffId, applicablePeople, pattern);
                    return true;
                }
            } catch (Exception e) {
                joblogger.error("正则表达式匹配失败: {}, 资费ID: {}, 正则表达式: {}", e.getMessage(), tariffId, pattern, e);
            }
        }

        joblogger.info("适用范围填写不规范检查结果: 无问题，资费ID: {}", tariffId);
        return false;
    }

    /**
     * 5. 有效期限填写不规范检查
     * @param validPeriod 有效期限
     * @param irregularKeywords 配置的关键词数组
     * @return 是否存在问题
     */
    public static boolean checkValidPeriodIrregular(String validPeriod, String[] irregularKeywords) {
        if (StringUtils.isBlank(validPeriod)) {
            return false;
        }

        // 如果没有配置，使用默认关键词
        String[] keywords = irregularKeywords;
        if (keywords == null || keywords.length == 0) {
            keywords = new String[]{"长期有效", "2099", "不限", "无限"};
        }

        // 检查是否包含不规范关键词
        for (String keyword : keywords) {
            if (validPeriod.contains(keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 6. 销售渠道填写不规范检查
     * @param channel 销售渠道
     * @param regexPatterns 配置的正则表达式数组
     * @return 是否存在问题
     */
    public static boolean checkChannelIrregular(String channel, String[] regexPatterns) {
        if (StringUtils.isBlank(channel)) {
            return false;
        }

        // 如果没有配置，使用默认正则表达式
        String[] patterns = regexPatterns;
        if (patterns == null || patterns.length == 0) {
            patterns = new String[]{
                "指定.*渠道", "指定.*受理", "指定.*办理", "指定营业厅", "特定.*渠道", "部分.*渠道"
            };
        }

        // 使用正则表达式匹配不规范的渠道描述
        for (String pattern : patterns) {
            try {
                if (getCompiledPattern(pattern).matcher(channel).find()) {
                    return true;
                }
            } catch (Exception e) {
                // 忽略无效的正则表达式
            }
        }

        return false;
    }

    /**
     * 7. 免费业务附加在网期限检查
     * @param type2 二级分类
     * @param fees 资费标准
     * @param duration 在网要求
     * @param validKeywords 配置的有效关键词数组
     * @return 是否存在问题
     */
    public static boolean checkFreeTariffWithDuration(String type2, String fees, String duration, String[] validKeywords) {
        if(StringUtils.isBlank(fees)){
            return false;
        }

        // 条件1: 二级分类为套餐
        boolean isPackage = "1".equals(type2);

        // 条件2: 资费标准为0元或0.0元
        boolean isFree = "0".equals(fees) || "0.0".equals(fees);

        // 条件3: "在网要求"字段不是以下内容
        boolean hasDuration = true;

        if (StringUtils.isNotBlank(duration)) {
            // 如果没有配置，使用默认关键词
            String[] keywords = validKeywords;
            if (keywords == null || keywords.length == 0) {
                keywords = new String[]{"无", "无要求", "无合约期", "无在网要求", "-", "0", "0个月", "不限制", "不涉及"};
            }

            for (String keyword : keywords) {
                if (duration.equals(keyword)) {
                    hasDuration = false;
                    break;
                }
            }
        }

        return isPackage && isFree && hasDuration;
    }

    /**
     * 8. 退订方式填写不规范检查
     * @param unsubscribe 退订方式
     * @param offlineKeywords 线下渠道关键词数组
     * @param onlineKeywords 线上渠道关键词数组
     * @return 是否存在问题
     */
    public static boolean checkUnsubscribeIrregular(String unsubscribe, String[] offlineKeywords, String[] onlineKeywords) {
        if (StringUtils.isBlank(unsubscribe)) {
            return false;
        }

        // 如果包含"退还"，则跳过检查
        if (unsubscribe.contains("退还")) {
            return false;
        }

        // 如果没有配置线下渠道关键词，使用默认关键词
        String[] offlineWords = offlineKeywords;
        if (offlineWords == null || offlineWords.length == 0) {
            offlineWords = new String[]{"10086", "10010", "10000", "10099", "营业厅", "线下", "门店", "热线", "客服"};
        }

        // 如果没有配置线上渠道关键词，使用默认关键词
        String[] onlineWords = onlineKeywords;
        if (onlineWords == null || onlineWords.length == 0) {
            onlineWords = new String[]{"客户端", "公众号", "小程序", "发送", "app", "APP", "线上", "短信", "手厅", "网厅"};
        }

        // 检查是否包含线下渠道
        boolean hasOffline = false;
        for (String keyword : offlineWords) {
            if (unsubscribe.contains(keyword)) {
                hasOffline = true;
                break;
            }
        }

        // 检查是否包含线上渠道
        boolean hasOnline = false;
        for (String keyword : onlineWords) {
            if (unsubscribe.contains(keyword)) {
                hasOnline = true;
                break;
            }
        }

        // 如果同时不包含线下和线上渠道，则为不规范
        return !hasOffline || !hasOnline;
    }

    /**
     * 9. 违约责任填写不规范检查
     * @param tariffId 资费ID
     * @param responsibility 违约责任
     * @param regexPatterns 配置的正则表达式数组
     * @return 是否存在问题
     */
    public static boolean checkResponsibilityIrregular(String tariffId, String responsibility, String[] regexPatterns) {
        joblogger.info("开始检查违约责任填写不规范，资费ID: {}", tariffId);

        if (StringUtils.isBlank(responsibility)) {
            joblogger.info("违约责任为空，跳过检查，资费ID: {}", tariffId);
            return false;
        }

        // 如果没有配置，使用默认正则表达式
        String[] patterns = regexPatterns;
        if (patterns == null || patterns.length == 0) {
            patterns = new String[]{
                "详询100\\d{2}", // 匹配"详询"后跟100开头的4位数字，如"详询10086"
                "按约定赔付"
            };
            joblogger.info("使用默认正则表达式列表检查违约责任，资费ID: {}", tariffId);
        } else {
            joblogger.info("使用配置正则表达式列表检查违约责任，资费ID: {}, 正则表达式列表长度: {}", tariffId, patterns.length);
        }

        // 使用正则表达式匹配不规范的违约责任描述
        for (String pattern : patterns) {
            try {
                if (getCompiledPattern(pattern).matcher(responsibility).find()) {
                    joblogger.info("违约责任填写不规范，资费ID: {}, 违约责任: {}, 匹配正则: {}", tariffId, responsibility, pattern);
                    return true;
                }
            } catch (Exception e) {
                joblogger.error("正则表达式匹配失败: {}, 资费ID: {}, 正则表达式: {}", e.getMessage(), tariffId, pattern, e);
            }
        }

        joblogger.info("违约责任填写不规范检查结果: 无问题，资费ID: {}", tariffId);
        return false;
    }

    /**
     * 10. 下线时间填写不规范检查
     * @param offlineDay 下线时间
     * @param irregularKeywords 配置的关键词数组
     * @return 是否存在问题
     */
    public static boolean checkOfflineDayIrregular(String offlineDay, String[] irregularKeywords) {
        if (StringUtils.isBlank(offlineDay)) {
            return true;
        }

        // 如果没有配置，使用默认关键词
        String[] keywords = irregularKeywords;
        if (keywords == null || keywords.length == 0) {
            keywords = new String[]{"2099", "4999"};
        }

        // 检查是否包含不规范关键词
        for (String keyword : keywords) {
            if (offlineDay.contains(keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 11. 资费方案内容冗长检查
     * @param contentFields 要检查的内容字段数组
     * @return 是否存在问题
     */
    public static boolean checkContentTooLong(String[] contentFields) {
        // 合并所有内容
        StringBuilder contentBuilder = new StringBuilder();
        if (contentFields != null) {
            for (String content : contentFields) {
                if (StringUtils.isNotBlank(content)) {
                    contentBuilder.append(content);
                }
            }
        }

        String content = contentBuilder.toString();

        // 检查内容长度是否超过550字
        return content.length() > 550;
    }

    /**
     * 11. 资费方案内容冗长检查 - 增强版，包含更多字段
     * @param contentFields 要检查的内容字段的Map
     * @return 是否存在问题
     */
    public static boolean checkContentTooLongEnhanced(String[] contentFields) {
        // 合并所有内容
        StringBuilder contentBuilder = new StringBuilder();
        if (contentFields != null) {
            for (String content : contentFields) {
                if (StringUtils.isNotBlank(content)) {
                    contentBuilder.append(content.trim());
                }
            }
        }

        String content = contentBuilder.toString();
        int length = content.length();

        // 记录日志，便于排查问题
        joblogger.info("资费内容总长度为: {} 字符", length);
//        joblogger.info("资费内容:"+content);

        if(length > 550){
            joblogger.info("资费内容:"+content);
//            joblogger.info("资费内容总长度为: {} 字符", length);
        }

        // 检查内容长度是否超过550字
        return length > 550;
    }

    /**
     * 12. 资费方案内容重复或矛盾检查
     * @param tariffId 资费ID
     * @param otherContent 服务内容
     * @param others 其他说明
     * @param checkFieldNames 要检查的字段中文名称数组
     * @return 是否存在问题
     */
    public static boolean checkContentConflict(String tariffId, String otherContent, String others, String[] checkFieldNames) {
        joblogger.info("开始检查资费方案内容重复，资费ID: {}", tariffId);

        // 如果服务内容和其他说明都为空，则不存在问题
        if (StringUtils.isBlank(otherContent) && StringUtils.isBlank(others)) {
            joblogger.info("服务内容和其他说明均为空，跳过检查，资费ID: {}", tariffId);
            return false;
        }

        // 合并服务内容和其他说明
        StringBuilder contentBuilder = new StringBuilder();
        if (StringUtils.isNotBlank(otherContent)) contentBuilder.append(otherContent);
        if (StringUtils.isNotBlank(others)) contentBuilder.append(others);

        String content = contentBuilder.toString();

        if (StringUtils.isBlank(content)) {
            joblogger.info("合并后的内容为空，跳过检查，资费ID: {}", tariffId);
            return false;
        }

        // 如果没有配置，使用默认字段中文名称
        String[] fieldNames = checkFieldNames;
        if (fieldNames == null || fieldNames.length == 0) {
            fieldNames = new String[]{
                "资费标准", "语音", "短信", "流量", "适用范围", "有效期限",
                "销售渠道", "在网要求", "退订方式", "违约责任"
            };
            joblogger.info("使用默认字段中文名称列表检查内容重复，资费ID: {}", tariffId);
        } else {
            joblogger.info("使用配置字段中文名称列表检查内容重复，资费ID: {}, 字段列表长度: {}", tariffId, fieldNames.length);
        }

        // 检查内容中是否包含了指定的字段内容
        for (String fieldName : fieldNames) {
            if (content.contains(fieldName)) {
                joblogger.info("内容中包含字段名称，资费ID: {}, 字段名称: {}", tariffId, fieldName);
                return true;
            }
        }

        joblogger.info("资费方案内容重复检查结果: 无问题，资费ID: {}", tariffId);
        return false;
    }

    /**
     * 13. 流量表述不规范检查
     * @param tariffId 资费ID
     * @param fieldValues 要检查的字段值数组
     * @param irregularKeywords 配置的关键词数组
     * @param ex2Array 优先级关键词数组（可配置）
     * @return 是否存在问题
     */
    public static boolean checkTrafficIrregular(String tariffId, String[] fieldValues, String[] irregularKeywords, String[] ex2Array) {
        joblogger.info("开始检查流量表述不规范，资费ID: {}", tariffId);

        if (fieldValues == null || fieldValues.length == 0) {
            joblogger.info("字段值为空，跳过检查，资费ID: {}", tariffId);
            return false;
        }

        // 合并所有字段内容
        StringBuilder contentBuilder = new StringBuilder();
        for (String value : fieldValues) {
            if (StringUtils.isNotBlank(value)) {
                contentBuilder.append(value);
            }
        }
        String content = contentBuilder.toString();

        if (StringUtils.isBlank(content)) {
            joblogger.info("合并后的内容为空，跳过检查，资费ID: {}", tariffId);
            return false;
        }

        // 流量关键词，从入参获取
        String[] flowKeywords;
        if (irregularKeywords != null && irregularKeywords.length > 0) {
            flowKeywords = irregularKeywords;
            joblogger.info("使用配置的流量关键词列表，关键词数量: {}", flowKeywords.length);
        } else {
            flowKeywords = new String[]{"省内流量", "本地流量", "基站流量", "区域流量"};
            joblogger.info("使用默认流量关键词列表: {}", String.join(", ", flowKeywords));
        }
        
        // 优先级关键词，通过 ex2Array 传入
        String[] priorityMarkers = ex2Array;
        if (priorityMarkers == null || priorityMarkers.length == 0) {
            priorityMarkers = new String[]{"优先级", "抵扣顺序", "使用顺序"};
        }
        joblogger.info("使用优先级关键词列表: {}", String.join(", ", priorityMarkers));

        // 查找第一个优先级关键词的位置
        int priorityPos = content.length();
        for (String marker : priorityMarkers) {
            int pos = content.indexOf(marker);
            if (pos != -1 && pos < priorityPos) {
                priorityPos = pos;
                joblogger.info("找到优先级关键词: {}，位置: {}", marker, pos);
            }
        }

        // 检查在优先级关键词之前是否出现了流量关键词
        String beforePriorityContent = content.substring(0, priorityPos);
        for (String keyword : flowKeywords) {
            if (beforePriorityContent.contains(keyword)) {
                joblogger.info("发现不规范流量表述，资费ID: {}, 流量关键词: {} 出现在优先级关键词之前", tariffId, keyword);
                return true;
            }
        }

        joblogger.info("流量表述不规范检查结果: 无问题，资费ID: {}", tariffId);
        return false;
    }

    /**
     * 14. 加装包附加限制性条件检查
     * @param type2 二级分类
     * @param duration 在网要求
     * @param validKeywords 配置的有效关键词数组
     * @return 是否存在问题
     */
    public static boolean checkAddonWithRestriction(String type2, String duration, String[] validKeywords) {
        // 条件1: 二级分类为加装包
        boolean isAddon = "2".equals(type2);

        if (!isAddon) {
            return false;
        }

        // 条件2: "在网要求"字段不是以下内容
        boolean hasRestriction = true;

        if (StringUtils.isNotBlank(duration)) {
            // 如果没有配置，使用默认关键词
            String[] keywords = validKeywords;
            if (keywords == null || keywords.length == 0) {
                keywords = new String[]{"无", "无要求", "无合约期", "无在网要求", "-", "0", "0个月", "不限制", "不涉及"};
            }

            for (String keyword : keywords) {
                if (duration.equals(keyword)) {
                    hasRestriction = false;
                    break;
                }
            }
        }

        return isAddon && hasRestriction;
    }

    /**
     * 15. 营销活动附加限制性条件检查
     * @param type2 二级分类
     * @param duration 在网要求
     * @param validKeywords 配置的有效关键词数组
     * @return 是否存在问题
     */
    public static boolean checkMarketingWithRestriction(String type2, String duration, String[] validKeywords) {
        // 条件1: 二级分类为营销活动
        boolean isMarketing = "3".equals(type2);

        if (!isMarketing) {
            return false;
        }

        // 条件2: "在网要求"字段不是以下内容
        boolean hasRestriction = true;

        if (StringUtils.isNotBlank(duration)) {
            // 如果没有配置，使用默认关键词
            String[] keywords = validKeywords;
            if (keywords == null || keywords.length == 0) {
                keywords = new String[]{"无", "无要求", "无合约期", "无在网要求", "-", "0", "0个月", "不限制", "不涉及"};
            }

            for (String keyword : keywords) {
                if (duration.equals(keyword)) {
                    hasRestriction = false;
                    break;
                }
            }
        }

        return isMarketing && hasRestriction;
    }

    /**
     * 2. 公示库资费标准缺失检查
     * @param tariffId 资费ID
     * @param type2 二级分类
     * @param fees 资费标准
     * @return 是否存在问题
     */
    public static boolean checkFeesMissingPublic(String tariffId, String type2, String fees) {
        joblogger.info("开始检查公示库资费标准缺失，资费ID: {}, 二级分类: {}, 资费标准: {}", tariffId, type2, fees);

        // 对于公示库，如果没有二级分类则直接返回false
        if (StringUtils.isBlank(type2)) {
            joblogger.info("公示库资费标准缺失检查结果: 无问题（无二级分类），资费ID: {}", tariffId);
            return false;
        }

        boolean result = ("1".equals(type2) || "2".equals(type2)) && isBlankOrDash(fees);

        if (result) {
            joblogger.info("公示库资费标准缺失检查结果: 存在问题，资费ID: {}, 二级分类: {}", tariffId, type2);
        } else {
            joblogger.info("公示库资费标准缺失检查结果: 无问题，资费ID: {}", tariffId);
        }

        return result;
    }

    /**
     * 16. 套餐附加限制性条件检查
     * @param type2 二级分类
     * @param duration 在网要求
     * @param validKeywords 配置的有效关键词数组
     * @return 是否存在问题
     */
    public static boolean checkPackageWithRestriction(String type2, String duration, String[] validKeywords) {
        joblogger.info("开始检查套餐附加限制性条件，二级分类: {}, 在网要求: {}", type2, duration);
        
        // 条件1: 二级分类为套餐
        boolean isPackage = "1".equals(type2);

        if (!isPackage) {
            joblogger.info("非套餐类资费，跳过检查，二级分类: {}", type2);
            return false;
        }

        // 如果在网要求为"0个月"，直接返回false
        if ("0个月".equals(duration)) {
            joblogger.info("在网要求为0个月，无限制条件，返回false");
            return false;
        }

        // 条件2: "在网要求"字段不是以下内容
        boolean hasRestriction = true;

        if (StringUtils.isNotBlank(duration)) {
            // 如果没有配置，使用默认关键词
            String[] keywords = validKeywords;
            if (keywords == null || keywords.length == 0) {
                keywords = new String[]{"无", "无要求", "无合约期", "无在网要求", "-", "0", "0个月", "不限制", "不涉及", "——"};
                joblogger.info("使用默认关键词列表检查套餐在网要求");
            } else {
                joblogger.info("使用配置关键词列表检查套餐在网要求，关键词列表长度: {}", keywords.length);
            }

            for (String keyword : keywords) {
                if (duration.equals(keyword)) {
                    hasRestriction = false;
                    joblogger.info("在网要求匹配关键词: {}, 不存在限制性条件", keyword);
                    break;
                }
            }
        }

        boolean result = isPackage && hasRestriction;
        if (result) {
            joblogger.info("套餐附加限制性条件检查结果: 存在问题，二级分类: {}, 在网要求: {}", type2, duration);
        } else {
            joblogger.info("套餐附加限制性条件检查结果: 无问题，二级分类: {}, 在网要求: {}", type2, duration);
        }
        
        return result;
    }
}
