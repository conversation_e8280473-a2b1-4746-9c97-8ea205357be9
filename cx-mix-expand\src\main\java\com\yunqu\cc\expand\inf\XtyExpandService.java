package com.yunqu.cc.expand.inf;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.IBaseService;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.expand.base.CommonLogger;
import com.yunqu.cc.expand.base.Constants;
import com.yunqu.cc.expand.base.QueryFactory;
import com.yunqu.cc.expand.logger.AppLogger;
import com.yunqu.cc.expand.task.ClearOldDataTask;
import com.yunqu.cc.expand.task.LogClearTask;
import com.yunqu.cc.expand.util.DataOldUtil;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.slf4j.Logger;

import java.util.Date;

/**
 * 信通院扩展包的通用服务接口
 */
public class XtyExpandService extends IBaseService {

	private Logger logger = AppLogger.getLogger();
	
	@Override
	public JSONObject invokeMethod(JSONObject json) throws ServiceException {
		String command = json.getString("command");
		if(StringUtils.equals(command, "dataOld")) {
			return dataOld(json);
		}

		JSONObject result = new JSONObject();
		result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
		result.put("respDesc", "处理失败:不支持的command");
		return result;
	}
	
	/**
	 * 处理过期数据
	 * @param json
	 * @return
	 */
	public JSONObject dataOld(JSONObject json) {
		String key = "IService_run_"+getServiceId()+"_dataOld";

		JSONObject result = new JSONObject();
		try {
			logger.info("开始处理过期数据...");

			if(StringUtils.isNotBlank(CacheUtil.get(key))){
				logger.info("任务正在处理中，本次不处理...");
				result.put("respCode", GWConstants.RET_CODE_SUCCESS);
				result.put("respDesc", "任务正在处理中,本次不处理");
				return result;
			}
			CacheUtil.put(key, DateUtil.getCurrentDateStr());

			if(Constants.isRunSyncOpLog()){
				logger.info("开始清理操作日志");
				new LogClearTask().execute();
				logger.info("清理操作日志结束");
			}else {
				logger.info("未开启清理操作日志");
			}

			CacheUtil.put(key, DateUtil.getCurrentDateStr());

			if("true".equals(Constants.getClearOldData())){
				logger.info("开始清理已结束的工作流数据");
				new ClearOldDataTask().execute();
				logger.info("清理已结束的工作流数据结束");
			}else {
				logger.info("未开启清理已结束的工作流数据");
			}

			CacheUtil.put(key, DateUtil.getCurrentDateStr());

			//删除某张表里过期的数据
			logger.info("开始清理大模型请求日志，删除["+Constants.getDataOldDaysLLMReqLog()+"]天前的请求日志...");
			DataOldUtil.dataOldByCreateTime(QueryFactory.getWriteQuery(), CommonLogger.getDataOldLogger(),"XTY_GC_LLM_REQ","",Constants.getDataOldDaysLLMReqLog());
			logger.info("完成清理大模型请求日志，删除["+Constants.getDataOldDaysLLMReqLog()+"]天前的请求日志...");

			CacheUtil.put(key, DateUtil.getCurrentDateStr());

			//删除某张表里过期的数据
			logger.info("开始清理短信回执记录，删除["+Constants.getDataOldDaysSmsReceipt()+"]天前的记录，同时迁移到历史表...");
			DataOldUtil.dataOldByCreateTime(QueryFactory.getWriteQuery(), CommonLogger.getDataOldLogger(),"C_SMS_RECEIPT","C_SMS_RECEIPT_HIS",Constants.getDataOldDaysSmsReceipt());
			logger.info("完成清理短信回执记录，删除["+Constants.getDataOldDaysSmsReceipt()+"]天前的记录，同时迁移到历史表...");

			CacheUtil.put(key, DateUtil.getCurrentDateStr());

			//清理流程的act_hi_detail表
			logger.info("开始清空 act_hi_detail");
			getQuery().execute("TRUNCATE TABLE act_hi_detail");
			logger.info("完成清空 act_hi_detail");

			CacheUtil.put(key, DateUtil.getCurrentDateStr());
			logger.info("结束处理过期数据...");

			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			result.put("respDesc", "处理成功");
			return result;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}finally {
			CacheUtil.delete(key);
		}
		result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
		result.put("respDesc", "处理失败");
		return result;
	}


	public EasyQuery getQuery() {
		return QueryFactory.getWriteQuery();
	}

	@Override
	public String getServiceId() {
		return "XTY_EXPAND_SERVICE";
	}

	@Override
	public String getName() {
		return "信通院扩展包通用服务接口";
	}
	
}
