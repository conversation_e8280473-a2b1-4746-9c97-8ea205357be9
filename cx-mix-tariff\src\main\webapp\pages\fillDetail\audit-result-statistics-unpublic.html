<!DOCTYPE html>
<html>
  <head>
    <title>省内未公示资费数量统计</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"
    />
    <!-- 基础的 css js 资源 -->
    <link
      rel="stylesheet"
      href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css"
    />
    <link
      rel="stylesheet"
      href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.4"
    />
    <link
      rel="stylesheet"
      href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.3"
    />
    <link rel="stylesheet" href="./common.css?v=20241127" />
    <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
    <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
    <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
    <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.1"></script>
    <script src="/cc-base/static/js/my_i18n.js?v=202111"></script>
    <script src="/cc-base/static/js/i18n.js?v=1"></script>
    <script src="/cc-base/static/cdn/axios@0.26.1/axios.min.js"></script>

    <style>
      .el-tabs__nav-wrap::after {
        background-color: #ffffff;
      }

      .export {
        display: flex;
        white-space: nowrap;
      }

      .card-content {
        overflow: auto !important;
      }

      .el-table__cell.self-defined {
        border-left: 1px solid #e1e1e1 !important;
      }

      .yq-table-page .yq-card .search-form {
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 16px 4px !important;
      }

      .yq-table-page .yq-card .search-form .btns {
        grid-column: 3;
      }
    </style>
  </head>

  <body class="yq-page-full vue-box">
    <div
      id="unPublic-statistics"
      class="flex yq-table-page"
      v-cloak
      v-auth:[permissions]="'cx-xty-audit-statistics-province;cx-xty-audit-statistics-ct;cx-xty-audit-statistics-cm;cx-xty-audit-statistics-un;cx-xty-audit-statistics-cbn;cx-xty-tariff-audit-export;cx-xty-audit-unpublic;'"
    >
      <div class="yq-card">
        <div class="card-header">
          <div class="head-title">省内未公示资费数量统计【{{subTitle}}】</div>
          <!-- <el-popover placement="bottom" title="温馨提示" trigger="hover">
          <p class="tip-item"><span class="tip-item_name">未报送资费数：</span>指有用户订购但未报送的资费方案数量。</p>
          <p class="tip-item"><span class="tip-item_name">资费报送率：</span>指活跃在售和续订资费方案数占用户订购的资费方案总数比例。</p>
          <div slot="reference" class="el-icon-info" style="margin-left: 8px; font-size: 16px;"></div>
        </el-popover> -->
          <div class="yq-table-control">
            <el-radio-group v-model="tableName_province" @input="handleRadio">
              <el-radio-button label="day">日报</el-radio-button>
              <el-radio-button label="month">月报</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="card-content">
          <div class="search-box">
            <el-form
              :inline="false"
              :model="searchForm"
              ref="form"
              class="search-form"
              label-width="70px"
              size="small"
            >
              <el-form-item :label="getI18nValue('出现日期')" prop="date">
                <el-date-picker
                  v-if="tableName_province==='day'"
                  key="day"
                  v-model="searchForm.date"
                  type="daterange"
                  align="right"
                  unlink-panels
                  range-separator="~"
                  :start-placeholder="getI18nValue('开始日期')"
                  :end-placeholder="getI18nValue('结束日期')"
                  value-format="yyyyMMdd"
                  clearable
                >
                </el-date-picker>
                <el-date-picker
                  v-if="tableName_province==='month'"
                  key="month"
                  v-model="searchForm.date"
                  type="month"
                  value-format="yyyyMM"
                  :clearable="false"
                  placeholder="选择月"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="订购省份" prop="provinceCode">
                <el-select
                  v-model="searchForm.provinceCode"
                  placeholder="请选择"
                  filterable
                  clearable
                  :disabled="(groupType=='03' || groupType=='01') ? true:false"
                >
                  <el-option
                    v-for="(item, index) in reportObj"
                    :key="index"
                    :label="item.PROVINCE_NAME"
                    :value="item.PROVINCE_CODE"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item class="btns" label-width="50px">
                <el-button
                  type="primary"
                  plain
                  size="small"
                  icon="el-icon-refresh"
                  @click="handleReset"
                  >{{getI18nValue('重置')}}</el-button
                >
                <el-button
                  type="primary"
                  size="small"
                  icon="el-icon-search"
                  @click="getList"
                  >{{getI18nValue('搜索')}}</el-button
                >
                <el-button
                  type="primary"
                  plain
                  size="small"
                  @click="handleExportFront($refs.table.$el, {title:'省内未报送/未公示资费数量统计'+getDateStamp()})"
                  v-if="permissions['cx-xty-tariff-audit-export']"
                >
                  <i class="el-icon-download"></i>导出
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <!-- <el-tabs v-model="activeName" style="margin-bottom: 24px;">
            <el-tab-pane :label="getI18nValue('未报送资费')" name="0">
              <div id="myChart1" style="width: 100%;height: 300px">
              </div>
            </el-tab-pane>
            <el-tab-pane :label="getI18nValue('资费报送率')" name="1">
              <div id="myChart2" style="width: 100%;height: 300px">
              </div>
            </el-tab-pane>
          </el-tabs> -->
          <div>
            <el-table
              ref="table"
              :data="tableData.data"
              style="width: 100%"
              v-loading="tableData.loading"
              stripe
            >
              <el-table-column
                prop="DATE_ID"
                align="center"
                width="160"
              ></el-table-column>
              <el-table-column
                v-if="!ent || ent==1"
                prop="TELECOM"
                :label="getI18nValue('电信')"
                align="center"
              >
                <el-table-column
                  class-name="self-defined"
                  prop="TELECOM"
                  :label="getI18nValue('未公示资费数')"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-link
                      v-if="permissions['cx-xty-audit-unpublic'] && tableName_province==='day'"
                      :underline="false"
                      @click="handleToPlan(scope.row,'1')"
                    >
                      {{scope.row.TELECOM?scope.row.TELECOM:'0'}}
                    </el-link>
                    <span v-else
                      >{{scope.row.TELECOM?scope.row.TELECOM:'0'}}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                  prop="TELECOM_RATE"
                  :label="getI18nValue('资费公示率')"
                  align="center"
                >
                  <template slot-scope="scope">
                    {{scope.row.TELECOM_RATE === '-' ? '-' :
                    scope.row.TELECOM_RATE + '%'}}
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column
                v-if="!ent || ent==2"
                prop="MOBILE"
                :label="getI18nValue('移动')"
                align="center"
              >
                <el-table-column
                  class-name="self-defined"
                  prop="MOBILE"
                  :label="getI18nValue('未公示资费数')"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-link
                      v-if="permissions['cx-xty-audit-unpublic'] && tableName_province==='day'"
                      :underline="false"
                      @click="handleToPlan(scope.row,'2')"
                    >
                      {{scope.row.MOBILE?scope.row.MOBILE:'0'}}
                    </el-link>
                    <span v-else
                      >{{scope.row.MOBILE?scope.row.MOBILE:'0'}}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                  prop="MOBILE_RATE"
                  :label="getI18nValue('资费公示率')"
                  align="center"
                >
                  <template slot-scope="scope">
                    {{scope.row.MOBILE_RATE === '-' ? '-' :
                    scope.row.MOBILE_RATE + '%'}}
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column
                v-if="!ent || ent==3"
                prop="UNICOM"
                :label="getI18nValue('联通')"
                align="center"
              >
                <el-table-column
                  class-name="self-defined"
                  prop="UNICOM"
                  :label="getI18nValue('未公示资费数')"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-link
                      v-if="permissions['cx-xty-audit-unpublic'] && tableName_province==='day'"
                      :underline="false"
                      @click="handleToPlan(scope.row,'3')"
                    >
                      {{scope.row.UNICOM?scope.row.UNICOM:'0'}}
                    </el-link>
                    <span v-else
                      >{{scope.row.UNICOM?scope.row.UNICOM:'0'}}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                  prop="UNICOM_RATE"
                  :label="getI18nValue('资费公示率')"
                  align="center"
                >
                  <template slot-scope="scope">
                    {{scope.row.UNICOM_RATE === '-' ? '-' :
                    scope.row.UNICOM_RATE + '%'}}
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column
                v-if="!ent || ent==5"
                prop="BROAD"
                :label="getI18nValue('广电')"
                align="center"
              >
                <el-table-column
                  class-name="self-defined"
                  prop="BROAD"
                  :label="getI18nValue('未公示资费数')"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-link
                      v-if="permissions['cx-xty-audit-unpublic'] && tableName_province==='day'"
                      :underline="false"
                      @click="handleToPlan(scope.row,'5')"
                    >
                      {{scope.row.BROAD?scope.row.BROAD:'0'}}
                    </el-link>
                    <span v-else>{{scope.row.BROAD?scope.row.BROAD:'0'}}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="BROAD_RATE"
                  :label="getI18nValue('资费公示率')"
                  align="center"
                >
                  <template slot-scope="scope">
                    {{scope.row.BROAD_RATE === '-' ? '-' : scope.row.BROAD_RATE
                    + '%'}}
                  </template>
                </el-table-column>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <script
      type="text/javascript"
      src="/cx-mix-tariff/static/js/time.js"
    ></script>
    <script src="./mixins.js?v=20250723"></script>
    <script src="/cx-mix-tariff/static/js/echarts.min.js?v=1.0.0"></script>
    <script src="/cx-mix-tariff/static/js/FileSaver.min.js"></script>

    <script>
      var qualityTask = new Vue({
        el: "#unPublic-statistics",
        mixins: [mixins],

        data: function () {
          return {
            key: "",
            activeName: "0",
            myChart1: null,
            myChart2: null,
            pickerOptions: {
              disabledDate(time) {
                return time.getTime() > Date.now();
              },
            },
            subTitle: "日报",
          };
        },
        computed: {
          // 权限
          hasPermission() {
            return this.ent
              ? this.ent == 1
                ? this.permissions["cx-xty-audit-statistics-ct"]
                : this.ent == 2
                ? this.permissions["cx-xty-audit-statistics-cm"]
                : this.ent == 3
                ? this.permissions["cx-xty-audit-statistics-un"]
                : this.ent == 5
                ? this.permissions["cx-xty-audit-statistics-cbn"]
                : false
              : this.permissions["cx-xty-audit-statistics-province"];
          },
        },
        watch: {
          activeName(v) {
            if (v === "0") {
              this.$nextTick(() => {
                this.myChart1.resize();
              });
            } else {
              this.$nextTick(() => {
                this.myChart2.resize();
              });
            }
          },
        },
        methods: {
          initchart: function () {
            var chartDom = document.getElementById("myChart1");
            this.myChart1 = echarts.init(chartDom);
            chartDom = document.getElementById("myChart2");
            this.myChart2 = echarts.init(chartDom);
            var option = {
              tooltip: {
                trigger: "axis",
                appendToBody: true,
              },
              grid: {
                left: "3%",
                right: "4%",
                bottom: "3%",
                containLabel: true,
              },
              toolbox: {
                feature: {
                  saveAsImage: {},
                },
              },
              xAxis: {
                type: "category",
                boundaryGap: false,
                data: [],
              },
            };
            var option1 = Object.assign({}, option, {
              yAxis: {
                name: "未报送资费数",
                type: "value",
              },
              series: this.ent
                ? [
                    {
                      name: this.entLabelMap[this.ent],
                      type: "line",
                      smooth: true,
                      data: [],
                    },
                  ]
                : [
                    {
                      name: "电信",
                      type: "line",
                      smooth: true,
                      data: [],
                    },
                    {
                      name: "移动",
                      type: "line",
                      smooth: true,
                      data: [],
                    },
                    {
                      name: "联通",
                      type: "line",
                      smooth: true,
                      data: [],
                    },
                    {
                      name: "广电",
                      type: "line",
                      smooth: true,
                      data: [],
                    },
                    {
                      name: "全行业",
                      type: "line",
                      smooth: true,
                      data: [],
                    },
                  ],
              legend: {
                data: this.ent
                  ? [this.entLabelMap[this.ent]]
                  : ["电信", "移动", "联通", "广电", "全行业"],
              },
            });
            var option2 = Object.assign({}, option, {
              yAxis: {
                name: "备案百分比（%）",
                type: "value",
                max: 100,
                min: 0,
                // axisLabel: {
                //   formatter: '{value} %'
                // }
              },
              series: this.ent
                ? [
                    {
                      name: this.entLabelMap[this.ent] + "资费报送率（%）",
                      type: "line",
                      smooth: true,
                      data: [],
                    },
                  ]
                : [
                    {
                      name: "电信资费报送率（%）",
                      type: "line",
                      smooth: true,
                      data: [],
                    },
                    {
                      name: "移动资费报送率（%）",
                      type: "line",
                      smooth: true,
                      data: [],
                    },
                    {
                      name: "联通资费报送率（%）",
                      type: "line",
                      smooth: true,
                      data: [],
                    },
                    {
                      name: "广电资费报送率（%）",
                      type: "line",
                      smooth: true,
                      data: [],
                    },
                    {
                      name: "全行业资费报送率（%）",
                      type: "line",
                      smooth: true,
                      data: [],
                    },
                  ],
              legend: {
                data: this.ent
                  ? [this.entLabelMap[this.ent] + "资费报送率（%）"]
                  : [
                      "电信资费报送率（%）",
                      "移动资费报送率（%）",
                      "联通资费报送率（%）",
                      "广电资费报送率（%）",
                      "全行业资费报送率（%）",
                    ],
              },
            });

            option1 && this.myChart1.setOption(option1);
            option2 && this.myChart2.setOption(option2);
            window.addEventListener("resize", () => {
              this.myChart1.resize(); //当浏览器窗口大小发生变化时，图表可以进行自适应
              this.myChart2.resize();
            });
          },
          getList(label) {
            let start = new Date(this.searchForm.date[0]);
            let end = new Date(this.searchForm.date[1]);
            if (this.tableName_province === "day") {
              if (end - start > 30 * 24 * 60 * 60 * 1000) {
                this.$message.warning("日报时间周期不能超过31天");
                return false;
              }
            } else if (this.tableName_province === "month") {
              if (end - start > 6 * 30 * 24 * 60 * 60 * 1000) {
                this.$message.warning("月报时间周期不能超过6个月");
                return false;
              }
            }

            const data = {
              type: this.tableName_province,
              dateValue: Array.isArray(this.searchForm.date)
                ? this.searchForm.date.join("~")
                : this.searchForm.date,
              provinceCode: this.searchForm.provinceCode,
            };

            if (label != "export") {
              this.tableData.loading = true;

              yq.remoteCall(
                "/cx-mix-tariff/webcall?action=tariffPublicDao.unpublicStatList",
                data,
                (res) => {
                  if (res.state == 1) {
                    // 确保 res.data 是数组
                    if (!Array.isArray(res.data)) {
                      this.tableData.loading = false;
                      this.$message.error("接口返回数据格式错误");
                      return;
                    }

                    // 使用数组方法处理数据
                    const processedData = [];
                    const dateMap = {};

                    // 第一步：按日期分组
                    res.data.forEach((item) => {
                      const dateValue = item.DATE_VALUE;

                      if (!dateMap[dateValue]) {
                        dateMap[dateValue] = {
                          DATE_ID: this.formatDateValue(dateValue),
                          DATE_VALUE: dateValue, // 保存原始日期值用于排序
                          TELECOM: "未报送，稽核失败",
                          TELECOM_RATE: "-",
                          MOBILE: "未报送，稽核失败",
                          MOBILE_RATE: "-",
                          UNICOM: "未报送，稽核失败",
                          UNICOM_RATE: "-",
                          BROAD: "未报送，稽核失败",
                          BROAD_RATE: "-",
                          ALLTOTAL: "未报送，稽核失败",
                          ALLTOTAL_RATE: "-",
                        };
                      }

                      // 根据企业类型设置对应字段
                      switch (item.ENT) {
                        case "1": // 电信
                          dateMap[dateValue].TELECOM = item.UN_PUBLIC_NUM;
                          dateMap[dateValue].TELECOM_RATE = item.PUBLIC_RATE;
                          break;
                        case "2": // 移动
                          dateMap[dateValue].MOBILE = item.UN_PUBLIC_NUM;
                          dateMap[dateValue].MOBILE_RATE = item.PUBLIC_RATE;
                          break;
                        case "3": // 联通
                          dateMap[dateValue].UNICOM = item.UN_PUBLIC_NUM;
                          dateMap[dateValue].UNICOM_RATE = item.PUBLIC_RATE;
                          break;
                        case "5": // 广电
                          dateMap[dateValue].BROAD = item.UN_PUBLIC_NUM;
                          dateMap[dateValue].BROAD_RATE = item.PUBLIC_RATE;
                          break;
                      }
                    });

                    // 转换为数组
                    this.tableData.data = Object.values(dateMap);

                    // 计算总数
                    this.tableData.data.forEach((item) => {
                      if (item.DATE_ID !== "汇总") {
                        item.ALLTOTAL = (
                          parseInt(item.TELECOM || 0) +
                          parseInt(item.MOBILE || 0) +
                          parseInt(item.UNICOM || 0) +
                          parseInt(item.BROAD || 0)
                        ).toString();
                      }
                    });

                    // 排序：按日期降序排序，确保汇总在最后
                    this.tableData.data.sort((a, b) => {
                      if (a.DATE_ID === "汇总") return 1;
                      if (b.DATE_ID === "汇总") return -1;
                      // 按日期降序排序（最新日期在前）
                      return b.DATE_VALUE.localeCompare(a.DATE_VALUE);
                    });

                    this.tableData.loading = false;
                  } else {
                    this.tableData.loading = false;
                  }
                }
              );
            } else {
              location.href =
                "/cx-mix-tariff/servlet/tariff?action=Export1&" + $.param(data);
            }
          },
          // 跳转到未备案方案
          handleToPlan(item, index) {
            const data = {
              ent: index,
              provinceCode: item.PROVINCE_CODE
                ? item.PROVINCE_CODE
                : this.searchForm.provinceCode,
            };

            if (this.tableName_province == "month") {
              const { firstDay = "", lastDay = "" } =
                this.getFirstAndLastDayOfMonth(this.searchForm.date);
              data.appearTime = [firstDay, lastDay].join(",");
            } else {
              if (item.DATE_VALUE && item.DATE_VALUE === '汇总') {
                const [firstDay, lastDay] = this.searchForm.date
                date = [this.formatDateString(firstDay), this.formatDateString(lastDay)]
              } else {
                date = [this.formatDateString(item.DATE_VALUE), this.formatDateString(item.DATE_VALUE)]
              }
              data.appearTime = date.join(",");
            }

            top.popup.openTab({
              url: "/cx-mix-tariff/pages/fillDetail/tariff-plan-list-unpublic.html",
              title: getI18nValue("未公示资费方案"),
              data: data,
              id: "plan" + this.generateUniqueEightDigitNumber(),
            });
          },

          handleRadio(val) {
            this.searchForm.date = "";
            switch (val) {
              case "day":
                (this.subTitle = "日报"),
                  (this.searchForm.date = [
                    getBeforeDayDate(8).replaceAll("-", ""),
                    getYesterDayDate().replaceAll("-", ""),
                  ]);
                break;
              case "month":
                (this.subTitle = "月报"),
                  (this.searchForm.date = getThisMonthStartDate2().replaceAll(
                    "-",
                    ""
                  ));
                break;
              default:
                break;
            }
            this.getList();
          },
          handleReset() {
            this.resetSearchForm();
          },
          // 添加一个格式化日期的函数
          formatDateValue(dateValue) {
            // 如果是汇总，直接返回
            if (dateValue === "汇总") {
              return "汇总";
            }

            // 处理年月格式 (YYYYMM)
            if (
              dateValue &&
              dateValue.length === 6 &&
              /^\d{6}$/.test(dateValue)
            ) {
              return (
                dateValue.substring(0, 4) +
                "年" +
                dateValue.substring(4, 6) +
                "月"
              );
            }

            // 处理月日格式 (MMDD)
            if (
              dateValue &&
              dateValue.length === 4 &&
              /^\d{4}$/.test(dateValue)
            ) {
              return (
                dateValue.substring(0, 2) +
                "月" +
                dateValue.substring(2, 4) +
                "日"
              );
            }

            // 处理年月日格式 (YYYYMMDD)
            if (
              dateValue &&
              dateValue.length === 8 &&
              /^\d{8}$/.test(dateValue)
            ) {
              return (
                dateValue.substring(0, 4) +
                "年" +
                dateValue.substring(4, 6) +
                "月" +
                dateValue.substring(6, 8) +
                "日"
              );
            }

            // 其他情况原样返回
            return dateValue;
          },
        },
        mounted() {
          // this.initchart()
          this.searchForm.date = [
            getBeforeDayDate(7).replaceAll("-", ""),
            getYesterDayDate().replaceAll("-", ""),
          ];
          this.getList();
        },
      });
    </script>
  </body>
</html>
