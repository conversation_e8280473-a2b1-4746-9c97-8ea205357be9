package com.yunqu.tariff.dao;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.base.AppDaoContext;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.QueryFactory;
import jodd.util.StringUtil;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

/**
 * 爬虫版本管理DAO
 *
 * @ClassName TariffCrawlerVersionDao
 * <AUTHOR> Copy This Tag)
 * @Description 爬虫版本管理相关数据访问层
 * @Since create in 2025/1/28 10:00
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
@WebObject(name = "tariffCrawlerVersion")
public class TariffCrawlerVersionDao extends AppDaoContext {

    /**
     * 获取版本列表（分页）
     * @return 版本列表分页数据
     */
    @WebControl(name = "getVersionList", type = Types.PAGE)
    public JSONObject getVersionList() {
        try {
            EasySQL sql = new EasySQL("SELECT * FROM " + getTableName("xty_crawler_version") + " WHERE 1=1");

            // 添加查询条件
            sql.append(param.getString("VERSION_STATUS"), "AND VERSION_STATUS = ?");
            sql.append(param.getString("VERSION_NO"), "AND VERSION_NO LIKE ?", true);
            sql.append(param.getString("START_DATE"), "AND CREATE_TIME >= ?");
            sql.append(param.getString("END_DATE"), "AND CREATE_TIME <= ?");

            // 排序
            sql.append("ORDER BY CREATE_TIME DESC");

            CommonLogger.getLogger().info("查询版本列表SQL: " + sql.toFullSql());
            return queryForPageList(sql.getSQL(), sql.getParams());

        } catch (Exception e) {
            CommonLogger.getLogger().error("查询版本列表失败: " + e.getMessage(), e);
            return EasyResult.fail("查询版本列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取版本详情
     * @return 版本详细信息
     */
    @WebControl(name = "getVersionDetail", type = Types.RECORD)
    public JSONObject getVersionDetail() {
        try {
            String versionId = param.getString("VERSION_ID");
            String versionNo = param.getString("VERSION_NO");

            if (StringUtil.isBlank(versionId) && StringUtil.isBlank(versionNo)) {
                return EasyResult.fail("版本ID或版本号不能为空");
            }

            EasySQL sql = new EasySQL("SELECT * FROM " + getTableName("xty_crawler_version") + " WHERE 1=1");

            if (StringUtil.isNotBlank(versionId)) {
                sql.append(versionId, "AND VERSION_ID = ?");
            } else {
                sql.append(versionNo, "AND VERSION_NO = ?");
            }

            CommonLogger.getLogger().info("查询版本详情SQL: " + sql.toFullSql());
            JSONObject result = queryForRecord(sql.getSQL(), sql.getParams());

            if (result == null || result.isEmpty()) {
                return EasyResult.fail("版本信息不存在");
            }

            return EasyResult.ok(result);

        } catch (Exception e) {
            CommonLogger.getLogger().error("查询版本详情失败: " + e.getMessage(), e);
            return EasyResult.fail("查询版本详情失败: " + e.getMessage());
        }
    }

    /**
     * 版本作废操作
     * @return 操作结果
     */
    @WebControl(name = "invalidVersion", type = Types.RECORD)
    public JSONObject invalidVersion() {
        try {
            String versionId = param.getString("VERSION_ID");
            String invalidReason = param.getString("INVALID_REASON");
            String currentUser = getUserAcct();
            String currentTime = getCurrentTimeString();

            if (StringUtil.isBlank(versionId)) {
                return EasyResult.fail("版本ID不能为空");
            }

            if (StringUtil.isBlank(invalidReason)) {
                return EasyResult.fail("作废原因不能为空");
            }

            // 检查版本是否存在且状态为正常
            EasySQL checkSql = new EasySQL("SELECT VERSION_STATUS FROM " + getTableName("xty_crawler_version"));
            checkSql.append(versionId, "WHERE VERSION_ID = ?");

            JSONObject statusRecord = queryForRecord(checkSql.getSQL(), checkSql.getParams());
            String currentStatus = statusRecord != null ? statusRecord.getString("VERSION_STATUS") : null;
            if (StringUtils.isBlank(currentStatus)) {
                return EasyResult.fail("版本不存在");
            }

            if ("INVALID".equals(currentStatus)) {
                return EasyResult.fail("版本已经是作废状态");
            }

            // 执行版本作废更新
            EasySQL updateSql = new EasySQL("UPDATE " + getTableName("xty_crawler_version") + " SET " +
                    "VERSION_STATUS = 'INVALID'");
            updateSql.append(currentTime, ", INVALID_TIME = ?");
            updateSql.append(currentUser, ", INVALID_USER = ?");
            updateSql.append(invalidReason, ", INVALID_REASON = ?");
            updateSql.append(currentTime, ", UPDATE_TIME = ?");
            updateSql.append(versionId, " WHERE VERSION_ID = ?");

            int updateCount = getQuery().executeUpdate(updateSql.getSQL(), updateSql.getParams());

            if (updateCount > 0) {
                CommonLogger.getLogger().info("版本作废成功，版本ID: " + versionId + ", 操作用户: " + currentUser);

                // 记录操作日志
                recordOperationLog("INVALID_VERSION", versionId, invalidReason, currentUser);

                return EasyResult.ok("版本作废成功");
            } else {
                return EasyResult.fail("版本作废失败");
            }

        } catch (Exception e) {
            CommonLogger.getLogger().error("版本作废操作失败: " + e.getMessage(), e);
            return EasyResult.fail("版本作废操作失败: " + e.getMessage());
        }
    }

    /**
     * 获取版本统计信息
     * @return 统计信息
     */
    @WebControl(name = "getVersionStatistics", type = Types.LIST)
    public JSONObject getVersionStatistics() {
        try {
            EasySQL sql = new EasySQL("SELECT " +
                    "VERSION_STATUS, " +
                    "COUNT(*) AS VERSION_COUNT, " +
                    "SUM(CASE WHEN DATA_COUNT IS NOT NULL THEN DATA_COUNT ELSE 0 END) AS TOTAL_DATA_COUNT, " +
                    "AVG(CASE WHEN CRAWL_DURATION IS NOT NULL THEN CRAWL_DURATION ELSE 0 END) AS AVG_CRAWL_DURATION " +
                    "FROM " + getTableName("xty_crawler_version") + " " +
                    "GROUP BY VERSION_STATUS");

            CommonLogger.getLogger().info("查询版本统计SQL: " + sql.toFullSql());
            return queryForList(sql.getSQL(), sql.getParams());

        } catch (Exception e) {
            CommonLogger.getLogger().error("查询版本统计失败: " + e.getMessage(), e);
            return EasyResult.fail("查询版本统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取版本影响范围分析
     * @return 影响范围信息
     */
    @WebControl(name = "getVersionImpactAnalysis", type = Types.RECORD)
    public JSONObject getVersionImpactAnalysis() {
        try {
            String versionId = param.getString("VERSION_ID");

            if (StringUtil.isBlank(versionId)) {
                return EasyResult.fail("版本ID不能为空");
            }

            // 查询版本基本信息
            EasySQL versionSql = new EasySQL("SELECT VERSION_NO, VERSION_TABLE_NAME, DATA_COUNT, CREATE_TIME " +
                    "FROM " + getTableName("xty_crawler_version"));
            versionSql.append(versionId, "WHERE VERSION_ID = ?");

            JSONObject versionInfo = queryForRecord(versionSql.getSQL(), versionSql.getParams());
            if (versionInfo == null) {
                return EasyResult.fail("版本信息不存在");
            }

            // 构建影响范围分析结果
            JSONObject result = new JSONObject();
            result.put("versionInfo", versionInfo);
            result.put("impactDescription", "作废此版本将清理 " + versionInfo.getIntValue("DATA_COUNT") + " 条爬取数据");
            result.put("tableName", versionInfo.getString("VERSION_TABLE_NAME"));

            return EasyResult.ok(result);

        } catch (Exception e) {
            CommonLogger.getLogger().error("获取版本影响范围分析失败: " + e.getMessage(), e);
            return EasyResult.fail("获取版本影响范围分析失败: " + e.getMessage());
        }
    }

    /**
     * 记录操作日志
     */
    private void recordOperationLog(String operationType, String targetId, String operationDesc, String operationUser) {
        try {
            String logId = generateId();
            String currentTime = getCurrentTimeString();
            String clientIp = getClientIp();

            String logSql = "INSERT INTO " + getTableName("xty_crawler_operation_log") + " " +
                    "(ID, OPERATION_TYPE, OPERATION_DESC, TARGET_TYPE, TARGET_ID, " +
                    "OPERATION_USER, OPERATION_TIME, OPERATION_IP, CREATE_TIME) VALUES " +
                    "(?, ?, ?, 'VERSION', ?, ?, ?, ?, ?)";

            Object[] logParams = {logId, operationType, operationDesc, targetId,
                    operationUser, currentTime, clientIp, currentTime};

            getQuery().executeUpdate(logSql, logParams);

        } catch (Exception e) {
            CommonLogger.getLogger().error("记录操作日志失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取版本操作日志
     * @return 操作日志列表
     */
    @WebControl(name = "getVersionOperationLog", type = Types.PAGE)
    public JSONObject getVersionOperationLog() {
        try {
            String versionId = param.getString("VERSION_ID");

            EasySQL sql = new EasySQL("SELECT " +
                    "ID, OPERATION_TYPE, OPERATION_DESC, OPERATION_USER, " +
                    "OPERATION_TIME, OPERATION_IP, OPERATION_RESULT, ERROR_MESSAGE " +
                    "FROM " + getTableName("xty_crawler_operation_log") + " WHERE 1=1");

            if (StringUtil.isNotBlank(versionId)) {
                sql.append(versionId, "AND TARGET_ID = ?");
            }

            sql.append(param.getString("OPERATION_TYPE"), "AND OPERATION_TYPE = ?");
            sql.append(param.getString("OPERATION_USER"), "AND OPERATION_USER = ?");
            sql.append(param.getString("START_TIME"), "AND OPERATION_TIME >= ?");
            sql.append(param.getString("END_TIME"), "AND OPERATION_TIME <= ?");

            sql.append("ORDER BY OPERATION_TIME DESC");

            return queryForPageList(sql.getSQL(), sql.getParams());

        } catch (Exception e) {
            CommonLogger.getLogger().error("查询版本操作日志失败: " + e.getMessage(), e);
            return EasyResult.fail("查询版本操作日志失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前时间字符串
     */
    private String getCurrentTimeString() {
        return String.valueOf(System.currentTimeMillis() / 1000);
    }

    /**
     * 生成唯一ID
     */
    private String generateId() {
        return String.valueOf(System.currentTimeMillis());
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp() {
        try {
            String ip = request.getHeader("X-Forwarded-For");
            if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("X-Real-IP");
            }
            if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
            }
            return ip;
        } catch (Exception e) {
            return "unknown";
        }
    }

    @Override
    protected EasyQuery getQuery() {
        return QueryFactory.getTariffQuery();
    }
}
