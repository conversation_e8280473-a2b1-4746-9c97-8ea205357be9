package com.yunqu.tariff.dao;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.tariff.base.AppDaoContext;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.xty.commonex.kit.ElasticsearchKit;
import com.yunqu.xty.commonex.util.RedissonUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.slf4j.Logger;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 资费公示库Dao
 * <AUTHOR>
 */
@WebObject(name = "tariffCrawlRecord")
public class TariffCrawlRecordDao extends AppDaoContext {
    Logger logger = CommonLogger.getLogger();

    /**
     * 获取公示资费字段配置
     */
    @WebControl(name = "crawlRecordFields", type = Types.LIST)
    public JSONObject crawlRecordFields() {
        try {
            // 公示资费字段映射 - 使用驼峰命名格式
            JSONObject fieldMap = new JSONObject(true);
            fieldMap.put("tariffNo", "方案编号");
            fieldMap.put("name", "资费名称");
            fieldMap.put("entName", "运营商");
            fieldMap.put("provinceName", "省份");
            fieldMap.put("tariffAttrType", "资费来源"); // 省内、全国
            fieldMap.put("tariffType", "资费分类");
            fieldMap.put("fees", "资费标准");
            fieldMap.put("feesUnit", "资费单位");
            fieldMap.put("exceedFees", "超出资费");
            fieldMap.put("otherFees", "其他费用");
            fieldMap.put("call", "语音/分钟");
            fieldMap.put("data", "通用流量");
            fieldMap.put("dataUnit", "通用流量单位");
            fieldMap.put("sms", "短信/条");
            fieldMap.put("orientTraffic", "定向流量");
            fieldMap.put("orientTrafficUnit", "定向流量单位");
            fieldMap.put("iptv", "IPTV");
            fieldMap.put("bandwidth", "带宽");
            fieldMap.put("rights", "权益");
            fieldMap.put("otherContent", "其他服务内容");
            fieldMap.put("applicablePeople", "适用范围");
            fieldMap.put("validPeriod", "有效期限");
            fieldMap.put("channel", "销售渠道");
            fieldMap.put("duration", "在网要求");
            fieldMap.put("unsubscribe", "退订方式");
            fieldMap.put("others", "其他事项");
            fieldMap.put("onlineDay", "上线日期");
            fieldMap.put("offlineDay", "下线日期");
//            fieldMap.put("versionNo", "版本号");
            fieldMap.put("versionNos", "出现版本号");
//            fieldMap.put("dateIds", "出现日期");
            fieldMap.put("appearMonths", "出现月份");

            // 添加其他重要字段
//            fieldMap.put("classicTypeOne", "一级分类");
//            fieldMap.put("classicTypeTwo", "二级分类");
//            fieldMap.put("isTelecom", "是否通信类");
//            fieldMap.put("reported", "是否报送");

//            fieldMap.put("tariffState", "资费状态");
//            fieldMap.put("tariffReporterName", "报送主体");

//            fieldMap.put("fieldCheckResult", "要素检查结果");
//            fieldMap.put("fieldCheckTime", "检查时间");

            return EasyResult.ok(fieldMap);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail("获取公示资费字段配置失败");
        }
    }


    /**
     * 获取公示资费字段配置
     */
    @WebControl(name = "crawlRecordFieldsByCheck", type = Types.LIST)
    public JSONObject crawlRecordFieldsByCheck() {
        try {
            // 公示资费字段映射 - 使用驼峰命名格式
            JSONObject fieldMap = new JSONObject(true);
            fieldMap.put("tariffNo", "方案编号");
            fieldMap.put("name", "资费名称");
            fieldMap.put("entName", "运营商");
            fieldMap.put("provinceName", "省份");
            fieldMap.put("tariffAttrType", "资费来源"); // 省内、全国
            fieldMap.put("tariffType", "资费分类");
            fieldMap.put("fees", "资费标准");
            fieldMap.put("feesUnit", "资费单位");
            fieldMap.put("exceedFees", "超出资费");
            fieldMap.put("otherFees", "其他费用");
            fieldMap.put("call", "语音/分钟");
            fieldMap.put("data", "通用流量");
            fieldMap.put("dataUnit", "通用流量单位");
            fieldMap.put("sms", "短信/条");
            fieldMap.put("orientTraffic", "定向流量");
            fieldMap.put("orientTrafficUnit", "定向流量单位");
            fieldMap.put("iptv", "IPTV");
            fieldMap.put("bandwidth", "带宽");
            fieldMap.put("rights", "权益");
            fieldMap.put("otherContent", "其他服务内容");
            fieldMap.put("applicablePeople", "适用范围");
            fieldMap.put("validPeriod", "有效期限");
            fieldMap.put("channel", "销售渠道");
            fieldMap.put("duration", "在网要求");
            fieldMap.put("unsubscribe", "退订方式");
            fieldMap.put("others", "其他事项");
            fieldMap.put("onlineDay", "上线日期");
            fieldMap.put("offlineDay", "下线日期");
//            fieldMap.put("versionNo", "版本号");
            fieldMap.put("versionNos", "出现版本号");
//            fieldMap.put("dateIds", "出现日期");
            fieldMap.put("appearMonths", "出现月份");

            // 添加其他重要字段
            fieldMap.put("classicTypeOne", "一级分类");
            fieldMap.put("classicTypeTwo", "二级分类");
            fieldMap.put("isTelecom", "是否通信类");
            fieldMap.put("reported", "是否报送");

            fieldMap.put("tariffState", "资费状态");
            fieldMap.put("tariffReporterName", "报送主体");


            fieldMap.put("fieldCheckResult", "字段检查结果");
            fieldMap.put("fieldCheckTime", "检查时间");

            return EasyResult.ok(fieldMap);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail("获取公示资费字段配置失败");
        }
    }

    /**
     * 根据部门组类型获取运营商编码
     * @param deptGroupType 部门组类型
     * @return 运营商编码
     */
    private String getEntCodeByGroupType(String deptGroupType) {
        if (StringUtils.startsWith(deptGroupType, "g_dx") || StringUtils.startsWith(deptGroupType, "p_dx")) {
            return "1"; // 电信
        } else if (StringUtils.startsWith(deptGroupType, "g_yd") || StringUtils.startsWith(deptGroupType, "p_yd")) {
            return "2"; // 移动
        } else if (StringUtils.startsWith(deptGroupType, "g_lt") || StringUtils.startsWith(deptGroupType, "p_lt")) {
            return "3"; // 联通
        } else if (StringUtils.startsWith(deptGroupType, "g_gd") || StringUtils.startsWith(deptGroupType, "p_gd")) {
            return "5"; // 广电
        }
        return null;
    }

    /**
     * 公示库资费列表查询
     * <p>
     * 查询字段包括：方案编号、资费名称、运营商、省份、资费类型、资费分类、资费标准、语音/分钟、通用流量、
     * 短信/条、定向流量、IPTV、带宽、权益、其他服务内容、适用范围、有效期限、销售渠道、在网要求、
     * 退订方式、其他事项、上线日期、下线日期、版本号、入库时间、一级分类、二级分类、是否通信类、是否报送、
     * 要素检查结果、检查时间
     * </p>
     *
     * @return 公示库资费列表
     */
//    @InfAuthCheck(resId = "cx-xty-tariff-public-list")
    @WebControl(name = "publicLibRecordList", type = Types.LIST)
    public JSONObject publicLibRecordList() {
        try {
            // 获取当前用户
            UserModel user = UserUtil.getUser(request);
            String deptGroupType = user.getDeptGroupType();
            String userProvinceCode = user.getDeptProvinceCode();

            // 获取查询参数 - 使用与 crawlRecordFields 一致的驼峰命名参数
            String id = param.getString("id");
            String tariffRecordId = param.getString("tariffRecordId");
            String taskId = param.getString("taskId");
            String tariffNo = param.getString("tariffNo");
            String name = param.getString("name");
            String tariffAttrType = param.getString("tariffAttr");
            String classicTypeOne = param.getString("classicTypeOne");
            String classicTypeTwo = param.getString("classicTypeTwo");
            JSONArray entCodes = param.getJSONArray("entName");
            String provinceCode = param.getString("provinceName");
            String dateId = param.getString("dateId");
            String monthId = param.getString("monthId");
            String crawlId = param.getString("crawlId");
            String tariffType = param.getString("tariffType");
            String updateTimeBegin = param.getString("updateTimeBegin");
            String updateTimeEnd = param.getString("updateTimeEnd");
            JSONArray isTelecom = param.getJSONArray("isTelecom");
            String reported = param.getString("reported");
            String fieldCheckResult = param.getString("fieldCheckResult");
            String fieldCheckTime = param.getString("fieldCheckTime");
            String fees = param.getString("fees");

            // 添加新的查询参数
            String feesUnit = param.getString("feesUnit");
            String dataUnit = param.getString("dataUnit");
            String orientTrafficUnit = param.getString("orientTrafficUnit");
            String otherContent = param.getString("otherContent");
            String validPeriod = param.getString("validPeriod");
            String channel = param.getString("channel");
            String duration = param.getString("duration");
            String unsubscribe = param.getString("unsubscribe");
            String others = param.getString("others");
            String versionNo = param.getString("versionNo");
            String versionNos = param.getString("versionNos");
            String dateIds = param.getString("dateIds");
            String appearMonths = param.getString("appearMonths");
            String exceedFees = param.getString("exceedFees");
            String otherFees = param.getString("otherFees");

            // 处理上线时间和下线时间范围查询
            JSONArray onlineDayRange = param.getJSONArray("onlineDay");
            String onlineDayBegin = null;
            String onlineDayEnd = null;
            if (onlineDayRange != null && !onlineDayRange.isEmpty()) {
                String beginDate = onlineDayRange.getString(0);
                if (beginDate != null && beginDate.length() >= 10) {
                    onlineDayBegin = beginDate.substring(0, 10).replace("-", "");
                }
                if (onlineDayRange.size() > 1) {
                    String endDate = onlineDayRange.getString(1);
                    if (endDate != null && endDate.length() >= 10) {
                        onlineDayEnd = endDate.substring(0, 10).replace("-", "");
                    }
                }
            }

            JSONArray offlineDayRange = param.getJSONArray("offlineDay");
            String offlineDayBegin = null;
            String offlineDayEnd = null;
            if (offlineDayRange != null && !offlineDayRange.isEmpty()) {
                String beginDate = offlineDayRange.getString(0);
                if (beginDate != null && beginDate.length() >= 10) {
                    offlineDayBegin = beginDate.substring(0, 10).replace("-", "");
                }
                if (offlineDayRange.size() > 1) {
                    String endDate = offlineDayRange.getString(1);
                    if (endDate != null && endDate.length() >= 10) {
                        offlineDayEnd = endDate.substring(0, 10).replace("-", "");
                    }
                }
            }

            // 获取分页参数
            int page = param.getIntValue("pageIndex");
            int rows = param.getIntValue("pageSize");

            // 构建ES查询参数
            JSONObject queryParams = new JSONObject();
            queryParams.put("from", (page - 1) * rows);
            queryParams.put("size", rows);

            // 构建排序
            JSONArray sortArray = new JSONArray();

            // 第一排序字段：date_id 降序
            JSONObject dateIdSort = new JSONObject();
            dateIdSort.put("date_id.keyword", new JSONObject().fluentPut("order", "desc"));
            sortArray.add(dateIdSort);

            // 第二排序字段：_id 降序
            JSONObject idSort = new JSONObject();
            idSort.put("_id", new JSONObject().fluentPut("order", "desc"));
            sortArray.add(idSort);

            queryParams.put("sort", sortArray);

            // 构建查询条件
            JSONObject boolQuery = new JSONObject();
            JSONArray mustArray = new JSONArray();

            // 权限控制
            // 工信部、信通院账号：能看到全部公示的资费 (deptGroupType=pc_xgj, pc_ssc, pc_xty)
            // 管局账号：能看全部公示的资费，默认展示本省的公示资费 (deptGroupType=pc_gj)
            // 集团账号：能看到本企业31省公示的资费 (deptGroupType=g_dx, g_yd, g_lt, g_gd)
            // 省企业：能看到本企业公示的资费 (deptGroupType=p_dx_xx, p_yd_xx, p_lt_xx, p_gd_xx)

            // 工信部、信通院账号：不需要进行限制
            if (StringUtils.startsWith(deptGroupType, Constants.GROUP_TYPE_GE)) {
                // 集团账号：只能看到本企业的资费
                String entCodeByGroup = getEntCodeByGroupType(deptGroupType);
                if (StringUtils.isNotBlank(entCodeByGroup)) {
                    addTermQuery(mustArray, "ent_code.keyword", entCodeByGroup);
                }
            } else if (StringUtils.startsWith(deptGroupType, Constants.GROUP_TYPE_PE)) {
                // 省企业账号：只能看到本企业本省的资费
                String entCodeByGroup = getEntCodeByGroupType(deptGroupType);
                if (StringUtils.isNotBlank(entCodeByGroup)) {
                    addTermQuery(mustArray, "ent_code.keyword", entCodeByGroup);
                }
                if (StringUtils.isNotBlank(userProvinceCode)) {
                    addTermQuery(mustArray, "province_code.keyword", userProvinceCode);
                }
            }

            // 添加查询条件
            addTermQuery(mustArray, "id.keyword", id);
            addTermQuery(mustArray, "tariff_record_id.keyword", tariffRecordId);
            addTermQuery(mustArray, "task_id", taskId);
            addTermQuery(mustArray, "tariff_no.keyword", tariffNo);
            addMatchPhraseQuery(mustArray, "name", name);
            addTermQuery(mustArray, "tariff_attr_type.keyword", tariffAttrType);
            addTermsQuery(mustArray, "ent_code.keyword", entCodes);
            addTermQuery(mustArray, "province_code.keyword", provinceCode);
//            addTermQuery(mustArray, "classic_type_one.keyword", classicTypeOne);
//            addTermQuery(mustArray, "classic_type_two.keyword", classicTypeTwo);
            addTermQuery(mustArray, "date_id", dateId);
            addTermQuery(mustArray, "month_id", monthId);
            addTermQuery(mustArray, "crawl_id.keyword", crawlId);
            addRangeQuery(mustArray, "update_time", updateTimeBegin, updateTimeEnd);
            addTermsQuery(mustArray, "is_telecom", isTelecom);
            addTermQuery(mustArray, "reported", reported);

            JSONArray tariffStates = param.getJSONArray("tariffState");
            String reporter = param.getString("tariffReporter");

            addTermsQuery(mustArray, "tariff_state", tariffStates);

            if (StringUtils.isNotBlank(reporter)) {
                if ("1".compareTo(reporter) <= 0 && "5".compareTo(reporter) >= 0) {
                    JSONObject entMatch = new JSONObject();
                    entMatch.put("term", new JSONObject().fluentPut("ent_code.keyword", reporter));
                    mustArray.add(entMatch);
                } else {
                    JSONObject reporterMatch = new JSONObject();
                    reporterMatch.put("wildcard", new JSONObject().fluentPut("tariff_reporter.keyword", "*" + reporter + "*"));
                    mustArray.add(reporterMatch);
                }
            }

            // 添加fees模糊查询
            addMatchPhraseQuery(mustArray, "fees", fees);

            addMatchPhraseQuery(mustArray, "exceed_fees", exceedFees);
            addMatchPhraseQuery(mustArray, "other_fees", otherFees);

            // 添加单位字段查询
            addMatchPhraseQuery(mustArray, "fees_unit", feesUnit);
            addMatchPhraseQuery(mustArray, "data_unit", dataUnit);
            addMatchPhraseQuery(mustArray, "orient_traffic_unit", orientTrafficUnit);

            // 添加新的模糊查询条件
            addMatchPhraseQuery(mustArray, "tariff_type", tariffType);

            addMatchPhraseQuery(mustArray, "other_content", otherContent);
            addMatchPhraseQuery(mustArray, "valid_period", validPeriod);
            addMatchPhraseQuery(mustArray, "channel", channel);
            addMatchPhraseQuery(mustArray, "duration", duration);
            addMatchPhraseQuery(mustArray, "unsubscribe", unsubscribe);
            addMatchPhraseQuery(mustArray, "others", others);

            // 添加精确匹配查询
            addTermQuery(mustArray, "version_no.keyword", versionNo);

            // 添加版本号数组查询
            if (StringUtils.isNotBlank(versionNos)) {
                try {
                    JSONObject termsQuery = new JSONObject();
                    JSONObject termsFilter = new JSONObject();
                    List<String> versionList = new ArrayList<>();
                    versionList.add(versionNos);
                    termsFilter.put("version_nos.keyword", versionList);
                    termsQuery.put("terms", termsFilter);
                    mustArray.add(termsQuery);
                    logger.info("添加版本号数组过滤条件，版本号：{}", versionNos);
                } catch (Exception e) {
                    logger.error("解析版本号数组参数失败: {}", versionNos, e);
                }
            }

            // 添加日期ID数组查询
            if (StringUtils.isNotBlank(dateIds)) {
                try {
                    JSONObject termsQuery = new JSONObject();
                    JSONObject termsFilter = new JSONObject();
                    JSONArray dateIdsArray = JSON.parseArray(dateIds);
                    termsFilter.put("date_id", dateIdsArray);
                    termsQuery.put("terms", termsFilter);
                    mustArray.add(termsQuery);
                    logger.info("添加日期ID数组过滤条件，日期ID：{}", dateIds);
                } catch (Exception e) {
                    logger.error("解析日期ID数组参数失败: {}", dateIds, e);
                }
            }

            // 添加fieldCheckResult和fieldCheckTime模糊查询

            // 资费规则核查时间
            if (StringUtils.isNotBlank(fieldCheckTime) && fieldCheckTime.contains("~")) {
                String[] fieldCheckTimeArray = fieldCheckTime.split("~");
                if (fieldCheckTimeArray.length == 2) {
                    JSONObject fieldCheckTimeStartMatch = new JSONObject();
                    fieldCheckTimeStartMatch.put("range", new JSONObject().fluentPut("field_check_time",
                            new JSONObject().fluentPut("gte", fieldCheckTimeArray[0])));
                    mustArray.add(fieldCheckTimeStartMatch);

                    JSONObject fieldCheckTimeEndMatch = new JSONObject();
                    fieldCheckTimeEndMatch.put("range", new JSONObject().fluentPut("field_check_time",
                            new JSONObject().fluentPut("lte", fieldCheckTimeArray[1])));
                    mustArray.add(fieldCheckTimeEndMatch);
                }
            }

            // 资费字段编号
            String fieldCheckNo = param.getString("fieldCheckNo");

            if (StringUtils.isNotBlank(fieldCheckNo)) {
                fieldCheckNo = fieldCheckNo.replace("[", "").replace("]", "")
                        .replaceAll("\"","");
                String[] fieldCheckNoArray = fieldCheckNo.split(",");
                if (fieldCheckNoArray.length > 0) {
                    JSONObject fieldCheckNoQuery = new JSONObject();
                    JSONArray fieldCheckNoOrArray = new JSONArray();

                    for (String no : fieldCheckNoArray) {
                        if (StringUtils.isNotBlank(no)) {
                            // 使用正则方式在 script 中模拟 FIND_IN_SET
                            JSONObject scriptQuery = new JSONObject();
                            JSONObject scriptObj = new JSONObject();

                            scriptObj.put("script", new JSONObject()
                                    .fluentPut("source", "if (doc['field_check_no.keyword'].size() > 0) { return /(^|,)" + no + "(,|$)/.matcher(doc['field_check_no.keyword'].value).find(); } return false;")
                                    .fluentPut("lang", "painless"));

                            scriptQuery.put("script", scriptObj);
                            fieldCheckNoOrArray.add(scriptQuery);
                        }
                    }

                    if (fieldCheckNoOrArray.size() > 0) {
                        fieldCheckNoQuery.put("bool", new JSONObject()
                                .fluentPut("should", fieldCheckNoOrArray)
                                .fluentPut("minimum_should_match", 1));
                        mustArray.add(fieldCheckNoQuery);
                    }
                }
            }

            // 一级分类
            String type1 = param.getString("classicTypeOne");
            if (StringUtils.isNotBlank(type1)) {
                type1 = type1.replace("[", "").replace("]", "")
                .replaceAll("\"","");
                if (StringUtils.isNotBlank(type1)) {
                    JSONObject type1Query = new JSONObject();
                    JSONArray type1OrArray = new JSONArray();
                    for (String code : type1.split(",")) {
                        if (StringUtils.isNotBlank(code)) {
                            JSONObject typeMatch = new JSONObject();
                            typeMatch.put("term", new JSONObject().fluentPut("classic_type_one", code));
                            type1OrArray.add(typeMatch);
                        }
                    }
                    type1Query.put("bool", new JSONObject().fluentPut("should", type1OrArray).fluentPut("minimum_should_match", 1));
                    mustArray.add(type1Query);
                }
            }

            // 二级分类
            String type2 = param.getString("classicTypeTwo");
            if (StringUtils.isNotBlank(type2)) {

                type2 = type2.replace("[", "").replace("]", "")
                        .replaceAll("\"","");

                if (StringUtils.isNotBlank(type2)) {
                    JSONObject type2Query = new JSONObject();
                    JSONArray type2OrArray = new JSONArray();

                    for (String code : type2.split(",")) {
                        if (StringUtils.isNotBlank(code)) {
                            JSONObject typeMatch = new JSONObject();
                            typeMatch.put("term", new JSONObject().fluentPut("classic_type_two", code));
                            type2OrArray.add(typeMatch);
                        }
                    }

                    type2Query.put("bool", new JSONObject().fluentPut("should", type2OrArray).fluentPut("minimum_should_match", 1));
                    mustArray.add(type2Query);
                }
            }

            // 添加上线日期范围查询
            addRangeQuery(mustArray, "online_day", onlineDayBegin, onlineDayEnd);

            // 添加下线日期范围查询
            addRangeQuery(mustArray, "offline_day", offlineDayBegin, offlineDayEnd);

            // 添加入库月份过滤（appear_months是数组字段）
            if (StringUtils.isNotBlank(appearMonths)) {
                JSONObject termsQuery = new JSONObject();
                JSONObject termsFilter = new JSONObject();
                List<String> monthList = new ArrayList<>();
                monthList.add(appearMonths);
                termsFilter.put("appear_months", monthList);
                termsQuery.put("terms", termsFilter);
                mustArray.add(termsQuery);
                logger.info("添加入库月份过滤条件，月份：{}", appearMonths);
            }

            // 添加新字段的模糊查询
            addMatchPhraseQuery(mustArray, "exceed_fees", exceedFees);
            addMatchPhraseQuery(mustArray, "other_fees", otherFees);





            // 设置查询条件
            if (!mustArray.isEmpty()) {
                boolQuery.put("must", mustArray);
                queryParams.put("query", new JSONObject().fluentPut("bool", boolQuery));
            } else {
                queryParams.put("query", new JSONObject().fluentPut("match_all", new JSONObject()));
            }
            queryParams.put("track_total_hits", true);

            // 不设置_source过滤，返回所有字段
            // queryParams.put("_source", new JSONObject().fluentPut("includes", includeFields));

            // 执行ES查询
            logger.info("公示库资费列表查询参数：{}", JSON.toJSONString(queryParams));
            JSONObject esResult = ElasticsearchKit.search(Constants.XTY_TARIFF_PUBLIC_LIB_INDEX, queryParams);

            // 输出详细的查询结果用于调试
            logger.info("公示库资费列表查询结果详情：{}", esResult != null ? esResult.toJSONString() : "null");

            // 处理查询结果
            return processEsResult(esResult);
        } catch (Exception e) {
            logger.error("查询公示库资费列表失败", e);
            JSONObject result = new JSONObject();
            result.put("total", 0);
            result.put("rows", new JSONArray());
            return result;
        }
    }

    /**
     * 添加精确匹配查询条件
     *
     * @param mustArray 必须匹配条件数组
     * @param field 字段名
     * @param value 字段值
     */
    private void addTermQuery(JSONArray mustArray, String field, String value) {
        if (StringUtils.isNotBlank(value)) {
            JSONObject termQuery = new JSONObject();
            termQuery.put("term", new JSONObject().fluentPut(field, value));
            mustArray.add(termQuery);
        }
    }

    /**
     * 添加精确匹配查询条件
     *
     * @param mustArray 必须匹配条件数组
     * @param field     字段名
     * @param values    字段值列表
     */
    private void addTermsQuery(JSONArray mustArray, String field, JSONArray values) {
        // null || [] || [""]
        if (values == null || values.isEmpty() || StringUtils.isBlank(values.getString(0))) {
            return;
        }
        JSONObject termQuery = new JSONObject();
        termQuery.put("terms", new JSONObject().fluentPut(field, values));
        mustArray.add(termQuery);
    }

    /**
     * 添加短语匹配查询条件（模糊查询）
     *
     * @param mustArray 必须匹配条件数组
     * @param field 字段名
     * @param value 字段值
     */
    private void addMatchPhraseQuery(JSONArray mustArray, String field, String value) {
        if (StringUtils.isNotBlank(value)) {
            JSONObject matchQuery = new JSONObject();
            matchQuery.put("match_phrase", new JSONObject().fluentPut(field, value));
            mustArray.add(matchQuery);
        }
    }

    /**
     * 添加范围查询条件
     *
     * @param mustArray 必须匹配条件数组
     * @param field 字段名
     * @param beginValue 开始值
     * @param endValue 结束值
     */
    private void addRangeQuery(JSONArray mustArray, String field, String beginValue, String endValue) {
        if (StringUtils.isNotBlank(beginValue) || StringUtils.isNotBlank(endValue)) {
            JSONObject rangeQuery = new JSONObject();
            JSONObject rangeCondition = new JSONObject();

            if (StringUtils.isNotBlank(beginValue)) {
                rangeCondition.put("gte", beginValue);
            }

            if (StringUtils.isNotBlank(endValue)) {
                rangeCondition.put("lte", endValue);
            }

            rangeQuery.put("range", new JSONObject().fluentPut(field, rangeCondition));
            mustArray.add(rangeQuery);
        }
    }

    /**
     * 处理ES查询结果
     *
     * @param esResult ES查询结果
     * @return 处理后的结果
     */
    private JSONObject processEsResult(JSONObject esResult) throws SQLException {
        JSONObject result = new JSONObject();
        JSONArray rows = new JSONArray();

        if (esResult != null && esResult.containsKey("hits")) {
            JSONObject hits = esResult.getJSONObject("hits");
            JSONObject total = hits.getJSONObject("total");
            int totalCount = total.getIntValue("value");
            result.put("total", totalCount);

            // 详细记录查询结果中的 hits 数组
            JSONArray hitArray = hits.getJSONArray("hits");
            logger.info("查询结果 hits 数组大小: {}", hitArray.size());

            // 获取无效版本号缓存
            Set<String> invalidVersions = getInvalidVersionsFromCacheOrDb();
            if (hitArray != null && !hitArray.isEmpty()) {
                for (int i = 0; i < hitArray.size(); i++) {
                    JSONObject hit = hitArray.getJSONObject(i);
                    if (hit.containsKey("_source")) {
                        JSONObject source = hit.getJSONObject("_source");

                        // 处理字段名称，转换为前端需要的格式
                        JSONObject row = new JSONObject();
                        for (String key : source.keySet()) {
                            // 将下划线命名转换为驼峰命名
                            // 特殊字段处理：versionNos（数组）过滤无效版本
                            if ("version_nos".equals(key)) {
                                logger.info("1");
                                JSONArray versionArray = source.getJSONArray(key);
                                JSONArray validVersions = new JSONArray();
                                for (Object ver : versionArray) {
                                    logger.info(ver.toString());
                                    if (ver instanceof String && !invalidVersions.contains(ver)) {
                                        validVersions.add(ver);
                                    }
                                }
                                row.put("versionNos", validVersions);
                            } else {
                                // 默认字段处理
                                String camelKey = toCamelCase(key);
                                row.put(camelKey, source.get(key));
                            }

//
//                            String camelKey = toCamelCase(key);
//                            row.put(camelKey, source.get(key));
                        }
                        rows.add(row);
                    } else {
                        logger.warn("Hit at index {} does not contain _source field", i);
                    }
                }
            } else {
                logger.warn("查询结果中 hits 数组为空或不存在");
            }
        } else {
            logger.warn("ES查询结果为空或不包含 hits 字段");
            result.put("total", 0);
        }

        result.put("rows", rows);
        return result;
    }

    /**
     * 将下划线命名转换为驼峰命名
     *
     * @param name 下划线命名
     * @return 驼峰命名
     */
    private String toCamelCase(String name) {
        if (StringUtils.isBlank(name)) {
            return name;
        }

        StringBuilder sb = new StringBuilder();
        boolean upperCase = false;

        for (int i = 0; i < name.length(); i++) {
            char c = name.charAt(i);

            if (c == '_') {
                upperCase = true;
            } else if (upperCase) {
                sb.append(Character.toUpperCase(c));
                upperCase = false;
            } else {
                sb.append(c);
            }
        }

        return sb.toString();
    }

    /**
     * 获取公示库资费字段列表
     *
     * @return 字段列表
     */
    @InfAuthCheck(resId = "cx-xty-tariff-public-list")
    @WebControl(name = "publicLibRecordFields", type = Types.LIST)
    public JSONObject publicLibRecordFields() {
        try {
            // 构建字段列表
            List<JSONObject> fieldList = new ArrayList<>();

            // 添加字段定义
            addField(fieldList, "reportNo", "方案编号", true);
            addField(fieldList, "name", "资费名称", true);
            addField(fieldList, "entName", "运营商", true);
            addField(fieldList, "provinceName", "省份", true);
            addField(fieldList, "tariffAttr", "资费类型", true);
            addField(fieldList, "tariffAttrType", "资费分类", true);
            addField(fieldList, "type1", "一级分类", true);
            addField(fieldList, "type2", "二级分类", true);
            addField(fieldList, "isTelecom", "是否通信类", true);
            addField(fieldList, "reported", "是否报送", true);
            addField(fieldList, "fees", "资费标准", true);
            addField(fieldList, "feesUnit", "资费单位", true);
            addField(fieldList, "exceedFees", "超出资费", true);
            addField(fieldList, "otherFees", "其他费用", true);
            addField(fieldList, "call", "语音/分钟", true);
            addField(fieldList, "dataNum", "通用流量", true);
            addField(fieldList, "dataUnit", "通用流量单位", true);
            addField(fieldList, "smsNum", "短信/条", true);
            addField(fieldList, "orientTraffic", "定向流量", true);
            addField(fieldList, "orientTrafficUnit", "定向流量单位", true);
            addField(fieldList, "iptv", "IPTV", true);
            addField(fieldList, "bandwidth", "带宽", true);
            addField(fieldList, "rights", "权益", true);
            addField(fieldList, "otherContent", "其他服务内容", true);
            addField(fieldList, "applicablePeople", "适用范围", true);
            addField(fieldList, "validPeriod", "有效期限", true);
            addField(fieldList, "channel", "销售渠道", true);
            addField(fieldList, "duration", "在网要求", true);
            addField(fieldList, "unsubscribe", "退订方式", true);
            addField(fieldList, "others", "其他事项", true);
            addField(fieldList, "onlineDay", "上线日期", true);
            addField(fieldList, "offlineDay", "下线日期", true);
            addField(fieldList, "versionNo", "版本号", true);
            addField(fieldList, "versionNos", "版本号数组", true);
            addField(fieldList, "dateIds", "出现日期数组", true);
            addField(fieldList, "appearMonths", "入库月份", true);
            addField(fieldList, "fieldCheckResult", "要素检查结果", true);
            addField(fieldList, "fieldCheckTime", "检查时间", true);

            JSONObject result = new JSONObject();
            result.put("total", fieldList.size());
            result.put("rows", fieldList);
            return result;
        } catch (Exception e) {
            logger.error("获取公示库资费字段列表失败", e);
            JSONObject result = new JSONObject();
            result.put("total", 0);
            result.put("rows", new JSONArray());
            return result;
        }
    }

    /**
     * 添加字段定义
     *
     * @param fieldList 字段列表
     * @param field 字段名
     * @param title 字段标题
     * @param visible 是否可见
     */
    private void addField(List<JSONObject> fieldList, String field, String title, boolean visible) {
        JSONObject fieldObj = new JSONObject();
        fieldObj.put("field", field);
        fieldObj.put("title", title);
        fieldObj.put("visible", visible);
        fieldList.add(fieldObj);
    }

    private static final String INVALID_VERSION_CACHE_KEY = "invalid_version_nos";

    private Set<String> getInvalidVersionsFromCacheOrDb() throws SQLException {
        // 尝试从 Redis 获取缓存的无效版本号集合
        Set<String> invalidVersionSet = RedissonUtil.get(INVALID_VERSION_CACHE_KEY);
        if (invalidVersionSet != null && !invalidVersionSet.isEmpty()) {
            return invalidVersionSet;
        }

        // 查询数据库
        List<JSONObject> dbList = QueryFactory.getTariffQuery().queryForList(
                "SELECT VERSION_NO FROM "+Constants.getBusiSchema()+".xty_crawler_version WHERE VERSION_STATUS = 'INVALID'",
                null,
                new JSONMapperImpl()
        );

        invalidVersionSet = new HashSet<>();
        for (JSONObject obj : dbList) {
            String versionNo = obj.getString("VERSION_NO");
            if (versionNo != null && !versionNo.isEmpty()) {
                invalidVersionSet.add(versionNo);
            }
        }

        // 写入 Redis，缓存 1 小时
        RedissonUtil.setEx(INVALID_VERSION_CACHE_KEY, invalidVersionSet, 5 * 1);

        return invalidVersionSet;
    }


}
