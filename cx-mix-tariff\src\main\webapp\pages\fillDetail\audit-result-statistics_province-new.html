<!DOCTYPE html>
<html>

<head>
  <title>未报送/未公示资费数量统计</title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
  <!-- 基础的 css js 资源 -->
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.4">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.3">
  <link rel="stylesheet" href="./common.css?v=20241127">
  <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
  <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
  <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
  <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.1"></script>
  <script src="/cc-base/static/js/my_i18n.js?v=202111"></script>
  <script src="/cc-base/static/js/i18n.js?v=1"></script>
  <script src="/cc-base/static/cdn/axios@0.26.1/axios.min.js"></script>

  <style>
    .el-tabs__nav-wrap::after {
      background-color: #ffffff;
    }

    .export {
      display: flex;
      white-space: nowrap;
    }

    .card-content {
      overflow: auto !important;
    }

    .el-table__cell.self-defined {
      border-left: 1px solid #e1e1e1 !important;
    }

    .yq-table-page .yq-card .search-form {
      grid-template-columns: repeat(3, 1fr) !important;
      gap: 16px 4px !important;
    }

    .yq-table-page .yq-card .search-form .btns {
      grid-column: 3;
    }
  </style>
</head>

<body class="yq-page-full vue-box">
  <div id="qualityTask" class="flex yq-table-page" v-cloak
    v-auth:[permissions]="'cx-xty-audit-statistics-province;cx-xty-audit-statistics-ct;cx-xty-audit-statistics-cm;cx-xty-audit-statistics-un;cx-xty-audit-statistics-cbn;cx-xty-tariff-audit-export;'">
    <div class="yq-card">
      <div class="card-header">
        <div class="head-title">{{ getI18nValue(entLabelMap[ent] + '未报送/未公示资费数量统计') }}【{{subTitle}}】</div>
        <el-popover placement="bottom" title="温馨提示" trigger="hover">
          <p class="tip-item"><span class="tip-item_name">未报送资费数：</span>指有用户订购但未报送的资费方案数量。</p>
          <p class="tip-item"><span class="tip-item_name">资费报送率：</span>指活跃在售和续订资费方案数占用户订购的资费方案总数比例。</p>
          <div slot="reference" class="el-icon-info" style="margin-left: 8px; font-size: 16px;"></div>
        </el-popover>
        <div class="yq-table-control">
          <el-radio-group v-model="tableName_province" @input="handleRadio">
            <el-radio-button label="day">日报</el-radio-button>
            <el-radio-button label="month">月报</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="card-content">
        <div class="search-box">
          <el-form :inline="false" :model="searchForm" ref="form" class="search-form" label-width="70px" size="small">
            <el-form-item :label="getI18nValue('出现日期')" prop="date">
              <el-date-picker v-if="tableName_province==='day'" key="day" v-model="searchForm.date" type="daterange"
                align="right" unlink-panels range-separator="~" :start-placeholder="getI18nValue('开始日期')"
                :end-placeholder="getI18nValue('结束日期')" value-format="yyyy-MM-dd" clearable>
              </el-date-picker>
              <el-date-picker v-if="tableName_province==='month'" key="month" v-model="searchForm.date"
                type="monthrange" range-separator="~" start-placeholder="开始月份" end-placeholder="结束月份" :clearable="false"
                value-format="yyyy-MM">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="订购省份" prop="provinceCode">
              <el-select v-model="searchForm.provinceCode" placeholder="请选择" filterable clearable
                :disabled="(groupType=='03' || groupType=='01') ? true:false">
                <el-option v-for="(item, index) in reportObj" :key="index" :label="item.PROVINCE_NAME"
                  :value="item.PROVINCE_CODE"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="btns" label-width="50px">
              <el-button type="primary" plain size="small" icon="el-icon-refresh"
                @click="handleReset">{{getI18nValue('重置')}}</el-button>
              <el-button type="primary" size="small" icon="el-icon-search"
                @click="getList">{{getI18nValue('搜索')}}</el-button>
              <el-button type="primary" plain size="small"
                @click="handleExportFront($refs.table.$el, {title:'省内未报送/未公示资费数量统计'+getDateStamp()})"
                v-if="permissions['cx-xty-tariff-audit-export']">
                <i class="el-icon-download"></i>导出
              </el-button>
            </el-form-item>
          </el-form>

        </div>
        <p v-if="tableName_province==='month'" style="text-align: right; color: red;">月报中未公示资费数、公示率仅供参考</p>
        <el-tabs v-model="activeName" style="margin-bottom: 24px;">
          <el-tab-pane :label="getI18nValue('未报送资费')" name="0">
            <div id="myChart1" style="width: 100%;height: 300px">
            </div>
          </el-tab-pane>
          <el-tab-pane :label="getI18nValue('资费报送率')" name="1">
            <div id="myChart2" style="width: 100%;height: 300px">
            </div>
          </el-tab-pane>
        </el-tabs>
        <div>
          <el-table ref="table" :data="tableData.data" style="width: 100%" v-loading="tableData.loading" stripe>
            <el-table-column prop="DATE_ID" align="center" width="160">
            </el-table-column>
            <el-table-column v-if="!ent || ent==1" prop="TELECOM" :label="getI18nValue('电信')" align="center">
              <el-table-column class-name="self-defined" prop="TELECOM" :label="getI18nValue('未报送资费数')" align="center">
                <template slot-scope="scope">
                  <el-link :underline="false" @click="handleToPlan(scope.row,'1')">
                    {{scope.row.TELECOM}}
                  </el-link>
                </template>
              </el-table-column>
              <el-table-column prop="TELECOM_RATE" :label="getI18nValue('资费报送率')" align="center">
              </el-table-column>
              <el-table-column class-name="self-defined" prop="TELECOM_UN_PUBLIC_NUM" :label="getI18nValue('未公示资费数')"
                align="center">
                <template slot-scope="scope">
                  <el-link v-if="tableName_province==='day'" :underline="false" @click="handleToUnpublic(scope.row,'1')">
                    {{scope.row.TELECOM_UN_PUBLIC_NUM}}
                  </el-link>
                  <span v-else>{{scope.row.TELECOM_UN_PUBLIC_NUM}}</span>
                </template>
              </el-table-column>
              <el-table-column prop="TELECOM_PUBLIC_RATE" :label="getI18nValue('资费公示率')" align="center">
              </el-table-column>
            </el-table-column>
            <el-table-column v-if="!ent || ent==2" prop="MOBILE" :label="getI18nValue('移动')" align="center">
              <el-table-column class-name="self-defined" prop="MOBILE" :label="getI18nValue('未报送资费数')" align="center">
                <template slot-scope="scope">
                  <el-link :underline="false" @click="handleToPlan(scope.row,'2')">
                    {{scope.row.MOBILE}}
                  </el-link>
                </template>
              </el-table-column>
              <el-table-column prop="MOBILE_RATE" :label="getI18nValue('资费报送率')" align="center">
              </el-table-column>
              <el-table-column class-name="self-defined" prop="MOBILE_UN_PUBLIC_NUM" :label="getI18nValue('未公示资费数')"
                align="center">
                <template slot-scope="scope">
                  <el-link v-if="tableName_province==='day'" :underline="false" @click="handleToUnpublic(scope.row,'2')">
                    {{scope.row.MOBILE_UN_PUBLIC_NUM}}
                  </el-link>
                  <span v-else>{{scope.row.MOBILE_UN_PUBLIC_NUM}}</span>
                </template>
              </el-table-column>
              <el-table-column prop="MOBILE_PUBLIC_RATE" :label="getI18nValue('资费公示率')" align="center">
              </el-table-column>
            </el-table-column>
            <el-table-column v-if="!ent || ent==3" prop="UNICOM" :label="getI18nValue('联通')" align="center">
              <el-table-column class-name="self-defined" prop="UNICOM" :label="getI18nValue('未报送资费数')" align="center">
                <template slot-scope="scope">
                  <el-link :underline="false" @click="handleToPlan(scope.row,'3')">
                    {{scope.row.UNICOM}}
                  </el-link>
                </template>
              </el-table-column>
              <el-table-column prop="UNICOM_RATE" :label="getI18nValue('资费报送率')" align="center">
              </el-table-column>
              <el-table-column class-name="self-defined" prop="UNICOM_UN_PUBLIC_NUM" :label="getI18nValue('未公示资费数')"
                align="center">
                <template slot-scope="scope">
                  <el-link v-if="tableName_province==='day'" :underline="false" @click="handleToUnpublic(scope.row,'3')">
                    {{scope.row.UNICOM_UN_PUBLIC_NUM}}
                  </el-link>
                  <span v-else>{{scope.row.UNICOM_UN_PUBLIC_NUM}}</span>
                </template>
              </el-table-column>
              <el-table-column prop="UNICOM_PUBLIC_RATE" :label="getI18nValue('资费公示率')" align="center">
              </el-table-column>
            </el-table-column>
            <el-table-column v-if="!ent || ent==5" prop="BROAD" :label="getI18nValue('广电')" align="center">
              <el-table-column class-name="self-defined" prop="BROAD" :label="getI18nValue('未报送资费数')" align="center">
                <template slot-scope="scope">
                  <el-link :underline="false" @click="handleToPlan(scope.row,'5')">
                    {{scope.row.BROAD}}
                  </el-link>
                </template>
              </el-table-column>
              <el-table-column prop="BROAD_RATE" :label="getI18nValue('资费报送率')" align="center">
              </el-table-column>
              <el-table-column class-name="self-defined" prop="BROAD_UN_PUBLIC_NUM" :label="getI18nValue('未公示资费数')"
                align="center">
                <template slot-scope="scope">
                  <el-link v-if="tableName_province==='day'" :underline="false" @click="handleToUnpublic(scope.row,'5')">
                    {{scope.row.BROAD_UN_PUBLIC_NUM}}
                  </el-link>
                  <span v-else>{{scope.row.BROAD_UN_PUBLIC_NUM}}</span>
                </template>
              </el-table-column>
              <el-table-column prop="BROAD_PUBLIC_RATE" :label="getI18nValue('资费公示率')" align="center">
              </el-table-column>
            </el-table-column>
            <el-table-column v-if="!ent" prop="ALLTOTAL" :label="getI18nValue('全行业')" align="center">
              <el-table-column class-name="self-defined" prop="ALLTOTAL" :label="getI18nValue('未报送资费数')" align="center">
                <template slot-scope="scope">
                  <el-link :underline="false" @click="handleToPlan(scope.row)">
                    {{scope.row.ALLTOTAL}}
                  </el-link>
                </template>
              </el-table-column>
              <el-table-column prop="ALLTOTAL_RATE" :label="getI18nValue('资费报送率')" align="center">
              </el-table-column>
              <el-table-column class-name="self-defined" prop="ALLTOTAL_UN_PUBLIC_NUM" :label="getI18nValue('未公示资费数')"
                align="center">
                <template slot-scope="scope">
                  <el-link v-if="tableName_province==='day'" :underline="false" @click="handleToUnpublic(scope.row)">
                    {{scope.row.ALLTOTAL_UN_PUBLIC_NUM}}
                  </el-link>
                  <span v-else>{{scope.row.ALLTOTAL_UN_PUBLIC_NUM}}</span>
                </template>
              </el-table-column>
              <el-table-column prop="ALLTOTAL_PUBLIC_RATE" :label="getI18nValue('资费公示率')" align="center">
              </el-table-column>
            </el-table-column>
          </el-table>
          <!-- <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="pageNav.page" :page-sizes="[10, 15, 20, 30]" :page-size="pageNav.size"
              layout="total, sizes, prev, pager, next, jumper" :total="pageNav.total">
            </el-pagination> -->
        </div>
      </div>
    </div>
  </div>

  <script type="text/javascript" src="/cx-mix-tariff/static/js/time.js"></script>
  <script src="./mixins.js?v=20250723"></script>
  <script src="/cx-mix-tariff/static/js/echarts.min.js?v=1.0.0"></script>
  <script src="/cx-mix-tariff/static/js/FileSaver.min.js"></script>

  <script>
    var qualityTask = new Vue({
      el: '#qualityTask',
      mixins: [mixins],

      data: function () {
        return {
          key: '',
          activeName: '0',
          myChart1: null,
          myChart2: null,
          pickerOptions: {
            disabledDate(time) {
              return time.getTime() > Date.now();
            }
          },
          subTitle: "日报",
        }
      },
      computed: {
        // 权限
        hasPermission() {
          return this.ent ?
            (this.ent == 1 ? this.permissions['cx-xty-audit-statistics-ct'] :
              this.ent == 2 ? this.permissions['cx-xty-audit-statistics-cm'] :
                this.ent == 3 ? this.permissions['cx-xty-audit-statistics-un'] :
                  this.ent == 5 ? this.permissions['cx-xty-audit-statistics-cbn'] : false) :
            this.permissions['cx-xty-audit-statistics-province']
        }
      },
      watch: {
        activeName(v) {
          if (v === '0') {
            this.$nextTick(() => {
              this.myChart1.resize();
            })
          } else {
            this.$nextTick(() => {
              this.myChart2.resize();
            })
          }
        },
      },
      methods: {
        initchart: function () {
          var chartDom = document.getElementById('myChart1');
          this.myChart1 = echarts.init(chartDom);
          chartDom = document.getElementById('myChart2');
          this.myChart2 = echarts.init(chartDom);
          var option = {
            tooltip: {
              trigger: 'axis',
              appendToBody: true,
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            toolbox: {
              feature: {
                saveAsImage: {}
              }
            },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: []
            },
          }
          var option1 = Object.assign({}, option, {
            yAxis: {
              name: '未报送资费数',
              type: 'value'
            },
            series: this.ent ? [{
              name: this.entLabelMap[this.ent],
              type: 'line',
              smooth: true,
              data: []
            }] : [
              {
                name: '电信',
                type: 'line',
                smooth: true,
                data: []
              },
              {
                name: '移动',
                type: 'line',
                smooth: true,
                data: []
              },
              {
                name: '联通',
                type: 'line',
                smooth: true,
                data: []
              },
              {
                name: '广电',
                type: 'line',
                smooth: true,
                data: []
              },
              {
                name: '全行业',
                type: 'line',
                smooth: true,
                data: []
              },
            ],
            legend: {
              data: this.ent ? [this.entLabelMap[this.ent]] : ['电信', '移动', '联通', '广电', '全行业']
            },
          });
          var option2 = Object.assign({}, option, {
            yAxis: {
              name: '备案百分比（%）',
              type: 'value',
              max: 100,
              min: 0,
              // axisLabel: {
              //   formatter: '{value} %'
              // }
            },
            series: this.ent ? [{
              name: this.entLabelMap[this.ent] + '资费报送率（%）',
              type: 'line',
              smooth: true,
              data: []
            }] : [
              {
                name: '电信资费报送率（%）',
                type: 'line',
                smooth: true,
                data: []
              },
              {
                name: '移动资费报送率（%）',
                type: 'line',
                smooth: true,
                data: []
              },
              {
                name: '联通资费报送率（%）',
                type: 'line',
                smooth: true,
                data: []
              },
              {
                name: '广电资费报送率（%）',
                type: 'line',
                smooth: true,
                data: []
              },
              {
                name: '全行业资费报送率（%）',
                type: 'line',
                smooth: true,
                data: []
              },
            ],
            legend: {
              data: this.ent ? [this.entLabelMap[this.ent] + '资费报送率（%）'] : ['电信资费报送率（%）', '移动资费报送率（%）', '联通资费报送率（%）', '广电资费报送率（%）', '全行业资费报送率（%）']
            },
          });

          option1 && this.myChart1.setOption(option1);
          option2 && this.myChart2.setOption(option2);
          window.addEventListener(
            "resize",
            () => {
              this.myChart1.resize(); //当浏览器窗口大小发生变化时，图表可以进行自适应
              this.myChart2.resize();
            },
          );
        },
        getList(label) {
          let start = new Date(this.searchForm.date[0])
          let end = new Date(this.searchForm.date[1])
          if (this.tableName_province === 'day') {
            if ((end - start) > 30 * 24 * 60 * 60 * 1000) {
              this.$message.warning('日报时间周期不能超过31天')
              return false
            }
          } else if (this.tableName_province === 'month') {
            if ((end - start) > 6 * 30 * 24 * 60 * 60 * 1000) {
              this.$message.warning('月报时间周期不能超过6个月')
              return false
            }
          }

          const data = {
            // pageType: "3",
            type: this.tableName_province,
            stime: this.searchForm.date[0],
            etime: this.searchForm.date[1],
            provinceCode: this.searchForm.provinceCode
          }
          if (label != 'export') {
            this.tableData.loading = true
            yq.remoteCall(
              '/cx-mix-tariff/webcall?action=tariffAuditStaticDao.getProvinceNotReportCount',
              data,
              (res) => {
                if (res.state == 1) {
                  res.data = (res.data || []).filter(item => item.DAYID !== '49990101' && item.MONTHID !== '499912')
                  // 前端不再处理
                  // if (this.tableName_province == 'day') {
                  //   res.data.forEach(val => {
                  //     val.DATE_ID_FORMAT = val.DATE_ID != '平均(去重)' ? this.hanldeFormatDate(val.DATE_ID) : val.DATE_ID
                  //   })
                  // }
                  let keys = ['TELECOM', 'MOBILE', 'UNICOM', 'BROAD', 'ALLTOTAL']
                  this.tableData.data = JSON.parse(JSON.stringify(res.data));
                  this.tableData.data = this.tableData.data
                    // .map(item => {
                    //   keys.forEach(key => {
                    //     item[key + '_PER'] = this.getRate(item, key)
                    //   })
                    //   return item
                    // })
                    .sort((a, b) => {
                      let reg = /[年月]/g
                      a = a.DATE_ID.replaceAll(reg, '-').replace('日', '')
                      b = b.DATE_ID.replaceAll(reg, '-').replace('日', '')
                      return new Date(b).valueOf() - new Date(a).valueOf()
                    })
                  this.tableData.data.forEach(item => {
                    keys.forEach(key => {
                      item[key] = (!item[key] && item[key] !== 0) ? '未报送，稽核失败' : item[key]
                      item[key + '_RATE'] = (!item[key + '_RATE'] && item[key] !== 0) ? '-' : (item[key + '_RATE'] + '%')
                    })
                  })

                  // 处理 data2 数据 - 公示数据
                  if (res.data2) {
                    // 创建一个映射来存储按日期分组的公示数据
                    const publicDataMap = {};

                    res.data2.forEach(item => {
                      // 处理日期格式转换，如果是"汇总"则直接使用
                      let dateKey = item.DATE_VALUE;
                      if (dateKey && dateKey !== '汇总') {
                        if (dateKey.length === 8) {
                          // 日报：转换 "20250603" 为 "2025年06月03日" 格式
                          const year = dateKey.substring(0, 4);
                          const month = dateKey.substring(4, 6);
                          const day = dateKey.substring(6, 8);
                          dateKey = `${year}年${month}月${day}日`;
                        } else if (dateKey.length === 6) {
                          // 月报：转换 "202506" 为 "2025年06月" 格式
                          const year = dateKey.substring(0, 4);
                          const month = dateKey.substring(4, 6);
                          dateKey = `${year}年${month}月`;
                        }
                      }

                      if (!publicDataMap[dateKey]) {
                        publicDataMap[dateKey] = {};
                      }

                      // 根据运营商代码映射到对应字段
                      const entMapping = {
                        '1': 'TELECOM',
                        '2': 'MOBILE',
                        '3': 'UNICOM',
                        '5': 'BROAD'
                      };

                      const entKey = entMapping[item.ENT];
                      if (entKey) {
                        publicDataMap[dateKey][entKey + '_UN_PUBLIC_NUM'] = (!item.UN_PUBLIC_NUM && item.UN_PUBLIC_NUM !== 0) ? '未报送，稽核失败' : item.UN_PUBLIC_NUM;
                        publicDataMap[dateKey][entKey + '_PUBLIC_RATE'] = !item.PUBLIC_RATE && item.PUBLIC_RATE !== 0 ? '-' : (item.PUBLIC_RATE + '%')
                      }

                      // 处理全行业汇总数据（当ENT为空或特殊值时）
                      if (!item.ENT || item.ENT === '' || item.ENT === 'ALL') {
                        publicDataMap[dateKey]['ALLTOTAL_UN_PUBLIC_NUM'] = (!item.UN_PUBLIC_NUM && item.UN_PUBLIC_NUM !== 0) ? '未报送，稽核失败' : item.UN_PUBLIC_NUM;
                        publicDataMap[dateKey]['ALLTOTAL_PUBLIC_RATE'] = !item.PUBLIC_RATE && item.PUBLIC_RATE !== 0 ? '-' : (item.PUBLIC_RATE + '%')
                      }
                    });

                    console.log('publicDataMap', publicDataMap)

                    // 将公示数据合并到主数据中
                    this.tableData.data.forEach(item => {
                      const dateKey = item.DATE_ID;

                      // 将汇总数据合并到"去重/平均"或"平均(去重)"行中
                      if ((item.DATE_ID === '去重/平均' || item.DATE_ID === '平均(去重)') && publicDataMap['汇总']) {
                        Object.assign(item, publicDataMap['汇总']);
                        return
                      }

                      // 处理未报送的数据
                      keys.forEach(key => {
                        item[key + '_UN_PUBLIC_NUM'] = '未报送，稽核失败'
                        item[key + '_PUBLIC_RATE'] = '-'
                      })

                      if (publicDataMap[dateKey]) {
                        Object.assign(item, publicDataMap[dateKey]);
                        return
                      }
                    });
                  }

                  this.tableData.loading = false

                  const xdata = []
                  const result = {
                    TELECOM: [],
                    MOBILE: [],
                    UNICOM: [],
                    BROAD: [],
                    ALLTOTAL: [],
                  }

                  keys.forEach(key => {
                    result[key + '_RATE'] = []
                  })

                  res.data.forEach(item => {
                    if (item.DATE_ID != '平均(去重)') {
                      xdata.push(item.DATE_ID)
                      keys.forEach(key => {
                        result[key].push(item[key])
                      })
                      keys.forEach(key => {
                        result[key + '_RATE'].push(item[key + '_RATE'])
                      })
                    }
                  })

                  const myChartOptions1 = this.myChart1.getOption()
                  for (let i = 0; i < keys.length; i++) {
                    let entKey = this.ent ? this.entKeyMap[this.ent] : keys[i]
                    myChartOptions1.series[i] && (myChartOptions1.series[i].data = result[entKey])
                  }
                  myChartOptions1.xAxis[0].data = xdata;
                  this.myChart1.setOption(myChartOptions1, true)

                  const myChartOptions2 = this.myChart2.getOption()
                  for (let i = 0; i < keys.length; i++) {
                    let entKey = this.ent ? this.entKeyMap[this.ent] : keys[i]
                    myChartOptions1.series[i] && (myChartOptions2.series[i].data = result[entKey + '_RATE'])
                  }
                  myChartOptions2.xAxis[0].data = xdata;
                  this.myChart2.setOption(myChartOptions2, true)

                } else {
                  this.tableData.loading = false
                }
                //this.tableData.data[this.tableData.data.length - 1].DATE_ID = '去重/平均'
                console.log('tableData', this.tableData.data)
              }
            );
          } else {
            location.href = '/cx-mix-tariff/servlet/tariff?action=Export1&' + $.param(data);
            // location.href = '/cx-mix-tariff/webcall?action=auditTotalDao.auditResultTimeTotal2'
            // yq.remoteCall(
            //   '/cx-mix-tariff/webcall?action=auditTotalDao.auditResultTimeTotal2',
            //   data,
            //   (res) => {}
            // );
          }

        },
        // getRate(item, prop) {
        //   if (!item[prop] || item[prop] == 0) {
        //     return '-'
        //   }
        //   let onSale = Number(item[prop + '_ONSALE'])
        //   let all = Number(item[prop + '_ALL'])
        //   return all === 0 ? 0 : ((onSale / all) * 100).toFixed(1)
        // },
        // 跳转到未备案方案
        handleToPlan(item, index) {
          const data = {
            ent: index,
            provinceCode: item.PROVINCE_CODE ? item.PROVINCE_CODE : this.searchForm.provinceCode,

          }
          if (this.tableName_province == 'month') {
            // const date = (item.DATE_ID != '平均(去重)' ?  : [this.getMonthDates(this.searchForm.date[0]).firstDay, this.getMonthDates(this.searchForm.date[1]).lastDay])
            let date
            if (item.MIN_DATE && item.MAX_DATE) {
              date = [this.getFirstAndLastDayOfMonth(item.MIN_DATE).firstDay, this.getFirstAndLastDayOfMonth(item.MAX_DATE).lastDay]
            } else {
              date = [this.getFirstAndLastDayOfMonth(this.searchForm.date[0]).firstDay, this.getFirstAndLastDayOfMonth(this.searchForm.date[1]).lastDay]
            }
            data.appearTime = date.join(',')
          } else {
            let date = item.DATE_ID !== '平均(去重)' ? [item.MIN_DATE, item.MAX_DATE] : this.searchForm.date
            if (item.MIN_DATE && item.MAX_DATE) {
              date = [item.MIN_DATE, item.MAX_DATE]
            } else {
              date = this.searchForm.date
            }
            data.appearTime = date.join(',')
          }

          top.popup.openTab({
            url: '/cx-mix-tariff/pages/fillDetail/unregistered-tariff-plan-new.html',
            title: getI18nValue('未报送资费方案'),
            data: data,
            id: 'plan' + this.generateUniqueEightDigitNumber()
          })

        },
        // 跳转到未公示资费方案
        handleToUnpublic(item, index) {
          const data = {
            ent: index,
            provinceCode: item.PROVINCE_CODE ? item.PROVINCE_CODE : this.searchForm.provinceCode,
          }

          if (this.tableName_province == 'month') {
            // const date = (item.DATE_ID != '平均(去重)' ?  : [this.getMonthDates(this.searchForm.date[0]).firstDay, this.getMonthDates(this.searchForm.date[1]).lastDay])
            let date
            if (item.MIN_DATE && item.MAX_DATE) {
              date = [this.getFirstAndLastDayOfMonth(item.MIN_DATE).firstDay, this.getFirstAndLastDayOfMonth(item.MAX_DATE).lastDay]
            } else {
              date = [this.getFirstAndLastDayOfMonth(this.searchForm.date[0]).firstDay, this.getFirstAndLastDayOfMonth(this.searchForm.date[1]).lastDay]
            }
            data.appearTime = date.join(',')
          } else {
            let date = item.DATE_ID !== '平均(去重)' ? [item.MIN_DATE, item.MAX_DATE] : this.searchForm.date

            if (item.MIN_DATE && item.MAX_DATE) {
              date = [item.MIN_DATE, item.MAX_DATE]
            } else {
              date = this.searchForm.date
            }
            data.appearTime = date.join(',')
          }

          top.popup.openTab({
            url: '/cx-mix-tariff/pages/fillDetail/tariff-plan-list-unpublic.html',
            title: getI18nValue('未公示资费方案'),
            data: data,
            id: 'unpublic' + this.generateUniqueEightDigitNumber()
          })

        },
      },
      mounted() {
        this.initchart()
        this.searchForm.date = [getBeforeDayDate(7), getYesterDayDate()]
        // this.getList()
      },
    })
  </script>
</body>

</html>