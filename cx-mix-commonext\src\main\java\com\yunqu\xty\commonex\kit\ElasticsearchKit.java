package com.yunqu.xty.commonex.kit;

import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * <p>
 * Elasticsearch工具类
 * </p>
 *
 * @ClassName ElasticsearchKit
 * <AUTHOR> Copy This Tag)
 * @Description Elasticsearch工具类
 * @Since create in 2024/5/14 15:21
 * @Version v1.0
 * @Copyright Copyright (c) 2024
 * @Company 广州云趣信息科技有限公司
 */
public class ElasticsearchKit {

    public static final Logger LOGGER = LoggerFactory.getLogger(LogEngine.getLogger("cx-mix-commonex-es").getName());

    static class Holder {

        private static final String ES_URL = ServerContext.getProperties("ES_URL", "");

        private static final String ES_USER = ServerContext.getProperties("ES_USER", "");

        private static final String ES_PASS = ServerContext.getProperties("ES_PASS", "");

        private static final String ES_INDEX_SUFFIX = ServerContext.getProperties("ES_INDEX_SUFFIX", "");
    }

    private static String getRealIndexName(String indexName) {
        if (StringUtils.isBlank(Holder.ES_INDEX_SUFFIX)) {
            return indexName;
        }
        return indexName + Holder.ES_INDEX_SUFFIX;
    }

    /**
     * 提供一个方法，使用http方案创建索引，入参为索引名称和索引的json格式的配置
     *
     * <AUTHOR>
     * @param indexName   索引名称
     * @param indexConfig 索引配置
     */
    public static JSONObject createIndex(String indexName, JSONObject indexConfig) {
        LOGGER.info("create index:{}, 配置内容为：{}", getRealIndexName(indexName), indexConfig);
        String result = HttpRequest.put(Holder.ES_URL + "/" + getRealIndexName(indexName))
                .basicAuth(Holder.ES_USER, Holder.ES_PASS)
                .contentType("application/json")
                .body(indexConfig.toJSONString())
                .execute()
                .body();
        LOGGER.info("create index:{} result:{}", getRealIndexName(indexName), result);
        return JSONObject.parseObject(result);
    }

    /**
     * 提供一个方法，使用http方案删除索引，入参为索引名称
     *
     * @param indexName 索引名称
     */
    public static JSONObject deleteIndex(String indexName) {
        LOGGER.info("delete index:{}", getRealIndexName(indexName));
        String result = HttpRequest.delete(Holder.ES_URL + "/" + getRealIndexName(indexName))
                .basicAuth(Holder.ES_USER, Holder.ES_PASS)
                .execute()
                .body();
        LOGGER.info("delete index result:{}", result);
        return JSONObject.parseObject(result);
    }

    /**
     * 提供一个方法，使用http方案更新索引字段，入参为索引名称和索引的json格式的配置
     *
     * @param indexName   索引名称
     * @param indexConfig 索引配置
     */
    public static JSONObject updateIndex(String indexName, JSONObject indexConfig) {
        LOGGER.info("update index:{}, 配置内容为：{}", getRealIndexName(indexName), indexConfig);
        String result = HttpRequest.put(Holder.ES_URL + "/" + getRealIndexName(indexName) + "/_mapping")
                .basicAuth(Holder.ES_USER, Holder.ES_PASS)
                .contentType("application/json")
                .body(indexConfig.toJSONString())
                .execute()
                .body();
        LOGGER.info("update index result:{}", result);
        return JSONObject.parseObject(result);
    }

    /**
     * 提供一个方法，用于新增数据到ES
     *
     * <AUTHOR>
     */
    public static JSONObject addDoc(String indexName, String id, JSONObject data) {
        LOGGER.info("add data to index:{}, id:{}, data:{}", getRealIndexName(indexName), id, data);
        String result = HttpRequest.put(Holder.ES_URL + "/" + getRealIndexName(indexName) + "/_doc/" + id)
                .basicAuth(Holder.ES_USER, Holder.ES_PASS)
                .contentType("application/json")
                .body(data.toJSONString())
                .execute()
                .body();
        LOGGER.info("add data result:{}", result);
        return JSONObject.parseObject(result);
    }

    /**
     * 提供一个方法，用于新增数据到ES
     *
     * <AUTHOR>
     */
    public static <T> JSONObject addDoc(String indexName, String id, T data) {
        LOGGER.info("add data to index:{}, id:{}, data:{}", getRealIndexName(indexName), id, data);
        String result = HttpRequest.put(Holder.ES_URL + "/" + getRealIndexName(indexName) + "/_doc/" + id)
                .basicAuth(Holder.ES_USER, Holder.ES_PASS)
                .contentType("application/json")
                .body(JSONObject.toJSONString(data))
                .execute()
                .body();
        LOGGER.info("add data result:{}", result);
        return JSONObject.parseObject(result);
    }

    /**
     * 更新索引
     *
     * <AUTHOR>
     * @date 2024/5/21 15:01
     * @param indexName 索引名称
     * @param id        文档ID
     * @param data      文档json数据
     */
    public static JSONObject editDoc(String indexName, String id, JSONObject data) {
        LOGGER.info("edit data to index:{}, id:{}, data:{}", getRealIndexName(indexName), id, data);
        String result = HttpRequest.post(Holder.ES_URL + "/" + getRealIndexName(indexName) + "/_update/" + id)
                .basicAuth(Holder.ES_USER, Holder.ES_PASS)
                .contentType("application/json")
                .body(data.toJSONString())
                .execute()
                .body();
        LOGGER.info("edit data result:{}", result);
        return JSONObject.parseObject(result);
    }

    /**
     * 提供一个方法，用于删除ES数据
     */
    public static JSONObject deleteDoc(String indexName, String id) {
        LOGGER.info("delete data from index:{}, id:{}", getRealIndexName(indexName), id);
        String result = HttpRequest.delete(Holder.ES_URL + "/" + getRealIndexName(indexName) + "/_doc/" + id)
                .basicAuth(Holder.ES_USER, Holder.ES_PASS)
                .execute()
                .body();
        LOGGER.info("delete data result:{}", result);
        return JSONObject.parseObject(result);
    }

    /**
     * 提供一个方法用于根据条件删除数据
     *
     * @param indexName
     * @param searchParam
     * @return
     */
    public static JSONObject deleteByQuery(String indexName, JSONObject searchParam) {
        String url = Holder.ES_URL + "/" + getRealIndexName(indexName) + "/_delete_by_query";
        LOGGER.info("delete by query index:{}, server:{}, param:{}", getRealIndexName(indexName), url, searchParam);
        String result = HttpRequest.post(url)
                .basicAuth(Holder.ES_USER, Holder.ES_PASS)
                .contentType("application/json")
                .body(searchParam.toJSONString())
                .execute()
                .body();
        LOGGER.info("delete by query result:{}", result);
        return JSONObject.parseObject(result);
    }

    /**
     * 提供一个方法，用于分页查询一个索引
     */
    public static JSONObject search(String indexName, JSONObject searchParam) {
        String url = Holder.ES_URL + "/" + getRealIndexName(indexName) + "/_search";
        LOGGER.info("search index:{}, server:{}, param:{}", getRealIndexName(indexName), url, searchParam);

        String responseBody = null;
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);

            // 编码用户名和密码
            String auth = Holder.ES_USER + ":" + Holder.ES_PASS;
            byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes());
            String authHeader = "Basic " + new String(encodedAuth);

            // 设置请求头
            httpPost.setHeader("Accept-Encoding", "gzip, deflate");
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Authorization", authHeader);

            // 设置请求体
            StringEntity entity = new StringEntity(searchParam.toString(), StandardCharsets.UTF_8);
            httpPost.setEntity(entity);

            // 执行请求
            CloseableHttpResponse response = httpClient.execute(httpPost);

            // 获取响应实体
            HttpEntity responseEntity = response.getEntity();

            // 检查响应的Content-Encoding头是否包含gzip
            String contentEncoding = response.getFirstHeader("Content-Encoding") != null
                    ? response.getFirstHeader("Content-Encoding").getValue()
                    : null;

            if (contentEncoding != null && contentEncoding.contains("gzip")) {
                // 解压缩gzip响应
                try (GZIPInputStream gis = new GZIPInputStream(responseEntity.getContent());
                        InputStreamReader reader = new InputStreamReader(gis);
                        BufferedReader in = new BufferedReader(reader)) {
                    StringBuilder sb = new StringBuilder();
                    String line;
                    while ((line = in.readLine()) != null) {
                        sb.append(line);
                    }
                    responseBody = sb.toString();
                }
            } else {
                // 直接获取响应内容
                responseBody = EntityUtils.toString(responseEntity);
            }

            // 打印响应内容
            System.out.println(responseBody);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        LOGGER.info("search result:{}", responseBody);
        return JSONObject.parseObject(responseBody);
    }

    /**
     * 批量新增或更新ES数据
     *
     * @param indexName 索引名称
     * @param dataList  数据列表，每个JSONObject应包含'id'字段
     * @return 批量操作的结果
     */
    public static JSONObject batchAddOrUpdateDoc(String indexName, List<JSONObject> dataList) {
        String realIndexName = getRealIndexName(indexName);
        String serialId = IdUtil.getSnowflakeNextIdStr();
        LOGGER.info("批量新增或更新数据到索引:{}, 数据数量:{}", realIndexName, dataList.size());
        LOGGER.info("batchAddOrUpdateDocInfo BEGIN:{}", serialId);

        StringBuilder bulkRequestBody = new StringBuilder(dataList.size() * 200);
        for (JSONObject data : dataList) {
            String id = data.getString("id");
            if (StringUtils.isBlank(id)) {
                LOGGER.warn("数据缺少id字段，将被跳过: {}", data);
                continue;
            }
            bulkRequestBody.append(String.format(
                    "{\"index\":{\"_index\":\"%s\",\"_id\":\"%s\"}}\n%s\n",
                    realIndexName, id, data.toJSONString()));
        }

        String result = null;
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(Holder.ES_URL + "/_bulk");

            // 设置基本认证
            String auth = Holder.ES_USER + ":" + Holder.ES_PASS;
            byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes(StandardCharsets.UTF_8));
            String authHeader = "Basic " + new String(encodedAuth);
            httpPost.setHeader(HttpHeaders.AUTHORIZATION, authHeader);

            // 设置内容类型和GZIP
            httpPost.setHeader(HttpHeaders.CONTENT_TYPE, "application/x-ndjson");
            httpPost.setHeader(HttpHeaders.ACCEPT_ENCODING, "gzip");

            // 压缩请求体
            byte[] compressedData = compress(bulkRequestBody.toString());
            ByteArrayEntity entity = new ByteArrayEntity(compressedData);
            entity.setContentEncoding("gzip");
            httpPost.setEntity(entity);

            // 执行请求
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity responseEntity = response.getEntity();
                result = EntityUtils.toString(responseEntity);
            }
            LOGGER.info("batchAddOrUpdateDocInfo END:{}", serialId);
        } catch (IOException e) {
            LOGGER.error("批量新增或更新数据时发生错误", e);
        }

        LOGGER.info("批量新增或更新数据结果:{}", result);
        return JSONObject.parseObject(result);
    }

    /**
     * 执行批量操作
     *
     * @param bulkRequestBody 批量操作的请求体，需要符合 Elasticsearch _bulk API 的格式
     * @return 批量操作的结果
     */
    public static JSONObject bulk(String bulkRequestBody) {
        String serialId = IdUtil.getSnowflakeNextIdStr();
        LOGGER.info("执行批量操作 BEGIN:{}", serialId);
        LOGGER.info("批量操作请求体:{}", bulkRequestBody);
        long startTime = System.currentTimeMillis();
        String result = null;
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(Holder.ES_URL + "/_bulk");

            // 设置基本认证
            String auth = Holder.ES_USER + ":" + Holder.ES_PASS;
            byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes(StandardCharsets.UTF_8));
            String authHeader = "Basic " + new String(encodedAuth);
            httpPost.setHeader(HttpHeaders.AUTHORIZATION, authHeader);

            // 设置内容类型和GZIP
            httpPost.setHeader(HttpHeaders.CONTENT_TYPE, "application/x-ndjson");
            httpPost.setHeader(HttpHeaders.ACCEPT_ENCODING, "gzip");

            // 压缩请求体
            byte[] compressedData = compress(bulkRequestBody);
            ByteArrayEntity entity = new ByteArrayEntity(compressedData);
            entity.setContentEncoding("gzip");
            httpPost.setEntity(entity);

            // 执行请求
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity responseEntity = response.getEntity();
                result = EntityUtils.toString(responseEntity);
            }

            long executionTime = System.currentTimeMillis() - startTime;
            if (executionTime > 1000) {
                LOGGER.info("[ElasticsearchKit.bulk]批量操作执行时间过长: [{}] ms, serialId: {}", executionTime, serialId);
            }

            LOGGER.info("执行批量操作 END:{}", serialId);
        } catch (IOException e) {
            LOGGER.error("执行批量操作时发生错误", e);
        }

        LOGGER.info("批量操作结果:{}", result);
        return JSONObject.parseObject(result);
    }

    /**
     * 批量新增文档
     *
     * @param indexName 索引名称
     * @param ids       文档ID列表
     * @param documents 文档数据列表
     * @return 批量操作的结果
     */
    public static JSONObject bulkAdd(String indexName, List<String> ids, List<?> documents) {
        if (ids.size() != documents.size()) {
            throw new IllegalArgumentException("IDs and documents must have the same size");
        }

        String realIndexName = getRealIndexName(indexName);
        String serialId = IdUtil.getSnowflakeNextIdStr();
        LOGGER.info("批量新增数据到索引:{}, 数据数量:{}, serialId:{}", realIndexName, documents.size(), serialId);

        // 构建批量请求体
        StringBuilder bulkRequestBody = new StringBuilder();
        for (int i = 0; i < documents.size(); i++) {
            // 添加action元数据
            JSONObject action = new JSONObject();
            action.put("_index", realIndexName);
            action.put("_id", ids.get(i));

            JSONObject index = new JSONObject();
            index.put("index", action);

            // 将文档转换为JSON字符串
            String docJson = JSONObject.toJSONString(documents.get(i));

            // 添加到批量请求体
            bulkRequestBody.append(index.toJSONString()).append("\n");
            bulkRequestBody.append(docJson).append("\n");
        }

        String result = null;
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(Holder.ES_URL + "/_bulk");

            // 设置基本认证
            String auth = Holder.ES_USER + ":" + Holder.ES_PASS;
            byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes(StandardCharsets.UTF_8));
            String authHeader = "Basic " + new String(encodedAuth);
            httpPost.setHeader(HttpHeaders.AUTHORIZATION, authHeader);

            // 设置内容类型和GZIP
            httpPost.setHeader(HttpHeaders.CONTENT_TYPE, "application/x-ndjson");
            httpPost.setHeader(HttpHeaders.ACCEPT_ENCODING, "gzip");

            // 压缩请求体
            byte[] compressedData = compress(bulkRequestBody.toString());
            ByteArrayEntity entity = new ByteArrayEntity(compressedData);
            entity.setContentEncoding("gzip");
            httpPost.setEntity(entity);

            // 执行请求
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity responseEntity = response.getEntity();
                result = EntityUtils.toString(responseEntity);
            }
        } catch (IOException e) {
            LOGGER.error("批量新增数据时发生错误", e);
            throw new RuntimeException("Failed to execute bulk request", e);
        }

        LOGGER.info("批量新增数据结果:{}", result);
        return JSONObject.parseObject(result);
    }

    /**
     * 批量新增文档
     *
     * @param indexName 索引名称
     * @param documents 文档数据列表
     * @return 批量操作的结果
     */
    public static JSONObject bulkAdd(String indexName, List<?> documents) {

        String realIndexName = getRealIndexName(indexName);
        String serialId = IdUtil.getSnowflakeNextIdStr();
        LOGGER.info("批量新增数据到索引:{}, 数据数量:{}, serialId:{}", realIndexName, documents.size(), serialId);

        // 构建批量请求体
        StringBuilder bulkRequestBody = new StringBuilder();
        for (int i = 0; i < documents.size(); i++) {
            // 添加action元数据
            JSONObject action = new JSONObject();
            action.put("_index", realIndexName);
            action.put("_id", IdUtil.getSnowflakeNextIdStr());

            JSONObject index = new JSONObject();
            index.put("index", action);

            // 将文档转换为JSON字符串
            String docJson = JSONObject.toJSONString(documents.get(i));

            // 添加到批量请求体
            bulkRequestBody.append(index.toJSONString()).append("\n");
            bulkRequestBody.append(docJson).append("\n");
        }

        String result = null;
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(Holder.ES_URL + "/_bulk");

            // 设置基本认证
            String auth = Holder.ES_USER + ":" + Holder.ES_PASS;
            byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes(StandardCharsets.UTF_8));
            String authHeader = "Basic " + new String(encodedAuth);
            httpPost.setHeader(HttpHeaders.AUTHORIZATION, authHeader);

            // 设置内容类型和GZIP
            httpPost.setHeader(HttpHeaders.CONTENT_TYPE, "application/x-ndjson");
            httpPost.setHeader(HttpHeaders.ACCEPT_ENCODING, "gzip");

            // 压缩请求体
            byte[] compressedData = compress(bulkRequestBody.toString());
            ByteArrayEntity entity = new ByteArrayEntity(compressedData);
            entity.setContentEncoding("gzip");
            httpPost.setEntity(entity);

            // 执行请求
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity responseEntity = response.getEntity();
                result = EntityUtils.toString(responseEntity);
            }
        } catch (IOException e) {
            LOGGER.error("批量新增数据时发生错误", e);
            throw new RuntimeException("Failed to execute bulk request", e);
        }

        LOGGER.info("批量新增数据结果:{}", result);
        return JSONObject.parseObject(result);
    }

    /**
     * 根据查询条件更新ES文档
     *
     * @param indexName    索引名称
     * @param searchParam  查询条件
     * @param updateScript 更新脚本
     * @return 更新操作的结果
     */
    public static JSONObject updateByQuery(String indexName, JSONObject searchParam, JSONObject updateScript) {

        // 构建完整的请求体
        JSONObject requestBody = new JSONObject();
        requestBody.put("query", searchParam);
        requestBody.put("script", updateScript);

        return updateByQuery(indexName, requestBody);
    }

    /**
     * 更新ES文档
     * @param index 索引名称
     * @param id 文档ID
     * @param updateRequest 更新请求体
     * @return 更新结果
     */
    public static JSONObject updateDoc(String index, String id, JSONObject updateRequest) {
        try {
            String url = String.format("%s/%s/_doc/%s/_update", Holder.ES_URL, index, id);

            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");

            // 发送POST请求
            String response = HttpRequest.post(url)
                .basicAuth(Holder.ES_USER, Holder.ES_PASS)
                .contentType("application/json")
                .body(updateRequest.toJSONString())
                .execute()
                .body();

            // 解析响应
            JSONObject result = JSONObject.parseObject(response);

            if (result != null && "updated".equals(result.getString("result"))) {
                LOGGER.info("Successfully updated document: index={}, id={}", index, id);
            } else {
                LOGGER.warn("Document update may have failed: index={}, id={}, response={}", index, id, response);
            }

            return result;
        } catch (Exception e) {
            LOGGER.error("Failed to update document: index={}, id={}, error={}", index, id, e.getMessage(), e);
            throw new RuntimeException("Failed to update ES document", e);
        }
    }

    /**
     * 根据查询条件更新ES文档
     *
     * @param indexName    索引名称
     * @return 更新操作的结果
     */
    public static JSONObject updateByQuery(String indexName, JSONObject param) {
        String url = Holder.ES_URL + "/" + getRealIndexName(indexName) + "/_update_by_query";
        LOGGER.info("update by query index:{}, server:{}, query:{}",
                getRealIndexName(indexName), url, param);

        String result = HttpRequest.post(url)
                .basicAuth(Holder.ES_USER, Holder.ES_PASS)
                .contentType("application/json")
                .body(param.toJSONString())
                .execute()
                .body();

        LOGGER.info("update by query result:{}", result);
        return JSONObject.parseObject(result);
    }

    /**
     * 提供一个方法，使用http方案获取索引映射信息，入参为索引名称
     *
     * @param indexName 索引名称
     * @return 索引映射信息
     */
    public static JSONObject getMapping(String indexName) {
        LOGGER.info("get mapping for index:{}", getRealIndexName(indexName));
        String result = HttpRequest.get(Holder.ES_URL + "/" + getRealIndexName(indexName) + "/_mapping")
                .basicAuth(Holder.ES_USER, Holder.ES_PASS)
                .execute()
                .body();
        LOGGER.info("get mapping result:{}", result);
        return JSONObject.parseObject(result);
    }

    private static byte[] compress(String str) throws IOException {
        if (str == null || str.length() == 0) {
            return new byte[0];
        }
        ByteArrayOutputStream obj = new ByteArrayOutputStream();
        GZIPOutputStream gzip = new GZIPOutputStream(obj);
        gzip.write(str.getBytes(StandardCharsets.UTF_8));
        gzip.close();
        return obj.toByteArray();
    }

    public static void main(String[] args) {
    }
}
