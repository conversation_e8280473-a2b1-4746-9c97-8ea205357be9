package com.yunqu.tariff.listener;

import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.task.TariffAuditScanTask;
import com.yunqu.tariff.task.TariffAuditResultScanTask;
import org.slf4j.Logger;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

/**
 * Web应用生命周期监听器
 * 负责管理线程池和资源的优雅关闭
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@WebListener
public class TariffWebAppListener implements ServletContextListener {

    private static final Logger logger = CommonLogger.getLogger("webapp-listener");

    @Override
    public void contextInitialized(ServletContextEvent sce) {
        logger.info("Tariff Web应用启动完成");
    }

    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        logger.info("Tariff Web应用开始关闭，执行资源清理");

        try {
            // 关闭稽核任务扫描线程池
            TariffAuditScanTask.shutdown();
            logger.info("稽核任务扫描线程池已关闭");
        } catch (Exception e) {
            logger.error("关闭稽核任务扫描线程池失败", e);
        }

        try {
            // 关闭稽核结果扫描线程池
            TariffAuditResultScanTask.shutdown();
            logger.info("稽核结果扫描线程池已关闭");
        } catch (Exception e) {
            logger.error("关闭稽核结果扫描线程池失败", e);
        }

        // 等待一段时间确保所有线程完成
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        logger.info("Tariff Web应用资源清理完成");
    }
}
