package com.yunqu.tariff.servlet;

import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.UserUtil;
import com.yq.busi.common.util.mq.MQBrokerUtil;
import com.yunqu.tariff.base.AppBaseServlet;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.handler.SearchHandler;
import com.yunqu.tariff.handler.SearchHandlerFactory;
import com.yunqu.tariff.inf.RegInitDataService;
import com.yunqu.tariff.utils.ExcelUtil;
import com.yunqu.tariff.utils.excel.CellStyleUtil;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.slf4j.Logger;

import javax.servlet.ServletOutputStream;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 资费报表导出Servlet
 * <AUTHOR>
 */
@WebServlet("/servlet/tariffReport/*")
public class TariffReportServlet extends AppBaseServlet {

    private static final long serialVersionUID = 1L;
    private static final Logger logger = CommonLogger.logger;

    // Excel表头定义
    private static final List<String> EXCEL_HEADERS = new ArrayList<String>() {{
        add("资费名称");
        add("资费状态");
        add("资费类别");
        add("订购占比");
        add("订购省份");
        add("运营商");
        add("订单量");
        add("地市数量");
        add("涉及地市及订单量");
        add("首次出现日期");
    }};

    // Excel字段定义
    private static final List<String> EXCEL_FIELDS = new ArrayList<String>() {{
        add("tariffName");
        add("tariffStatus");
        add("tariffType");
        add("allOrderCount");
        add("provinceName");
        add("entName");
        add("orderCount");
        add("cityCount");
        add("citiesAndOrders");
        add("firstAppearance");
    }};


    public EasyResult actionForExport() {
        try {
            JSONObject param = buildExportParams();
            UserModel user = UserUtil.getUser(getRequest());
            String serialId = IdUtil.getSnowflakeNextIdStr();

            JSONObject json = new JSONObject();
            json.put("ID", serialId);
            json.put("TASK_CODE", "TariffReportServlet");
            json.put("TASK_NAME", "资费方案列表导出");
            json.put("TASK_TYPE", "2");
            json.put("CREATOR", user.getUserName());
            json.put("CREATE_ACCT", user.getUserAcc());
            json.put("CREATE_TIME", EasyDate.getCurrentTimeStampString());
            json.put("PARAMS", param.toString());
            json.put("STATUS", 1);
            json.put("FILE_NAME", "资费方案列表_"+EasyDate.getCurrentDateString("yyyyMMdd")+"_"+user.getUserAcc()+"_"+serialId+".xlsx");


            EasyRecord record = new EasyRecord(getTableName("xty_tariff_export_task"), "ID");
            record.setColumns(json);
            getQuery().save(record);
            json.put("PARAMS", param);
            JSONObject params = new JSONObject();
            params.put("serialId", serialId);
            params.put("command", "TariffReportServlet");
            params.put("taskObj", json);
            MQBrokerUtil.sendMsg(Constants.TARIFF_NOTIFY_EXPORT_BROKER, params.toString());

            return EasyResult.ok("", "创建导出任务成功");
        } catch (Exception e) {
            error(e.getMessage(), e);
            return EasyResult.fail();
        }
    }



    /**
     * 导出资费报表的主要方法
     * @throws Exception 如果导出过程中发生异常
     */
    public void actionForExportBak() throws Exception {
        long startTime = System.currentTimeMillis();
        logger.info("开始导出资费报表...");

        // 1. 构建查询参数
        JSONObject param = buildExportParams();

        try (ServletOutputStream os = getResponse().getOutputStream()) {
            // 2. 设置响应头
            setExportResponseHeader("资费方案列表" + DateUtil.getCurrentDateStr("yyyyMMdd") + ".xlsx");

            // 3. 创建ExcelWriter
            ExcelWriter excelWriter = null;
            try {
                excelWriter = EasyExcel.write(os)
                        .head(ExcelUtil.formatHeader(EXCEL_HEADERS, "用户订单"))
                        .registerWriteHandler(CellStyleUtil.getHorizontalCellStyleStrategy())
                        .build();

                WriteSheet activeSheet = EasyExcel.writerSheet("活跃").build();
                WriteSheet inactiveSheet = EasyExcel.writerSheet("非活跃").build();

                // 4. 流式查询并写入活跃数据
                exportDataByStatus(param, excelWriter, activeSheet, true);

                // 5. 流式查询并写入非活跃数据
                exportDataByStatus(param, excelWriter, inactiveSheet, false);
            } finally {
                // 确保ExcelWriter正确关闭
                if (excelWriter != null) {
                    excelWriter.finish();
                }
            }

            logger.info("资费报表导出完成,耗时:{}ms", System.currentTimeMillis() - startTime);

        } catch (Exception e) {
            logger.error("导出资费报表异常", e);
            throw new Exception("导出失败:" + e.getMessage());
        }
    }

    /**
     * 构建导出参数
     */
    private JSONObject buildExportParams() {
        JSONObject param = new JSONObject();
        param.put("provinceCodes", getPara("provinceCodes"));
        param.put("auditBeginDate", getPara("auditBeginDate"));
        param.put("auditEndDate", getPara("auditEndDate"));
        param.put("ent", getPara("ent"));
        param.put("firstTime", getPara("firstTime"));
        param.put("endTime", getPara("endTime"));
        param.put("tariffName", getPara("tariffName"));
        param.put("tariffType", getPara("tariffType"));
        param.put("pageType", "3");
        param.put("pageSize", "1000"); // 减小每页数据量

        logger.info("导出报表参数: {}", param);
        return param;
    }

    /**
     * 设置导出响应头
     */
    private void setExportResponseHeader(String fileName) throws IOException {
        HttpServletResponse response = getResponse();
        response.reset();
        response.setContentType("application/octet-stream; charset=utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition",
                "attachment; filename=" + URLEncoder.encode(fileName,"UTF-8"));
    }

    /**
     * 获取数据字典
     */
    private JSONArray getDataDict() throws ServiceException {
        RegInitDataService service = new RegInitDataService();
        JSONObject result = service.invokeMethod(new JSONObject());
        JSONObject data = (JSONObject) result.get("data");
        return data.getJSONArray("dict");
    }

    /**
     * 流式导出数据
     */
    private void exportDataByStatus(JSONObject param, ExcelWriter excelWriter,
            WriteSheet writeSheet, boolean isActive) throws Exception {

        int pageIndex = 1;
        int total = 0;

        // 获取数据字典
        JSONArray dict = getDataDict();

        while (true) {
            param.put("pageIndex", String.valueOf(pageIndex));

            // 根据状态获取不同的查询处理器
            SearchHandler<JSONObject> searchHandler = isActive ?
                    SearchHandlerFactory.getTariffActiveSearchHandler(param) :
                    SearchHandlerFactory.getTariffNotActiveSearchHandler(param);

            JSONObject result = searchHandler.search();
            List<Map<String, String>> records = result.getObject("data",
                    new TypeReference<List<Map<String, String>>>() {});

            if (records == null || records.isEmpty()) {
                break;
            }

            // 转换并写入数据
            List<List<Object>> rows = convertToExcelRows(records, dict);
            excelWriter.write(rows, writeSheet);

            total += records.size();
            pageIndex++;

            // 每1000条记录输出一次日志
            if (total % 1000 == 0) {
                logger.info("已处理{}条{}记录", total, isActive ? "活跃" : "非活跃");
            }
        }

        logger.info("{}数据导出完成,共{}条记录", isActive ? "活跃" : "非活跃", total);
    }

    /**
     * 转换数据为Excel行格式
     */
    private List<List<Object>> convertToExcelRows(List<Map<String, String>> records,
            JSONArray dict) {
        List<List<Object>> rows = new ArrayList<>(records.size());

        for (Map<String, String> record : records) {
            List<Object> row = new ArrayList<>();
            // 按字段顺序添加数据
            row.add(record.get("tariffName"));
            row.add(getDataInversion(dict, "XTY_TARIFF_STATUS", record.get("tariffStatus")));
            row.add(record.get("tariffType"));
            row.add(getDgzb(record.get("orderCount"), record.get("allOrderCount")));
            row.add(record.get("provinceName"));
            row.add(record.get("entName"));
            row.add(parseNumber(record.get("orderCount")));
            row.add(parseNumber(record.get("cityCount")));
            row.add(record.get("citiesAndOrders"));
            row.add(record.get("firstAppearance"));

            rows.add(row);
        }

        return rows;
    }

    /**
     * 数字格式转换
     */
    private Object parseNumber(String value) {
        if (StringUtils.isBlank(value) || "null".equals(value)) {
            return "";
        }
        try {
            if (value.matches("[0-9]{1,6}+")) {
                return Long.valueOf(value);
            }
            if (value.matches("^([0-9]{1,6}[.][0-9]*)$")) {
                return Double.valueOf(value);
            }
        } catch (NumberFormatException e) {
            logger.warn("数字转换异常:{}", value);
        }
        return value;
    }

    /**
     * 计算订购占比
     */
    private String getDgzb(String orderCount, String allOrderCount) {
        if (StringUtils.isNotBlank(orderCount) && StringUtils.isNotBlank(allOrderCount)) {
            try {
                double dgzb = Double.parseDouble(orderCount) / Double.parseDouble(allOrderCount);
                return String.format("%.4f", dgzb * 100) + "%";
            } catch (NumberFormatException e) {
                logger.warn("订购占比计算异常: orderCount={}, allOrderCount={}", orderCount, allOrderCount);
                return "0.0000%";
            }
        }
        return "0.0000%";
    }

    /**
     * 数据字典转换
     */
    private String getDataInversion(JSONArray dict, String code, String value) {
        if (StringUtils.isBlank(value)) {
            return "";
        }

        for (int x = 0; x < dict.size(); x++) {
            JSONObject dictJSONObject = dict.getJSONObject(x);
            String code1 = dictJSONObject.getString("CODE");
            if (code1.equals(code)) {
                JSONArray jsonArray = dictJSONObject.getJSONArray("DICTS");
                for (int y = 0; y < jsonArray.size(); y++) {
                    JSONObject jsonArrayJSONObject = jsonArray.getJSONObject(y);
                    String code2 = jsonArrayJSONObject.getString("CODE");
                    if (value.equals(code2)) {
                        return jsonArrayJSONObject.getString("NAME");
                    }
                }
            }
        }
        return value;
    }



    /**
     * 构建查询参数 - 根据需求调整搜索条件
     */
    private JSONObject buildUnpublicTariffListParams() {
        JSONObject param = new JSONObject();

        // 基本搜索功能参数
        param.put("tariffName", getPara("tariffName")); // 资费名称
        param.put("reportNo", getPara("reportNo")); // 资费编码
        param.put("ent", getPara("ent")); // 运营商
        param.put("provinceCodes", getPara("provinceCodes")); // 订购省份

        // 高级搜索功能参数
        param.put("appearTime", getPara("appearTime")); // 出现日期
        param.put("firstTime", getPara("firstTime")); // 首次出现日期
        param.put("endTime", getPara("endTime")); // 最后出现日期
        param.put("pageSize", getPara("pageSize", "1000"));

        // 未公示资费查询标识
        param.put("isUnpublicQuery", "true");

        logger.info("未公示资费查询参数: {}", param);
        return param;
    }

    /**
     * 导出未公示资费列表（创建异步导出任务）
     */
    public EasyResult actionForExportUnpublicTariffList() {
        try {
            JSONObject param = buildUnpublicTariffListParams();
            UserModel user = UserUtil.getUser(getRequest());
            String serialId = IdUtil.getSnowflakeNextIdStr();

            JSONObject json = new JSONObject();
            json.put("ID", serialId);
            json.put("TASK_CODE", "TariffUnpublicServlet");
            json.put("TASK_NAME", "未公示资费方案导出");
            json.put("TASK_TYPE", "2");
            json.put("CREATOR", user.getUserName());
            json.put("CREATE_ACCT", user.getUserAcc());
            json.put("CREATE_TIME", EasyDate.getCurrentTimeStampString());
            json.put("PARAMS", param.toString());
            json.put("STATUS", 1);
            json.put("FILE_NAME", "未公示资费方案_"+EasyDate.getCurrentDateString("yyyyMMdd")+"_"+user.getUserAcc()+"_"+serialId+".xlsx");

            EasyRecord record = new EasyRecord(getTableName("xty_tariff_export_task"), "ID");
            record.setColumns(json);
            getQuery().save(record);

            json.put("PARAMS", param);
            JSONObject params = new JSONObject();
            params.put("serialId", serialId);
            params.put("command", "TariffUnpublicServlet");
            params.put("taskObj", json);
            logger.info("未公示资费导出参数: {}", params);
            MQBrokerUtil.sendMsg(Constants.TARIFF_NOTIFY_EXPORT_BROKER, params.toString());

            return EasyResult.ok("", "创建导出任务成功");
        } catch (Exception e) {
            error(e.getMessage(), e);
            return EasyResult.fail();
        }
    }
}
