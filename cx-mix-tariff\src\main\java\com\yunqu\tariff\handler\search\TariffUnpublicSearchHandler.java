package com.yunqu.tariff.handler.search;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.yunqu.tariff.handler.SearchHandler;
import com.yunqu.xty.commonex.kit.ElasticsearchKit;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 未公示资费查询
 */
public class TariffUnpublicSearchHandler implements SearchHandler<JSONObject> {

    private static final Logger logger = LoggerFactory.getLogger(TariffUnpublicSearchHandler.class);
    private JSONObject param;

    public TariffUnpublicSearchHandler(JSONObject param) {
        this.param = param;
    }

    @Override
    public JSONObject search() {
        List<String> dayVals = new ArrayList<>();
        JSONObject queryParams = buildUnpublicTariffListQuery(dayVals);
        JSONObject esResult = ElasticsearchKit.search("xty_tariff_unpublic_audit_process_storage", queryParams);
        return parseUnpublicTariffListResult(esResult, dayVals);
    }

    /**
     * 构建未公示资费查询条件
     * @param dayVals 日期值列表
     * @return 查询参数对象
     */
    private JSONObject buildUnpublicTariffListQuery(List<String> dayVals) {
        JSONObject queryParams = new JSONObject();
        JSONObject query = new JSONObject();
        JSONObject bool = new JSONObject();
        JSONArray must = new JSONArray();

        // 资费类型条件
        String tariffType = param.getString("tariffType");
        if (StringUtils.isBlank(tariffType)) {
            tariffType = "1,5"; // 默认查询未公示资费类型
        }
        List<String> tariffTypes = StrUtil.split(tariffType, ",");
        JSONObject terms = new JSONObject();
        terms.put("terms", new JSONObject().fluentPut("type.keyword", tariffTypes));
        must.add(terms);

        // 资费类型1条件
        String type1 = param.getString("type1");
        if(StringUtils.isNotBlank(type1)){
            List<String> type1s = StrUtil.split(type1, ",");
            JSONObject type1Terms = new JSONObject();
            type1Terms.put("terms", new JSONObject().fluentPut("type1.keyword", type1s));
            must.add(type1Terms);
        }

        // 省份条件
        String provinceCode = param.getString("provinceCodes");
        if("JT".equals(provinceCode)){
            JSONObject term = new JSONObject();
            term.put("term", new JSONObject().fluentPut("tariffProvinceCode.keyword", provinceCode));
            must.add(term);
        } else if(StringUtils.isNotBlank(provinceCode)){
            JSONObject term = new JSONObject();
            term.put("term", new JSONObject().fluentPut("provinceCode.keyword", provinceCode));
            must.add(term);
        }

        // 企业条件
        String entCode = param.getString("ent");
        if (StringUtils.isNotBlank(entCode)) {
            JSONObject term = new JSONObject();
            term.put("term", new JSONObject().fluentPut("entCode.keyword", entCode));
            must.add(term);
        }

        String reportNo = param.getString("reportNo");
        if (StringUtils.isNotBlank(reportNo)) {
            JSONObject term = new JSONObject();
            term.put("term", new JSONObject().fluentPut("reportNo.keyword", reportNo));
            must.add(term);
        }

        String firstTime = param.getString("firstTime");
        if (StringUtils.isNotBlank(firstTime)) {
            String[] firstTimes = firstTime.split("~");
            if (firstTimes.length == 2) {
                JSONObject range = new JSONObject();
                range.put("range", new JSONObject().fluentPut("firstDate", new JSONObject().fluentPut("gte", firstTimes[0]).fluentPut("lte", firstTimes[1])));
                must.add(range);
            }
        }

        String endTime = param.getString("endTime");
        if (StringUtils.isNotBlank(endTime)) {
            String[] endTimes = endTime.split("~");
            if (endTimes.length == 2) {
                JSONObject range = new JSONObject();
                range.put("range", new JSONObject().fluentPut("lastDate", new JSONObject().fluentPut("gte", endTimes[0]).fluentPut("lte", endTimes[1])));
                must.add(range);
            }
        }

        // 资费名称
        String tariffName = param.getString("tariffName");
        if (StringUtils.isNotBlank(tariffName)) {
            JSONObject wildcard = new JSONObject();
            wildcard.put("wildcard", new JSONObject().fluentPut("tariffName.keyword", "*" + tariffName + "*"));
            must.add(wildcard);
        }

        // 异常编码条件
        String errorReportNo = param.getString("errorReportNo");
        if (StringUtils.isNotBlank(errorReportNo)) {
            JSONObject wildcard = new JSONObject();
            wildcard.put("wildcard", new JSONObject().fluentPut("errorReportNo.keyword", "*" + errorReportNo + "*"));
            must.add(wildcard);
        }


        String appearTime = param.getString("appearTime");
        if (StringUtils.isNotBlank(appearTime)) {
            String[] appearTimes = appearTime.split("~");
            String beginDate = appearTimes[0];
            String endDate = appearTimes[1];
            EasyCalendar beginCalendar = EasyCalendar.newInstance(beginDate, "yyyy-MM-dd");
            EasyCalendar endCalendar = EasyCalendar.newInstance(endDate, "yyyy-MM-dd");
            getTermsDate(dayVals, beginCalendar, endCalendar);
            getLogger().info("1:组装查询日期集合>>>{}", dayVals);
        } else {
            EasyCalendar beginCalendar = EasyCalendar.newInstance("2024-12-01", "yyyy-MM-dd");
            EasyCalendar endCalendar = EasyCalendar.newInstance(EasyDate.getCurrentDateString("yyyy-MM-dd"), "yyyy-MM-dd");
            getTermsDate(dayVals, beginCalendar, endCalendar);
            getLogger().info("2:组装查询日期集合>>>{}", dayVals);
        }
        if (!dayVals.isEmpty()) {
            JSONObject existsBool = new JSONObject()
                    .fluentPut("bool", new JSONObject()
                            .fluentPut("should", dayVals.stream()
                                    .map(dayVal -> new JSONObject()
                                            .fluentPut("exists", new JSONObject()
                                                    .fluentPut("field", dayVal)))
                                    .collect(Collectors.toCollection(JSONArray::new)))
                            .fluentPut("minimum_should_match", 1));
            must.add(existsBool);
        }

        bool.put("must", must);
        query.put("bool", bool);
        queryParams.put("query", query);
        
        // 查询字段
        List<String> sourceList = new ArrayList<>();
        sourceList.addAll(Arrays.asList(
                "serialId", "provinceCode", "provinceName", "entCode", "entName",
                "tariffName", "firstDate", "lastDate", "reporter", "reporterName",
                "reportNo", "reportId", "type", "type1", "errorReportNo"
        ));
        sourceList.addAll(dayVals);
        queryParams.put("_source", sourceList);
        queryParams.put("track_total_hits", true);
        
        // 排序
        JSONArray sortArray = new JSONArray();

        // 按销量排序
        JSONObject scriptSort = new JSONObject();
        StringBuilder scriptBuilder = new StringBuilder();
        scriptBuilder.append("int total = 0;");
        scriptBuilder.append("for (String dayVal : params.dayVals) {");
        scriptBuilder.append("  String month = dayVal.substring(0, 6);");
        scriptBuilder.append("  String day = dayVal.substring(7, 15);");
        scriptBuilder.append("  def source = params._source;");
        scriptBuilder.append("  if (source.containsKey(month)) {");
        scriptBuilder.append("    def monthData = source.get(month);");
        scriptBuilder.append("    if (monthData != null && monthData.containsKey(day)) {");
        scriptBuilder.append("      def dayData = monthData.get(day);");
        scriptBuilder.append("      if (dayData != null && dayData.containsKey('tariffSaleCount')) {");
        scriptBuilder.append("        total += dayData.tariffSaleCount;");
        scriptBuilder.append("      }");
        scriptBuilder.append("    }");
        scriptBuilder.append("  }");
        scriptBuilder.append("}");
        scriptBuilder.append("return total;");

        scriptSort.put("_script", new JSONObject()
                .fluentPut("type", "number")
                .fluentPut("script", new JSONObject()
                        .fluentPut("lang", "painless")
                        .fluentPut("source", scriptBuilder.toString())
                        .fluentPut("params", new JSONObject()
                                .fluentPut("dayVals", dayVals)))
                .fluentPut("order", "desc"));

        sortArray.add(scriptSort);

        // 按名称排序
        JSONObject nameSort = new JSONObject();
        nameSort.put("tariffName.keyword", new JSONObject()
                .fluentPut("order", "asc")
                .fluentPut("missing", "_last"));

        sortArray.add(nameSort);

        queryParams.put("sort", sortArray);
        
        // 分页参数
        queryParams.put("size", param.getIntValue("pageSize"));
        queryParams.put("from", (param.getIntValue("pageIndex") - 1) * param.getIntValue("pageSize"));

        logger.info("未公示资费查询参数: {}", queryParams.toJSONString());
        return queryParams;
    }
    /**
     * 解析未公示资费查询结果
     * @param esResult ES查询结果
     * @param dayVals 日期值列表
     * @return 处理后的结果
     */
    private JSONObject parseUnpublicTariffListResult(JSONObject esResult, List<String> dayVals) {
        JSONObject result = new JSONObject();
        result.put("pageNumber", param.getIntValue("pageIndex"));
        result.put("pageSize", param.getIntValue("pageSize"));
        result.put("pageType", 3);
        result.put("state", 1);
        result.put("msg", "查询成功");
        
        JSONObject hitsObject = esResult.getJSONObject("hits");
        result.put("total", hitsObject.getJSONObject("total").getIntValue("value"));
        
        List<JSONObject> hits = hitsObject.getObject("hits", new TypeReference<List<JSONObject>>() {});
        
        result.put("data", hits.stream().map(o -> {
            JSONObject source = o.getJSONObject("_source");
            Set<String> errorReportNos = new HashSet<>();
            Set<String> likeNames = new HashSet<>();
            Map<String, Integer> areaMap = new HashMap<>();
            int orderCounts = 0;
            
            // 处理每个日期的数据
            for (String dayVal : dayVals) {
                List<String> split = StrUtil.split(dayVal, ".");
                String month = split.get(0);
                String day = split.get(1);
                
                if (!source.containsKey(month)) {
                    continue;
                }
                
                JSONObject monthObject = source.getJSONObject(month);
                if (!monthObject.containsKey(day)) {
                    continue;
                }
                
                JSONObject dayObject = monthObject.getJSONObject(day);
                String errorReportNo = dayObject.getString("errorReportNo");
                if (StringUtils.isNotBlank(errorReportNo)) {
                    errorReportNos.add(errorReportNo);
                }
                
                String likeName = dayObject.getString("likeName");
                if (StringUtils.isNotBlank(likeName)) {
                    likeNames.add(likeName);
                }
                
                int tariffSaleCount = dayObject.getIntValue("tariffSaleCount");
                orderCounts += tariffSaleCount;
                
                // 处理销售区域信息
                JSONArray saleList = dayObject.getJSONArray("saleList");
                if (saleList != null) {
                    for (int i = 0; i < saleList.size(); i++) {
                        JSONObject sale = saleList.getJSONObject(i);
                        String areaName = sale.getString("areaName");
                        int saleCount = sale.getIntValue("saleCount");
                        areaMap.compute(areaName, (k, v) -> v == null ? saleCount : v + saleCount);
                    }
                }
            }
            
            // 构建区域信息字符串
            List<String> areaInfos = new ArrayList<>();
            for (Map.Entry<String, Integer> entry : areaMap.entrySet()) {
                areaInfos.add(entry.getKey() + ":" + entry.getValue());
            }
            
            // 获取资费类型信息
            String type = source.getString("type");
            String reportNo = source.getString("reportNo");
            String errorReportNo = source.getString("errorReportNo");
            
            // 构建返回结果
            JSONObject resultJson = new JSONObject();
            resultJson.put("cityCount", areaMap.size());
            resultJson.put("provinceCode", source.getString("provinceCode"));
            resultJson.put("provinceName", source.getString("provinceName"));
            resultJson.put("ent", source.getString("entCode"));
            resultJson.put("entName", source.getString("entName"));
            resultJson.put("tariffName", source.getString("tariffName"));
            
            // 处理资费编码
            if (StringUtils.isNotBlank(reportNo)) {
                resultJson.put("reportNo", reportNo);
            } else if (!errorReportNos.isEmpty()) {
                resultJson.put("reportNo", CollUtil.join(errorReportNos, ","));
            } else if (StringUtils.isNotBlank(errorReportNo)) {
                resultJson.put("reportNo", errorReportNo);
            } else {
                resultJson.put("reportNo", "");
            }
            
            resultJson.put("id", source.getString("reportId"));
            resultJson.put("serialId", source.getString("serialId"));
            resultJson.put("firstAppearance", source.getString("firstDate"));
            resultJson.put("lastAppearance", source.getString("lastDate"));
            resultJson.put("orderCount", orderCounts);
            resultJson.put("citiesAndOrders", CollUtil.join(areaInfos, ","));
            
            // 设置资费类型显示
            if ("5".equals(type)) {
                resultJson.put("tariffType", "未报送");
            } else if ("1".equals(type)) {
                resultJson.put("tariffType", "活跃在售");
            } else {
                resultJson.put("tariffType", "未公示资费");
            }
            // 添加标识字段，便于前端识别
            resultJson.put("isUnpublicTariff", true);
            
            return resultJson;
        }).collect(Collectors.toList()));
        
        return result;
    }
} 