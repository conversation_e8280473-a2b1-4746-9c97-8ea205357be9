package com.yunqu.tariff.handler;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.handler.search.*;

import java.util.List;

/**
 * SearchHandlerFactory
 *
 * @ClassName SearchHandlerFactory
 * <AUTHOR> Copy This Tag)
 * @Since create in 2025/1/10 15:52
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
public class SearchHandlerFactory {



    public static SearchHandler<JSONObject> getAuditOrderUserSearchHandler(JSONObject param) {
        return new AuditOrderUserSearchHandler(param);
    }

    public static SearchHandler<List<JSONObject>> getActiveTariffStatisticsSearchHandler(JSONObject param) {
        return new ActiveTariffStatisticsSearchHandler(param);
    }

    public static SearchHandler<JSONObject> getUnfiledTariffSearchHandler(JSONObject param) {
        return new UnfiledTariffSearchHandler(param);
    }

    public static SearchHandler<JSONObject> getTariffActiveSearchHandler(JSONObject param) {
        return new TariffActiveSearchHandler(param);
    }

    public static SearchHandler<JSONObject> getTariffNotActiveSearchHandler(JSONObject param) {
        return new TariffNotActiveSearchHandler(param);
    }

    public static SearchHandler<JSONObject> getTariffUnpublicSearchHandler(JSONObject param) {
        return new TariffUnpublicSearchHandler(param);
    }

    public static SearchHandler<JSONObject> getProvinceEntAggregationSearchHandler(JSONObject param) {
        return new ProvinceEntAggregationSearchHandler(param);
    }

    public static SearchHandler<JSONObject> getProvinceNotReportCountSearchHandler(JSONObject param) {
        return new ProvinceNotReportCountSearchHandler(param);
    }

    public static SearchHandler<JSONObject> getProvinceUnreportCountStatisticsSearchHandler(JSONObject param) {
        return new ProvinceUnreportCountStatisticsSearchHandler(param);
    }

    public static SearchHandler<JSONObject> getReportedTariffStatisticsSearchHandler(JSONObject param) {
        return new ReportedTariffStatisticsHandler(param);
    }

    public static SearchHandler<JSONObject> getFindStatusChangeTotalSearchHandler(JSONObject param) {
        return new FindStatusChangeTotalSearchHandler(param);
    }

    public static SearchHandler<List<JSONObject>> getFindStatusChangeTotalSecSearchHandler(JSONObject param) {
        return new FindStatusChangeTotalSecSearchHandler(param);
    }

    public static SearchHandler<List<JSONObject>> getFindDistributionTotalSearchHandler(JSONObject param) {
        return new FindDistributionTotalSearchHandler(param);
    }

    public static SearchHandler<List<JSONObject>> getProvTariffChgStatSearchHandler(JSONObject param) {
        return new ProvTariffChgStatSearchHandler(param);
    }

    public static SearchHandler<JSONObject> getProvTariffWholeStatSearchHandler(JSONObject param) {
        return new ProvTariffWholeStatSearchHandler(param);
    }

    public static SearchHandler<List<JSONObject>> getTariffReportChkStatSearchHandler(JSONObject param) {
        return new TariffReportChkStatSearchHandler(param);
    }

    public static SearchHandler<JSONObject> TariffReportWholeStatSearchHandler(JSONObject param) {
        return new TariffReportWholeStatSearchHandler(param);
    }

}
