/**
 *
 */
package com.yunqu.xty.servlet.config;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.model.Dict;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.task.BaseTask;
import com.yq.busi.common.util.*;
import com.yq.busi.common.util.thread.ThreadManager;
import com.yunqu.xty.base.AppBaseServlet;
import com.yunqu.xty.base.CommonLogger;
import com.yunqu.xty.base.Constants;
import com.yunqu.xty.inf.AutoAssignService;
import com.yunqu.xty.utils.DutyUtil;
import com.yunqu.xty.utils.OptionLogUtil;
import com.yunqu.xty.utils.OrderNodeUtil;
import com.yunqu.xty.utils.XtyOptionLogUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;

import javax.servlet.annotation.WebServlet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 省份参数配置
 * <AUTHOR>
 * @date 2023-10-16 18:06:06
 */
@WebServlet("/servlet/provinceConfig")
public class ProvinceConfigServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;

	private Logger logger = CommonLogger.getLogger();


	private Logger configLogger = CommonLogger.getLogger("config");

	@InfAuthCheck(resId = {"cx-xty-complain-config-province-param-rule","cx-xty-complain-config-global-ent", "cx-xty-complain-config-province-param-node", "cx-xty-complain-config-province-params"})
	public EasyResult actionForConfigEdit() {
		try {
			JSONObject jsonObject = this.getJSONObject();
			String saveType = jsonObject.getString("SAVE_TYPE");
			String configType = jsonObject.getString("CONFIG_TYPE");

			UserModel user = UserUtil.getUser(getRequest());
			String entId = user.getEpCode();
			String busiOrderId = user.getBusiOrderId();
			String userDept = user.getDeptCode();
			String userAcc = user.getUserAcc();
			String provinceCode = StringUtils.isNotBlank(jsonObject.getString("provinceCode")) ?
					jsonObject.getString("provinceCode") : user.getDeptProvinceCode();

			JSONObject row = this.getQuery().queryForRow("select * from " + this.getTableName("XTY_PROVINCE_CONFIG") + " t1 where t1.ENT_ID = ? and t1.BUSI_ORDER_ID = ? and t1.PROVINCE = ? and CONFIG_TYPE = ?", new Object[] {entId, busiOrderId, provinceCode, configType}, new JSONMapperImpl());
			EasyRecord record = new EasyRecord(getTableName("XTY_PROVINCE_CONFIG"), "ID");
			JSONObject configJson = jsonObject.getJSONObject("CONFIG_JSON");
			if(configJson == null) {
				configJson = new JSONObject();
			}

			if("1".equals(saveType)) {
				String styleId = configJson.getString("TIME_STYLE_ID");
				String provinceName = this.getQuery().queryForString("select PROVINCE_NAME from CC_PROVINCE where PROVINCE_CODE = ? ", provinceCode);
				styleId = saveWorkTimeStyle(entId, busiOrderId, styleId, provinceName, configJson.getJSONObject("WORK_TIME_CONFIG"));
				configJson.put("TIME_STYLE_ID", styleId);
			}

			if(row != null) {
				// 已存在配置数据，根据saveType类型替换掉配置信息
				JSONObject configOldJson = row.getJSONObject("CONFIG_JSON");
				if("1".equals(saveType)) {
					// 工作时间配置
					configOldJson.put("TIME_STYLE_ID", configJson.get("TIME_STYLE_ID"));
					configOldJson.put("WORK_TIME_CONFIG", configJson.get("WORK_TIME_CONFIG"));

					configLogger.info("用户[" + user.getUserAcc() + "] 修改了省份[" + provinceCode + "] 工作时间配置信息:" + JSONObject.toJSONString(configJson));
				} else if("3".equals(saveType)) {
					JSONObject clientConfig = configJson.getJSONObject("CLIENT_CONFIG");
					if(clientConfig == null) {
						clientConfig = new JSONObject();
					}
					logger.info("clientConfig:" + clientConfig);
					configOldJson.put("CLIENT_CONFIG", clientConfig);
					configLogger.info("用户[" + user.getUserAcc() + "] 修改了省份[" + provinceCode + "] 公众端配置信息:" + JSONObject.toJSONString(clientConfig));
					// 公众端配置
//					clientConfig.put("CLIENT_TITLE", configJson.get("CLIENT_TITLE"));
//					clientConfig.put("CLIENT_NOTES_CONTENT", configJson.get("CLIENT_NOTES_CONTENT"));
				} else if("4".equals(saveType)) {
					JSONObject fixDutyConfig = configJson.getJSONObject("FIX_DUTY_CONFIG");
					if(fixDutyConfig == null) {
						fixDutyConfig = new JSONObject();
					}
					configOldJson.put("FIX_DUTY_CONFIG", fixDutyConfig);

					configLogger.info("用户[" + user.getUserAcc() + "] 修改了省份[" + provinceCode + "] 定责配置信息:" + JSONObject.toJSONString(fixDutyConfig));

					// 公众端配置
//					clientConfig.put("CLIENT_TITLE", configJson.get("CLIENT_TITLE"));
//					clientConfig.put("CLIENT_NOTES_CONTENT", configJson.get("CLIENT_NOTES_CONTENT"));
				} else {
					// 其他配置
//					configOldJson.put("PROVINCE_ENT", configJson.get("PROVINCE_ENT"));
//					configOldJson.put("TRANSFER_NOTICE_TEMPLATE_STATUS", configJson.get("TRANSFER_NOTICE_TEMPLATE_STATUS"));
//					configOldJson.put("TRANSFER_NOTICE_TEMPLATE_FILE", configJson.get("TRANSFER_NOTICE_TEMPLATE_FILE"));
//					configOldJson.put("AUTO_PROCESS_CHECK", configJson.get("AUTO_PROCESS_CHECK"));
//					configOldJson.put("ORDER_ARCHIVING_TIME", configJson.get("ORDER_ARCHIVING_TIME"));
//					configOldJson.put("ORDER_HISTORY_TIMES", configJson.get("ORDER_HISTORY_TIMES"));
//					configOldJson.put("SEND_SMS_ENT_TYPE", configJson.get("SEND_SMS_ENT_TYPE"));

					configOldJson.putAll(configJson);
					configLogger.info("用户[" + user.getUserAcc() + "] 修改了省份[" + provinceCode + "] 基础配置信息:" + JSONObject.toJSONString(configJson));
				}
				XtyOptionLogUtils.handleLog(XtyOptionLogUtils.OptionFlag.PROVINCE_CONF_EDIT, getRequest(), jsonObject, row.getJSONObject("CONFIG_JSON"));
				record.put("CONFIG_JSON", configOldJson.toJSONString());
			} else {
				record.put("CONFIG_JSON", configJson.toJSONString());
				configLogger.info("用户[" + user.getUserAcc() + "] 修改了省份[" + provinceCode + "] 初始化配置信息:" + JSONObject.toJSONString(configJson));
			}

			record.put("CONFIG_TYPE", configType);
			if(row == null) {
				record.put("ID", RandomKit.randomStr());
				record.put("ENT_ID", entId);
				record.put("BUSI_ORDER_ID", busiOrderId);
				record.put("PROVINCE", user.getDeptProvinceCode());
				record.put("CREATE_ACC", userAcc);
				record.put("CREATE_DEPT", userDept);
				record.put("CREATE_TIME", DateUtil.getCurrentDateStr());
				this.getQuery().save(record);
				// 9-配置修改
				OptionLogUtil.insertLog(record.getString("ID"),"9",JSONObject.toJSONString(record),user);
			} else {
				record.put("ID", row.getString("ID"));
				record.put("UPDATE_ACC", userAcc);
				record.put("UPDATE_TIME", DateUtil.getCurrentDateStr());
				this.getQuery().update(record);
				// 9-配置修改
				OptionLogUtil.insertLog(record.getString("ID"),"9",JSONObject.toJSONString(record),user);
			}

			CacheUtil.delete(Constants.getClientConfigCache(entId, user.getDeptProvinceCode()));

			CacheUtil.delete("XTY_PROVINCE_CONFIG_" + provinceCode + "_" + configType);
			return EasyResult.ok();
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
		return EasyResult.fail();
	}

	/**
	 * 工单环节同步
	 * @return
	 */
	@InfAuthCheck(resId = {"cx-xty-complain-config-global-ent", "cx-xty-complain-config-province-param-node"})
	public JSONObject actionForOrderNodeSync() {
		try {
			JSONObject jsonObject = this.getJSONObject();
			String processId = jsonObject.getString("PROCESS_ID");

			UserModel user = UserUtil.getUser(getRequest());
			String entId = user.getEpCode();
			String busiOrderId = user.getBusiOrderId();
			String userDept = user.getDeptCode();
			String deptType = user.getDeptGroupType();
			String userAccType = Constants.getUserAccType(user);
			String provinceCode = null;
			if(StringUtils.equals(Constants.USER_TYPE_ADMIN, userAccType) ||  StringUtils.equals(Constants.USER_TYPE_B, userAccType)) {
				userDept = jsonObject.getString("userDept");
				deptType = jsonObject.getString("deptType");
				provinceCode = this.getQuery().queryForString("select PROVINCE_CODE from " + this.getTableName("CC_SKILL_GROUP") + " where ENT_ID = ? and BUSI_ORDER_ID = ? and SKILL_GROUP_CODE = ?", entId, busiOrderId, userDept);
			} else if(StringUtils.equals(Constants.USER_TYPE_P, userAccType)) {
				provinceCode = user.getDeptProvinceCode();
			} else {
				if (!Constants.DUTY_FLOW_KEY.equals(processId)) {
					return EasyResult.fail("当前账号无权限访问");
				}
				provinceCode = user.getDeptProvinceCode();
			}

			provinceCode = StringUtils.isNotBlank(jsonObject.getString("provinceCode")) ?
					jsonObject.getString("provinceCode") : provinceCode;

			EasySQL sql = new EasySQL("select t1.ID,t1.CONFIG_JSON");
			sql.append("from " + this.getTableName("XTY_PROVINCE_CONFIG") + " t1");
			sql.append("where 1=1");
			sql.append(entId, "and t1.ENT_ID = ?");
			sql.append(busiOrderId, "and t1.BUSI_ORDER_ID = ?");
			sql.append(provinceCode, "and t1.PROVINCE = ?");
			sql.append(processId, "and t1.CONFIG_TYPE = ?");

			JSONObject configData = this.getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

			String configId = configData == null ? "" : configData.getString("ID");
			List<JSONObject> oldNodeList = new ArrayList<JSONObject>();
			if(StringUtils.isNotBlank(configId)) {
				oldNodeList = configData.getJSONObject("CONFIG_JSON").getJSONArray("orderNodeList").toJavaList(JSONObject.class);
			}
			Map<String, JSONObject> oldNodeMap = oldNodeList.stream().collect(Collectors.toMap(t -> t.getString("NODE_ID"), t -> t,(o1,o2) -> o1));
			List<JSONObject> nodeList = new ArrayList<JSONObject>();

			if(StringUtils.equals(Constants.DUTY_FLOW_KEY, processId)) {
				// 认定流程从数据字典获取
				List<Dict> dutyProcessNode = DictCache.getAllDictListByGroupCode(entId, "XTY_DUTY_PROCESS");
				for (Dict dict : dutyProcessNode) {
					JSONObject nodeJson = new JSONObject();
					nodeJson.put("NODE_ID", dict.getCode());
					nodeJson.put("NODE_NAME", dict.getName());
					nodeJson.put("SORT_INDEX", dict.getSortNum());
					nodeList.add(nodeJson);
				}
			} else {
				// 其他流程从工单流程定义获取
				nodeList = this.getQuery().queryForList("select NODE_ID,NODE_NAME,SORT_INDEX from " + this.getTableName("C_WF_PROCESS_NODE") + " where FLOW_KEY = ? and ENT_ID = ? and BUSI_ORDER_ID = ?  AND NODE_TYPE not in ('branch','start','end','receiveTask') and NODE_ID not in ('USERTASK_z9cry6t8t4','USERTASK_qpm35i8u1') order by SORT_INDEX asc", new Object[] {processId, entId, busiOrderId}, new JSONMapperImpl());
			}

			List<JSONObject> configArray = new ArrayList<JSONObject>();
			for (JSONObject nodeJson : nodeList) {
				String nodeId = nodeJson.getString("NODE_ID");
				String nodeName = nodeJson.getString("NODE_NAME");

				JSONObject oldNodeData = oldNodeMap.get(nodeId);

				JSONObject row = new JSONObject();
				if(oldNodeData != null) {
					row = JSONObject.parseObject(oldNodeData.toJSONString());
					row.put("NODE_NAME", nodeName);
					row.put("SORT_INDEX", nodeJson.getString("SORT_INDEX"));
					if(nodeId.startsWith("pre")) {
						row.put("ALLOW_STATUS", DictConstants.ENABEL_STATUS_ENABLE);
					} else {
						row.put("ALLOW_STATUS", DictConstants.ENABEL_STATUS_DISENABLE);
					}
				} else {
					row.put("NODE_ID", nodeId);
					row.put("NODE_NAME", nodeName);
					row.put("DEPT_TYPE", deptType);
					row.put("ALLOCATION_TYPE", Constants.ORDER_NODE_ALLOCATION_TYPE_01);
					row.put("DISTRIBUTE_TYPE", Constants.ORDER_NODE_DISTRIBUTE_TYPE_01);
					row.put("DEAL_DEPT_TYPE", "");
					row.put("ASSOCIATION_NODE", "");
					// 处理日期类型 1-自然日 2-工作日
					row.put("DEAL_DAY_TYPE", "1");
					// 处理天数
					row.put("DEAL_DAYS", "3");
					// 是否分配组长
					row.put("IS_ALLOCATION_LEADER", "01");
					row.put("STATUS", DictConstants.ENABEL_STATUS_ENABLE);
					if(nodeId.startsWith("pre")) {
						row.put("ALLOW_STATUS", DictConstants.ENABEL_STATUS_ENABLE);
					} else {
						row.put("ALLOW_STATUS", DictConstants.ENABEL_STATUS_DISENABLE);
					}
				}
				configArray.add(row);
			}

			EasyRecord record = new EasyRecord(this.getTableName("XTY_PROVINCE_CONFIG"), "ID");
			JSONObject configJson = new JSONObject();
			configJson.put("orderNodeList", configArray);

			record.put("CONFIG_JSON", JSONObject.toJSONString(configJson));
			if(StringUtils.isBlank(configId)) {
				configId = RandomKit.randomStr();
				record.put("ID", configId);
				record.put("ENT_ID", entId);
				record.put("BUSI_ORDER_ID", busiOrderId);
				record.put("CREATE_TIME", DateUtil.getCurrentDateStr());
				record.put("CREATE_ACC", user.getUserAcc());
				record.put("CREATE_DEPT", userDept);
				record.put("PROVINCE", provinceCode);
				record.put("CONFIG_TYPE", processId);
				this.getQuery().save(record);
			} else {
				record.put("ID", configId);
				record.put("UPDATE_TIME", DateUtil.getCurrentDateStr());
				record.put("UPDATE_ACC", user.getUserAcc());
				this.getQuery().update(record);
			}

			OrderNodeUtil.getInstance().reloadNode(processId, entId, provinceCode, this.getDbName());

			CacheUtil.delete("XTY_PROVINCE_CONFIG_" + provinceCode + "_" + processId);
			return EasyResult.ok();
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
		return EasyResult.fail();
	}


	@InfAuthCheck(resId = {"cx-xty-complain-config-global-ent", "cx-xty-complain-config-province-param-node"})
	public EasyResult actionForOrderNodeEdit() {
		try {
			JSONObject jsonObject = this.getJSONObject();
			String processId = jsonObject.getString("PROCESS_ID");
			String nodeId = jsonObject.getString("NODE_ID");
			String allocationType = jsonObject.getString("ALLOCATION_TYPE");
			String distributeType = jsonObject.getString("DISTRIBUTE_TYPE");
			String dealDayType = jsonObject.getString("DEAL_DAY_TYPE");
			String dealDays = jsonObject.getString("DEAL_DAYS");
			String dealDayType2 = jsonObject.getString("DEAL_DAY_TYPE2");
			String dealDays2 = jsonObject.getString("DEAL_DAYS2");
			String associationProcId = jsonObject.getString("ASSOCIATION_PROC_ID");
			String associationNode = jsonObject.getString("ASSOCIATION_NODE");
			String dealDeptType = jsonObject.getString("DEAL_DEPT_TYPE");
			String isAllocationLeader = jsonObject.getString("IS_ALLOCATION_LEADER");
			if(StringUtils.isBlank(nodeId) || StringUtils.isBlank(processId)) {
				return EasyResult.fail("缺少必填参数");
			}

			UserModel user = UserUtil.getUser(getRequest());
			String entId = user.getEpCode();
			String busiOrderId = user.getBusiOrderId();
			String userDept = user.getDeptCode();
			String provinceCode = user.getDeptProvinceCode();
			String userAccType = Constants.getUserAccType(user);
			String deptType = user.getDeptGroupType();
			logger.info("用户【" + user.getUserName() + "】修改省份【" + provinceCode + "】环节【" + nodeId + "】配置：" + JSONObject.toJSONString(jsonObject));
			if(StringUtils.equals(Constants.USER_TYPE_ADMIN, userAccType) ||  StringUtils.equals(Constants.USER_TYPE_B, userAccType)) {
				userDept = StringUtils.isNotBlank(jsonObject.getString("userDept")) ? jsonObject.getString("userDept") : userDept;
				deptType = jsonObject.getString("deptType");
				provinceCode = this.getQuery().queryForString("select PROVINCE_CODE from " + this.getTableName("CC_SKILL_GROUP") + " where ENT_ID = ? and BUSI_ORDER_ID = ? and SKILL_GROUP_CODE = ?", entId, busiOrderId, userDept);
			} else if(StringUtils.equals(Constants.USER_TYPE_P, userAccType)) {
				userDept = user.getDeptCode();
				provinceCode = user.getDeptProvinceCode();
			} else {
				if (!Constants.DUTY_FLOW_KEY.equals(processId)) {
					return EasyResult.fail("当前账号无权限访问");
				}
				provinceCode = user.getDeptProvinceCode();
			}

			if (Constants.DUTY_FLOW_KEY.equals(processId)) {
				boolean flag = dealDutyNode(jsonObject, provinceCode, deptType, userDept, entId, busiOrderId, user);
				if (flag) {
					return EasyResult.ok();
				}
			} else if (Constants.CHECK_FLOW_KEY.equals(processId)) {
				boolean flag = dealCheckNode(jsonObject, provinceCode, user);
				if (flag) {
					return EasyResult.ok();
				}

			}else if (Constants.APPEAL_FLOW_KEY.equals(processId)) {
				boolean flag = dealAppealNode(jsonObject, provinceCode, deptType, userDept, entId, busiOrderId, user);
				if (flag) {
					return EasyResult.ok();
				}
			} else if (Constants.MEDIATE_FLOW_KEY.equals(processId)) {
				boolean flag = dealMediateNode(jsonObject, provinceCode, deptType, userDept, entId, busiOrderId, user);
				if (flag) {
					return EasyResult.ok();
				}
			}

			provinceCode = StringUtils.isNotBlank(jsonObject.getString("provinceCode")) ?
					jsonObject.getString("provinceCode") : provinceCode;
			EasySQL sql = new EasySQL("select t1.ID,t1.CONFIG_JSON");
			sql.append("from " + this.getTableName("XTY_PROVINCE_CONFIG") + " t1");
			sql.append("where 1=1");
			sql.append(provinceCode, "and t1.PROVINCE = ?");
			sql.append(processId, "and t1.CONFIG_TYPE = ?");

			JSONObject configData = this.getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if(configData == null) {
				return EasyResult.fail("未找到相关配置信息");
			}

			JSONObject configJson = configData.getJSONObject("CONFIG_JSON");
			List<JSONObject> nodeList = configJson.getJSONArray("orderNodeList").toJavaList(JSONObject.class);
			for(JSONObject nodeJson : nodeList) {
				if(nodeId.equals(nodeJson.getString("NODE_ID"))) {
					nodeJson.put("IS_ALLOCATION_LEADER", isAllocationLeader);
					nodeJson.put("ALLOCATION_TYPE", allocationType);
					nodeJson.put("DISTRIBUTE_TYPE", distributeType);
					nodeJson.put("DEAL_DAY_TYPE", dealDayType);
					nodeJson.put("DEAL_DAYS", dealDays);
					nodeJson.put("DEAL_DAY_TYPE2", dealDayType2);
					nodeJson.put("DEAL_DAYS2", dealDays2);
					nodeJson.put("ASSOCIATION_NODE", associationNode);
					nodeJson.put("ASSOCIATION_PROC_ID", associationProcId);
					nodeJson.put("DEAL_DEPT_TYPE", dealDeptType);
					String finalProvinceCode = provinceCode;
					ThreadManager.getInstance().executeOneTimes(Constants.APP_NAME, new BaseTask(Constants.APP_NAME,logger) {
						@Override
						public void doing() {
							try {
								JSONObject entryParam = new JSONObject();
								entryParam.put("provinceOrderNodeCfg",nodeJson);
								entryParam.put("province", finalProvinceCode);
								ServiceUtil.invoke2(AutoAssignService.SERVICE_ID,entryParam);
							} catch (Exception e) {
								logger.error(e.getMessage(),e);
							}
						}
					});
					break;
				}
			}

			EasyRecord record = new EasyRecord(this.getTableName("XTY_PROVINCE_CONFIG"), "ID");
			record.put("ID", configData.getString("ID"));
			record.put("UPDATE_TIME", DateUtil.getCurrentDateStr());
			record.put("UPDATE_ACC", user.getUserAcc());
			record.put("CONFIG_JSON", JSONObject.toJSONString(configJson));
			this.getQuery().update(record);
			// 9-配置修改
			OptionLogUtil.insertLog(configData.getString("ID"),"9",JSONObject.toJSONString(record),user);
			OrderNodeUtil.getInstance().reloadNode(processId, entId, provinceCode, this.getDbName());

			CacheUtil.delete("XTY_PROVINCE_CONFIG_" + provinceCode + "_" + processId);

			XtyOptionLogUtils.handleLog(XtyOptionLogUtils.OptionFlag.ORDER_NODE_EDIT, getRequest(), jsonObject, configData);
			return EasyResult.ok();
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
		return EasyResult.fail();
	}

	private boolean dealCheckNode(JSONObject jsonObject, String entId, UserModel user) {
		String processId = jsonObject.getString("PROCESS_ID");
		String nodeId = jsonObject.getString("NODE_ID");
		String allocationType = jsonObject.getString("ALLOCATION_TYPE");
		String distributeType = jsonObject.getString("DISTRIBUTE_TYPE");
		String dealDayType = jsonObject.getString("DEAL_DAY_TYPE");
		String dealDays = jsonObject.getString("DEAL_DAYS");
		String dealDayType2 = jsonObject.getString("DEAL_DAY_TYPE2");
		String dealDays2 = jsonObject.getString("DEAL_DAYS2");
		String associationProcId = jsonObject.getString("ASSOCIATION_PROC_ID");
		String associationNode = jsonObject.getString("ASSOCIATION_NODE");
		String dealDeptType = jsonObject.getString("DEAL_DEPT_TYPE");
		String isAllocationLeader = jsonObject.getString("IS_ALLOCATION_LEADER");
		boolean flag = false;
		JSONObject nodeJson = new JSONObject();
		nodeJson.put("IS_ALLOCATION_LEADER", isAllocationLeader);
		nodeJson.put("ALLOCATION_TYPE", allocationType);
		nodeJson.put("DISTRIBUTE_TYPE", distributeType);
		nodeJson.put("DEAL_DAY_TYPE", dealDayType);
		nodeJson.put("DEAL_DAYS", dealDays);
		nodeJson.put("DEAL_DAY_TYPE2", dealDayType2);
		nodeJson.put("DEAL_DAYS2", dealDays2);
		nodeJson.put("ASSOCIATION_NODE", associationNode);
		nodeJson.put("ASSOCIATION_PROC_ID", associationProcId);
		nodeJson.put("DEAL_DEPT_TYPE", dealDeptType);
		nodeJson.put("IS_REPAIR_INFO", jsonObject.getString("IS_REPAIR_INFO"));
		nodeJson.put("NODE_ID", nodeId);
		try {
			EasySQL sql = new EasySQL();
			sql.append(" select t1.ID,t1.PROVINCE,t1.CONFIG_JSON ");
			sql.append(" from " + this.getTableName("XTY_PROVINCE_CONFIG") + " t1");
			sql.append(" where 1=1");
			sql.append(processId, " and t1.CONFIG_TYPE = ?");
			List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if (CollectionUtils.isNotEmpty(list)) {
				list.stream().forEach(item -> {
					try {
						JSONObject configJson = item.getJSONObject("CONFIG_JSON");
						List<JSONObject> nodeList = configJson.getJSONArray("orderNodeList").toJavaList(JSONObject.class);
						for (JSONObject node : nodeList) {
							if (nodeId.equals(node.getString("NODE_ID"))) {
								node.putAll(nodeJson);
								break;
							}
						}
						EasyRecord record2 = new EasyRecord(this.getTableName("XTY_PROVINCE_CONFIG"), "ID");
						record2.put("ID", item.getString("ID"));
						record2.put("UPDATE_TIME", DateUtil.getCurrentDateStr());
						record2.put("UPDATE_ACC", user.getUserAcc());
						record2.put("CONFIG_JSON", JSONObject.toJSONString(configJson));
						this.getQuery().update(record2);
						// 9-配置修改
						OptionLogUtil.insertLog(item.getString("ID"), "9", JSONObject.toJSONString(record2), user);
						OrderNodeUtil.getInstance().reloadNode(processId, entId, item.getString("PROVINCE"), this.getDbName());
						CacheUtil.delete("XTY_PROVINCE_CONFIG_" + user.getDeptProvinceCode() + "_" + processId);
						XtyOptionLogUtils.handleLog(XtyOptionLogUtils.OptionFlag.ORDER_NODE_EDIT, getRequest(), jsonObject, item);
					} catch (Exception e) {
						logger.error(e.getMessage(), e);
					}
				});
				CacheUtil.delete("XTY_PROVINCE_CONFIG_" + "_" + processId);
			}
			flag = true;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return flag;
	}


	private boolean dealDutyNode (JSONObject jsonObject,String provinceCode,String deptType,String userDept,String entId,String busiOrderId,UserModel user) {
		String processId = jsonObject.getString("PROCESS_ID");
		String nodeId = jsonObject.getString("NODE_ID");
		String allocationType = jsonObject.getString("ALLOCATION_TYPE");
		String distributeType = jsonObject.getString("DISTRIBUTE_TYPE");
		String dealDayType = jsonObject.getString("DEAL_DAY_TYPE");
		String dealDays = jsonObject.getString("DEAL_DAYS");
		String dealDayType2 = jsonObject.getString("DEAL_DAY_TYPE2");
		String dealDays2 = jsonObject.getString("DEAL_DAYS2");
		String associationProcId = jsonObject.getString("ASSOCIATION_PROC_ID");
		String associationNode = jsonObject.getString("ASSOCIATION_NODE");
		String dealDeptType = jsonObject.getString("DEAL_DEPT_TYPE");
		String isAllocationLeader = jsonObject.getString("IS_ALLOCATION_LEADER");
		String configType = "";
		boolean flag = false;
		String finalProvinceCode = provinceCode;
		JSONObject nodeJson = new JSONObject();
		nodeJson.put("IS_ALLOCATION_LEADER", isAllocationLeader);
		nodeJson.put("ALLOCATION_TYPE", allocationType);
		nodeJson.put("DISTRIBUTE_TYPE", distributeType);
		nodeJson.put("DEAL_DAY_TYPE", dealDayType);
		nodeJson.put("DEAL_DAYS", dealDays);
		nodeJson.put("DEAL_DAY_TYPE2", dealDayType2);
		nodeJson.put("DEAL_DAYS2", dealDays2);
		nodeJson.put("ASSOCIATION_NODE", associationNode);
		nodeJson.put("ASSOCIATION_PROC_ID", associationProcId);
		nodeJson.put("DEAL_DEPT_TYPE", dealDeptType);
		nodeJson.put("IS_REPAIR_INFO", jsonObject.getString("IS_REPAIR_INFO"));
		nodeJson.put("NODE_ID",nodeId);
		//nodeJson.put("STATUS", StringUtils.isNotBlank(jsonObject.getString("STATUS"))?jsonObject.getString("STATUS"):"01");
		JSONObject oldParam = null;
		try {
			switch (nodeId) {
				case Constants.DUTY_PROCESS_NODE_APPEND:
				case Constants.DUTY_PROCESS_NODE_JUDGEMENT:
				case Constants.DUTY_PROCESS_NODE_JUDGEMENT_CHECK:
					configType = "1";
					if (Constants.USER_TYPE_GE.equals(Constants.getUserAccType(deptType))) {
						configType = "2";
					}
					flag = true;
					EasyRow row = this.getQuery().queryForRow("select * from " + this.getTableName("XTY_DEPT_CONFIG") + " t1 where t1.ENT_ID = ? and t1.BUSI_ORDER_ID = ? and t1.CONFIG_TYPE = ? and DEPT_CODE = ?", entId, busiOrderId,configType, userDept);
					EasyRecord record = new EasyRecord(getTableName("XTY_DEPT_CONFIG"), "ID");
					record.put("CONFIG_TYPE", configType);
					record.put("GROUP_TYPE",  deptType);
					if(row == null) {
						JSONObject configJson = new JSONObject();
						configJson.put(nodeId,nodeJson);
						record.put("ID", RandomKit.randomStr());
						record.put("ENT_ID", entId);
						record.put("BUSI_ORDER_ID", busiOrderId);
						record.put("CREATE_ACC", user.getUserAcc());
						record.put("CREATE_TIME", DateUtil.getCurrentDateStr());
						record.put("UPDATE_ACC", user.getUserAcc());
						record.put("UPDATE_TIME", DateUtil.getCurrentDateStr());
						record.put("DEPT_CODE", userDept);
						record.put("CONFIG_JSON", JSONObject.toJSONString(configJson));
						this.getQuery().save(record);
					} else {
						oldParam = row.toJSONObject();
						String configJsonStr = row.getColumnValue("CONFIG_JSON");
						JSONObject configJson = JSONObject.parseObject(configJsonStr);
						JSONObject object = configJson.getJSONObject(nodeId);
						if (Objects.isNull(object)) {
							object = new JSONObject();
						}
						object.putAll(nodeJson);
						configJson.put(nodeId,object);
						record.put("ID", row.getColumnValue("ID"));
						record.put("UPDATE_ACC", user.getUserAcc());
						record.put("UPDATE_TIME", DateUtil.getCurrentDateStr());
						record.put("CONFIG_JSON", JSONObject.toJSONString(configJson));
						this.getQuery().update(record);
						// 9-配置修改
						OptionLogUtil.insertLog(row.getColumnValue("ID"),"9",JSONObject.toJSONString(record),user);
					}
					XtyOptionLogUtils.handleLog(XtyOptionLogUtils.OptionFlag.ORDER_NODE_EDIT, getRequest(), jsonObject, oldParam);
					String finalUserDept = userDept;
					ThreadManager.getInstance().executeOneTimes(Constants.APP_NAME, new BaseTask(Constants.APP_NAME,logger) {
						@Override
						public void doing() {
							try {
								JSONObject entryParam = new JSONObject();
								entryParam.put("provinceOrderNodeCfg",nodeJson);
								entryParam.put("province", finalProvinceCode);
								entryParam.put("deptCode", finalUserDept);
								ServiceUtil.invoke2(AutoAssignService.SERVICE_ID,entryParam);
							} catch (Exception e) {
								logger.error(e.getMessage(),e);
							}
						}
					});
					logger.info("清除缓存：" + "XTY_CONFIG_DEPT_" + finalUserDept + "|" + "XTY_JUDGEMENT_ENT_" + finalUserDept);
					CacheUtil.delete("XTY_CONFIG_DEPT_" + finalUserDept);
					CacheUtil.delete("XTY_JUDGEMENT_ENT_" + finalUserDept);
					break;
				case Constants.DUTY_PROCESS_NODE_REVIEW:
				case Constants.DUTY_PROCESS_NODE_REVIEW_CHECK:
				case Constants.DUTY_PROCESS_NODE_DOUBLE_CHECK:
				case Constants.DUTY_PROCESS_NODE_APPROVE:
				case Constants.DUTY_PROCESS_NODE_APPROVE_CHECK:
					EasySQL sql = new EasySQL();
					sql.append(" select t1.ID,t1.PROVINCE,t1.CONFIG_JSON ");
					sql.append(" from " + this.getTableName("XTY_PROVINCE_CONFIG") + " t1");
					sql.append(" where 1=1");
					sql.append(processId, " and t1.CONFIG_TYPE = ?");
					List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
					//logger.info("node list:" + JSONObject.toJSONString(list));
					if (CollectionUtils.isNotEmpty(list)) {
						list.stream().forEach(item -> {
							try {
								JSONObject configJson = item.getJSONObject("CONFIG_JSON");
								List<JSONObject> nodeList = configJson.getJSONArray("orderNodeList").toJavaList(JSONObject.class);
								for(JSONObject node : nodeList) {
									if(nodeId.equals(node.getString("NODE_ID"))) {
										node.putAll(nodeJson);
										ThreadManager.getInstance().executeOneTimes(Constants.APP_NAME, new BaseTask(Constants.APP_NAME,logger) {
											@Override
											public void doing() {
												try {
													JSONObject entryParam = new JSONObject();
													entryParam.put("provinceOrderNodeCfg",nodeJson);
													entryParam.put("province", item.getString("PROVINCE"));
													ServiceUtil.invoke2(AutoAssignService.SERVICE_ID,entryParam);
												} catch (Exception e) {
													logger.error(e.getMessage(),e);
												}
											}
										});
										break;
									}
								}
								EasyRecord record2 = new EasyRecord(this.getTableName("XTY_PROVINCE_CONFIG"), "ID");
								record2.put("ID", item.getString("ID"));
								record2.put("UPDATE_TIME", DateUtil.getCurrentDateStr());
								record2.put("UPDATE_ACC", user.getUserAcc());
								record2.put("CONFIG_JSON", JSONObject.toJSONString(configJson));
								this.getQuery().update(record2);
								// 9-配置修改
								OptionLogUtil.insertLog(item.getString("ID"),"9",JSONObject.toJSONString(record2),user);
								OrderNodeUtil.getInstance().reloadNode(processId, entId, item.getString("PROVINCE"), this.getDbName());
								CacheUtil.delete("XTY_PROVINCE_CONFIG_" + user.getDeptProvinceCode() + "_" + processId);
								XtyOptionLogUtils.handleLog(XtyOptionLogUtils.OptionFlag.ORDER_NODE_EDIT, getRequest(), jsonObject, item);
							} catch (Exception e) {

							}
						});
						CacheUtil.delete("XTY_PROVINCE_CONFIG_" + "_" + processId);
					}
					flag = true;
					break;
				default:
					flag = false;
					break;
			}
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return flag;
	}


	private boolean dealAppealNode (JSONObject jsonObject,String provinceCode,String deptType,String userDept,String entId,String busiOrderId,UserModel user) {
		String processId = jsonObject.getString("PROCESS_ID");
		String nodeId = jsonObject.getString("NODE_ID");
		String allocationType = jsonObject.getString("ALLOCATION_TYPE");
		String distributeType = jsonObject.getString("DISTRIBUTE_TYPE");
		String dealDayType = jsonObject.getString("DEAL_DAY_TYPE");
		String dealDays = jsonObject.getString("DEAL_DAYS");
		String dealDayType2 = jsonObject.getString("DEAL_DAY_TYPE2");
		String dealDays2 = jsonObject.getString("DEAL_DAYS2");
		String associationProcId = jsonObject.getString("ASSOCIATION_PROC_ID");
		String associationNode = jsonObject.getString("ASSOCIATION_NODE");
		String dealDeptType = jsonObject.getString("DEAL_DEPT_TYPE");
		String isAllocationLeader = jsonObject.getString("IS_ALLOCATION_LEADER");
		boolean flag = false;
		String finalProvinceCode = provinceCode;
		JSONObject nodeJson = new JSONObject();
		nodeJson.put("IS_ALLOCATION_LEADER", isAllocationLeader);
		nodeJson.put("ALLOCATION_TYPE", allocationType);
		nodeJson.put("DISTRIBUTE_TYPE", distributeType);
		nodeJson.put("DEAL_DAY_TYPE", dealDayType);
		nodeJson.put("DEAL_DAYS", dealDays);
		nodeJson.put("DEAL_DAY_TYPE2", dealDayType2);
		nodeJson.put("DEAL_DAYS2", dealDays2);
		nodeJson.put("ASSOCIATION_NODE", associationNode);
		nodeJson.put("ASSOCIATION_PROC_ID", associationProcId);
		nodeJson.put("DEAL_DEPT_TYPE", dealDeptType);
		nodeJson.put("IS_REPAIR_INFO", jsonObject.getString("IS_REPAIR_INFO"));
		nodeJson.put("NODE_ID",nodeId);
		//nodeJson.put("STATUS", StringUtils.isNotBlank(jsonObject.getString("STATUS"))?jsonObject.getString("STATUS"):"01");
		try {
			switch (nodeId) {
				case Constants.COMPLAIN_PREACCEPT:
				case Constants.COMPLAIN_DEAL:
				case Constants.COMPLAIN_PREACCEPT_CHECK:
				case Constants.COMPLAIN_DEAL_CHECK:
					flag = true;
					EasyRow row = this.getQuery().queryForRow("select * from " + this.getTableName("XTY_DEPT_CONFIG") + " t1 where t1.ENT_ID = ? and t1.BUSI_ORDER_ID = ? and t1.CONFIG_TYPE = ? and DEPT_CODE = ?", entId, busiOrderId,"1", userDept);
					EasyRecord record = new EasyRecord(getTableName("XTY_DEPT_CONFIG"), "ID");
					record.put("CONFIG_TYPE", "1");
					record.put("GROUP_TYPE",  deptType);
					JSONObject oldParam = null;
					if(row == null) {
						JSONObject configJson = new JSONObject();
						configJson.put(nodeId,nodeJson);
						record.put("ID", RandomKit.randomStr());
						record.put("ENT_ID", entId);
						record.put("BUSI_ORDER_ID", busiOrderId);
						record.put("CREATE_ACC", user.getUserAcc());
						record.put("CREATE_TIME", DateUtil.getCurrentDateStr());
						record.put("UPDATE_ACC", user.getUserAcc());
						record.put("UPDATE_TIME", DateUtil.getCurrentDateStr());
						record.put("DEPT_CODE", userDept);
						record.put("CONFIG_JSON", JSONObject.toJSONString(configJson));
						this.getQuery().save(record);
						// 9-配置修改
						OptionLogUtil.insertLog(record.getString("ID"),"9",JSONObject.toJSONString(record),user);
					} else {
						oldParam = row.toJSONObject();
						String configJsonStr = row.getColumnValue("CONFIG_JSON");
						JSONObject configJson = JSONObject.parseObject(configJsonStr);
						JSONObject object = configJson.getJSONObject(nodeId);
						if (Objects.isNull(object)) {
							object = new JSONObject();
						}
						object.putAll(nodeJson);
						configJson.put(nodeId,object);
						record.put("ID", row.getColumnValue("ID"));
						record.put("UPDATE_ACC", user.getUserAcc());
						record.put("UPDATE_TIME", DateUtil.getCurrentDateStr());
						record.put("CONFIG_JSON", JSONObject.toJSONString(configJson));
						this.getQuery().update(record);
						// 9-配置修改
						OptionLogUtil.insertLog(row.getColumnValue("ID"),"9",JSONObject.toJSONString(record),user);
					}
					XtyOptionLogUtils.handleLog(XtyOptionLogUtils.OptionFlag.ORDER_NODE_EDIT, getRequest(), jsonObject, oldParam);
					String finalUserDept = userDept;
					ThreadManager.getInstance().executeOneTimes(Constants.APP_NAME, new BaseTask(Constants.APP_NAME,logger) {
						@Override
						public void doing() {
							try {
								JSONObject entryParam = new JSONObject();
								entryParam.put("provinceOrderNodeCfg",nodeJson);
								entryParam.put("province", finalProvinceCode);
								entryParam.put("deptCode", finalUserDept);
								ServiceUtil.invoke2(AutoAssignService.SERVICE_ID,entryParam);
							} catch (Exception e) {
								logger.error(e.getMessage(),e);
							}
						}
					});
					CacheUtil.delete("XTY_CONFIG_DEPT_" + finalUserDept);
					break;
				default:
					flag = false;
					break;
			}
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return flag;
	}

	private boolean dealMediateNode (JSONObject jsonObject,String provinceCode,String deptType,String userDept,String entId,String busiOrderId,UserModel user) {
		String processId = jsonObject.getString("PROCESS_ID");
		String nodeId = jsonObject.getString("NODE_ID");
		String allocationType = jsonObject.getString("ALLOCATION_TYPE");
		String distributeType = jsonObject.getString("DISTRIBUTE_TYPE");
		String dealDayType = jsonObject.getString("DEAL_DAY_TYPE");
		String dealDays = jsonObject.getString("DEAL_DAYS");
		String dealDayType2 = jsonObject.getString("DEAL_DAY_TYPE2");
		String dealDays2 = jsonObject.getString("DEAL_DAYS2");
		String associationProcId = jsonObject.getString("ASSOCIATION_PROC_ID");
		String associationNode = jsonObject.getString("ASSOCIATION_NODE");
		String dealDeptType = jsonObject.getString("DEAL_DEPT_TYPE");
		String isAllocationLeader = jsonObject.getString("IS_ALLOCATION_LEADER");
		boolean flag = false;
		String finalProvinceCode = provinceCode;
		JSONObject nodeJson = new JSONObject();
		nodeJson.put("IS_ALLOCATION_LEADER", isAllocationLeader);
		nodeJson.put("ALLOCATION_TYPE", allocationType);
		nodeJson.put("DISTRIBUTE_TYPE", distributeType);
		nodeJson.put("DEAL_DAY_TYPE", dealDayType);
		nodeJson.put("DEAL_DAYS", dealDays);
		nodeJson.put("DEAL_DAY_TYPE2", dealDayType2);
		nodeJson.put("DEAL_DAYS2", dealDays2);
		nodeJson.put("ASSOCIATION_NODE", associationNode);
		nodeJson.put("ASSOCIATION_PROC_ID", associationProcId);
		nodeJson.put("DEAL_DEPT_TYPE", dealDeptType);
		nodeJson.put("IS_REPAIR_INFO", jsonObject.getString("IS_REPAIR_INFO"));
		nodeJson.put("NODE_ID",nodeId);
		//nodeJson.put("STATUS", StringUtils.isNotBlank(jsonObject.getString("STATUS"))?jsonObject.getString("STATUS"):"01");
		JSONObject oldParam = null;
		try {
			switch (nodeId) {
				case Constants.MEDIATE_ENT_CONFIRM:
				case Constants.MEDIATE_ENT_CONFIRM_CHECK:
				case Constants.MEDIATE_ENT_DEAL:
				case Constants.MEDIATE_ENT_DEAL_CHECK:
					flag = true;
					EasyRow row = this.getQuery().queryForRow("select * from " + this.getTableName("XTY_DEPT_CONFIG") + " t1 where t1.ENT_ID = ? and t1.BUSI_ORDER_ID = ? and t1.CONFIG_TYPE = ? and DEPT_CODE = ?", entId, busiOrderId,"1", userDept);
					EasyRecord record = new EasyRecord(getTableName("XTY_DEPT_CONFIG"), "ID");
					record.put("CONFIG_TYPE", "1");
					record.put("GROUP_TYPE",  deptType);
					if(row == null) {
						JSONObject configJson = new JSONObject();
						configJson.put(nodeId,nodeJson);
						record.put("ID", RandomKit.randomStr());
						record.put("ENT_ID", entId);
						record.put("BUSI_ORDER_ID", busiOrderId);
						record.put("CREATE_ACC", user.getUserAcc());
						record.put("CREATE_TIME", DateUtil.getCurrentDateStr());
						record.put("UPDATE_ACC", user.getUserAcc());
						record.put("UPDATE_TIME", DateUtil.getCurrentDateStr());
						record.put("DEPT_CODE", userDept);
						record.put("CONFIG_JSON", JSONObject.toJSONString(configJson));
						this.getQuery().save(record);
						// 9-配置修改
						OptionLogUtil.insertLog(record.getString("ID"),"9",JSONObject.toJSONString(record),user);
					} else {
						oldParam = row.toJSONObject();
						String configJsonStr = row.getColumnValue("CONFIG_JSON");
						JSONObject configJson = JSONObject.parseObject(configJsonStr);
						JSONObject object = configJson.getJSONObject(nodeId);
						if (Objects.isNull(object)) {
							object = new JSONObject();
						}
						object.putAll(nodeJson);
						configJson.put(nodeId,object);
						record.put("ID", row.getColumnValue("ID"));
						record.put("UPDATE_ACC", user.getUserAcc());
						record.put("UPDATE_TIME", DateUtil.getCurrentDateStr());
						record.put("CONFIG_JSON", JSONObject.toJSONString(configJson));
						this.getQuery().update(record);
						// 9-配置修改
						OptionLogUtil.insertLog(row.getColumnValue("ID"),"9",JSONObject.toJSONString(record),user);
					}
					String finalUserDept = userDept;
					XtyOptionLogUtils.handleLog(XtyOptionLogUtils.OptionFlag.ORDER_NODE_EDIT, getRequest(), jsonObject, oldParam);
					ThreadManager.getInstance().executeOneTimes(Constants.APP_NAME, new BaseTask(Constants.APP_NAME,logger) {
						@Override
						public void doing() {
							try {
								JSONObject entryParam = new JSONObject();
								entryParam.put("provinceOrderNodeCfg",nodeJson);
								entryParam.put("province", finalProvinceCode);
								entryParam.put("deptCode", finalUserDept);
								ServiceUtil.invoke2(AutoAssignService.SERVICE_ID,entryParam);
							} catch (Exception e) {
								logger.error(e.getMessage(),e);
							}
						}
					});
					CacheUtil.delete("XTY_CONFIG_DEPT_" + finalUserDept);
					break;
				default:
					flag = false;
					break;
			}
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return flag;
	}

	@InfAuthCheck(resId = {"cx-xty-complain-config-global-ent", "cx-xty-complain-config-province-param-node"})
	public EasyResult actionForOrderNodeStatus() {
		try {
			JSONObject jsonObject = this.getJSONObject();
			String nodeId = jsonObject.getString("NODE_ID");
			String status = jsonObject.getString("STATUS");
			String processId = jsonObject.getString("PROCESS_ID");
			if(StringUtils.isBlank(nodeId) || StringUtils.isBlank(status) || StringUtils.isBlank(processId)) {
				return EasyResult.fail("缺少必填参数");
			}

			UserModel user = UserUtil.getUser(getRequest());
			String entId = user.getEpCode();
			String busiOrderId = user.getBusiOrderId();
			String userDept = null;
			String provinceCode = null;
			String userAccType = Constants.getUserAccType(user);
			if(StringUtils.equals(Constants.USER_TYPE_ADMIN, userAccType) ||  StringUtils.equals(Constants.USER_TYPE_B, userAccType)) {
				userDept = jsonObject.getString("userDept");
				provinceCode = this.getQuery().queryForString("select PROVINCE_CODE from " + this.getTableName("CC_SKILL_GROUP") + " where ENT_ID = ? and BUSI_ORDER_ID = ? and SKILL_GROUP_CODE = ?", entId, busiOrderId, userDept);
			} else if(StringUtils.equals(Constants.USER_TYPE_P, userAccType)) {
				userDept = user.getDeptCode();
				provinceCode = user.getDeptProvinceCode();
			} else {
				return EasyResult.fail("当前账号无权限访问");
			}

			if (StringUtils.equalsAny(nodeId, new String[]{Constants.COMPLAIN_PREACCEPT,Constants.COMPLAIN_DEAL,
					Constants.MEDIATE_ENT_DEAL,Constants.MEDIATE_ENT_DEAL_CHECK,
					Constants.MEDIATE_ENT_CONFIRM,Constants.MEDIATE_ENT_CONFIRM_CHECK,
					Constants.DUTY_PROCESS_NODE_APPEND,Constants.DUTY_PROCESS_NODE_JUDGEMENT,
					Constants.DUTY_PROCESS_NODE_JUDGEMENT_CHECK,Constants.COMPLAIN_DEAL_CHECK,
					Constants.COMPLAIN_PREACCEPT_CHECK})) {
				JSONObject config = OrderNodeUtil.getDeptConfig(userDept, user.getSchemaName());
				logger.info("config:" + JSONObject.toJSONString(config));
				if(config == null || Objects.isNull(JSONPath.eval(config, "$.CONFIG_JSON." + nodeId))) {
					return EasyResult.fail("请先保存环节基本配置信息");
				}
				JSONObject configJson = config.getJSONObject("CONFIG_JSON");
				JSONObject nodeJson = configJson.getJSONObject(nodeId);
				if(Objects.nonNull(nodeJson)) {
					nodeJson.put("STATUS", status);
					configJson.put(nodeId, nodeJson);
					EasyRecord record = new EasyRecord(this.getTableName("XTY_DEPT_CONFIG"), "ID");
					record.put("ID", config.getString("ID"));
					record.put("UPDATE_TIME", DateUtil.getCurrentDateStr());
					record.put("UPDATE_ACC", user.getUserAcc());
					record.put("CONFIG_JSON", JSONObject.toJSONString(configJson));
					this.getQuery().update(record);
					CacheUtil.delete("XTY_CONFIG_DEPT_" + userDept);
					return EasyResult.ok();
				}
			}

			provinceCode = StringUtils.isNotBlank(jsonObject.getString("provinceCode")) ?
					jsonObject.getString("provinceCode") : provinceCode;

			EasySQL sql = new EasySQL("select t1.ID,t1.CONFIG_JSON");
			sql.append("from " + this.getTableName("XTY_PROVINCE_CONFIG") + " t1");
			sql.append("where 1=1");
			sql.append(provinceCode, "and t1.PROVINCE = ?");
			sql.append(processId, "and t1.CONFIG_TYPE = ?");

			List<JSONObject> configDataList = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if(configDataList == null || configDataList.isEmpty()) {
				return EasyResult.fail("未找到相关配置信息");
			}

			for (JSONObject configData : configDataList) {
				JSONObject configJson = configData.getJSONObject("CONFIG_JSON");
				List<JSONObject> nodeList = configJson.getJSONArray("orderNodeList").toJavaList(JSONObject.class);
				for(JSONObject nodeJson : nodeList) {
					if(nodeId.equals(nodeJson.getString("NODE_ID"))) {
						nodeJson.put("STATUS", status);
//					if(DictConstants.ENABEL_STATUS_ENABLE.equals(nodeJson.getString("ALLOW_STATUS"))) {
//						nodeJson.put("STATUS", status);
//					} else {
//						return EasyResult.fail("当前节点不允许修改状态");
//					}
						break;
					}
				}

				EasyRecord record = new EasyRecord(this.getTableName("XTY_PROVINCE_CONFIG"), "ID");
				record.put("ID", configData.getString("ID"));
				record.put("UPDATE_TIME", DateUtil.getCurrentDateStr());
				record.put("UPDATE_ACC", user.getUserAcc());
				record.put("CONFIG_JSON", JSONObject.toJSONString(configJson));
				this.getQuery().update(record);
			}

			OrderNodeUtil.getInstance().reloadNode(processId, entId, provinceCode, this.getDbName());
			return EasyResult.ok();
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
		return EasyResult.fail();
	}

	/**
	 * 加载流程节点列表
	 * @return
	 */
	public EasyResult actionForProcNodeList() {
		try {
			JSONObject jsonObject = this.getJSONObject();
			String processId = jsonObject.getString("PROCESS_ID");
			String dealDeptType = jsonObject.getString("DEAL_DEPT_TYPE");

			UserModel user = UserUtil.getUser(getRequest());
			String entId = user.getEpCode();
			String busiOrderId = user.getBusiOrderId();
			String userDept = null;
			String deptType = user.getDeptGroupType();
			String userAccType = Constants.getUserAccType(user);
			String provinceCode = null;
			if(StringUtils.equals(Constants.USER_TYPE_ADMIN, userAccType) ||  StringUtils.equals(Constants.USER_TYPE_B, userAccType)) {
				userDept = StringUtils.isNotBlank(jsonObject.getString("userDept")) ? jsonObject.getString("userDept") : userDept;
				deptType = StringUtils.isNotBlank(jsonObject.getString("deptType")) ? jsonObject.getString("deptType") : deptType;
				provinceCode = this.getQuery().queryForString("select PROVINCE_CODE from " + this.getTableName("CC_SKILL_GROUP") + " where ENT_ID = ? and BUSI_ORDER_ID = ? and SKILL_GROUP_CODE = ?", entId, busiOrderId, userDept);
			} else if(StringUtils.equals(Constants.USER_TYPE_P, userAccType)) {
				userDept = user.getDeptCode();
				provinceCode = user.getDeptProvinceCode();
			} else {
				return EasyResult.fail("当前账号无权限访问");
			}

			provinceCode = StringUtils.isNotBlank(jsonObject.getString("provinceCode")) ?
					jsonObject.getString("provinceCode") : provinceCode;

			List<JSONObject> orderNodeList = new ArrayList<JSONObject>();
			EasySQL sql = getProcNodeSql(entId, provinceCode, processId);
			List<JSONObject> procList = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			for (JSONObject procJson : procList) {
				String procId = procJson.getString("PROC_ID");
				String procName = procJson.getString("PROC_NAME");
				JSONObject configJson = procJson.getJSONObject("CONFIG_JSON");
				if(configJson != null) {
					JSONArray orderNodeArray = configJson.getJSONArray("orderNodeList");
					if(CommonUtil.listIsNotNull(orderNodeArray)) {
						for(int i = 0; i < orderNodeArray.size(); i++ ) {
							JSONObject nodeJson = orderNodeArray.getJSONObject(i);
							String nodeId = nodeJson.getString("NODE_ID");
							switch (nodeId) {
								case Constants.DUTY_PROCESS_NODE_APPEND:
									if (StringUtils.equalsAny(deptType, Constants.GROUP_TYPE_PE_ARR)) {
										JSONObject node = getNodeJson(user.getEpCode(), user.getBusiOrderId(), "1", userDept, nodeId);
										if (Objects.nonNull(nodeJson)) {
											nodeJson.putAll(node);
										}
									}
									break;
								case Constants.DUTY_PROCESS_NODE_JUDGEMENT:
								case Constants.DUTY_PROCESS_NODE_JUDGEMENT_CHECK:
									logger.info("deptType:" + deptType);
									if (StringUtils.equalsAny(deptType, Constants.GROUP_TYPE_GE_ARR)) {
										logger.info("nodeId:" + nodeId);
										boolean isEnt = false;
										String groupType = "";
										if (Constants.GROUP_TYPE_PE_TELECOM.equals(deptType)) {
											groupType = Constants.GROUP_TYPE_GE_TELECOM;
										} else if (Constants.GROUP_TYPE_PE_MOBILE.equals(deptType)) {
											groupType = Constants.GROUP_TYPE_GE_MOBILE;
										} else if (Constants.GROUP_TYPE_PE_UNICOM.equals(deptType)) {
											groupType = Constants.GROUP_TYPE_GE_UNICOM;
										} else if (Constants.GROUP_TYPE_PE_BROAD.equals(deptType)) {
											groupType = Constants.GROUP_TYPE_GE_BROAD;
										} else {
											isEnt = true;
											groupType = deptType;
										}
										JSONObject entConfig = DutyUtil.getEntConfig(groupType, user.getSchemaName());
										if (Objects.nonNull(entConfig)) {
											JSONObject json = entConfig.getJSONObject("CONFIG_JSON");
											String judge = nodeId.equals(Constants.DUTY_PROCESS_NODE_JUDGEMENT_CHECK) ? json.getString("INITIAL_JUDGM_CHECK") : json.getString("INITIAL_JUDGM");
											if (Constants.MAGIC_01.equals(judge)) {
												if (isEnt) {
													JSONObject nodeJson1 = getNodeJson(user.getEpCode(), user.getBusiOrderId(), "2", userDept, nodeId);
													if (Objects.nonNull(nodeJson1)) {
														logger.info("nodeJson:" + JSONObject.toJSONString(nodeJson));
														nodeJson.putAll(nodeJson1);
													}
												}
											}else {
												if (!isEnt) {
													JSONObject nodeJson1 = getNodeJson(user.getEpCode(), user.getBusiOrderId(), "1", userDept, nodeId);
													if (Objects.nonNull(nodeJson1)) {
														logger.info("nodeJson:" + JSONObject.toJSONString(nodeJson1));
														nodeJson.putAll(nodeJson1);
													}
												}
											}
										}

									}
									break;
								default:
									break;
							}
							// 匹配相同处理部门的数据
							if(StringUtils.equals(nodeJson.getString("DEAL_DEPT_TYPE"), dealDeptType)) {
								JSONObject newJson = new JSONObject();
								// 流程ID
								newJson.put("ASSOCIATION_PROC_ID", procId);
								// 流程名称
								newJson.put("ASSOCIATION_PROC_NAME", procName);
								// 节点ID
								newJson.put("ASSOCIATION_NODE", nodeJson.getString("NODE_ID"));
								// 节点名称
								newJson.put("ASSOCIATION_NAME", nodeJson.getString("NODE_NAME"));
								orderNodeList.add(newJson);
							}
						}
					}
				}
			}
			Collection<JSONObject> objects = orderNodeList.stream().collect(Collectors.toMap(item -> item.getString("ASSOCIATION_NODE"), item -> item, (o1, o2) -> o1)).values();
			return EasyResult.ok(objects);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
		return EasyResult.fail();
	}

	private JSONObject getNodeJson (String entId,String busiOrderId,String configType,String userDept,String nodeId) {
		try {
			JSONObject row = this.getQuery().queryForRow("select * from " + this.getTableName("XTY_DEPT_CONFIG") + " t1 where t1.ENT_ID = ? and t1.BUSI_ORDER_ID = ? and t1.CONFIG_TYPE = ? and DEPT_CODE = ?", new String[]{entId, busiOrderId,configType, userDept},new JSONMapperImpl());
			if (row != null) {
				JSONObject configJson = row.getJSONObject("CONFIG_JSON");
				if (configJson != null) {
					return configJson.getJSONObject(nodeId);
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return null;
	}

	/**
	 * 根据流程ID往前获取节点配置
	 * @param processId
	 * @return
	 */
	public EasySQL getProcNodeSql(String entId, String provinceCode, String processId) {
		EasySQL sql = new EasySQL();
		sql.append("select t1.CONFIG_TYPE PROC_ID,t1.CONFIG_JSON,t2.NAME PROC_NAME");
		sql.append("from " + this.getTableName("XTY_PROVINCE_CONFIG") + " t1");
		sql.append(entId, "left join " + this.getTableName("C_WF_PROCESS") + " t2 on t1.CONFIG_TYPE = t2.FLOW_KEY and t2.ENT_ID = ?");
		sql.append("where 1=1");
		sql.append(Constants.APPEAL_FLOW_KEY, "and t1.CONFIG_TYPE = ?");
		sql.append(provinceCode, "and t1.PROVINCE = ?");

		if(Constants.MEDIATE_FLOW_KEY.equals(processId) || Constants.DUTY_FLOW_KEY.equals(processId)) {
			sql.append("union all");
			sql.append("select t1.CONFIG_TYPE PROC_ID,t1.CONFIG_JSON,t2.NAME PROC_NAME");
			sql.append("from " + this.getTableName("XTY_PROVINCE_CONFIG") + " t1");
			sql.append(entId, "left join " + this.getTableName("C_WF_PROCESS") + " t2 on t1.CONFIG_TYPE = t2.FLOW_KEY and t2.ENT_ID = ?");
			sql.append("where 1=1");
			sql.append(provinceCode, "and t1.PROVINCE = ?");
			sql.append(Constants.MEDIATE_FLOW_KEY, "and t1.CONFIG_TYPE = ?");
		}

		if(Constants.DUTY_FLOW_KEY.equals(processId)) {
			sql.append("union all");
			sql.append("select t1.CONFIG_TYPE PROC_ID,t1.CONFIG_JSON,'认定流程' PROC_NAME");
			sql.append("from " + this.getTableName("XTY_PROVINCE_CONFIG") + " t1");
			sql.append("where 1=1");
			sql.append(provinceCode, "and t1.PROVINCE = ?");
			sql.append(Constants.DUTY_FLOW_KEY, "and t1.CONFIG_TYPE = ?");
		}
		logger.info("sql:" + sql.getFullSq());
		return sql;
	}

	/**
	 * 保存工作时间
	 * @param entId 企业ID
	 * @param busiOrderId 业务订购ID
	 * @param styleId 工作时间样式ID
	 * @param json 配置
	 * @return
	 * @throws SQLException
	 */
	public String saveWorkTimeStyle(String entId, String busiOrderId, String styleId, String provinceName, JSONObject json) throws SQLException {
		JSONObject json2 = JSONObject.parseObject(JSONObject.toJSONString(json));
		JSONArray week = json2.getJSONArray("week");
		json2.put("week", week.toJavaList(JSONObject.class).stream().collect(Collectors.groupingBy(t->(t.getIntValue("BUSI_ID")-1) + "")));
		EasyRecord record=new EasyRecord(getTableName("C_CF_WTIME_STYLE"),"ID");
		record.set("NAME", "[" + provinceName + "]工作时间");
		json.put("NAME", "[" + provinceName + "]工作时间");
		record.set("TIME_TYPE", json.getString("TIME_TYPE"));
		record.set("ENABLE_STATUS", json.getString("ENABLE_STATUS"));
		record.set("SORT_NUM", json.getIntValue("SORT_NUM"));
		record.set("BAKUP", json.getString("BAKUP"));
		record.set("EX_JSON", json2.toJSONString());

		// 多个省份包含此工作时间样式ID，重新生成工作时间样式
		int styleCount = 0;
		if(StringUtils.isNotBlank(styleId)) {
			EasySQL sql = new EasySQL("select count(1)");
			sql.append("from " + this.getTableName("XTY_PROVINCE_CONFIG") + " t1");
			sql.append("0", "where CONFIG_TYPE = ?");
			sql.appendLike(styleId, "and CONFIG_JSON like ?");
			logger.info("sql:" + sql.getFullSq());
			styleCount = getQuery().queryForInt(sql.getSQL(), sql.getParams());
			logger.info("styleCount:" + styleCount);
		}
		if(StringUtils.isBlank(styleId) || styleCount > 1) {
			styleId = RandomKit.randomStr();
			record.set("ID",styleId);
			record.set("BUSI_ORDER_ID", busiOrderId);
			record.set("ENT_ID",entId);
			record.set("CREATE_ACC", this.getUserPrincipal().getUserId());
			record.set("CREATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
			this.getQuery().save(record);
		} else {
			record.set("ID", styleId);
			record.set("UPDATE_ACC", this.getUserPrincipal().getUserName());
			record.set("UPDATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
			this.getQuery().update(record);

			//删除该样式原有的信息，只清除按星期的
			this.getQuery().execute("DELETE FROM " + this.getTableName("C_CF_WTIME_DETAIL") + " WHERE WTIME_STYPE_ID = ? AND WT_TIME_RANGE_TYPE = ?", styleId, "1");
		}

		for (int i = 0; i < week.size(); i++) {
			JSONObject object = week.getJSONObject(i);
			EasyRecord time = new EasyRecord(getTableName("C_CF_WTIME_DETAIL"),"ID");
			time.set("ID", RandomKit.randomStr());
			time.set("WTIME_STYPE_ID", styleId);
			time.set("WT_TIME_RANGE_TYPE", 1);
			time.set("BUSI_ID", object.getInteger("BUSI_ID"));
			time.set("ENABLE_STATUS", object.getString("ENABLE_STATUS"));
			time.set("TIME_CODE", object.getString("TIME_CODE"));
			time.set("BEGIN_TIME", object.getString("BEGIN_TIME"));
			time.set("END_TIME", object.getString("END_TIME"));
			time.set("SORT_NUM", object.getString("SORT_NUM"));
			time.set("WORK_TYPE", object.getString("WORK_TYPE"));
			time.set("BUSI_ORDER_ID", busiOrderId);
			time.set("ENT_ID",entId);
			time.set("CREATE_ACC", UserUtil.getRequestUserAcc(getRequest()));
			time.set("CREATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
			this.getQuery().save(time);
		}
		return styleId;
	}


}
