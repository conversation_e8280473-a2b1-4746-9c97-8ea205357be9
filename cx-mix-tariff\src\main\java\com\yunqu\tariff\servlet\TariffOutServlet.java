package com.yunqu.tariff.servlet;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.tariff.base.AppBaseServlet;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.inf.TariffInfService;
import com.yunqu.tariff.model.RespResult;
import com.yunqu.tariff.utils.Sm4Util;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.crypt.HashKit;
import org.slf4j.Logger;

import javax.servlet.annotation.WebServlet;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

@WebServlet("/out/tariff/*")
public class TariffOutServlet  extends AppBaseServlet {

	private static final long serialVersionUID = 1L;

    private Logger logger = CommonLogger.getLogger();

	public JSONObject actionForHandle() {
		try {
			String requestBodyDataStr = getRequestBodyDataStr();
			if (StringUtils.isBlank(requestBodyDataStr)) {
				return RespResult.error("401", "请求内容为空");
			}
			JSONObject param = JSON.parseObject(requestBodyDataStr);
			TariffInfService tariffInfService = new TariffInfService();
			return tariffInfService.invoke(param);
		} catch (Exception e) {
			logger.error(e.getMessage());
			return RespResult.error();
		}
	}

	public JSONObject actionForBuildTestData() throws Exception {
		if (DictConstants.DICT_SY_YN_N.equals(Constants.getEnableTestdataInf())) {
			return EasyResult.error(500, "当前模式不支持构建测试数据");
		}
		String timestamp = DateUtil.getCurrentDateStr();
		// String clientId = "ct_JT";
		String data = getRequestBodyDataStr();
		JSONObject param = JSON.parseObject(data);
		String clientId = param.getString("clientId");
		if(StringUtils.isBlank(clientId)){
			clientId = getClientIdByReporter(param.getString("reporter"));
		}
		param.remove("clientId");
		JSONObject systemReg = CacheUtil.getSystemRegCache().getCache(Constants.getEntId(), null, clientId);
		String password = "";
		if(systemReg == null) {
			password=param.getString("password");
			param.remove("password");
		}else{
			password = systemReg.getString("PASSWORD");
		}
		param.remove("password");
		// logger.info("password"+password);
		data = Sm4Util.encryptEcb(password, param.toJSONString());
		String sign = "timestamp"+ timestamp +"data"+ data +"client_id"+ clientId + password;
		sign = HashKit.sha256(sign);

		JSONObject result = new JSONObject();
		result.put("timestamp", timestamp);
		result.put("client_id", clientId);
		result.put("data", data);
		result.put("sign", sign);
		return result;
	}

	private String getClientIdByReporter(String reporter) throws SQLException {
		String sql = "SELECT SENDER FROM "+Constants.getBusiSchema()+".c_cf_system_reg WHERE REPORTER=?";
		return QueryFactory.getWriteQuery().queryForString(sql, new Object[]{reporter});
	}

	public JSONObject actionForBuildSysCache() throws Exception {
		if (DictConstants.DICT_SY_YN_N.equals(Constants.getEnableTestdataInf())) {
			return EasyResult.error(500, "当前模式不支持构建测试数据");
		}
		EasySQL sql = new EasySQL("SELECT * FROM "+Constants.getBusiSchema()+".c_cf_system_reg");
		List<JSONObject> queryForList = QueryFactory.getReadQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		for (JSONObject jsonObject : queryForList) {
			String sender = jsonObject.getString("SENDER");
			String epCode = jsonObject.getString("EP_CODE");
			String busiOrderId = jsonObject.getString("BUSI_ORDER_ID");
			CacheUtil.getSystemRegCache().reloadCache(epCode, busiOrderId, sender);
		}
		return EasyResult.ok();
	}

}
