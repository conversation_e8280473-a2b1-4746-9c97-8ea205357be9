<template>
  <el-form
    class="tariff-detail"
    :model="configForm"
    ref="configForm"
    label-width="160px"
  >
    <div class="baseflex mr-b16">
      <div class="title">基本信息</div>
      <el-divider></el-divider>
    </div>
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item :label="getI18nValue('资费名称')" prop="name">
          <el-input v-model="configForm.name" readonly></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item :label="getI18nValue('方案编号')" prop="tariffNo">
          <el-input v-model="configForm.tariffNo" readonly></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :label="getI18nValue('运营商')" prop="entName">
          <el-input v-model="configForm.entName" readonly></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :label="getI18nValue('省份')" prop="provinceName">
          <el-input v-model="configForm.provinceName" readonly></el-input>
        </el-form-item>
      </el-col>
   <el-col :span="12">
     <el-form-item :label="getI18nValue('资费来源')" prop="tariffAttrType">
       <el-input :value="configForm.tariffAttrType === '0' ? '全国' : '省内'" readonly></el-input>
     </el-form-item>
   </el-col>

      <el-col :span="12">
        <el-form-item :label="getI18nValue('资费分类')" prop="VERSION_NUM">
          <el-input v-model="configForm.tariffType" readonly></el-input>
        </el-form-item>
      </el-col>
    </el-row>

    <div class="baseflex mr-b16">
      <div class="title">资费详情</div>
      <el-divider></el-divider>
    </div>
    <el-row>
      <el-col :span="12">
        <el-form-item :label="getI18nValue('资费标准')">
          <el-col :span="15">
            <el-form-item prop="fees">
              <el-input v-model="configForm.fees" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-left: 8px">
            <el-form-item prop="feesUnit">
              <el-input v-model="configForm.feesUnit" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
      </el-col>
    </el-row>

     <el-row>
          <el-col :span="24">
            <el-form-item
              :label="getI18nValue('超出资费')"
              prop="EXTRA_FEES">
              <el-input
                v-model="configForm.EXTRA_FEES"
                type="textarea"
                autosize
                readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item
              :label="getI18nValue('其他费用')"
              prop="OTHER_FEES">
              <el-input
                v-model="configForm.OTHER_FEES"
                type="textarea"
                autosize
                readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>

    <el-descriptions
      class="vertical"
      direction="vertical"
      :column="4"
      border
      style="margin-left: 96px; margin-bottom: 16px"
    >
      <el-descriptions-item :label="getI18nValue('语音/分钟')">
        <el-form-item prop="call" label-width="0">
          <el-input v-model="configForm.call" readonly></el-input>
        </el-form-item>
      </el-descriptions-item>
      <el-descriptions-item :label="getI18nValue('短信/条')">
        <el-form-item prop="sms" label-width="0">
          <el-input v-model="configForm.sms" readonly></el-input>
        </el-form-item>
      </el-descriptions-item>
      <el-descriptions-item :label="getI18nValue('通用流量')">
        <el-form-item label-width="0">
          <el-col :span="15">
            <el-form-item prop="data">
              <el-input v-model="configForm.data" readonly></el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col
            style="text-align: center"
            :span="2"
            >/</el-col
          > -->
          <el-col :span="8" style="margin-left: 8px">
            <el-form-item prop="dataUnit">
              <el-input v-model="configForm.dataUnit" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
      </el-descriptions-item>
      <el-descriptions-item :label="getI18nValue('定向流量')">
        <el-form-item label-width="0">
          <el-col :span="15">
            <el-form-item prop="orientTraffic">
              <el-input v-model="configForm.orientTraffic" readonly></el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col
            style="text-align: center"
            :span="2"
            >/</el-col
          > -->
          <el-col :span="8" style="margin-left: 8px">
            <el-form-item prop="orientTrafficUnit">
              <el-input
                v-model="configForm.orientTrafficUnit"
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions
      class="horizontal"
      :column="1"
      border
      style="margin-left: 96px; margin-bottom: 16px"
    >
      <el-descriptions-item :label="getI18nValue('IPTV')">
        <el-form-item prop="IPTV" label-width="0">
          <el-input
            v-model="configForm.iptv"
            type="textarea"
            autosize
            readonly
          ></el-input>
        </el-form-item>
      </el-descriptions-item>
      <el-descriptions-item :label="getI18nValue('带宽')">
        <el-form-item prop="bandwidth" label-width="0">
          <el-input
            v-model="configForm.bandwidth"
            type="textarea"
            autosize
            readonly
          ></el-input>
        </el-form-item>
      </el-descriptions-item>
      <el-descriptions-item :label="getI18nValue('权益')">
        <el-form-item prop="rights" label-width="0">
          <el-input
            v-model="configForm.rights"
            type="textarea"
            autosize
            readonly
          ></el-input>
        </el-form-item>
      </el-descriptions-item>
      <el-descriptions-item :label="getI18nValue('其他服务内容')">
        <el-form-item prop="otherContent" label-width="0">
          <el-input
            v-model="configForm.otherContent"
            type="textarea"
            autosize
            readonly
          ></el-input>
        </el-form-item>
      </el-descriptions-item>

    </el-descriptions>
    <div class="baseflex mr-b16">
      <div class="title">其他信息</div>
      <el-divider></el-divider>
    </div>
    <el-row>
      <el-col :span="24">
        <el-form-item
          :label="getI18nValue('适用范围')"
          prop="applicablePeople"
        >
          <el-input
            v-model="configForm.applicablePeople"
            type="textarea"
            autosize
            readonly
          ></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item :label="getI18nValue('适用地区')">
          <el-input
            v-model="configForm.area"
            type="textarea"
            autosize
            readonly
          ></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item :label="getI18nValue('有效期限')" prop="validPeriod">
          <el-input
            v-model="configForm.validPeriod"
            type="textarea"
            autosize
            readonly
          ></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item :label="getI18nValue('销售渠道')" prop="channel">
          <el-input
            v-model="configForm.channel"
            type="textarea"
            autosize
            readonly
          ></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item :label="getI18nValue('在网要求')" prop="duration">
          <el-input
            v-model="configForm.duration"
            type="textarea"
            autosize
            readonly
          ></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item :label="getI18nValue('退订方式')" prop="unsubscribe">
          <el-input
            v-model="configForm.unsubscribe"
            type="textarea"
            autosize
            readonly
          ></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item :label="getI18nValue('违约责任')" prop="responsibility">
          <el-input
            v-model="configForm.responsibility"
            type="textarea"
            autosize
            readonly
          ></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item :label="getI18nValue('其他事项')" prop="others">
          <el-input
            v-model="configForm.others"
            type="textarea"
            autosize
            readonly
          ></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item :label="getI18nValue('上线日期')" prop="onlineDay">
          <el-date-picker
            v-model="configForm.onlineDay"
            type="date"
            :clearable="false"
            format="yyyy年MM月dd日"
            value-format="yyyyMMdd"
            style="width: 100%"
            readonly
          >
          </el-date-picker>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :label="getI18nValue('下线日期')" prop="offlineDay">
          <el-date-picker
            v-model="configForm.offlineDay"
            type="date"
            clearable
            format="yyyy年MM月dd日"
            value-format="yyyyMMdd"
            style="width: 100%"
            readonly
          >
          </el-date-picker>
        </el-form-item>
      </el-col>
    </el-row>
     <el-row>
    <el-col :span="24">
                  <el-form-item :label="getI18nValue('出现版本')" prop="versionNos">
                    <el-input v-model="configForm.versionNos" readonly></el-input>
                  </el-form-item>
                </el-col>
                 </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item :label="getI18nValue('最新版本号')" prop="versionNo">
          <el-input :value="latestVersionNo" readonly></el-input>
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item :label="getI18nValue('入库时间')" prop="createTime">
          <el-input v-model="configForm.createTime" readonly></el-input>
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 只有检查页面显示检查结果 -->
    <template v-if="model === 'check'">
      <div class="baseflex mr-b16">
        <div class="title red-text">检查结果</div>
        <el-divider></el-divider>
      </div>
      <el-row>
        <el-col :span="24">
          <el-form-item
            class="red-label"
            :label="getI18nValue('字段检查结果')"
            prop="FIELD_CHECK_RESULT">
            <el-input
              v-model="configForm.fieldCheckResult"
              type="textarea"
              autosize
              readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            class="red-label"
            :label="getI18nValue('字段检查时间')"
            prop="FIELD_CHECK_TIME">
            <el-input
              v-model="configForm.fieldCheckTime"
              type="textarea"
              autosize
              readonly></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </template>

  </el-form>
</template>

<script>
module.exports = {
  components: {},
  props: {
    configForm: {
      type: Object,
      default: () => ({}),
    },
    model: {
      type: String,
      default: '',
    }
  },
  data() {
    return {};
  },
  computed: {

  latestVersionNo() {
      const versions = this.configForm.versionNos || [];
      if (!versions.length) return '';
      return versions.sort().slice(-1)[0]; // 取最后一个（最大值）
    }
    // areaDesc() {
    //   const map = {
    //     2: "待下架",
    //     3: "下架",
    //   };
    //   if (
    //     (this.configForm.AREA_SELECT_TYPE === "2" ||
    //       this.configForm.AREA_SELECT_TYPE === "指定省市") &&
    //     this.configForm.areaTree.some((i) => (i && i[3]) !== "1")
    //   ) {
    //     return `${this.configForm.PROVINCE_NAME}：${this.configForm.areaTree
    //       .map((i) => ((i && i[3]) === "1" ? i[2] : `${i[2]}（${map[i[3]]}）`))
    //       .join("，")}`;
    //   }
    //   return this.configForm.AREA_DESC;
    // },
  },
  methods: {},
};
</script>

<style>
.baseflex .title {
  white-space: nowrap;
}

/* el-descriptions样式覆盖 */
.tariff-detail .el-descriptions-item__label.is-bordered-label {
  background: rgba(5, 85, 206, 0.05);
  color: #262626;
  text-align: center;
}

.tariff-detail .el-descriptions .is-bordered .el-descriptions-item__cell {
  padding-bottom: 0;
}

.tariff-detail .el-descriptions.vertical th {
  width: 5%;
}

.tariff-detail .el-descriptions.horizontal th {
  width: 24.5%;
}

.red-text {
  color: red;
}

.red-label .el-form-item__label {
  color: red !important;
}
</style>
