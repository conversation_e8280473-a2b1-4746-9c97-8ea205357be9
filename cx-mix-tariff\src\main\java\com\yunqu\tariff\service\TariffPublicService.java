package com.yunqu.tariff.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.container.XtyTariffContainer;
import com.yunqu.tariff.handler.SearchHandler;
import com.yunqu.tariff.handler.SearchHandlerFactory;
import com.yunqu.tariff.utils.BusiUtil;
import com.yunqu.xty.commonex.kit.ElasticsearchKit;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.Map;
import java.util.HashMap;

/**
 * 资费备份初始化服务
 */
public class TariffPublicService {

    private static final Logger logger = LoggerFactory.getLogger(CommonLogger.getPublicLogger().getName());
    private static final String[] ENT_TYPE_ARR = {"1", "2", "3", "5"};


    private static final class Holder {
        private static final TariffPublicService INSTANCE = new TariffPublicService();
    }

    public static TariffPublicService getInstance() {
        return TariffPublicService.Holder.INSTANCE;
    }



    /**
     * <AUTHOR>
     * @Description 更新日报公示率数据
     * @Param: [dataJson, provinceCode, entCode, auditDate]
     * &#064;Return:  void
     * @Since create in 2025/7/1 17:07
     * @Company 广州云趣信息科技有限公司
     */
    public void syncDayPublicInfo(JSONObject dataJson, String provinceCode, String entCode, String auditDate,String fileName,String tariffProvinceCode) throws SQLException {
       logger.info("开始同步" + tariffProvinceCode + "("+provinceCode+")的" + entCode + "的" + auditDate + "的日报公示率数据");
        EasyQuery query = QueryFactory.getWriteQuery();
        //稽核在售资费
        JSONArray reportedArr = dataJson.getJSONArray("reported");
        //稽核续订资费
        JSONArray offlinedreportedArr = dataJson.getJSONArray("offlined");
        //稽核未报送资费
        JSONArray unreportedArr = dataJson.getJSONArray("unreported");
        auditDate = auditDate.replace("-", "");
        String monthId = auditDate.substring(0, 6);

        logger.info("稽核在售资费数量：{},稽核续订资费数量：{},稽核未报送资费数量：{}", reportedArr.size(),offlinedreportedArr.size(),unreportedArr.size());
        //稽核在售&公众类资费
        JSONArray reportedList = new JSONArray();
        //稽核续订&公众类资费
        JSONArray offlinedList = new JSONArray();
        String provinceName = XtyTariffContainer.getProvinceName(provinceCode);
        String entName = BusiUtil.getEntByEntCode(entCode);

        for (int i = 0; i < reportedArr.size(); i++) {
            JSONObject reportedObj = reportedArr.getJSONObject(i);
            String reportNo = reportedObj.getString("report_no");
            String type1 = reportedObj.getString("type1");
            if(StringUtils.isBlank(reportNo)){
                continue;
            }
            if("1".equals(type1)){
                reportedList.add(reportedObj);
            }
//            //不是公众类的资费就略过
//            if(!checkType1(reportNo)){
//                continue;
//            }

        }
        logger.info("稽核在售&公众类资费数量：{}", reportedList.size());
        for (int i = 0; i < offlinedreportedArr.size(); i++) {
            JSONObject offlinedObj = offlinedreportedArr.getJSONObject(i);
            String reportNo = offlinedObj.getString("report_no");
            String type1 = offlinedObj.getString("type1");
            if(StringUtils.isBlank(reportNo)){
                continue;
            }
            if("1".equals(type1)){
                offlinedList.add(offlinedObj);
            }
            //不是公众类的资费就略过
//            if(!checkType1(reportNo)){
//                continue;
//            }
        }

        logger.info("稽核续订&公众类资费数量：{}", offlinedList.size());
        //用户订购总数 = 稽核在售&公众类资费+稽核续订&公众类资费+稽核未报送资费
        int allNum = reportedList.size() + offlinedList.size()+unreportedArr.size();
        logger.info("日报用户订购总数：{}", allNum);
        // 使用ES查询获取实际公示数量

        logger.info("日报开始从活跃资费列表和未报送资费列表异常编码中查询公示数量...");
        // 创建一个存储未公示资费的列表
        List<JSONObject> unpublicTariffs = new ArrayList<>();
        
        // 修改：将unpublicTariffs传入方法，收集未公示资费
        int publicNum = getDayPublicNumFromActiveTariffs(reportedList, unreportedArr, provinceCode, entCode, auditDate, unpublicTariffs);
        int unpublicNum = unpublicTariffs.size();
        logger.info("日报活跃在售+未报送 公示资费数量：{},未公示资费数量:{}", publicNum,unpublicNum);

        // 检查是否已存在该省份+运营商+月份的记录
        EasySQL checkSql = new EasySQL("select ID from "+Constants.getBusiSchema()+".xty_tariff_public_day where 1=1 ");
        checkSql.append(provinceCode,"and PROVINCE_CODE = ?");
        checkSql.append(tariffProvinceCode,"and TARIFF_PROVINCE_CODE = ?");
        checkSql.append(entCode,"and ENT = ? ");
        checkSql.append(auditDate,"and DATE_ID = ?");
        logger.info("检查日公示率记录SQL：{}，参数：{}", checkSql.getSQL(), JSON.toJSONString(checkSql.getParams()));
        String existingId = query.queryForString(checkSql.getSQL(), checkSql.getParams());
        logger.info("查询到记录ID：{}", existingId);
        String currentTime = EasyDate.getCurrentTimeStampString();
        if (StringUtils.isNotBlank(existingId)) {
            EasyRecord record = new EasyRecord(Constants.getBusiSchema()+".xty_tariff_public_day", "ID");
            record.setPrimaryValues(existingId)
                    .set("PUBLIC_NUM", publicNum)
                    .set("UN_PUBLIC_NUM", unpublicNum)
                    .set("OFFLINE_NUM", offlinedList.size())
                    .set("ACTIVE_NUM", allNum)
                    .set("UPDATE_TIME", currentTime);
            query.update(record);
            // 更新现有记录
            logger.info("更新记录record={}", record);
            logger.info("更新日报公示率数据：省份={}, 运营商={}, 日期={}", provinceCode, entCode, auditDate);
        } else {
            // 插入新记录
            String id = RandomKit.uniqueStr();
            EasyRecord record = new EasyRecord(Constants.getBusiSchema()+".xty_tariff_public_day", "ID");
            record.setPrimaryValues(id)
                    .set("PROVINCE_CODE", provinceCode)
                    .set("TARIFF_PROVINCE_CODE", tariffProvinceCode)
                    .set("ENT", entCode)
                    .set("PROVINCE_NAME", provinceName)
                    .set("ENT_NAME", entName)
                    .set("DATE_ID", auditDate)
                    .set("MONTH_ID", monthId)
                    .set("PUBLIC_NUM", publicNum)
                    .set("UN_PUBLIC_NUM", unpublicNum)
                    .set("OFFLINE_NUM", offlinedList.size())
                    .set("ACTIVE_NUM", allNum)
                    .set("CREATE_TIME", currentTime)
                    .set("UPDATE_TIME", currentTime);
            query.save(record);
            logger.info("新增记录record：{}", record.toJSONString());
            logger.info("新增日报公示率数据：省份={}, 运营商={}, 日期={}", provinceCode, entCode, auditDate);
        }
        logger.info("未公示资费数据量："+unpublicTariffs.size());
        // 将未公示资费数据存入ES
        //为空也要跑ES 删除旧数据
        // 恢复auditDate的格式为yyyy-MM-dd
        String formattedAuditDate = auditDate.substring(0, 4) + "-" +
                                   auditDate.substring(4, 6) + "-" +
                                   auditDate.substring(6, 8);
        // 调用处理未公示资费的方法
        processUnpublicTariffs(unpublicTariffs, formattedAuditDate, fileName);
    }

    /**
     * 从活跃资费列表中统计在公示库中出现的数量，并收集未公示的资费
     * @param reportedList 活跃资费列表
     * @param unreportedList 未报送资费列表
     * @param provinceCode 省份编码
     * @param entType 运营商类型
     * @param auditDate 稽核日期
     * @param unpublicTariffs 未公示资费列表，用于收集未公示的资费
     * @return 公示数量
     */
    private int getDayPublicNumFromActiveTariffs(JSONArray reportedList, JSONArray unreportedList, 
                                                String provinceCode, String entType, String auditDate, 
                                                List<JSONObject> unpublicTariffs) {
        int publicCount = 0;
        try {
            if ( (reportedList == null || reportedList.isEmpty()) &&
                    (unreportedList==null || unreportedList.isEmpty()) ) {
                logger.error("日报活跃和未报送资费列表为空");
                return 0;
            }

            // 根据稽核日期确定应该使用的公示库版本
            String dateVersion = determinePublicDateId(auditDate);
            logger.info("稽核日期 {} 对应的公示库日期版本为: {}", auditDate, dateVersion);

            // 收集所有活跃资费的reportNo
            Set<String> reportNos = new HashSet<>();
            Map<String, JSONObject> reportNoToTariffMap = new HashMap<>(); // 用于快速查找资费对象
            
            for (int i = 0; i < reportedList.size(); i++) {
                JSONObject tariff = reportedList.getJSONObject(i);
                String reportNo = tariff.getString("report_no");
                if (StringUtils.isNotBlank(reportNo)) {
                    reportNos.add(reportNo);
                    reportNoToTariffMap.put(reportNo, tariff);
                }
            }

            for (int i = 0; i < unreportedList.size(); i++) {
                JSONObject tariff = unreportedList.getJSONObject(i);
                String reportNo = tariff.getString("error_report_no");
                if (StringUtils.isNotBlank(reportNo)) {
                    reportNos.add(reportNo);
                    reportNoToTariffMap.put(reportNo, tariff);
                } else {
                    // 对于没有error_report_no的未报送资费，直接添加到未公示列表
                    tariff.put("type", "5"); // 未报送
                    unpublicTariffs.add(tariff);
                }
            }

            if (reportNos.isEmpty() && unpublicTariffs.isEmpty()) {
                logger.error("日报reportNos为空且没有未报送空编码资费");
                return 0;
            }

            // 如果有需要查询的报告编号，则继续查询公示库
            if (!reportNos.isEmpty()) {
                logger.info("需要查询的资费report_no数量: {}", reportNos.size());

                // 构建ES查询，从公示库中查询特定省份、运营商和日期的所有资费
                JSONObject queryParams = new JSONObject();
                queryParams.put("size", 1000000); // 获取较大数量的数据，确保能获取所有记录

                // 构建查询条件
                JSONObject query = new JSONObject();
                JSONObject bool = new JSONObject();
                JSONArray must = new JSONArray();

                // 添加日期条件
                JSONObject dateTerms = new JSONObject();
                JSONObject dateFilter = new JSONObject();
                dateFilter.put("version_nos.keyword", new JSONArray().fluentAdd(dateVersion));
                dateTerms.put("terms", dateFilter);
                must.add(dateTerms);

                // 添加省份条件
                if (StringUtils.isNotBlank(provinceCode)) {
                    JSONObject provinceTerm = new JSONObject();
                    provinceTerm.put("term", new JSONObject().fluentPut("province_code.keyword", provinceCode));
                    must.add(provinceTerm);
                }

                // 添加运营商条件
                if (StringUtils.isNotBlank(entType)) {
                    JSONObject entTerm = new JSONObject();
                    entTerm.put("term", new JSONObject().fluentPut("ent_code.keyword", entType));
                    must.add(entTerm);
                }

                bool.put("must", must);
                query.put("bool", bool);
                queryParams.put("query", query);

                // 只获取tariff_no字段
                JSONArray sourceArray = new JSONArray();
                sourceArray.add("tariff_no");
                queryParams.put("_source", sourceArray);

                // 执行ES查询
                logger.info("查询公示库资费数据，参数: {}", queryParams.toJSONString());
                JSONObject esResult = ElasticsearchKit.search(Constants.XTY_TARIFF_PUBLIC_LIB_INDEX, queryParams);

                // 处理查询结果，获取公示库中的资费编号列表
                Set<String> publicTariffNos = new HashSet<>();
                if (esResult != null && esResult.containsKey("hits")) {
                    JSONObject hits = esResult.getJSONObject("hits");
                    JSONArray hitsArray = hits.getJSONArray("hits");

                    for (int i = 0; i < hitsArray.size(); i++) {
                        JSONObject hit = hitsArray.getJSONObject(i);
                        JSONObject source = hit.getJSONObject("_source");
                        String tariffNo = source.getString("tariff_no");
                        if (StringUtils.isNotBlank(tariffNo)) {
                            publicTariffNos.add(tariffNo);
                        }
                    }
                }

                logger.info("从公示库中获取到 {} 条资费记录", publicTariffNos.size());

                // 统计reportedList中有多少条report_no存在于公示库中
                // 同时收集未公示的资费
                for (String reportNo : reportNos) {
                    if (publicTariffNos.contains(reportNo)) {
                        publicCount++;
                    } else {
                        // 收集未公示的资费
                        JSONObject tariff = reportNoToTariffMap.get(reportNo);
                        if (tariff != null) {
                            // 标记资费类型
                            if (reportedList.contains(tariff)) {
                                tariff.put("type", "1"); // 活跃在售
                            } else {
                                tariff.put("type", "5"); // 未报送
                            }
                            unpublicTariffs.add(tariff);
                        }
                    }
                }
            }

            logger.info("在 {} 条reportNo中，有 {} 条存在于公示库中，有 {} 条未公示", 
                        reportNos.size(), publicCount, unpublicTariffs.size());

        } catch (Exception e) {
            logger.error("查询日公示数据失败：省份={}, 运营商={}, 日期={}", provinceCode, entType, auditDate, e);
        }
        return publicCount;
    }
    
    /**
     * 处理未公示资费数据并保存到ES
     * 包括三种类型的未公示资费：
     * 1. 活跃在售公众类非公示（没有匹配到公示库）
     * 2. 未报送资费（异常编码为空）
     * 3. 未报送资费（异常编码未匹配到公示库）
     *
     * @param unpublicTariffs 未公示资费列表
     * @param auditDate 稽核日期（格式：yyyy-MM-dd）
     * @param fileName 文件名
     */
    private void processUnpublicTariffs(List<JSONObject> unpublicTariffs, String auditDate, String fileName) {
        try {
            logger.info("开始处理 {} 条未公示资费数据，保存到ES索引", unpublicTariffs.size());
            
            // 调用TariffUnpublicAuditProcessStorageService处理未公示资费
            TariffUnpublicAuditProcessStorageService.getInstance().execute(unpublicTariffs, fileName, auditDate);
            
            logger.info("未公示资费数据处理完成，已保存到ES索引");
        } catch (Exception e) {
            logger.error("处理未公示资费数据失败: {}", e.getMessage(), e);
        }
    }
    /**
     * 根据稽核日期确定应该使用的公示库版本日期
     * 规则：1-10号使用当月1号版本，11-20号使用当月11号版本，21号及以后使用当月21号版本
     * @param auditDate 稽核日期，格式为"yyyyMMdd"
     * @return 对应的公示库日期，格式为"yyyyMMdd"
     */
    private String determinePublicDateId(String auditDate) {
        if (StringUtils.isBlank(auditDate)) {
            logger.error("稽核日期为空，无法确定公示库版本");
            return auditDate;
        }

        try {
            // 确保auditDate格式为yyyyMMdd
            if (auditDate.contains("-")) {
                auditDate = auditDate.replace("-", "");
            }

            // 格式化为yyyyMMdd
            EasySQL sql = new EasySQL("select version_no from "+Constants.getBusiSchema()+".xty_crawler_version");
            sql.append("where 1=1");
            sql.append(auditDate,"and belong_date_id <= ?",false);
            sql.append(Constants.CRAWLER_VERSION_STATUS_1,"and version_status = ?");
            sql.append("order by belong_date_id desc limit 1");
            return QueryFactory.getTariffQuery().queryForString(sql.getSQL(), sql.getParams());
        } catch (Exception e) {
            logger.error("解析稽核日期失败: {}", auditDate, e);
            return auditDate;
        }
    }

    /**
     * 公示库打标或者月初清理公示标签 更新ES索引中资费的IS_PUBLIC字段
     */
    public void updateBakEsIsPublic(String reportNo, String recordId, String isPublic, String publicVersions, String publicMonths) {
        try {
            if (StringUtils.isBlank(reportNo)) {
                return;
            }
            if(StringUtils.isBlank(isPublic)){
                isPublic = "N";
            }
            // 2. 更新ES索引
            IService service = ServiceContext.getService("XTY_EVT_ES_ORDER_OPERATE");
            JSONObject updateData = new JSONObject();
            updateData.put("IS_PUBLIC", isPublic);

            if("Y".equals(isPublic)){
                try {
                    // 先查询ES获取已有的数据
                    JSONObject termQuery = new JSONObject();
                    termQuery.put("term", new JSONObject().fluentPut("ID.keyword", recordId));

                    // 构建查询参数
                    JSONObject queryParams = new JSONObject();
                    queryParams.put("query", termQuery);

                    List<String> existingMonth = new ArrayList<>();
                    List<String> existingVersions = new ArrayList<>();
                    // 使用 ElasticsearchKit.search 执行查询
                    JSONObject esResult = ElasticsearchKit.search(Constants.XTY_TARIFF_BAK_INFO_INDEX, queryParams);
                    // 检查查询结果
                    if (esResult != null && !esResult.containsKey("error")) {
                        // 提取命中的文档
                        JSONObject hits = esResult.getJSONObject("hits");
                        if (hits != null && hits.getJSONArray("hits") != null && hits.getJSONArray("hits").size() > 0) {
                            // 获取第一个匹配的文档
                            JSONObject hit = hits.getJSONArray("hits").getJSONObject(0);
                            JSONObject existingDoc = hit.getJSONObject("_source");
                            if (existingDoc != null) {
                                // 获取已有的出现月份
                                Object existingMonths = existingDoc.get("PUBLIC_MONTHS");
                                if (existingMonths instanceof List) {
                                    existingMonth.addAll((List<String>) existingMonths);
                                }

                                // 获取已有的版本号数组
                                Object existingVersionNos = existingDoc.get("PUBLIC_VERSIONS");
                                if (existingVersionNos instanceof List) {
                                    existingVersions.addAll((List<String>) existingVersionNos);
                                }
                            }
                        }
                    }
                    // 添加新的月份，确保不重复
                    if (!existingMonth.contains(publicMonths)) {
                        existingMonth.add(publicMonths);
                    }
                    // 添加新的月份，确保不重复
                    if (!existingVersions.contains(publicVersions)) {
                        existingVersions.add(publicVersions);
                    }

                    // 更新ES数据，使用合并后的数组
                    updateData.put("PUBLIC_MONTHS", existingMonth);
                    updateData.put("PUBLIC_VERSIONS", existingVersions);

                } catch (Exception e) {
                    logger.error("获取并合并公示记录数据失败，reportNo: {}", reportNo, e);
                    // 如果查询失败，至少保存传入的新数据
                    if (publicMonths != null && !publicMonths.isEmpty()) {
                        updateData.put("PUBLIC_MONTHS", publicMonths);
                    }
                    if (publicVersions != null && !publicVersions.isEmpty()) {
                        updateData.put("PUBLIC_VERSIONS", publicVersions);
                    }
                }
            }

            JSONObject reqParam = new JSONObject();
            reqParam.put("primary", recordId);
            reqParam.put("indexName", Constants.XTY_TARIFF_BAK_INFO_INDEX);
            reqParam.put("command", Constants.ES_OPERATE_UPDATE_DOC);
            reqParam.put("data", updateData);

            JSONObject result = service.invoke(reqParam);

            new Thread(() -> {
                try {
                    // 等待5秒确保ES数据同步
                    Thread.sleep(5000);
                    TariffCheckService checkService = new TariffCheckService();
                    checkService.checkTariffFields(null, null, true, recordId, null, 1);
                } catch (Exception e) {
                    logger.error("异步执行核查服务失败", e);
                }
            }).start();

        } catch (Exception e) {
            logger.error("更新ES索引IS_PUBLIC字段失败，reportNo: {}", reportNo, e);
        }
    }


    /**
     * 公示库打标或者月初清理公示标签更新ES索引中资费的IS_PUBLIC字段
     */
    public void updateStorageEsIsPublic(String reportNo, String serialId, String isPublic, String publicVersions, String publicMonths) {
        try {
            if (StringUtils.isBlank(reportNo)) {
                return;
            }
            if(StringUtils.isBlank(isPublic)){
                isPublic = "N";
            }
            // 2. 更新ES索引
            IService service = ServiceContext.getService("XTY_EVT_ES_ORDER_OPERATE");
            JSONObject updateData = new JSONObject();
            updateData.put("is_public", isPublic);
            if("Y".equals(isPublic)){
                try {
                    // 先查询ES获取已有的数据
                    JSONObject termQuery = new JSONObject();
                    termQuery.put("term", new JSONObject().fluentPut("serialId.keyword", serialId));

                    // 构建查询参数
                    JSONObject queryParams = new JSONObject();
                    queryParams.put("query", termQuery);

                    List<String> existingMonth = new ArrayList<>();
                    List<String> existingVersions = new ArrayList<>();
                    // 使用 ElasticsearchKit.search 执行查询
                    JSONObject esResult = ElasticsearchKit.search("xty_tariff_audit_process_storage", queryParams);
                    // 检查查询结果
                    if (esResult != null && !esResult.containsKey("error")) {
                        // 提取命中的文档
                        JSONObject hits = esResult.getJSONObject("hits");
                        if (hits != null && hits.getJSONArray("hits") != null && hits.getJSONArray("hits").size() > 0) {
                            // 获取第一个匹配的文档
                            JSONObject hit = hits.getJSONArray("hits").getJSONObject(0);
                            JSONObject existingDoc = hit.getJSONObject("_source");
                            if (existingDoc != null) {
                                // 获取已有的出现月份
                                Object existingMonths = existingDoc.get("public_months");
                                if (existingMonths instanceof List) {
                                    existingMonth.addAll((List<String>) existingMonths);
                                }

                                // 获取已有的版本号数组
                                Object existingVersionNos = existingDoc.get("public_versions");
                                if (existingVersionNos instanceof List) {
                                    existingVersions.addAll((List<String>) existingVersionNos);
                                }
                            }
                        }
                    }
                    // 添加新的月份，确保不重复
                    if (!existingMonth.contains(publicMonths)) {
                        existingMonth.add(publicMonths);
                    }
                    // 添加新的月份，确保不重复
                    if (!existingVersions.contains(publicVersions)) {
                        existingVersions.add(publicVersions);
                    }

                    // 更新ES数据，使用合并后的数组
                    updateData.put("public_months", existingMonth);
                    updateData.put("public_versions", existingVersions);

                } catch (Exception e) {
                    logger.error("获取并合并公示记录数据失败，reportNo: {}", reportNo, e);
                    // 如果查询失败，至少保存传入的新数据
                    if (publicMonths != null && !publicMonths.isEmpty()) {
                        List<String> tmp = new ArrayList<>();
                        tmp.add(publicMonths);
                        updateData.put("public_months", tmp);
                    }
                    if (publicVersions != null && !publicVersions.isEmpty()) {
                        List<String> tmp = new ArrayList<>();
                        tmp.add(publicVersions);
                        updateData.put("public_versions", tmp);
                    }
                }
            }

            JSONObject reqParam = new JSONObject();
            reqParam.put("primary", serialId);
            reqParam.put("indexName", "xty_tariff_audit_process_storage");
            reqParam.put("command", Constants.ES_OPERATE_UPDATE_DOC);
            reqParam.put("data", updateData);

            JSONObject result = service.invoke(reqParam);
        } catch (Exception e) {
            logger.error("更新ES索引is_public字段失败，reportNo: {}", reportNo, e);
        }
    }


}
