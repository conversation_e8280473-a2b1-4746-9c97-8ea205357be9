-- 爬虫版本生成配置表结构

-- 创建版本生成配置表
CREATE TABLE `xty_crawler_version_config` (
  `ID` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键ID',
  `CONFIG_NAME` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置名称',
  `CONFIG_TYPE` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'CUSTOM' COMMENT '配置类型：SYSTEM-系统配置，CUSTOM-自定义配置',
  `RULE_VALUE` varchar(10) COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行日期：每月的第几天（1-31）',
  `IS_ENABLED` varchar(1) COLLATE utf8mb4_general_ci DEFAULT 'Y' COMMENT '是否启用：Y-启用，N-禁用',
  `EFFECTIVE_TYPE` int DEFAULT NULL COMMENT '生效类型 1：长期有效 2：固定时限',
  `EFFECTIVE_START_DATE` date DEFAULT NULL COMMENT '生效开始日期，格式：yyyy-MM-dd',
  `EFFECTIVE_END_DATE` date DEFAULT NULL COMMENT '生效结束日期，格式：yyyy-MM-dd',
  `DESCRIPTION` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '配置描述',
  `CREATE_USER` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建用户',
  `CREATE_TIME` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建时间',
  `UPDATE_USER` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新用户',
  `UPDATE_TIME` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新时间',
  `REMARK` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`ID`),
  KEY `idx_config_type` (`CONFIG_TYPE`),
  KEY `idx_rule_value` (`RULE_VALUE`),
  KEY `idx_is_enabled` (`IS_ENABLED`),
  KEY `idx_priority_order` (`PRIORITY_ORDER`),
  KEY `idx_create_time` (`CREATE_TIME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='爬虫版本生成配置表';

-- 插入系统默认配置（每月1号、11号、21号）
INSERT INTO xty_crawler_version_config (
    ID, CONFIG_NAME, CONFIG_TYPE, RULE_VALUE,
    IS_ENABLED, PRIORITY_ORDER, DESCRIPTION,
    CREATE_USER, CREATE_TIME
) VALUES
(
    'SYS_CONFIG_001',
    '系统默认配置-每月1号',
    'SYSTEM',
    '1',
    'Y',
    1,
    '系统默认的版本生成配置，每月1号凌晨1点执行',
    'system',
    DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')
),
(
    'SYS_CONFIG_002',
    '系统默认配置-每月11号',
    'SYSTEM',
    '11',
    'Y',
    2,
    '系统默认的版本生成配置，每月11号凌晨1点执行',
    'system',
    DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')
),
(
    'SYS_CONFIG_003',
    '系统默认配置-每月21号',
    'SYSTEM',
    '21',
    'Y',
    3,
    '系统默认的版本生成配置，每月21号凌晨1点执行',
    'system',
    DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')
);

-- 创建版本生成执行记录表
CREATE TABLE xty_crawler_version_generate_log (
    ID VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    CONFIG_ID VARCHAR(64) COMMENT '配置ID',
    CONFIG_NAME VARCHAR(200) COMMENT '配置名称',
    EXECUTE_DATE VARCHAR(20) NOT NULL COMMENT '执行日期，格式：yyyy-MM-dd',
    EXECUTE_TIME VARCHAR(20) COMMENT '执行时间，格式：yyyy-MM-dd HH:mm:ss',
    EXECUTE_STATUS VARCHAR(20) DEFAULT 'PENDING' COMMENT '执行状态：PENDING-待执行，RUNNING-执行中，SUCCESS-成功，FAILED-失败，SKIPPED-跳过',
    TASK_COUNT INT DEFAULT 0 COMMENT '生成的任务数量',
    SUCCESS_COUNT INT DEFAULT 0 COMMENT '成功生成的任务数量',
    FAILED_COUNT INT DEFAULT 0 COMMENT '失败的任务数量',
    GENERATED_VERSION_IDS TEXT COMMENT '生成的版本ID列表，逗号分隔',
    ERROR_MESSAGE TEXT COMMENT '错误信息',
    START_TIME VARCHAR(20) COMMENT '开始执行时间',
    END_TIME VARCHAR(20) COMMENT '结束时间',
    DURATION_SECONDS INT DEFAULT 0 COMMENT '执行耗时（秒）',
    EXECUTE_RESULT TEXT COMMENT '执行结果详情（JSON格式）',
    CREATE_TIME VARCHAR(20) COMMENT '创建时间',
    INDEX idx_config_id (CONFIG_ID),
    INDEX idx_execute_date (EXECUTE_DATE),
    INDEX idx_execute_status (EXECUTE_STATUS),
    INDEX idx_create_time (CREATE_TIME)
) COMMENT '版本生成执行记录表';

-- 创建版本生成配置规则说明表（用于存储规则格式说明）
CREATE TABLE xty_crawler_version_config_rule_desc (
    ID VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    RULE_TYPE VARCHAR(20) NOT NULL COMMENT '规则类型',
    RULE_NAME VARCHAR(100) NOT NULL COMMENT '规则名称',
    RULE_FORMAT VARCHAR(500) NOT NULL COMMENT '规则格式说明',
    RULE_EXAMPLE TEXT COMMENT '规则示例',
    RULE_DESCRIPTION TEXT COMMENT '规则详细描述',
    IS_ACTIVE VARCHAR(1) DEFAULT 'Y' COMMENT '是否有效',
    CREATE_TIME VARCHAR(20) COMMENT '创建时间',
    INDEX idx_rule_type (RULE_TYPE)
) COMMENT '版本生成配置规则说明表';

-- 插入规则说明数据
INSERT INTO xty_crawler_version_config_rule_desc (
    ID, RULE_TYPE, RULE_NAME, RULE_FORMAT, RULE_EXAMPLE, RULE_DESCRIPTION, CREATE_TIME
) VALUES 
(
    'RULE_DESC_001',
    'MONTHLY_DAYS',
    '每月指定日期',
    '{"days":[日期数组],"time":"HH:mm:ss"}',
    '{"days":[1,11,21],"time":"01:00:00"}',
    '每月的指定日期执行，days数组包含1-31的日期，time指定执行时间',
    DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')
),
(
    'RULE_DESC_002',
    'WEEKLY_DAYS',
    '每周指定日期',
    '{"days":[星期数组],"time":"HH:mm:ss"}',
    '{"days":[1,3,5],"time":"02:00:00"}',
    '每周的指定日期执行，days数组包含1-7的星期（1=周一，7=周日），time指定执行时间',
    DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')
),
(
    'RULE_DESC_003',
    'CUSTOM_DATES',
    '自定义日期列表',
    '{"dates":["yyyy-MM-dd"],"time":"HH:mm:ss"}',
    '{"dates":["2025-02-01","2025-02-15"],"time":"01:30:00"}',
    '指定具体的日期列表执行，dates数组包含具体日期，time指定执行时间',
    DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')
),
(
    'RULE_DESC_004',
    'FIXED_DAYS',
    '固定间隔日期',
    '{"interval":间隔天数,"startDate":"yyyy-MM-dd","time":"HH:mm:ss"}',
    '{"interval":10,"startDate":"2025-01-01","time":"01:00:00"}',
    '从指定开始日期按固定间隔执行，interval为间隔天数，startDate为开始日期',
    DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')
);
