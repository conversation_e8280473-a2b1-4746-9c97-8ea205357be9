package com.yunqu.tariff.service;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.xty.commonex.util.RedissonUtil;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import com.yunqu.tariff.container.XtyTariffContainer;
import com.yunqu.tariff.executor.EventDispatcher;
import com.yunqu.tariff.model.XtyTariffEventData;
import com.yunqu.tariff.utils.BusiUtil;
import com.yunqu.xty.commonex.kit.ElasticsearchKit;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 未公示资费审计处理存储服务类
 * 负责处理和存储未公示资费审计相关数据，包括:
 * - 未公示资费审计数据的处理和存储
 * - ES文档的增删改查
 * - 未公示资费记录的初始化和更新
 */
public class TariffUnpublicAuditProcessStorageService {

    // 日志记录器
    private static final Logger log = LoggerFactory.getLogger(CommonLogger.getJobLogger().getName());

    // ES索引名称常量
    private static final String UNPUBLIC_TARIFF_AUDIT_ES_INDEX = "xty_tariff_unpublic_audit_process_storage";

    /**
     * 私有构造函数，防止实例化
     */
    private TariffUnpublicAuditProcessStorageService() {
        // Private constructor to prevent instantiation
    }

    /**
     * 静态内部类，用于实现单例模式
     */
    private static final class Holder {
        private static final TariffUnpublicAuditProcessStorageService INSTANCE = new TariffUnpublicAuditProcessStorageService();
    }

    /**
     * 获取TariffUnpublicAuditProcessStorageService的单例实例
     *
     * @return TariffUnpublicAuditProcessStorageService实例
     */
    public static TariffUnpublicAuditProcessStorageService getInstance() {
        return Holder.INSTANCE;
    }

    /**
     * 执行未公示资费审计数据处理
     *
     * @param unpublicTariffs 未公示资费列表
     * @param fileName        文件名,用于标识数据来源
     * @param auditDate       审计日期
     */
    public void execute(List<JSONObject> unpublicTariffs, String fileName, String auditDate) {
        log.info("开始解析文件名 : {}, auditDate: {}, 未公示资费数据size: {}", fileName, auditDate, unpublicTariffs.size());

        // 解析文件名获取基础信息
        String reporter = fileName.split("-")[0];
        String tariffProvinceCode = reporter.substring(0, reporter.length() - 1);
        String entCode = reporter.substring(reporter.length() - 1);

        // 获取省份信息
        JSONObject province = XtyTariffContainer.getProvice(tariffProvinceCode);
        String provinceCode = province.getString("PROVINCE_CODE");
        String provinceName = province.getString("PROVINCE_NAME");

        // 获取企业名称
        String entName = mapEntName(entCode);

        // 处理日期信息
        String dateId = auditDate.replace("-", "");
        String monthId = dateId.substring(0, 6);

        // 使用异步线程处理，解决ES数据同步延迟问题
        new Thread(() -> {
            try {
                // 删除旧的未公示审计数据
                deleteOldAuditData(monthId, dateId, entCode, tariffProvinceCode);
                if (unpublicTariffs == null || unpublicTariffs.isEmpty()) {
                    log.info("没有未公示资费需要处理");
                    return;
                }
                // 添加延时，确保ES删除操作完成
                Thread.sleep(2000);
                
                // 处理新的未公示审计数据
                processUnpublicTariffs(unpublicTariffs, auditDate, tariffProvinceCode, provinceCode, provinceName, entCode, entName, reporter, monthId, dateId, fileName);
                
                log.info("完成处理未资费公示文件 fileName: {}, auditDate: {} in async thread", fileName, auditDate);
            } catch (Exception e) {
                log.error("Error in async thread processing unpublic tariffs: {}", e.getMessage(), e);
            }
        }).start();

        log.info("Started async thread for fileName: {}, auditDate: {}", fileName, auditDate);
    } 

    /**
     * 删除旧的未公示审计数据
     *
     * @param monthId            月份ID
     * @param dateId             日期ID
     * @param entCode            企业代码
     * @param tariffProvinceCode 资费省份代码
     * @param fileName           文件名
     */
    public void deleteOldAuditData(String monthId, String dateId, String entCode, String tariffProvinceCode) {
        // 删除只有这一天数据的记录
        deleteDocumentsWithOnlyOneDate(dateId, entCode, tariffProvinceCode);

        // 移除指定日期的字段并更新最后日期
        removeDateFieldAndUpdateLastDate(monthId, dateId, entCode, tariffProvinceCode);
    }

    /**
     * 删除只有一天数据且该天为指定日期的文档
     *
     * @param dateId             日期ID
     * @param entCode            企业代码
     * @param tariffProvinceCode 资费省份代码
     * @return 删除的文档数量
     */
    private int deleteDocumentsWithOnlyOneDate(String dateId, String entCode, String tariffProvinceCode) {
        try {
            // 将dateId转换为yyyy-MM-dd格式
            String formattedDate = dateId.substring(0, 4) + "-" + 
                                   dateId.substring(4, 6) + "-" + 
                                   dateId.substring(6, 8);
            
            // 构建查询条件
            JSONObject termQuery1 = new JSONObject()
                    .fluentPut("term", new JSONObject()
                            .fluentPut("entCode.keyword", entCode));
            
            JSONObject termQuery2 = new JSONObject()
                    .fluentPut("term", new JSONObject()
                            .fluentPut("tariffProvinceCode.keyword", tariffProvinceCode));
            
            JSONObject termQuery3 = new JSONObject()
                    .fluentPut("term", new JSONObject()
                            .fluentPut("firstDate", formattedDate));
            
            JSONObject termQuery4 = new JSONObject()
                    .fluentPut("term", new JSONObject()
                            .fluentPut("lastDate", formattedDate));

            JSONObject termQuery5 = new JSONObject()
                    .fluentPut("term", new JSONObject()
                            .fluentPut("tariffCreateType.keyword", "unpublic_audit"));
            
            // 构建完整的bool查询
            JSONObject boolQuery = new JSONObject()
                    .fluentPut("bool", new JSONObject()
                            .fluentPut("must", new JSONArray()
                                    .fluentAdd(termQuery1)
                                    .fluentAdd(termQuery2)
                                    .fluentAdd(termQuery3)
                                    .fluentAdd(termQuery4)
                                    .fluentAdd(termQuery5)
                                    ));
            
            // 构建删除请求
            JSONObject deleteRequest = new JSONObject()
                    .fluentPut("query", boolQuery);
            
            log.info("删除ES未公示稽核日期等于首次出现日期等于最后出现日期 ({}): {}", formattedDate, deleteRequest);
            // 执行删除操作
            JSONObject response = ElasticsearchKit.deleteByQuery(UNPUBLIC_TARIFF_AUDIT_ES_INDEX, deleteRequest);
            
            int deleted = response.getIntValue("deleted");
            log.info("删除ES未公示稽核日期等于首次出现日期等于最后出现日期 Deleted {} date ({}) entCode={}, tariffProvinceCode={}",
                    deleted, formattedDate, entCode, tariffProvinceCode);
            
            return deleted;
        } catch (Exception e) {
            log.error("删除ES未公示稽核日期等于首次出现日期等于最后出现日期失败 dateId={}, entCode={}, tariffProvinceCode={}",
                    dateId, entCode, tariffProvinceCode, e);
            return 0;
        }
    }

    /**
     * 移除指定日期字段并更新最后日期
     *
     * @param monthId            月份ID
     * @param dateId             日期ID
     * @param entCode            企业代码
     * @param tariffProvinceCode 资费省份代码
     * @return 是否移除成功
     */
    public boolean removeDateFieldAndUpdateLastDate(String monthId, String dateId, String entCode, String tariffProvinceCode) {
        try {
            // 先删除指定日期字段
            boolean removed = removeDateField(monthId, dateId, entCode, tariffProvinceCode);
            if (!removed) {
                log.warn("No date field {}.{} was removed for (entCode={}, tariffProvinceCode={})",
                        monthId, dateId, entCode, tariffProvinceCode);
                return false;
            }
            
            // 将dateId转换为yyyy-MM-dd格式，用于比较
            String formattedDateToExclude = dateId.substring(0, 4) + "-" + 
                                            dateId.substring(4, 6) + "-" + 
                                            dateId.substring(6, 8);
            
            // 查询匹配的文档
            JSONObject termQuery1 = new JSONObject()
                    .fluentPut("term", new JSONObject()
                            .fluentPut("entCode.keyword", entCode));
            
            JSONObject termQuery2 = new JSONObject()
                    .fluentPut("term", new JSONObject()
                            .fluentPut("tariffProvinceCode.keyword", tariffProvinceCode));

            JSONObject termQuery5 = new JSONObject()
                    .fluentPut("term", new JSONObject()
                            .fluentPut("tariffCreateType.keyword", "unpublic_audit"));
            
            // 添加firstDate或lastDate等于要删除日期的条件
            JSONObject termQuery3 = new JSONObject()
                    .fluentPut("term", new JSONObject()
                            .fluentPut("firstDate", formattedDateToExclude));
            
            JSONObject termQuery4 = new JSONObject()
                    .fluentPut("term", new JSONObject()
                            .fluentPut("lastDate", formattedDateToExclude));

            // 构建should查询（firstDate=dateId OR lastDate=dateId）
            JSONObject shouldQuery = new JSONObject()
                    .fluentPut("bool", new JSONObject()
                            .fluentPut("should", new JSONArray()
                    .fluentAdd(termQuery3)
                                    .fluentAdd(termQuery4)));
            
            // 构建完整的bool查询
            JSONObject boolQuery = new JSONObject()
                    .fluentPut("bool", new JSONObject()
                            .fluentPut("must", new JSONArray()
                                    .fluentAdd(termQuery1)
                                    .fluentAdd(termQuery2)
                                    .fluentAdd(termQuery5)
                                    .fluentAdd(shouldQuery)));

            // 构建查询请求
            JSONObject queryRequest = new JSONObject()
                    .fluentPut("query", boolQuery)
                    .fluentPut("size", 1000);

            // 执行查询
            JSONObject response = ElasticsearchKit.search(UNPUBLIC_TARIFF_AUDIT_ES_INDEX, queryRequest);
            JSONArray hits = response.getJSONObject("hits").getJSONArray("hits");

            // 更新每个文档的firstDate和lastDate
            for (int i = 0; i < hits.size(); i++) {
                JSONObject doc = hits.getJSONObject(i);
                JSONObject source = doc.getJSONObject("_source");
                String serialId = source.getString("serialId");
                log.info("hits["+i+"]===="+hits);
                // 查找最早和最晚的日期，排除要删除的日期
                String[] dates = findEarliestAndLatestDates(source, formattedDateToExclude);
                String earliestDate = dates[0];
                String latestDate = dates[1];
                
                String currentFirstDate = source.getString("firstDate");
                String currentLastDate = source.getString("lastDate");
                
                // 如果日期有变化，则更新
                // 构建脚本更新
                StringBuilder scriptBuilder = new StringBuilder();
                JSONObject scriptParams = new JSONObject();

                if (earliestDate != null) {
                    scriptBuilder.append("ctx._source.firstDate = params.earliestDate;");
                    scriptParams.put("earliestDate", earliestDate);
                }

                if (latestDate != null) {
                    scriptBuilder.append("ctx._source.lastDate = params.latestDate;");
                    scriptParams.put("latestDate", latestDate);
                }

                // 构建更新请求，使用serialId而非_id
                JSONObject termQueryForUpdate = new JSONObject()
                        .fluentPut("term", new JSONObject()
                                .fluentPut("serialId.keyword", serialId));

                JSONObject updateRequest = new JSONObject()
                        .fluentPut("query", termQueryForUpdate)
                        .fluentPut("script", new JSONObject()
                                .fluentPut("source", scriptBuilder.toString())
                                .fluentPut("lang", "painless")
                                .fluentPut("params", scriptParams));

                try {
                    JSONObject updateResponse = ElasticsearchKit.updateByQuery(UNPUBLIC_TARIFF_AUDIT_ES_INDEX, updateRequest);
                    log.info("Updated document firstDate/lastDate for serialId: {}, response: {}", serialId, updateResponse);
                } catch (Exception e) {
                    log.error("Failed to update document firstDate/lastDate for serialId: {}", serialId, e);
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("Failed to remove date field and update last date for monthId={}, dateId={}, entCode={}, tariffProvinceCode={}",
                    monthId, dateId, entCode, tariffProvinceCode, e);
            return false;
        }
    }

    /**
     * 移除指定日期字段
     *
     * @param monthId            月份ID
     * @param dateId             日期ID
     * @param entCode            企业代码
     * @param tariffProvinceCode 资费省份代码
     * @return 是否移除成功
     */
    private boolean removeDateField(String monthId, String dateId, String entCode, String tariffProvinceCode) {
        try {
            // 构建bool查询条件
            JSONObject boolQuery = new JSONObject()
                    .fluentPut("bool", new JSONObject()
                            .fluentPut("must", new JSONArray()
                                    .fluentAdd(new JSONObject().fluentPut("term",
                                            new JSONObject().fluentPut("entCode.keyword", entCode)))
                                    .fluentAdd(new JSONObject().fluentPut("term",
                                            new JSONObject().fluentPut("tariffProvinceCode.keyword",
                                                    tariffProvinceCode)))
                                    .fluentAdd(new JSONObject().fluentPut("exists",
                                            new JSONObject().fluentPut("field", monthId + "." + dateId)))));

            // 构建更新请求
            JSONObject updateRequest = new JSONObject()
                    .fluentPut("query", boolQuery)
                    .fluentPut("script", new JSONObject()
                            .fluentPut("source",
                                    String.format("if (ctx._source.%s != null) { ctx._source.%s.remove('%s'); }",
                                            monthId, monthId, dateId))
                            .fluentPut("lang", "painless"));

            log.info("updateRequest:{}", updateRequest);
            // 执行更新
            JSONObject response = ElasticsearchKit.updateByQuery(UNPUBLIC_TARIFF_AUDIT_ES_INDEX, updateRequest);

            log.info("response:{}", response);
            // 检查更新结果
            int updated = response.getIntValue("updated");
            log.info("Removed date field {}.{} for {} documents (entCode={}, tariffProvinceCode={})",
                    monthId, dateId, updated, entCode, tariffProvinceCode);

            return updated > 0;
        } catch (Exception e) {
            log.error("Failed to remove date field {}.{} for (entCode={}, tariffProvinceCode={})",
                    monthId, dateId, entCode, tariffProvinceCode, e);
            return false;
        }
    }

    /**
     * 查找最早和最晚的日期，排除指定日期
     *
     * @param source             源文档
     * @param dateToExclude      要排除的日期
     * @return 最早和最晚日期数组
     */
    private String[] findEarliestAndLatestDates(JSONObject source, String dateToExclude) {
        String earliestDate = null;
        String latestDate = null;
        
        // 遍历所有月份字段
        for (String key : source.keySet()) {
            if (key.matches("\\d{6}")) { // 月份格式：yyyyMM
                JSONObject monthData = source.getJSONObject(key);
                if (monthData != null) {
                    for (String dateKey : monthData.keySet()) {
                        if (dateKey.matches("\\d{8}")) { // 日期格式：yyyyMMdd
                            String fullDate = dateKey.substring(0, 4) + "-" + 
                                                   dateKey.substring(4, 6) + "-" + 
                                                   dateKey.substring(6, 8);
                            
                            // 排除指定日期
                            if (!fullDate.equals(dateToExclude)) {
                                if (earliestDate == null || fullDate.compareTo(earliestDate) < 0) {
                                    earliestDate = fullDate;
                                }
                                if (latestDate == null || fullDate.compareTo(latestDate) > 0) {
                                    latestDate = fullDate;
                                }
                            }
                        }
                    }
                }
            }
        }
        log.info("earliestDate="+earliestDate+",latestDate="+latestDate);
        return new String[] {earliestDate, latestDate};
    }

    /**
     * 处理未公示资费数据
     *
     * @param unpublicTariffs    未公示资费列表
     * @param auditDate          审计日期
     * @param tariffProvinceCode 资费省份代码
     * @param provinceCode       省份代码
     * @param provinceName       省份名称
     * @param entCode            企业代码
     * @param entName            企业名称
     * @param reporter           报告人
     * @param monthId            月份ID
     * @param dateId             日期ID
     * @param fileName           文件名
     */
    private void processUnpublicTariffs(List<JSONObject> unpublicTariffs, String auditDate, String tariffProvinceCode, String provinceCode,
                                       String provinceName, String entCode, String entName, String reporter, String monthId, String dateId, String fileName) {
        log.info("Starting processUnpublicTariffs for reporter: {}, monthId: {}", reporter, monthId);
        
        for (JSONObject tariff : unpublicTariffs) {
            // 获取资费基本信息
            String tariffName = tariff.getString("name");
            String errorReportNo = tariff.getString("error_report_no");
            String reportNo = tariff.getString("report_no");
            String type = tariff.getString("type");
            String type1 = tariff.getString("type1");
            String type2 = tariff.getString("type2");

            String singleKey = reportNo;
            if (StringUtils.isBlank(reportNo)) {
                singleKey = tariffName;
            }
            String serialId = getTariffSerialId(singleKey, reportNo, type, reporter);
                
            // 构建日期数据
            JSONObject dateData = new JSONObject();
            JSONObject dateJson = new JSONObject();
            if (StringUtils.isNotBlank(errorReportNo)) {
                dateJson.put("errorReportNo", errorReportNo);
            }
            String likeName = tariff.getString("like_name");
            if (StringUtils.isNotBlank(likeName)) {
                dateJson.put("likeName", likeName);
            }
            dateJson.put("type", type);
            dateJson.put("tariffSaleCount", tariff.getIntValue("total"));
            dateJson.put("source", fileName);
            dateJson.put("auditDate", auditDate);
            
            // 添加其他可能的字段
            if (tariff.containsKey("fees")) {
                dateJson.put("fees", tariff.getString("fees"));
            }
            if (tariff.containsKey("valid_period")) {
                dateJson.put("validPeriod", tariff.getString("valid_period"));
            }
            if (tariff.containsKey("channel")) {
                dateJson.put("channel", tariff.getString("channel"));
                }
                
                dateData.put(dateId, dateJson);
                
            // 检查是否已存在该资费的记录
            JSONObject existingDoc = getUnpublicTariffDoc(serialId);
            if (existingDoc != null) {
                // 更新现有记录
                updateUnpublicTariffDoc(auditDate, monthId, dateId, existingDoc, serialId, dateJson);
                } else {
                // 创建新记录
                createUnpublicTariffDoc(auditDate, tariffProvinceCode, provinceCode, provinceName,
                        entCode, entName, type, reporter, monthId, serialId, tariffName, reportNo,errorReportNo, dateData,
                        type1,type2);
            }
        }

        log.info("Finished processUnpublicTariffs for reporter: {}", reporter);
    }

    /**
     * 获取未公示资费文档
     *
     * @param serialId 序列ID
     * @return JSON对象
     */
    private JSONObject getUnpublicTariffDoc(String serialId) {
        try {
            // 构建term查询
            JSONObject termQuery = new JSONObject()
                    .fluentPut("term", new JSONObject()
                            .fluentPut("serialId", serialId));

            JSONObject queryJson = new JSONObject()
                    .fluentPut("query", termQuery)
                    .fluentPut("size", 1)
                    .fluentPut("_source", Arrays.asList("firstDate", "lastDate"));

            // 执行查询
            JSONObject result = ElasticsearchKit.search(UNPUBLIC_TARIFF_AUDIT_ES_INDEX, queryJson);

            // 解析结果
            JSONArray hits = result.getJSONObject("hits")
                    .getJSONArray("hits");

            return hits.isEmpty() ? null : hits.getJSONObject(0).getJSONObject("_source");

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 创建新的未公示资费审计记录
     *
     * @param auditDate          审计日期
     * @param tariffProvinceCode 资费省份代码
     * @param provinceCode       省份代码
     * @param provinceName       省份名称
     * @param entCode            企业代码
     * @param entName            企业名称
     * @param type               类型
     * @param reporter           报告人
     * @param monthId            月份ID
     * @param serialId           序列ID
     * @param tariffName         资费名称
     * @param reportNo           报告编号
     * @param dateData           日期数据
     */
    private void createUnpublicTariffDoc(String auditDate, String tariffProvinceCode, String provinceCode, String provinceName, String entCode,
                                        String entName, String type, String reporter, String monthId, String serialId,
                                         String tariffName, String reportNo,String errorReportNo, JSONObject dateData,String type1,String type2) {
        log.info("Starting createUnpublicTariffDoc for serialId: {}, tariffName: {}, reportNo: {}", serialId, tariffName, reportNo);

        JSONObject json = new JSONObject();
        json.put("serialId", serialId);
        json.put("tariffName", tariffName);
        if(StringUtils.isBlank(reportNo)){
            json.put("reportNo", errorReportNo);
        }else{
            json.put("reportNo", reportNo);
        }
        if (StringUtils.isNotBlank(reportNo)) {
            String reportId = XtyTariffContainer.getReportIdByNo(reportNo);
            json.put("reportId", reportId);
        }
        json.put("reporter", reporter);
        json.put("reporterName", getReporterName(reporter));
        json.put("tariffProvinceCode", tariffProvinceCode);
        json.put("provinceCode", provinceCode);
        json.put("provinceName", provinceName);
        json.put("entCode", entCode);
        json.put("entName", entName);
        json.put("type", type);
        json.put("type1", type1);
        json.put("type2", type2);
        json.put("firstDate", auditDate);
        json.put("lastDate", auditDate);
        json.put("tariffCreateType", "unpublic_audit");
        json.put("relation", 0);
        json.put(monthId, dateData);

        EventDispatcher.addEvent(XtyTariffEventData.builder().id(serialId).data(json).command(Constants.ES_COMMAND_BATCH_ADD).indexName(UNPUBLIC_TARIFF_AUDIT_ES_INDEX).build());

        log.info("Finished createUnpublicTariffDoc for serialId: {}", serialId);
    }

    /**
     * 更新未公示资费审计记录
     *
     * @param auditDate   审计日期
     * @param monthId     月份ID
     * @param dateId      日期ID
     * @param existingDoc 现有文档
     * @param serialId    序列ID
     * @param dateJson    日期数据
     */
    private void updateUnpublicTariffDoc(String auditDate, String monthId, String dateId, JSONObject existingDoc, String serialId, JSONObject dateJson) {
        log.info("Starting updateUnpublicTariffDoc for serialId: {}, monthId: {}, dateId: {}", serialId, monthId, dateId);
        String firstDate = existingDoc.getString("firstDate");
        if (StringUtils.isBlank(firstDate) || firstDate.compareTo(auditDate) > 0) {
            firstDate = auditDate;
        }
        String lastDate = existingDoc.getString("lastDate");
        if (StringUtils.isBlank(lastDate) || lastDate.compareTo(auditDate) < 0) {
            lastDate = auditDate;
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("monthId", monthId);
        jsonObject.put("dateId", dateId);
        jsonObject.put("firstDate", firstDate);
        jsonObject.put("lastDate", lastDate);
        jsonObject.put("dateJson", dateJson);

        String script = String.format(
                "if (ctx._source.%s == null) { ctx._source.%s = [:]; } " +
                        "ctx._source.%s.%s = params.dateJson; " +
                        "ctx._source.firstDate = params.firstDate; " +
                        "ctx._source.lastDate = params.lastDate;",
                monthId, monthId, monthId, dateId);


        JSONObject updateParams = new JSONObject()
                .fluentPut("dateJson", dateJson)
                .fluentPut("firstDate", firstDate)
                .fluentPut("lastDate", lastDate);

        JSONObject actionMetadata = new JSONObject()
                .fluentPut("update", new JSONObject()
                        .fluentPut("_index", UNPUBLIC_TARIFF_AUDIT_ES_INDEX)
                        .fluentPut("_id", serialId));

        XtyTariffEventData eventData = XtyTariffEventData.builder()
                .id(serialId)
                .data(jsonObject)
                .command(Constants.ES_COMMAND_BATCH_UPT)
                .indexName(UNPUBLIC_TARIFF_AUDIT_ES_INDEX)
                .script(script)
                .updateParams(updateParams)
                .actionMetadata(actionMetadata)
                .build();
        EventDispatcher.addEvent(eventData);

        log.info("Finished updateUnpublicTariffDoc for serialId: {}", serialId);
    }

    /**
     * 获取资费序列ID
     *
     * @param tariffName 资费名称
     * @param reportNo   报告编号
     * @param type       类型
     * @param reporter   报告人
     * @return 序列ID
     */
    private String getTariffSerialId(String tariffName, String reportNo, String type, String reporter) {
        // 根据入参组合数据进行MD5生成唯一流水
        return SecureUtil.sha256(tariffName + reportNo + type + reporter);
    }

    /**
     * 映射企业名称
     *
     * @param entCode 企业代码
     * @return 企业名称
     */
    private String mapEntName(String entCode) {
        return BusiUtil.getEntByEntCode(entCode);
    }

    /**
     * 获取报送主体名称
     *
     * @param reporter 报送主体编码
     * @return 报送主体名称
     */
    private String getReporterName(String reporter) {
        String reporterName = RedissonUtil.hget("xty:tariff:reporter", reporter);
        if (StringUtils.isNotBlank(reporterName)) {
            return reporterName;
        }
        String result = "";
        try {
            List<String> ents = Arrays.asList("1", "2", "3", "5");
            String sql = "select TARIFF_PROVINCE_CODE,TARIFF_PROVINCE_NAME from " + Constants.getBusiTable("xty_tariff_province") + " where TARIFF_PROVINCE_CODE<>''";
            List<JSONObject> list = QueryFactory.getReadQuery().queryForList(sql, new Object[]{}, new JSONMapperImpl());
            for (JSONObject obj : list) {
                String tariffProvinceCode = obj.getString("TARIFF_PROVINCE_CODE");
                String tariffProvinceName = obj.getString("TARIFF_PROVINCE_NAME");
                for (String ent : ents) {
                    String _reporter = tariffProvinceCode + ent;
                    String _reporterName = "";
                    if (StringUtils.equals("集团", tariffProvinceName)) {
                        _reporterName = mapEntName(ent) + tariffProvinceName;
                    } else {
                        _reporterName = tariffProvinceName + mapEntName(ent);
                    }
                    if (StringUtils.equals(reporter, _reporter)) {
                        result = _reporterName;
                    }
                    RedissonUtil.hset("xty:tariff:reporter", _reporter, _reporterName);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }
} 