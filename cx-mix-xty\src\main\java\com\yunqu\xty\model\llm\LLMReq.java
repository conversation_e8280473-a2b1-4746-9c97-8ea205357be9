package com.yunqu.xty.model.llm;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.SystemErrorLog;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.LogUtil;
import com.yunqu.xty.base.CommonLogger;
import com.yunqu.xty.base.Constants;
import com.yunqu.xty.base.QueryFactory;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.EasyRecord;

import java.io.Serializable;
import java.sql.SQLException;

/**
 * 大模型请求对象
 */
public class LLMReq implements Serializable {

	private static final long serialVersionUID = 1L;

	//请求id，默认为0，如果是重新发起，则存放数据库里的请求记录值
	private String id;

	private String reqType;

	private String reqTime;

	private String busiType;

	private String busiId;

	private String content;

	private String entId;

	private String schema;

	private String busiOrderId;

	private String createTime;

	private String createAcc;

	private String reqUrl;

	private String callbackUrl;

	private JSONObject reqJson;

	private String respTime;

	private String respCode;

	private String respDesc;

	private int reqTimes = 0;

	private JSONObject respJson;

	private String macCode;

	private String status;

	public String getBusiType() {
		return busiType;
	}

	public void setBusiType(String busiType) {
		this.busiType = busiType;
	}

	public String getBusiId() {
		return busiId;
	}

	public void setBusiId(String busiId) {
		this.busiId = busiId;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getReqType() {
		return reqType;
	}

	public void setReqType(String reqType) {
		this.reqType = reqType;
	}

	public String getReqTime() {
		return reqTime;
	}

	public void setReqTime(String reqTime) {
		this.reqTime = reqTime;
	}

	public String getEntId() {
		return entId;
	}

	public void setEntId(String entId) {
		this.entId = entId;
	}

	public String getSchema() {
		return schema;
	}

	public void setSchema(String schema) {
		this.schema = schema;
	}

	public String getBusiOrderId() {
		return busiOrderId;
	}

	public void setBusiOrderId(String busiOrderId) {
		this.busiOrderId = busiOrderId;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getCreateAcc() {
		return createAcc;
	}

	public void setCreateAcc(String createAcc) {
		this.createAcc = createAcc;
	}

	public String getReqUrl() {
		return reqUrl;
	}

	public void setReqUrl(String reqUrl) {
		this.reqUrl = reqUrl;
	}

	public String getCallbackUrl() {
		return callbackUrl;
	}

	public void setCallbackUrl(String callbackUrl) {
		this.callbackUrl = callbackUrl;
	}

	public JSONObject getReqJson() {
		return reqJson;
	}

	public void setReqJson(JSONObject reqJson) {
		this.reqJson = reqJson;
	}

	public String getRespTime() {
		return respTime;
	}

	public void setRespTime(String respTime) {
		this.respTime = respTime;
	}

	public String getRespCode() {
		return respCode;
	}

	public void setRespCode(String respCode) {
		this.respCode = respCode;
	}

	public String getRespDesc() {
		return respDesc;
	}

	public void setRespDesc(String respDesc) {
		this.respDesc = respDesc;
	}

	public int getReqTimes() {
		return reqTimes;
	}

	public void setReqTimes(int reqTimes) {
		this.reqTimes = reqTimes;
	}

	public JSONObject getRespJson() {
		return respJson;
	}

	public void setRespJson(JSONObject respJson) {
		this.respJson = respJson;
	}

	public String getMacCode() {
		return macCode;
	}

	public void setMacCode(String macCode) {
		this.macCode = macCode;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public LLMReq() {

	}

	public LLMReq(String reqId, String entId, String schema)	{
		this.id = reqId;
		this.entId = entId;
		this.schema = schema;


	}


	public LLMReq(UserModel user, JSONObject reqJson, String busiId, String busiType)	{
		this.reqJson = reqJson;
		this.busiId = busiId;
		this.busiType = busiType;
		this.entId = user.getEpCode();
		this.schema = user.getSchemaName();
		this.busiOrderId = user.getBusiOrderId();
		this.createAcc = user.getUserAcc();
		this.createTime = DateUtil.getCurrentDateStr();
	}

	/**
	 * 生成请求记录
	 */
	public void create() {
		try {
			EasyRecord record = new EasyRecord(schema + ".XTY_GC_LLM_REQ", "ID");
			record.put("ID", this.id);
			record.put("CREATE_TIME", this.createTime);
			record.put("ENT_ID", this.entId);
			record.put("BUSI_ORDER_ID", this.busiOrderId);
			record.put("CREATE_ACC", this.createAcc);
			record.put("REQ_TYPE", this.reqType);
			record.put("BUSI_ID", this.busiId);
			record.put("BUSI_TYPE", this.busiType);
			record.put("STATUS", this.status);
			record.put("REQ_URL", this.reqUrl);
			record.put("REQ_JSON", JSONObject.toJSONString(this.reqJson));
			record.put("CALLBACK_URL", this.callbackUrl);
			record.put("REQ_TIMES", 0);
			record.put("MAC_CODE", this.macCode);
			QueryFactory.getQuery(entId).save(record);
		} catch (Exception e) {
			CommonLogger.getLogger("nlp").error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
	}

	/**
	 * 更新请求次数
	 */
	public void update(boolean reqTimes) {
		try {
			EasyRecord record = new EasyRecord(schema + ".XTY_GC_LLM_REQ", "ID");
			record.put("ID", this.id);
			record.put("STATUS", this.status);
			if(reqTimes) {
				record.put("REQ_TIMES", this.reqTimes + 1);
			}
			if(StringUtils.isNotBlank(this.reqTime)) {
				record.put("REQ_TIME", this.reqTime);
			}

			QueryFactory.getQuery(entId).update(record);
		} catch (SQLException e) {
			CommonLogger.getLogger("nlp").error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
	}

	/**
	 * 执行成功
	 */
	public void succ() {
		try {
			CommonLogger.getLogger("nlp").info("执行成功");
			EasyRecord record = new EasyRecord(schema + ".XTY_GC_LLM_REQ", "ID");
			record.put("ID", id);
			record.put("RESP_TIME", DateUtil.getCurrentDateStr());
			record.put("STATUS", Constants.AILLM_REQ_STATUS_2);
			record.put("RESP_JSON", JSONObject.toJSONString(this.respJson));
			QueryFactory.getQuery(entId).update(record);
		} catch (Exception e) {
			CommonLogger.getLogger("nlp").error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
	}

	/**
	 * 执行失败
	 */
	public void fail() {
		try {
			// 在达到最大重试次数时，针对分类码和业务码生成失败进行埋点
			if (reqTimes >= 2 && (Constants.NLP_COMMAND_CATEGORY_CODE.equals(reqType) || Constants.NLP_COMMAND_BUSINESS_CODE.equals(reqType))) {
				String errorCode = Constants.NLP_COMMAND_CATEGORY_CODE.equals(reqType) ? "XTY_T_GD_002" : "XTY_T_GD_003";
				String errorTitle = Constants.NLP_COMMAND_CATEGORY_CODE.equals(reqType) ? "分类码生成失败" : "业务码生成失败";
				String errorDesc = String.format("工单[%s]%s，已重试%d次仍然失败，请及时处理！", busiId, errorTitle, reqTimes + 1);

				SystemErrorLog log = new SystemErrorLog(entId, busiOrderId, Constants.APP_NAME, SystemErrorLog.ERROR_TYPE_SYSTEM,
						errorCode, errorTitle, errorDesc);
				log.setErrorLevel("error");
				LogUtil.addSystemErrorLog(log, CommonLogger.getLogger("nlp"));
			}

			EasyRecord record = new EasyRecord(schema + ".XTY_GC_LLM_REQ", "ID");
			record.put("ID", id);
			record.put("RESP_TIME", DateUtil.getCurrentDateStr());
			record.put("STATUS", Constants.AILLM_REQ_STATUS_3);
			record.put("RESP_JSON", JSONObject.toJSONString(this.respJson));
			QueryFactory.getQuery(entId).update(record);
		} catch (SQLException e) {
			CommonLogger.getLogger("nlp").error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
	}

}
