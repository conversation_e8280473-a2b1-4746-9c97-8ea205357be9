package com.yunqu.tariff.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.model.tariffReportStat.ProvinceAreaBean;
import com.yunqu.tariff.model.tariffReportStat.TariffReportedStatCell;
import com.yunqu.tariff.model.tariffReportStat.TariffReportedStatParam;
import com.yunqu.tariff.utils.CacheUtil;
import com.yunqu.xty.commonex.kit.ElasticsearchKit;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.kit.RandomKit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.*;

/**
 * 报送资费es统计索引同步更新服务
 */
public class TariffReportStatSyncService {
    private static final Logger logger = LoggerFactory.getLogger(CommonLogger.getCheckSyncLogger().getName());
    private static final Object LOAD_LOCK = new Object();
    // 缓存: 省份编码 -> 包含该省所有信息的 JSONObject (key: areas, name, pinyin)
    // 结构示例: {"areas": ["130100", "130200", ...], "label": "河北省", "pinyin": "hebei"}
    private Map<String, JSONObject> provinceInfoMap = null;

    public TariffReportStatSyncService() {
        ensureAreaDataLoaded();
    }

    /**
     * 解析从 xty_tariff_area 表查询到的数据，填充到 infoMap 中。
     *
     * @param tariffAreas 查询结果列表
     * @param infoMap     存储解析结果的 Map
     */
    private static void parseTariffAreaData(List<JSONObject> tariffAreas, Map<String, JSONObject> infoMap) {
        if (tariffAreas == null || tariffAreas.isEmpty()) {
            logger.error("TariffReportStatSyncService xty_tariff_area 表中未查询到数据");
            return;
        }

        for (JSONObject row : tariffAreas) {
            // 获取关键字段，直接使用 getString
            String provinceCode = row.getString("PROVINCE_CODE");
            // 如果省份编码为空，跳过此行
            if (provinceCode == null || provinceCode.isEmpty()) {
                logger.error("TariffReportStatSyncService 跳过无效数据行，缺少 PROVINCE_CODE: {}", row);
                continue;
            }

            // 获取城市编码 (使用 TARIFF_AREA_CODE)
            String cityCode = row.getString("TARIFF_AREA_CODE"); // 可能为 null

            // 尝试获取已存在的省份信息，不存在则创建
            JSONObject provinceInfo = infoMap.computeIfAbsent(provinceCode, k -> {
                JSONObject newInfo = new JSONObject();
                newInfo.put("label", row.getString("PROVINCE_NAME")); // 设置省份名称
                newInfo.put("pinyin", row.getString("TARIFF_PROVINCE_CODE")); // 设置拼音
                newInfo.put("areas", new ArrayList<String>()); // 初始化城市列表
                return newInfo;
            });

            // 如果 cityCode 存在且非空，则添加到城市列表 (避免重复)
            if (cityCode != null && !cityCode.isEmpty()) {
                @SuppressWarnings("unchecked")
                List<String> cities = (List<String>) provinceInfo.get("areas");
                // 使用 Set 临时去重再转回 List (如果数据量大且重复多，可考虑直接用 LinkedHashSet)
                // 这里保持 List 以维持顺序感，去重检查开销小
                if (!cities.contains(cityCode)) {
                    cities.add(cityCode);
                }
            }
            // 注意: cityCode 为 null 的行仅用于设置/更新省份的 label 和 pinyin
        }
    }

    /**
     * 处理批量添加的错误信息
     */
    private static void handleBulkError(JSONObject bulkResponse) {
        JSONArray items = bulkResponse.getJSONArray("items");
        StringBuilder errorBuilder = new StringBuilder("批量添加文档部分失败: ");
        for (int i = 0; i < items.size(); i++) {
            JSONObject item = items.getJSONObject(i);
            if (item.containsKey("index") && item.getJSONObject("index").containsKey("error")) {
                JSONObject error = item.getJSONObject("index").getJSONObject("error");
                errorBuilder.append("Item ").append(i).append(": ").append(error.toJSONString()).append("; ");
            }
        }
        logger.error("TariffReportStatSyncService " + errorBuilder.toString());
    }

    /**
     * 懒加载获取省份信息映射
     * 使用 getTariffArea() 方法从 xty_tariff_area 表加载数据
     * 结构: Map<provinceCode, {label: provinceName, pinyin: tariffProvinceCode, cities: [tariffAreaCode1, tariffAreaCode2, ...]}>
     */
    private void ensureAreaDataLoaded() {
        if (provinceInfoMap != null) return;

        synchronized (LOAD_LOCK) {
            if (provinceInfoMap != null) return;
            try {
                Map<String, JSONObject> infoMap = (Map<String, JSONObject>) CacheUtil.get("report_sync_area_tree");
                if (infoMap == null || infoMap.isEmpty()) {
                    infoMap = new HashMap<>();
                    List<JSONObject> tariffAreas = getTariffArea();
                    parseTariffAreaData(tariffAreas, infoMap);
                    CacheUtil.put("report_sync_area_tree", infoMap, 3600);
                }
                // 只有加载成功才赋值
                provinceInfoMap = Collections.unmodifiableMap(infoMap);
            } catch (Exception e) {
                logger.error("TariffReportStatSyncService 加载区域数据失败，使用空map", e);
                // 设置一个空map，避免反复重试（可考虑加延迟重试）
                provinceInfoMap = Collections.emptyMap();
            }
        }
    }

    /**
     * 从 xty_tariff_area 表获取数据
     *
     * @return 查询结果列表
     * @throws SQLException 如果查询失败
     */
    private List<JSONObject> getTariffArea() throws SQLException {
        EasySQL sql = new EasySQL("select * from " + Constants.getBusiSchema() + ".xty_tariff_area");
        logger.info("TariffReportStatSyncService xty_tariff_area sql: " + sql.getSQL());
        try {
            return QueryFactory.getReadQuery().queryForList(sql.getSQL(), new Object[]{}, new JSONMapperImpl());
        } catch (SQLException e) {
            throw new RuntimeException("查询 xty_tariff_area 表失败", e);
        }
    }

    /**
     * 【核心抽象】处理单个 ProvinceAreaBean 的区域通配符 "000"。
     * 如果其 areas 包含 "000" 或为空，则用该省所有区域替换。
     *
     * @param tariffNo 资费编码
     * @param bean     需要处理的 ProvinceAreaBean
     */
    public void handleAreaWildcard(String tariffNo, ProvinceAreaBean bean) {
        if (bean == null || provinceInfoMap == null) return;

        String[] areas = bean.getAreas();
        // 检查是否需要替换为全量区域
        if (areas == null || areas.length == 0 || Arrays.asList(areas).contains("000")) {
            JSONObject provinceInfo = provinceInfoMap.get(bean.getProvinceCode());
            if (provinceInfo != null && provinceInfo.containsKey("areas")) {
                List<String> allAreas = provinceInfo.getObject("areas", List.class);
                if (allAreas != null && !allAreas.isEmpty()) {
                    bean.setAreas(allAreas.toArray(new String[0])); // 替换数组
                    logger.info("TariffReportStatSyncService 资费编码：【{}】 省份：【{}】 的区域被 '000' 通配符替换为全量区域", tariffNo, bean.getProvinceCode());
                } else {
                    logger.info("TariffReportStatSyncService 资费编码：【{}】 省份：【{}】 的全量区域信息为空", tariffNo, bean.getProvinceCode());
                    bean.setAreas(new String[0]);
                }
            } else {
                logger.error("TariffReportStatSyncService 资费编码：【{}】,无法获取省份 [{}] 的信息", tariffNo, bean.getProvinceCode());
                bean.setAreas(new String[0]);
            }
        }
        // 如果不包含 "000" 且不为空，则无需处理
    }

    /**
     * 【核心抽象】处理全国通配符 "000"。
     * 清空原列表，并用全国所有省份及其全量区域重新填充。
     *
     * @param tariffNo      资费编码
     * @param provinceDatas 原始的省份区域列表
     */
    public void handleProvinceWildcard(String tariffNo, List<ProvinceAreaBean> provinceDatas) {
        if (provinceDatas == null || provinceInfoMap == null) return;

        // 清空原有数据
        provinceDatas.clear();
        logger.info("TariffReportStatSyncService 资费编码{}， 检测到 '000' 省份通配符，清空原有省份数据", tariffNo);

        // 遍历所有省份，填充全国数据
        for (String provinceCode : provinceInfoMap.keySet()) {
            JSONObject provinceInfo = provinceInfoMap.get(provinceCode);
            if (provinceInfo != null && provinceInfo.containsKey("areas")) {
                List<String> allAreaCodes = provinceInfo.getObject("areas", List.class);
                if (allAreaCodes != null && !allAreaCodes.isEmpty()) {
                    ProvinceAreaBean newBean = new ProvinceAreaBean(provinceCode, allAreaCodes.toArray(new String[0]));
                    provinceDatas.add(newBean);
                    logger.info("TariffReportStatSyncService 资费编码{}， 添加全国省份数据: [{}]", tariffNo, provinceCode);
                }
            }
        }
    }

    /**
     * 向ES索引中添加文档。
     * 基于 TariffReportedStatParam 生成多个 TariffReportedStatCell 文档。
     * 处理 000 通用编码：替换为所有省份或省份下所有区域。
     * 保证逻辑一致性，避免重复。
     *
     * @param param 包含基础数据和省份区域信息的参数对象
     * @throws Exception 如果添加失败
     */
    public void addDocumentsToEs(TariffReportedStatParam param) throws Exception {
//        param = JSON.parseObject("{\"data\":{\"addonPackageRestriction\":0,\"areaCodes\":[\"0100\"],\"busiOrderId\":\"83880897233159997654981\",\"contentDuplicateOrConflict\":1,\"contentVerbose\":0,\"dataQuotaInconsistent\":0,\"entCode\":\"2\",\"freeServiceTerm\":0,\"id\":\"17321915056521498984388\",\"isPublic\":\"Y\",\"isTelecom\":\"1\",\"liabilityInconsistent\":0,\"marketingCampaignRestriction\":0,\"offlineTimeInconsistent\":0,\"packageRestriction\":0,\"provinceCode\":\"110000\",\"reporter\":\"BJ2\",\"salesChannelInconsistent\":0,\"scopeInconsistent\":0,\"serviceContentMissing\":0,\"tariffNameDuplication\":0,\"tariffNo\":\"24BJ200999\",\"tariffStandardMissing\":0,\"type1\":\"1\",\"type2\":\"2\",\"unsubscribeInconsistent\":1,\"validityInconsistent\":0,\"versionNos\":[\"V20250612_2\",\"V20250621_1\",\"V20250701_1\",\"V20250711_1\",\"V20250711_4\",\"V20250721_1\",\"V20250723_1\",\"V20250726_5\",\"V20250728_1\",\"V20250729_2\",\"V20250730_1\",\"V20250731_1\"]},\"provinceDatas\":[{\"areas\":[\"0100\"],\"provinceCode\":\"110000\"}]}", TariffReportedStatParam.class);
        String tariffNo = param.getData().getTariffNo();
        logger.info("TariffReportStatSyncService 资费编码【{}】，开始处理添加文档到ES{}", tariffNo, JSON.toJSONString(param));
        try {
            if (param.getData() == null || param.getProvinceDatas() == null) {
                throw new IllegalArgumentException("TariffReportStatSyncService TariffReportedStatParam 或其必要字段为空");
            }

            List<ProvinceAreaBean> provinceDatas = param.getProvinceDatas();
            if (provinceDatas.isEmpty()) {
                logger.info("TariffReportStatSyncService 资费编码【{}】，ProvinceDatas 列表为空，未生成任何文档。", tariffNo);
                return;
            }

            List<JSONObject> documentsToAdd = new ArrayList<>();
            TariffReportedStatCell baseData = param.getData();

            // --- 1. 懒加载区域数据 ---
            ensureAreaDataLoaded();

            // --- 2. 决策阶段：确定最终的 (省份, 区域) 组合 ---
            // 2.1 检查是否存在全国通配符 "000"
            if (provinceDatas.stream()
                    .anyMatch(bean -> "000".equals(bean.getProvinceCode()))) {
                // 存在 "000"，处理全国通配符，替换整个列表
                handleProvinceWildcard(tariffNo, provinceDatas);
            } else {
                // 不存在 "000"，处理每个省份自身的区域通配符 "000"
                for (ProvinceAreaBean bean : provinceDatas) {
                    handleAreaWildcard(tariffNo, bean);
                }
            }

            // --- 3. 执行阶段：生成文档 ---
            // 此时 provinceDatas 中的每个 bean 都是明确的
            for (ProvinceAreaBean bean : provinceDatas) {
                String provinceCode = bean.getProvinceCode();
                String[] areaCodes = bean.getAreas(); // 已经是最终确定的数组

                // 从统一的 Map 中获取省份信息
                JSONObject provinceInfo = provinceInfoMap != null ? provinceInfoMap.get(provinceCode) : null;
                if (provinceInfo == null) {
                    logger.error("TariffReportStatSyncService 资费编码【{}】， 未找到省份编码对应的信息: {}，provinceInfoMap 为空或不包含该key", tariffNo, provinceCode);
                    continue;
                }
                logger.info("TariffReportStatSyncService 资费编码【{}】，获取到当前资费省份信息: {}", tariffNo, JSON.toJSONString(provinceInfo));

                String provinceName = provinceInfo.getString("label");
                String provincePinyin = provinceInfo.getString("pinyin");

                if (StringUtils.isBlank(provinceName) || StringUtils.isBlank(provincePinyin)) {
                    logger.error("TariffReportStatSyncService 资费编码【{}】，省份 [{}] 的名称或拼音信息为空", tariffNo, provinceCode);
                    continue;
                }

                // 清空资费id重新生成避免多省记录覆盖;
                baseData.setId(null);

                // 创建文档
                JSONObject doc = (JSONObject) JSONObject.toJSON(baseData);
                doc.put("provinceCode", provinceCode);
                doc.put("provinceName", provinceName);
                doc.put("province", provincePinyin);
                doc.put("areaCodes", areaCodes);


                // 同步到ES索引，使用相同的ID
                try {
                    // 添加到ES
                    addDocumentToEs(doc);
                } catch (Exception e) {
                    logger.error("同步文档到ES索引失败，资费编码【{}】，err,{}", tariffNo, e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            logger.error("TariffReportStatSyncService 资费编码【{}】，添加ES文档失败: {}", tariffNo, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 向ES添加文档
     *
     * @param document 文档内容
     * @throws Exception 如果添加失败
     */
    public void addDocumentToEs(JSONObject document) throws Exception {
        String tariffNo = document.getString("tariffNo");
        try {
            // 获取文档ID
            String id = document.getString("id");
            if (StringUtils.isBlank(id)) {
                document.put("id", id = RandomKit.uniqueStr());
            }

            // 使用ElasticsearchKit添加文档
            JSONObject response = ElasticsearchKit.addDoc(Constants.XTY_TARIFF_BAK_INFO_STAT_INDEX, id, document);
            // 检查添加结果
            if (response == null || response.containsKey("error")) {
                String errorMsg = response != null ? response.toJSONString() : "响应为空";
                throw new Exception("添加文档失败: " + errorMsg);
            }
            logger.info("添加ES文档成功，ID: {}, tariffNo：{}", id, tariffNo);
        } catch (Exception e) {
            logger.info("添加ES文档失败, ID: {}, tariffNo：{}, msg: {}", document.getString("id"), tariffNo, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据 tariffNo 删除 ES 索引中的文档。
     * 删除所有 tariffNo 匹配的记录。
     *
     * @param tariffNo 要删除的资费编码
     * @throws Exception 如果删除失败
     */
    public void deleteDocumentsByTariffNo(String tariffNo) throws Exception {
        logger.info("TariffReportStatSyncService 删除 tariffNo: {} 的文档", tariffNo);
        try {
            if (StringUtils.isBlank(tariffNo)) {
                throw new IllegalArgumentException("tariffNo 不能为空");
            }

            // 构建 term 查询，根据 tariffNo 查找文档
            JSONObject termQuery = new JSONObject()
                    .fluentPut("term", new JSONObject()
                            .fluentPut("tariffNo.keyword", tariffNo));

            // 构建删除请求
            JSONObject deleteRequest = new JSONObject()
                    .fluentPut("query", termQuery);

            // 执行删除
            JSONObject response = ElasticsearchKit.deleteByQuery(Constants.XTY_TARIFF_BAK_INFO_STAT_INDEX, deleteRequest);

            // 检查删除结果
            if (response.containsKey("error")) {
                String errorMsg = response.toJSONString();
                throw new Exception("删除文档失败: " + errorMsg);
            }
            // 'deleted' 字段表示成功删除的文档数量
            long deletedCount = response.getLongValue("deleted");
            logger.info("TariffReportStatSyncService 删除ES文档成功，tariffNo: {}, 删除数量: {}", tariffNo, deletedCount);
        } catch (Exception e) {
            logger.error("TariffReportStatSyncService 删除ES文档失败，tariffNo:{}， err,{}", tariffNo, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新ES索引中的文档。
     * 策略：先根据 tariffNo 删除所有相关记录，然后根据 TariffReportedStatParam 重新添加新的记录。
     * 这确保了数据的最终一致性。
     *
     * @param param 包含新数据和新省份区域信息的参数对象
     * @throws Exception 如果删除或添加失败
     */
    public void updateDocumentsByTariffNo(TariffReportedStatParam param) throws Exception {
        if (param == null || param.getData() == null || StringUtils.isBlank(param.getData().getTariffNo())) {
            logger.error("TariffReportedStatParam 或其 tariffNo 为空");
            return;
        }
        try {
            String tariffNo = param.getData().getTariffNo();

            // 步骤1: 删除所有旧的记录
            deleteDocumentsByTariffNo(tariffNo);

            // 步骤2: 添加新的记录
            addDocumentsToEs(param);

            logger.info("TariffReportStatSyncService 更新ES文档成功，tariffNo: {}", tariffNo);

        } catch (Exception e) {
            logger.error("TariffReportStatSyncService 更新ES文档失败 (tariffNo: {}): {}", param.getData().getTariffNo(), e.getMessage(), e);
            throw e;
        }
    }

}
