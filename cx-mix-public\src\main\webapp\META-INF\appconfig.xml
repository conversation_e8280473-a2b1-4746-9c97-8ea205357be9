<?xml version="1.0" encoding="UTF-8"?>
<config>
	<param key="ENT_ID" name="企业ID" type="String" description="企业ID" value="" ></param>
	<param key="BUSI_ORDER_ID" name="企业订购ID" type="String" description="企业订购ID" value="" ></param>
	<param key="AUTHENTICATION_ADDRESS"  name="三要素验证接口地址" 	type="string" 	description="三要素验证接口地址" index='4' value="https://verify.opene164.org.cn:22100/info/verify.do"/>
	<param key="PLACE_AUTHENTICATION_ADDRESS"  name="归属地验证接口地址" 	type="string" 	description="归属地验证接口地址" index='5' value="https://verify.opene164.org.cn:22100/info/location.do"/>
	<param key="AUTHENTICATION_USERID"  name="三要素/归属地验证接口用户id" 	type="string" 	description="三要素/归属地验证接口用户id" index='6' value="dianxinfuwupingtai"/>
	<param key="AUTHENTICATION_SECRET_KEY"  name="三要素/归属地验证接口SecretKey" 	type="string" 	description="三要素/归属地验证接口SecretKey" index='7' value="7DA49A929F1561003B10F6BE99AA7EEE"/>
    <param key="SMS_SERVICE_BROKER"  name="短信发送MQ名称" 	type="string" 	description="短信发送MQ名称" index='99' value="XTY_SMS_SERVICE_BROKER"/>
	<param key="CUST_SERVICE_BROKER" name="请求的消息队列名称" type="String" description="请求的消息队列名称" value="XTY_CUST_SERVER_BROKER" ></param>
	<param key="TRAN_SERVICE_BROKER" name="携转系统请求的消息队列名称" type="String" description="携转系统请求的消息队列名称" value="XTY_TRAN_SERVICE_BROKER" ></param>
	<param key="TRAN_SYS_URL" name="携转系统地址" type="String" description="携转系统地址" value="" ></param>
	<param key="TRAN_SYS_PLATFORM" name="携转系统平台名称" type="String" description="携转系统平台名称" value="" ></param>
	<param key="TRAN_SYS_SIGN" name="携转系统平台签名" type="String" description="携转系统平台签名" value="" ></param>
	<param key="TRAN_SYS_DE" name="携转系统平台解密KEY" type="String" description="携转系统平台解密KEY" value="D5A99CD51E0C0DC6D8C42966BB261D9A" ></param>
     <param key="ATTACHMENT_SUFFIX"  name="附件上传格式" 	type="string" 	description="附件上传格式,需要和内网服务器的cc-base配置中附件上传格式参数保持一致" index='99' value="jpg;jpeg;xls;txt;word;mp4;png;xlsx;docx;zip;pdf;AVI;FLV;mov;FLV;"/>

    <param key="SFTP_IP" name="SFTP服务器IP地址" type="String" description="FTP服务器IP地址" value="" index="20"></param>
    <param key="SFTP_PORT" name="SFTP服务器端口" type="String" description="FTP服务器端口" value="" index="21"></param>
    <param key="SFTP_USERNAME" name="SFTP服务器登录用户名" type="String" description="FTP服务器登录用户名" value=""  index="22"></param>
    <param key="SFTP_PASSWORD" name="SFTP服务器登录密码" type="String" description="FTP服务器登录密码" value=""  index="23"></param>
   	<param key="SFTP_BASE_PATH" name="SFTP服务器文件根目录" type="String" description="FTP服务器文件根目录，如：/market/interface" value="" index="24"></param>
    <param key="SFTP_LOCAL_BASE_PATH" name="SFTP本地文件存放路径" type="String" description="本地文件存放路径，如：/home/<USER>/interface" value="" index="25"></param>
	<param key="TRAIFF_AUDIT_LOAD_BROKER" name="资费稽核文件下载的消息队列名称" type="String" description="资费稽核文件下载的消息队列名称" value="XTY_TRAIFF_AUDIT_LOAD_BROKER" index="26"></param>
	<param key="TRAIFF_BAK_BROKER" name="资费备案的消息队列名称" type="String" description="资费备案的消息队列名称" value="XTY_TRAIFF_BAK_BROKER" index="27"></param>
	<param key="TRAIFF_REQ_BAK_BROKER" name="资费请求备案的消息队列名称" type="String" description="资费请求备案的消息队列名称" value="XTY_TRAIFF_REQ_BAK_BROKER" index="28" ></param>
	<param key="TASK_BROKER" name="定时任务保存数据队列名称" type="String" description="定时任务保存数据队列名称" value="XTY_TASK_BROKER" index="29"></param>
	<param key="REQ_FORWARD_BROKER" name="请求转发数据队列名称" type="String" description="请求转发数据队列名称" value="REQ_FORWARD_BROKER" index="30"></param>
	<param key="TRAIFF_ORDER_QUERY_BROKER" name="资费订单查询的消息队列" type="String" description="资费订单查询的消息队列" value="TRAIFF_ORDER_QUERY_BROKER" index="31"></param>
	<param key="TRAIFF_ORDER_QUERY_URL" name="资费订单查询地址" type="String" description="资费订单查询地址" value="http://***************:8087/message/getMessageInfoByPhone" index="32"></param>
	<param key="TRAIFF_ORDER_QUERY_KEY" name="资费订单查询解密key" type="String" description="资费订单查询解密key" value="D5A99CD51E0C0DC6D8C42966BB261D9A" index="33"></param>
	<param key="XTY_SMS_MSG_OT" name="短信消息过期时间（小时）" type="int" description="短信消息过期时间（小时）" value="2" index="34"></param>
	<param key="PHONE_VERIFY_BROKER" name="断卡涉验查询的消息队列" type="String" description="断卡涉验查询的消息队列" value="PHONE_VERIFY_BROKER" index="35"></param>

	<param key="TRAIFF_DEL_MSG_BROKER" name="短信订购删除通知队列" type="String" description="短信订购删除通知队列" value="TRAIFF_DEL_MSG_BROKER" index="36"></param>

	<param key="TRAIFF_EXE_MESSAGE_BROKER" name="短信订购重跑队列" type="String" description="短信订购重跑队列" value="TRAIFF_EXE_MESSAGE_BROKER" index="37"></param>
	<param key="TRAIFF_EXE_AUDIT_MESSAGE_BROKER" name="短信稽核重跑队列" type="String" description="短信稽核重跑队列" value="TRAIFF_EXE_AUDIT_MESSAGE_BROKER" index="38"></param>
	<param key="TARIFF_STAT_FILE_ENCODING" name="资费下载解码格式" type="String" description="资费下载解码格式" value="UTF-8" index="39"></param>

	<param key="TARIFF_NLP_FILE_BASE_PATH" name="资费语义分析目录" type="String" description="资费语义分析目录" value="/ai" index="45"></param>
	<param key="AUDIT_TASK_SFTP_REMOTE_BASE_FOLDER" name="稽核任务目录" type="String" description="稽核任务目录" value="/check" index="49"></param>
	<param key="REPORT_SFTP_REMOTE_BASE_FOLDER" name="报送资费文件目录" type="String" description="报送资费文件远程推送目录" value="/report" index="50"></param>

</config>
