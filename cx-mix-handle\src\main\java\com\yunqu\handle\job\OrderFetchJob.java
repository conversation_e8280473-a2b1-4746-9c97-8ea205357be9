package com.yunqu.handle.job;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.SystemErrorLog;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.LogUtil;
import com.yunqu.handle.base.CommonLogger;
import com.yunqu.handle.base.Constants;
import com.yunqu.handle.service.OrderFetchService;
import com.yunqu.handle.util.RedisLockUtil;

import java.util.List;

import com.yunqu.handle.util.XtyModuleRuntimeDataCollector;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 工单拉取定时任务
 * 定时调用OutInterfaceServlet拉取工单信息并写入数据库
 */
public class OrderFetchJob implements Job {
    
    private static final Logger logger = LoggerFactory.getLogger(CommonLogger.getLogger("fetch").getName());
    private static final String LOCK_KEY = "order_fetch_job_lock";
    private static final int LOCK_EXPIRE_TIME = 60 * 30; // 锁过期时间，30分钟
    private static final String LAST_SYNC_TIME = "last_sync_time";
    
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        logger.info("开始执行工单拉取定时任务");
        
        // 使用分布式锁确保在集群环境中只有一个实例执行任务
        boolean locked = false;
        try {
            // 尝试获取锁
            locked = RedisLockUtil.lock(LOCK_KEY, LOCK_EXPIRE_TIME);
            if (!locked) {
                logger.info("未能获取到分布式锁，跳过本次执行");
                return;
            }
            
            OrderFetchService service = new OrderFetchService();
            
            // 分页拉取工单数据
            boolean hasMoreData = true;
            
            while (hasMoreData) {
                logger.info("拉取第{}页工单数据，每页{}条", 1, 100);
                CacheUtil.put(LAST_SYNC_TIME, DateUtil.getCurrentDateStr());
                // 调用服务拉取数据
                JSONObject result = service.fetchOrders(0, 100);
                
                if (result != null && result.getBooleanValue("success")) {
                    List<JSONObject> dataList = JSONObject.parseArray(result.getString("data"),JSONObject.class);
                    
                    if (dataList != null && !dataList.isEmpty()) {
                        logger.info("本页获取到{}条工单数据", dataList.size());
                        
                        // 将数据写入数据库
                        service.saveOrdersToDatabase(dataList);
                        
                        // 如果返回的数据少于pageSize，说明没有更多数据了
                        if (dataList.size() < 100) {
                            hasMoreData = false;
                        } 
                    } else {
                        logger.info("本页没有获取到工单数据，结束拉取");
                        hasMoreData = false;
                    }
                } else {
                    logger.error("拉取工单数据失败: {}", result != null ? result.getString("message") : "未知错误");
                    hasMoreData = false;
                }
            }

            //上报运行状态
            XtyModuleRuntimeDataCollector.report(Constants.APP_NAME, "OrderFetchJob", "工单定时拉取", 1200);

            logger.info("工单拉取定时任务执行完成");
            
        } catch (Exception e) {
            //采集错误信息
            SystemErrorLog log = new SystemErrorLog(Constants.getEntId(),Constants.getBusiOrderId(),Constants.APP_NAME,SystemErrorLog.ERROR_TYPE_SYSTEM,
                    "XTY_T_GD_012","工单拉取异常","从公众端拉取工单异常:"+e.getMessage()+",请及时处理!");
            log.setErrorLevel("warn");
            LogUtil.addSystemErrorLog(log, CommonLogger.logger);

            logger.error("执行工单拉取定时任务失败: " + e.getMessage(), e);
            throw new JobExecutionException(e);
        } finally {
            // 释放锁
            if (locked) {
                RedisLockUtil.unlock(LOCK_KEY);
                logger.debug("已释放分布式锁");
            }
        }
    }
}