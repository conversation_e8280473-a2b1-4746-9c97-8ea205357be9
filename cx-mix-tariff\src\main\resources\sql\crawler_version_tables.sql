-- 爬虫版本管理相关表结构
CREATE TABLE `xty_crawler_version` (
  `id` bigint NOT NULL,
  `version_no` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `lastest` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `idx` int DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `create_by` bigint DEFAULT NULL,
  `create_dept` bigint DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `update_by` bigint DEFAULT NULL,
  `belong_date_id` int DEFAULT NULL,
  `version_table_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `VERSION_STATUS` varchar(10) COLLATE utf8mb4_general_ci DEFAULT 'ACTIVE' COMMENT '版本状态：ACTIVE-正常，INVALID-已作废',
  `INVALID_TIME` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '作废时间',
  `INVALID_USER` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '作废操作人',
  `INVALID_REASON` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '作废原因',
  `DATA_COUNT` int DEFAULT '0' COMMENT '版本数据量',
  `CRAWL_STATUS` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '爬取状态 PENDING-待爬取，PROCESSING-爬取中，COMPLETED-已完成',
  `CRAWL_DURATION` int DEFAULT '0' COMMENT '爬取耗时（秒）',
  `CRAWL_START_TIME` datetime DEFAULT NULL COMMENT '爬取开始时间',
  `CRAWL_END_TIME` datetime DEFAULT NULL COMMENT '爬取结束时间',
  `AUDIT_STATUS` varchar(10) COLLATE utf8mb4_general_ci DEFAULT 'PENDING' COMMENT '稽核状态：PENDING-待稽核，PROCESSING-稽核中，COMPLETED-已完成',
  `AUDIT_START_TIME` datetime DEFAULT NULL COMMENT '稽核开始时间',
  `AUDIT_END_TIME` datetime DEFAULT NULL COMMENT '稽核结束时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
-- 1. 修改现有版本表，添加新字段
ALTER TABLE xty_crawler_version ADD COLUMN VERSION_STATUS VARCHAR(10) DEFAULT 'ACTIVE' COMMENT '版本状态：ACTIVE-正常，INVALID-已作废';
ALTER TABLE xty_crawler_version ADD COLUMN INVALID_TIME VARCHAR(20) COMMENT '作废时间';
ALTER TABLE xty_crawler_version ADD COLUMN INVALID_USER VARCHAR(64) COMMENT '作废操作人';
ALTER TABLE xty_crawler_version ADD COLUMN INVALID_REASON VARCHAR(500) COMMENT '作废原因';
ALTER TABLE xty_crawler_version ADD COLUMN DATA_COUNT INT DEFAULT 0 COMMENT '版本数据量';
ALTER TABLE xty_crawler_version ADD COLUMN CRAWL_DURATION INT DEFAULT 0 COMMENT '爬取耗时（秒）';
ALTER TABLE xty_crawler_version ADD COLUMN AUDIT_STATUS VARCHAR(10) DEFAULT 'PENDING' COMMENT '稽核状态：PENDING-待稽核，PROCESSING-稽核中，COMPLETED-已完成';
ALTER TABLE xty_crawler_version ADD COLUMN AUDIT_START_TIME VARCHAR(20) COMMENT '稽核开始时间';
ALTER TABLE xty_crawler_version ADD COLUMN AUDIT_END_TIME VARCHAR(20) COMMENT '稽核结束时间';
ALTER TABLE xty_crawler_version
ADD COLUMN `CRAWL_START_TIME` varchar(20) NULL COMMENT '爬取开始时间' AFTER `CRAWL_DURATION`,
ADD COLUMN `CRAWL_END_TIME` varchar(20) NULL COMMENT '爬取结束时间' AFTER `CRAWL_START_TIME`;
ALTER TABLE xty_crawler_version
ADD COLUMN `CRAWL_STATUS` varchar(20) NULL COMMENT '爬取状态 PENDING-待爬取，PROCESSING-爬取中，COMPLETED-已完成' AFTER `DATA_COUNT`;

-- 2. 创建自定义调度任务表
CREATE TABLE xty_crawler_schedule (
    ID VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    SCHEDULE_NAME VARCHAR(200) COMMENT '调度任务名称',
    SCHEDULE_DATE VARCHAR(20) NOT NULL COMMENT '调度日期，格式：yyyy-MM-dd',
    SCHEDULE_TIME VARCHAR(20) DEFAULT '02:00:00' COMMENT '调度时间，格式：HH:mm:ss',
    SCHEDULE_TYPE VARCHAR(10) NOT NULL COMMENT '调度类型：IMMEDIATE-立即执行，SCHEDULED-定时执行',
    TASK_STATUS VARCHAR(10) DEFAULT 'PENDING' COMMENT '任务状态：PENDING-待执行，RUNNING-执行中，SUCCESS-成功，FAILED-失败，CANCELLED-已取消',
    PROVINCE_CODE VARCHAR(64) COMMENT '省份编码',
    PROVINCE_NAME VARCHAR(200) COMMENT '省份名称',
    ENT_TYPE VARCHAR(20) COMMENT '企业类型',
    CREATE_USER VARCHAR(64) COMMENT '创建用户',
    CREATE_TIME VARCHAR(20) COMMENT '创建时间',
    UPDATE_USER VARCHAR(64) COMMENT '更新用户',
    UPDATE_TIME VARCHAR(20) COMMENT '更新时间',
    EXECUTE_TIME VARCHAR(20) COMMENT '实际执行时间',
    COMPLETE_TIME VARCHAR(20) COMMENT '完成时间',
    ERROR_MESSAGE TEXT COMMENT '错误信息',
    VERSION_NO VARCHAR(50) COMMENT '生成的版本号',
    REMARK VARCHAR(500) COMMENT '备注信息',
    INDEX idx_schedule_date (SCHEDULE_DATE),
    INDEX idx_task_status (TASK_STATUS),
    INDEX idx_create_time (CREATE_TIME)
) COMMENT '爬虫自定义调度任务表';

-- 3. 创建操作日志表
CREATE TABLE xty_crawler_operation_log (
    ID VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    OPERATION_TYPE VARCHAR(20) NOT NULL COMMENT '操作类型：CREATE_SCHEDULE-创建调度，INVALID_VERSION-作废版本，DATA_CLEANUP-数据清理，AUDIT_TRIGGER-触发稽核',
    OPERATION_DESC VARCHAR(500) COMMENT '操作描述',
    TARGET_TYPE VARCHAR(20) COMMENT '操作目标类型：VERSION-版本，SCHEDULE-调度任务',
    TARGET_ID VARCHAR(64) COMMENT '操作目标ID',
    TARGET_NAME VARCHAR(200) COMMENT '操作目标名称',
    OPERATION_USER VARCHAR(64) COMMENT '操作用户',
    OPERATION_TIME VARCHAR(20) COMMENT '操作时间',
    OPERATION_IP VARCHAR(50) COMMENT '操作IP地址',
    OPERATION_RESULT VARCHAR(10) DEFAULT 'SUCCESS' COMMENT '操作结果：SUCCESS-成功，FAILED-失败',
    ERROR_MESSAGE TEXT COMMENT '错误信息',
    BEFORE_DATA TEXT COMMENT '操作前数据（JSON格式）',
    AFTER_DATA TEXT COMMENT '操作后数据（JSON格式）',
    AFFECTED_RANGE TEXT COMMENT '影响范围描述',
    REMARK VARCHAR(500) COMMENT '备注',
    INDEX idx_operation_type (OPERATION_TYPE),
    INDEX idx_operation_user (OPERATION_USER),
    INDEX idx_operation_time (OPERATION_TIME),
    INDEX idx_target_id (TARGET_ID)
) COMMENT '爬虫操作日志表';

-- 4. 创建稽核任务表
CREATE TABLE xty_crawler_audit_task (
    ID VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    AUDIT_NAME VARCHAR(200) COMMENT '稽核任务名称',
    TRIGGER_TYPE VARCHAR(20) NOT NULL COMMENT '触发类型：VERSION_INVALID-版本作废触发，MANUAL-手动触发',
    TRIGGER_VERSION_ID VARCHAR(64) COMMENT '触发的版本ID',
    AUDIT_START_DATE VARCHAR(20) COMMENT '稽核开始日期',
    AUDIT_END_DATE VARCHAR(20) COMMENT '稽核结束日期',
    AUDIT_STATUS VARCHAR(20) DEFAULT 'PENDING' COMMENT '稽核状态：PENDING-待执行，RUNNING-执行中，SUCCESS-成功，FAILED-失败',
    PROGRESS_PERCENT INT DEFAULT 0 COMMENT '执行进度百分比',
    TOTAL_RECORDS INT DEFAULT 0 COMMENT '总记录数',
    PROCESSED_RECORDS INT DEFAULT 0 COMMENT '已处理记录数',
    SUCCESS_RECORDS INT DEFAULT 0 COMMENT '成功处理记录数',
    FAILED_RECORDS INT DEFAULT 0 COMMENT '失败记录数',
    CREATE_USER VARCHAR(64) COMMENT '创建用户',
    CREATE_TIME VARCHAR(20) COMMENT '创建时间',
    START_TIME VARCHAR(20) COMMENT '开始执行时间',
    END_TIME VARCHAR(20) COMMENT '结束时间',
    ERROR_MESSAGE TEXT COMMENT '错误信息',
    RESULT_SUMMARY TEXT COMMENT '结果摘要（JSON格式）',
    INDEX idx_audit_status (AUDIT_STATUS),
    INDEX idx_trigger_version (TRIGGER_VERSION_ID),
    INDEX idx_create_time (CREATE_TIME)
) COMMENT '爬虫稽核任务表';

-- 5. 创建数据清理记录表
CREATE TABLE xty_crawler_cleanup_record (
    ID VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '主键ID',
    CLEANUP_TYPE VARCHAR(20) NOT NULL COMMENT '清理类型：VERSION_DATA-版本数据，RELATED_DATA-关联数据',
    TARGET_VERSION_ID VARCHAR(64) COMMENT '目标版本ID',
    TARGET_TABLE_NAME VARCHAR(100) COMMENT '目标表名',
    CLEANUP_STATUS VARCHAR(20) DEFAULT 'PENDING' COMMENT '清理状态：PENDING-待执行，RUNNING-执行中，SUCCESS-成功，FAILED-失败',
    TOTAL_RECORDS INT DEFAULT 0 COMMENT '总记录数',
    CLEANED_RECORDS INT DEFAULT 0 COMMENT '已清理记录数',
    BACKUP_PATH VARCHAR(500) COMMENT '备份文件路径',
    CREATE_USER VARCHAR(64) COMMENT '创建用户',
    CREATE_TIME VARCHAR(20) COMMENT '创建时间',
    START_TIME VARCHAR(20) COMMENT '开始执行时间',
    END_TIME VARCHAR(20) COMMENT '结束时间',
    ERROR_MESSAGE TEXT COMMENT '错误信息',
    CLEANUP_SQL TEXT COMMENT '清理SQL语句',
    INDEX idx_cleanup_status (CLEANUP_STATUS),
    INDEX idx_target_version (TARGET_VERSION_ID),
    INDEX idx_create_time (CREATE_TIME)
) COMMENT '数据清理记录表';