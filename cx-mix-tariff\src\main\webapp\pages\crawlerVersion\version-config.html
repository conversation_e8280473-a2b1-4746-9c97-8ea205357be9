<!DOCTYPE html>
<html>

<head>
  <title>版本调度配置</title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
  <!-- 基础的 css js 资源 -->
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.4">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.3">
  <link rel="stylesheet" href="./common.css?v=20241104">
  <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
  <script src="/easitline-cdn/vue-yq/libs/httpVueLoader.js"></script>
  <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
  <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
  <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.1"></script>
  <script src="/cc-base/static/js/my_i18n.js?v=202111"></script>
  <script src="/cc-base/static/js/i18n.js?v=1"></script>
</head>

<body class="yq-page-full vue-box">
  <div id="versionConfig" class="flex yq-table-page" v-loading="loading" element-loading-text="加载中..." v-cloak>
    <div class="yq-card">
      <div class="card-header">
        <div class="head-title">{{ getI18nValue('版本调度配置管理') }}</div>
        <div class="yq-table-control">
          <el-button type="primary" plain size="small" icon="el-icon-circle-plus-outline"
            @click="handleAdd">{{getI18nValue('新增配置')}}</el-button>
        </div>
      </div>
      <div class="card-content">
        <!-- 搜索区域 -->
        <div class="search-box">
          <senior-search :show.sync="moreSearch">
            <el-form class="search-form grid-5" :inline="false" :model="searchForm" ref="searchForm" size="small"
              label-width="80px">
              <el-form-item :label="getI18nValue('配置名称')" prop="configName">
                <el-input v-model="searchForm.configName" :placeholder="getI18nValue('请输入')" clearable></el-input>
              </el-form-item>
              <el-form-item :label="getI18nValue('执行日期')" prop="ruleValue">
                <el-input v-model="searchForm.ruleValue" :placeholder="getI18nValue('请输入日期(1-31)')" clearable></el-input>
              </el-form-item>
              <el-form-item :label="getI18nValue('启用状态')" prop="isEnabled">
                <el-select v-model="searchForm.isEnabled" placeholder="请选择" filterable clearable>
                  <el-option label="已启用" value="Y"></el-option>
                  <el-option label="已禁用" value="N"></el-option>
                </el-select>
              </el-form-item>
              <template v-if="moreSearch">
                <el-form-item :label="getI18nValue('配置类型')" prop="configType">
                  <el-select v-model="searchForm.configType" placeholder="请选择" filterable clearable>
                    <el-option label="系统配置" value="SYSTEM"></el-option>
                    <el-option label="自定义配置" value="CUSTOM"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item :label="getI18nValue('创建时间')" style="grid-column-start: span 2;">
                  <el-date-picker v-model="searchForm.createTime" type="datetimerange"
                    value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']" start-placeholder="开始时间"
                    end-placeholder="结束时间" clearable :unlink-panels="true">
                  </el-date-picker>
                </el-form-item>
              </template>
              <el-form-item class="btns" label-width="0px">
                <el-button type="primary" plain icon="el-icon-refresh" @click="handleReset">重置</el-button>
                <el-button type="primary" icon="el-icon-search" @click="getList(1)">搜索</el-button>
                <el-button type="primary" plain size="small" @click.stop="moreSearch = !moreSearch">
                  <img src="/easitline-cdn/vue-yq/static/imgs/filter.png" alt="">高级搜索
                </el-button>
              </el-form-item>
            </el-form>
          </senior-search>
        </div>

        <!-- 表格区域 -->
        <div class="yq-table">
          <el-table ref="table" :data="tableData.data" style="width: 100%" height="100%" v-loading="tableData.loading"
            border stripe>
            <el-table-column prop="CONFIG_NAME" :label="getI18nValue('配置名称')" width="200" fixed="left"></el-table-column>
            <el-table-column prop="CONFIG_TYPE" :label="getI18nValue('配置类型')" width="120">
              <template slot-scope="scope">
                <el-tag size="small" :type="getConfigTypeTagType(scope.row.CONFIG_TYPE)">
                  {{formatConfigType(scope.row.CONFIG_TYPE)}}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="RULE_VALUE" :label="getI18nValue('执行日期')" width="120">
              <template slot-scope="scope">
                每月{{scope.row.RULE_VALUE}}日
              </template>
            </el-table-column>
            <el-table-column prop="IS_ENABLED" :label="getI18nValue('启用状态')" width="120">
              <template slot-scope="scope">
                <el-tag size="small" :type="getEnabledStatusTagType(scope.row.IS_ENABLED)">
                  {{formatEnabledStatus(scope.row.IS_ENABLED)}}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column :label="getI18nValue('有效期')" width="200">
              <template slot-scope="scope">
                {{formatEffectivePeriod(scope.row.EFFECTIVE_START_DATE, scope.row.EFFECTIVE_END_DATE)}}
              </template>
            </el-table-column>
            <el-table-column prop="DESCRIPTION" :label="getI18nValue('配置描述')" min-width="200" show-overflow-tooltip></el-table-column>
            <el-table-column prop="CREATE_TIME" :label="getI18nValue('创建时间')" width="180"></el-table-column>
            <el-table-column prop="opt" :label="getI18nValue('操作')" min-width="150" fixed="right">
              <template slot-scope="scope">
                <el-link v-if="scope.row.CONFIG_TYPE === 'CUSTOM'" class="custlink" @click="handleEdit(scope.row)">{{getI18nValue('修改')}}</el-link>
                <el-link v-if="scope.row.CONFIG_TYPE === 'CUSTOM'" class="custlink" type="danger" @click="handleDelete(scope.row)">{{getI18nValue('删除')}}</el-link>
                <span v-if="scope.row.CONFIG_TYPE === 'SYSTEM'" style="color: #909399; font-size: 12px;">系统配置不可操作</span>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination background @current-change="onPageChange" @size-change="onPageSizeChange"
            :current-page="tableData.pageIndex" :page-size="tableData.pageSize" :page-sizes="[15, 30, 50, 100]"
            layout="total, prev, pager, next, jumper,sizes" :total="tableData.totalRow">
          </el-pagination>
        </div>
      </div>
    </div>

    <!-- 新增/编辑配置弹窗 -->
    <el-drawer :visible.sync="configDrawer" :title="configForm.ID ? '编辑配置' : '新增配置'" size="50%"
      :wrapper-closable="false">
      <div class="drawer-content" style="padding: 20px;">
        <el-form :model="configForm" ref="configForm" label-width="120px">
          <el-form-item label="配置名称" prop="CONFIG_NAME" :rules="[{required: true, message: '请输入配置名称'}]">
            <el-input v-model="configForm.CONFIG_NAME" placeholder="请输入配置名称" maxlength="100"></el-input>
          </el-form-item>
          <el-form-item label="配置类型" prop="CONFIG_TYPE" :rules="[{required: true, message: '请选择配置类型'}]">
            <el-select v-model="configForm.CONFIG_TYPE" placeholder="请选择" style="width: 100%;">
              <el-option label="系统配置" value="SYSTEM"></el-option>
              <el-option label="自定义配置" value="CUSTOM"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="执行日期" prop="RULE_VALUE" :rules="[{required: true, message: '请选择执行日期'}]">
            <el-select v-model="configForm.RULE_VALUE" placeholder="请选择执行日期" style="width: 100%;">
              <el-option v-for="day in 31" :key="day" :label="`每月${day}日`" :value="day"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="启用状态" prop="IS_ENABLED" :rules="[{required: true, message: '请选择启用状态'}]">
            <el-radio-group v-model="configForm.IS_ENABLED">
              <el-radio label="Y">启用</el-radio>
              <el-radio label="N">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="生效开始日期" prop="EFFECTIVE_START_DATE">
            <el-date-picker v-model="configForm.EFFECTIVE_START_DATE" type="date" placeholder="选择日期"
              value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
          </el-form-item>
          <el-form-item label="生效结束日期" prop="EFFECTIVE_END_DATE">
            <el-date-picker v-model="configForm.EFFECTIVE_END_DATE" type="date" placeholder="选择日期"
              value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
          </el-form-item>
          <el-form-item label="配置描述" prop="DESCRIPTION">
            <el-input v-model="configForm.DESCRIPTION" type="textarea" :rows="3" placeholder="请输入配置描述"
              maxlength="500" show-word-limit></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="drawer-footer" style="padding: 20px; text-align: right; border-top: 1px solid #e4e7ed;">
        <el-button @click="configDrawer = false">取消</el-button>
        <el-button type="primary" @click="saveConfig" :loading="saveLoading">保存</el-button>
      </div>
    </el-drawer>

    <!-- 执行历史弹窗 -->
  </div>

  <script type="text/javascript" src="../common/api.js"></script>
  <script src="/cx-mix-tariff/static/js/crawlerVersion/version-config.js"></script>
  <script>
    var versionConfig = new Vue({
      el: '#versionConfig',
      data: function () {
        return {
          loading: false,
          moreSearch: false,
          searchForm: {
            configName: '',
            ruleValue: '',
            isEnabled: '',
            configType: '',
            createTime: ''
          },
          tableData: {
            pageIndex: 1,
            pageSize: 15,
            totalRow: 0,
            loading: false,
            data: []
          },
          configDrawer: false,
          configForm: {
            ID: '',
            CONFIG_NAME: '',
            CONFIG_TYPE: 'CUSTOM',
            RULE_VALUE: null,
            IS_ENABLED: 'Y',
            EFFECTIVE_START_DATE: '',
            EFFECTIVE_END_DATE: '',
            DESCRIPTION: ''
          },
          saveLoading: false
        }
      },
      filters: {
        numberFormat: function (value) {
          if (!value) return '0'
          return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        }
      },
      methods: {
        // 列表增删改查 分页查询
        onPageChange: function (page) {
          this.tableData.pageIndex = page;
          this.getList();
        },
        onPageSizeChange: function (size) {
          this.tableData.pageSize = size;
          this.getList();
        },
        handleReset: function () {
          this.$refs['searchForm'].resetFields();
          for (var key in this.searchForm) {
            this.searchForm[key] = ""
          }
        },
        // 获取配置列表
        getList: function (page) {
          this.tableData.loading = true
          this.tableData.pageIndex = page || this.tableData.pageIndex
          const data = JSON.parse(JSON.stringify(this.searchForm))
          data.pageIndex = this.tableData.pageIndex
          data.pageSize = this.tableData.pageSize
          
          yq.daoCall({
            controls: ["tariffCrawlerVersionConfig.getConfigList"],
            params: data
          }, (res) => {
            if (res && res['tariffCrawlerVersionConfig.getConfigList']) {
              const result = res['tariffCrawlerVersionConfig.getConfigList']
              if (result.state === 1) {
                this.tableData.data = result.data || []
                this.tableData.totalRow = result.totalRow || 0
              } else {
                this.$message.error(result.msg || '获取配置列表失败')
              }
            }
            this.tableData.loading = false
          }, {
            contextPath: '/cx-mix-tariff'
          })
        },
        // 新增配置
        handleAdd: function() {
          this.configForm = {
            ID: '',
            CONFIG_NAME: '',
            CONFIG_TYPE: 'CUSTOM',
            RULE_VALUE: null,
            IS_ENABLED: 'Y',
            EFFECTIVE_START_DATE: '',
            EFFECTIVE_END_DATE: '',
            DESCRIPTION: ''
          }
          this.configDrawer = true
          this.$nextTick(() => {
            this.$refs['configForm'].resetFields()
          })
        },
        // 编辑配置
        handleEdit: function(row) {
          // 系统配置不允许编辑
          if (row.CONFIG_TYPE === 'SYSTEM') {
            this.$message.warning('系统配置不允许修改')
            return
          }
          this.configForm = JSON.parse(JSON.stringify(row))
          this.configDrawer = true
        },
        // 保存配置
        saveConfig: function() {
          this.$refs['configForm'].validate((valid) => {
            if (valid) {
              this.saveLoading = true
              const action = this.configForm.ID ? 'updateConfig' : 'addConfig'
              const data = {
                ID: this.configForm.ID,
                configName: this.configForm.CONFIG_NAME,
                configType: this.configForm.CONFIG_TYPE,
                ruleValue: this.configForm.RULE_VALUE ? this.configForm.RULE_VALUE.toString() : '',
                isEnabled: this.configForm.IS_ENABLED,
                effectiveStartDate: this.configForm.EFFECTIVE_START_DATE,
                effectiveEndDate: this.configForm.EFFECTIVE_END_DATE,
                description: this.configForm.DESCRIPTION
              }
              
              yq.remoteCall(`/cx-mix-tariff/servlet/crawlerVersionConfig?action=${action}`, data, (res) => {
                this.saveLoading = false
                if (res.state === 1) {
                  this.$message.success(this.configForm.ID ? '修改成功' : '新增成功')
                  this.configDrawer = false
                  this.getList()
                } else {
                  this.$message.error(res.msg || '保存失败')
                }
              })
            }
          })
        },
        // 删除配置
        handleDelete: function(row) {
          // 系统配置不允许删除
          if (row.CONFIG_TYPE === 'SYSTEM') {
            this.$message.warning('系统配置不允许删除')
            return
          }
          
          this.$confirm('确定要删除配置【' + row.CONFIG_NAME + '】吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            yq.remoteCall("/cx-mix-tariff/servlet/crawlerVersionConfig?action=deleteConfig", {
              ID: row.ID
            }, (res) => {
              if (res.state === 1) {
                this.$message.success('删除成功')
                this.getList()
              } else {
                this.$message.error(res.msg || '删除失败')
              }
            })
          })
        },
        // 格式化配置类型
        formatConfigType: function(type) {
          return type === 'SYSTEM' ? '系统配置' : '自定义配置'
        },
        // 获取配置类型标签样式
        getConfigTypeTagType: function(type) {
          return type === 'SYSTEM' ? 'warning' : 'primary'
        },
        // 格式化启用状态
        formatEnabledStatus: function(status) {
          return status === 'Y' ? '已启用' : '已禁用'
        },
        // 获取启用状态标签样式
        getEnabledStatusTagType: function(status) {
          return status === 'Y' ? 'success' : 'danger'
        },
        // 格式化有效期
        formatEffectivePeriod: function(startDate, endDate) {
          if (!startDate && !endDate) {
            return '永久有效'
          }
          return `${startDate || '不限'} ~ ${endDate || '不限'}`
        }
      },
      mounted() {
        this.getList()
      }
    })
  </script>
</body>

</html>
