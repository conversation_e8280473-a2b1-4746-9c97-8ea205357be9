package com.yunqu.tariff.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.thread.ThreadPoolManager;
import com.yunqu.xty.commonex.kit.ElasticsearchKit;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

/**
 * 资费备份初始化服务
 */
public class TariffBakInfoInitService {

    private static Map<String, String> MAP = new ConcurrentHashMap<>();

    /**
     * 初始化索引，创建自定义映射结构
     */
    public void initIndex() {
        try {
            // 检查索引是否存在，不存在则创建
            String indexName = Constants.XTY_TARIFF_BAK_INFO_INDEX;
            JSONObject checkResult = ElasticsearchKit.search(indexName, new JSONObject());

            if (checkResult != null && checkResult.containsKey("error") && "index_not_found_exception".equals(checkResult.getJSONObject("error").getString("type"))) {
                // 索引不存在，需要创建
                CommonLogger.logger.info("[initIndex] 索引 {} 不存在，准备创建索引和映射", indexName);

                // 创建映射结构
                JSONObject indexConfig = new JSONObject();
                JSONObject mappings = new JSONObject();
                JSONObject properties = new JSONObject();

                // 仅设置FEES为数值类型，使其可以进行范围查询
                JSONObject feesField = new JSONObject();
                feesField.put("type", "double");
                properties.put("FEES", feesField);

                // 为AREA_LIST中的元素设置嵌套映射，优化地区搜索
                JSONObject areaListField = new JSONObject();
                areaListField.put("type", "nested");
                JSONObject areaListProperties = new JSONObject();

                JSONObject areaCodeField = new JSONObject();
                areaCodeField.put("type", "keyword");
                areaListProperties.put("AREA_CODE", areaCodeField);

                JSONObject provinceCodeField = new JSONObject();
                provinceCodeField.put("type", "keyword");
                areaListProperties.put("PROVINCE_CODE", provinceCodeField);

                areaListField.put("properties", areaListProperties);
                properties.put("AREA_LIST", areaListField);

                // 为AREA_CODES设置关键字数组类型
                JSONObject areaCodesField = new JSONObject();
                areaCodesField.put("type", "keyword");
                properties.put("AREA_CODES", areaCodesField);

                // 为其他关键字段设置类型
                addKeywordField(properties, "REPORT_NO");
                addKeywordField(properties, "NAME");
                addKeywordField(properties, "TYPE1");
                addKeywordField(properties, "TYPE2");
                addKeywordField(properties, "PROVINCE");
                addKeywordField(properties, "ENT");
                addKeywordField(properties, "STATUS");
                addKeywordField(properties, "IS_HISTORY");
                addKeywordField(properties, "TARIFF_ATTR");

                // 将properties添加到mappings
                mappings.put("properties", properties);
                indexConfig.put("mappings", mappings);

                // 设置索引分析器和其他配置
                JSONObject settings = new JSONObject();
                settings.put("number_of_shards", 5);
                settings.put("number_of_replicas", 1);
                indexConfig.put("settings", settings);

                // 创建索引
                JSONObject createResult = ElasticsearchKit.createIndex(indexName, indexConfig);
                CommonLogger.logger.info("[initIndex] 创建索引 {} 结果: {}", indexName, createResult);
            } else {
                CommonLogger.logger.info("[initIndex] 索引 {} 已存在，无需创建", indexName);
            }
        } catch (Exception e) {
            CommonLogger.logger.error("[initIndex] 初始化索引映射时出错", e);
        }
    }

    /**
     * 添加关键字字段映射
     */
    private void addKeywordField(JSONObject properties, String fieldName) {
        JSONObject field = new JSONObject();
        field.put("type", "keyword");
        properties.put(fieldName, field);
    }


    /**
     * 初始化资费信息备份数据
     */
    public void initTariffBackup(String provinceCode) throws SQLException {
        try {
            // 获取数据库中的记录数量
            int dbRecordCount = getTariffRecordCount(provinceCode);
            CommonLogger.logger.info("[initTariffBackup] 数据库中符合条件的记录数量: {}", dbRecordCount);

            AtomicInteger count = new AtomicInteger(0);

            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("select ID, ENT_ID, BUSI_ORDER_ID, CREATE_TIME, UPDATE_TIME, CREATE_ACC, UPDATE_ACC, ");
            sqlBuilder.append("CREATE_DEPT, PROVINCE, PROVINCE_NAME, CITY, CITY_NAME, ENT, ENT_NAME, REPORTER_TYPE, ");
            sqlBuilder.append("CREATE_TYPE, DATE_ID, SEQ_NO, REPORT_OBJ, REPORT_NO, REPORT_KEY, VERSION_NO, VERSION_NUM, ");
            sqlBuilder.append("IS_HISTORY, REPORTER, IS_PUBLIC, REASON_NO_PUBLIC, TYPE1, TYPE2, NAME, FEES, ");
            sqlBuilder.append("CALL_NUM, DATA_NUM, SMS_NUM, INTERNATIONAL_CALL, INTERNATIONAL_ROAMING_DATA, INTERNATIONAL_SMS, ");
            sqlBuilder.append("ORIENT_TRAFFIC, IPTV, BANDWIDTH, RIGHTS, OTHER_CONTENT, APPLICABLE_PEOPLE, APPLICABLE_AREA, ");
            sqlBuilder.append("VALID_PERIOD, RESPONSIBILITY, RESTRICTIONS, ONLINE_DAY, OFFLINE_DAY, OTHERS, CHANNEL, DURATION, ");
            sqlBuilder.append("STATUS, DEL_TIME, REASON, PLAN, DEL_ACC, APPLICABLE_PROVINCE, FEES_UNIT, DATA_UNIT, ");
            sqlBuilder.append("ORIENT_TRAFFIC_UNIT, APPLICABLE_AREA_NAME, REPORTER_NAME, TARIFF_ATTR, AREA_DESC, AREA_SELECT_TYPE, ");
            sqlBuilder.append("TARIFF_ANOTHER_NAME, UNSUBSCRIBE, FIELD_CHECK_RESULT, FIELD_CHECK_TIME, FIELD_CHECK_NO, EXTRA_FEES, ");
            sqlBuilder.append("OTHER_FEES, IS_TELECOM from ").append(Constants.getBusiSchema()).append(".xty_tariff_record");

            Object[] params = new Object[]{};
            if(StringUtils.isNotBlank(provinceCode)) {
                sqlBuilder.append(" where province=?");
                params = new Object[]{provinceCode};
            }

            sqlBuilder.append(" order by id");
            String sql = sqlBuilder.toString();

            //分页查询 每页1000条
            EasyQuery query = getReadQuery();
            int pageIndex = 1;
            int pageSize = 1000;

            CommonLogger.logger.info("[initTariffBackup] Start backing up tariff data to ES index: {}", Constants.XTY_TARIFF_BAK_INFO_INDEX);


            while (true) {
                List<JSONObject> list = getTariffRecordList(sql, params, query, pageIndex, pageSize, new JSONMapperImpl());
                if (list == null || list.isEmpty()) {
                    break;
                }
                for (JSONObject json : list) {
                    count.incrementAndGet();
                    // 数据预处理，转换数值型字段
                    preprocessNumericFields(json);
                    // 获取关联的地区信息
                    enrichWithAreaInfo(json);
                    // 完全按照TariffInitService的方式提交任务
                    ThreadPoolManager.getExecutorService().execute(() -> sendTariffToEs().accept(json));
                }

                CommonLogger.logger.info("[initTariffBackup] Processed {} records in page {}, total processed: {}",
                        list.size(), pageIndex, count.get());
                pageIndex = pageIndex + 1;
            }
        } catch (Exception e) {
            CommonLogger.logger.error("[initTariffBackup] Error while backing up tariff data", e);
        }
    }

    private static String mapProvince(String provinceCode) {
        CommonLogger.logger.info("mapProvince key:{}", provinceCode);
        if (StringUtils.isBlank(provinceCode)) return "";
        return MAP.computeIfAbsent(provinceCode, key -> {
            if (StringUtils.equals(provinceCode, "0") || StringUtils.isBlank(provinceCode)) {
                return "全国";
            }
            try {
                return getReadQuery().queryForString("select province_name from cc_province where province_code = ?", new Object[]{provinceCode});
            } catch (SQLException e) {
                CommonLogger.logger.error("mapProvince error", e);
                return "";
            }
        });
    }

    private static String mapEnt(String ent) {
        Map<String, String> entMap = new HashMap<String, String>() {{
            put("1", "电信");
            put("2", "移动");
            put("3", "联通");
            put("5", "广电");
            put("0", "全行业");
        }};
        return entMap.getOrDefault(ent, ent);
    }
    /**
     * 获取资费记录总数
     */
    private int getTariffRecordCount(String provinceCode) {
        try {
            EasyQuery query = getReadQuery();
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT COUNT(1) FROM ").append(Constants.getBusiSchema()).append(".xty_tariff_record");

            Object[] params = new Object[]{};
            if(StringUtils.isNotBlank(provinceCode)) {
                sqlBuilder.append(" WHERE province=?");
                params = new Object[]{provinceCode};
            }

            return query.queryForInt(sqlBuilder.toString(), params);
        } catch (Exception e) {
            CommonLogger.logger.error("[getTariffRecordCount] Error getting record count", e);
            return -1;
        }
    }

    /**
     * 预处理数值型字段
     */
    private void preprocessNumericFields(JSONObject json) {
        try {
            // 仅处理FEES字段，确保是数值类型
            String feesStr = json.getString("FEES");
            json.put("FEES_STR", feesStr);
            if (StringUtils.isNotBlank(feesStr) && !"-".equals(feesStr)) {
                try {
                    double fees = Double.parseDouble(feesStr);
                    json.put("FEES", fees);
                } catch (Exception e) {
                    // 如果转换失败，保留原始字符串
                    CommonLogger.logger.warn("[preprocessNumericFields] Failed to convert FEES to number: {}", feesStr);
                }
            }else{
                json.remove("FEES");
            }
        } catch (Exception e) {
            CommonLogger.logger.error("[preprocessNumericFields] Error processing numeric fields for ID: {}", json.getString("ID"), e);
        }
    }

    /**
     * 获取资费信息记录列表
     */
    private List<JSONObject> getTariffRecordList(String sql, Object[] params, EasyQuery query, int pageIndex, int pageSize, EasyRowMapper<JSONObject> mapper) throws SQLException {
        List<JSONObject> list = query.queryForList(sql, params, pageIndex, pageSize, mapper);
        return list;
    }

    /**
     * 为资费信息增加地区数据
     */
    private void enrichWithAreaInfo(JSONObject json) {
        try {
            String tariffRecordId = json.getString("ID");
            if (StringUtils.isBlank(tariffRecordId)) {
                return;
            }

            EasyQuery query = getReadQuery();

            // 查询关联的XTY_TARIFF_AREQ表数据（只获取TYPE为1的记录）
            EasySQL areaSql = new EasySQL();
            areaSql.append("SELECT ID, TARIFF_RECORD_ID, AREA_CODE, AREA_NAME, REPORT_NO, PROVINCE_CODE, PROVINCE_NAME, TYPE");
            areaSql.append(" FROM ").append(Constants.getBusiSchema()).append(".XTY_TARIFF_AREQ");
            areaSql.append(" WHERE TARIFF_RECORD_ID = ?");
            areaSql.append(" AND TYPE = '1'"); // 只获取TYPE为1的记录

            List<JSONObject> areaList = query.queryForList(areaSql.getSQL(), new Object[]{tariffRecordId}, new JSONMapperImpl());

            // 创建地区代码和地区名称数组
            JSONArray areaCodes = new JSONArray();
            JSONArray areaNames = new JSONArray();
            JSONArray provinceCodesArray = new JSONArray();

            if (areaList != null && !areaList.isEmpty()) {
                for (JSONObject area : areaList) {
                    String areaCode = area.getString("AREA_CODE");
                    String areaName = area.getString("AREA_NAME");
                    String provinceCode = area.getString("PROVINCE_CODE");

                    if (StringUtils.isNotBlank(areaCode)) {
                        areaCodes.add(areaCode);
                    }

                    if (StringUtils.isNotBlank(areaName)) {
                        areaNames.add(areaName);
                    }

                    if (StringUtils.isNotBlank(provinceCode) && !provinceCodesArray.contains(provinceCode)) {
                        provinceCodesArray.add(provinceCode);
                    }
                }
            }

            // 将地区信息添加到资费信息中
            json.put("AREA_LIST", areaList);
            json.put("AREA_CODES", areaCodes);
            json.put("AREA_NAMES", areaNames);
            json.put("PROVINCE_CODES", provinceCodesArray);

            // 构建地区树结构，便于前端展示
            List<JSONObject> areaTree = buildAreaTree(tariffRecordId);
            json.put("AREA_TREE", areaTree);

        } catch (Exception e) {
            CommonLogger.logger.error("[enrichWithAreaInfo] Error while enriching tariff with area info, tariffId: {}", json.getString("ID"), e);
        }
    }

    /**
     * 构建地区树结构
     */
    private List<JSONObject> buildAreaTree(String tariffRecordId) {
        List<JSONObject> areaTree = new ArrayList<>();
        try {
            EasyQuery query = getReadQuery();
            query.setConvertField(1);

            // 先获取省编码
            EasySQL provinceSql = new EasySQL();
            provinceSql.append("SELECT DISTINCT PROVINCE_CODE as value, PROVINCE_NAME as label FROM ");
            provinceSql.append(Constants.getBusiSchema()).append(".XTY_TARIFF_AREQ");
            provinceSql.append(" WHERE 1 = 1");
            provinceSql.append(" AND PROVINCE_CODE != ''");
            provinceSql.append(" AND TARIFF_RECORD_ID = ?");

            List<JSONObject> provinceList = query.queryForList(provinceSql.getSQL(), new Object[]{tariffRecordId}, new JSONMapperImpl());

            // 获取省下的市信息，组织成树
            for (JSONObject province : provinceList) {
                try {
                    EasySQL areaSql = new EasySQL();
                    areaSql.append("SELECT AREA_CODE as value, AREA_NAME as label FROM ");
                    areaSql.append(Constants.getBusiSchema()).append(".XTY_TARIFF_AREQ");
                    areaSql.append(" WHERE 1 = 1");
                    areaSql.append(" AND TARIFF_RECORD_ID = ?");
                    areaSql.append(" AND PROVINCE_CODE = ?");
                    areaSql.append(" AND TYPE = ?");

                    List<JSONObject> areaList = query.queryForList(
                            areaSql.getSQL(),
                            new Object[]{tariffRecordId, province.getString("value"),Constants.TARIFF_AREA_TYPE_1},
                            new JSONMapperImpl()
                    );

                    province.put("children", areaList);
                } catch (Exception e) {
                    CommonLogger.logger.error("[buildAreaTree] Error building area tree for province: {}", province.getString("value"), e);
                }
            }

            areaTree = provinceList;
        } catch (Exception e) {
            CommonLogger.logger.error("[buildAreaTree] Error building area tree for tariffId: {}", tariffRecordId, e);
        }

        return areaTree;
    }

        /**
     * 将资费信息发送到ES（完全参照TariffInitService.initTariffToEs）
     */
    public Consumer<JSONObject> sendTariffToEs() {
        return json -> {
            try {
                String docId = json.getString("ID");
                if (StringUtils.isBlank(docId)) {
                    CommonLogger.logger.warn("[sendTariffToEs] Document ID为空，跳过处理");
                    return;
                }

                // 先查询现有文档，保留ES特有字段
                JSONObject existingDoc = queryExistingDocument(docId);
                if (existingDoc != null) {
                    // 保留ES特有字段
                    if (existingDoc.containsKey("PUBLIC_VERSIONS")) {
                        json.put("PUBLIC_VERSIONS", existingDoc.get("PUBLIC_VERSIONS"));
                    }
                    if (existingDoc.containsKey("PUBLIC_MONTHS")) {
                        json.put("PUBLIC_MONTHS", existingDoc.get("PUBLIC_MONTHS"));
                    }
                    // 保留规则检查的字段
                    if (existingDoc.containsKey("FIELD_CHECK_RESULT")) {
                        json.put("FIELD_CHECK_RESULT", existingDoc.get("FIELD_CHECK_RESULT"));
                    }
                    if (existingDoc.containsKey("FIELD_CHECK_TIME")) {
                        json.put("FIELD_CHECK_TIME", existingDoc.get("FIELD_CHECK_TIME"));
                    }
                    if (existingDoc.containsKey("FIELD_CHECK_NO")) {
                        json.put("FIELD_CHECK_NO", existingDoc.get("FIELD_CHECK_NO"));
                    }
                    if (existingDoc.containsKey("FIELD_CHECK_NOS")) {
                        json.put("FIELD_CHECK_NOS", existingDoc.get("FIELD_CHECK_NOS"));
                    }
                }

                JSONObject reqParam = new JSONObject() {{
                    put("data", json);
                    put("primary", docId);
                    put("indexName", Constants.XTY_TARIFF_BAK_INFO_INDEX);
                    put("command", Constants.ES_OPERATE_CREATE_DOC);
                }};

                // 请求XTY事件ES操作服务
                IService service = ServiceContext.getService("XTY_EVT_ES_ORDER_OPERATE");
                CommonLogger.logger.info("[sendTariffToEs]请求soa服务创建es文档参数：{}", reqParam.toJSONString());
                JSONObject result = service.invoke(reqParam);
                CommonLogger.logger.info("[sendTariffToEs]请求soa服务创建es文档结果:{}", result.toJSONString());

//                new Thread(() -> {
//                    try {
//
//                        crawlService.updatePublicLibRecord(tariffId);
//                    } catch (Exception e) {
//                        CommonLogger.logger.error("异步执行核查服务失败", e);
//                    }
//                }).start();
            } catch (Exception e) {
                CommonLogger.logger.error("sendTariffToEs error", e);
                throw new RuntimeException(e);
            }
        };
    }


    /**
     * 查询现有ES文档
     */
    private JSONObject queryExistingDocument(String docId) {
        try {
            JSONObject termQuery = new JSONObject();
            termQuery.put("term", new JSONObject().fluentPut("ID.keyword", docId));

            JSONObject queryParams = new JSONObject();
            queryParams.put("query", termQuery);
            queryParams.put("size", 1);

            // 只获取需要保留的字段
            JSONObject sourceFilter = new JSONObject();
            JSONArray includeFields = new JSONArray();
            includeFields.add("PUBLIC_VERSIONS");
            includeFields.add("PUBLIC_MONTHS");
            includeFields.add("FIELD_CHECK_RESULT");
            includeFields.add("FIELD_CHECK_TIME");
            includeFields.add("FIELD_CHECK_NO");
            includeFields.add("FIELD_CHECK_NOS");
            sourceFilter.put("includes", includeFields);
            queryParams.put("_source", sourceFilter);

            JSONObject result = ElasticsearchKit.search(Constants.XTY_TARIFF_BAK_INFO_INDEX, queryParams);

            if (result != null && result.containsKey("hits")) {
                JSONObject hits = result.getJSONObject("hits");
                JSONArray hitArray = hits.getJSONArray("hits");
                if (hitArray != null && hitArray.size() > 0) {
                    return hitArray.getJSONObject(0).getJSONObject("_source");
                }
            }
        } catch (Exception e) {
            CommonLogger.logger.warn("[queryExistingDocument] 查询现有文档失败, docId: {}", docId, e);
        }
        return null;
    }

    /**
     * 获取读取查询对象
     */
    private static EasyQuery getReadQuery() {
        return QueryFactory.getReadQuery();
    }
}
