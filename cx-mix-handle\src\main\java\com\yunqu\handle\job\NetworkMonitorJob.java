package com.yunqu.handle.job;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CacheUtil;
import com.yunqu.handle.base.CommonLogger;
import com.yunqu.handle.base.Constants;
import com.yunqu.handle.util.OkHttpUtil;
import com.yunqu.handle.util.RedisLockUtil;
import com.yunqu.handle.util.SmsMsgUtil;

import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 网络监控定时任务
 * 监控外部接口的可用性，如果在指定时间内连续失败多次，则判定为网络异常
 */
public class NetworkMonitorJob implements Job {
    
    private static final Logger logger = LoggerFactory.getLogger(CommonLogger.getNetworkLogger().getName());
    private static final String LOCK_KEY = "network_monitor_job_lock";
    private static final int LOCK_EXPIRE_TIME = 60 * 30; // 锁过期时间，30分钟
    
    // 失败计数器和时间窗口
    private static final int MAX_FAILURES = 12; // 最大失败次数
    private static final int TIME_WINDOW_MINUTES = 15; // 时间窗口（分钟）
    
    // 使用Redis存储的计数器和时间窗口信息
    private static final String REDIS_KEY_PREFIX = "network_monitor:";
    private static final String FAILURE_COUNT_KEY = REDIS_KEY_PREFIX + "failure_count";
    private static final String LAST_FAILURE_TIME_KEY = REDIS_KEY_PREFIX + "last_failure_time";
    
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        logger.info("开始执行网络监控定时任务");
        
        // 使用分布式锁确保在集群环境中只有一个实例执行任务
        boolean locked = false;
        try {
            // 尝试获取锁
            locked = RedisLockUtil.lock(LOCK_KEY, LOCK_EXPIRE_TIME);
            if (!locked) {
                logger.info("未能获取到分布式锁，跳过本次执行");
                return;
            }
            
            // 检查接口可用性
            checkNetworkStatus();
            
            logger.info("网络监控定时任务执行完成");
            
        } catch (Exception e) {
            logger.error("执行网络监控定时任务异常: " + e.getMessage(), e);
            throw new JobExecutionException(e);
        } finally {
            // 释放锁
            if (locked) {
                RedisLockUtil.unlock(LOCK_KEY);
                logger.debug("已释放分布式锁");
            }
        }
    }
    
    /**
     * 检查网络状态
     */
    private void checkNetworkStatus() {
        try {
            // 构建请求参数
            JSONObject requestParams = new JSONObject();
            requestParams.put("command", "ping"); // 简单的ping命令
            requestParams.put("timestamp", System.currentTimeMillis());
            
            // 发送请求
            String result = OkHttpUtil.getInstance().postSync(
                 Constants.getOutInterface() + "/check/service",
                requestParams.toJSONString(),
                "application/json"
            );
            
            // 请求成功，重置计数器
            resetFailureCounter();
            logger.info("接口访问成功，响应: {}", result);
            
        } catch (Exception e) {
            // 请求失败，增加计数器
            handleFailure(e);
        }
    }
    
    /**
     * 处理失败情况
     */
    private void handleFailure(Exception e) {
        try {
            long currentTime = System.currentTimeMillis();
            
            // 从Redis获取上次失败时间
            String lastTimeStr = CacheUtil.get(LAST_FAILURE_TIME_KEY);
            long lastTime = lastTimeStr != null ? Long.parseLong(lastTimeStr) : 0;
            
            // 检查是否在时间窗口内
            if (currentTime - lastTime > TIME_WINDOW_MINUTES * 60 * 1000) {
                // 超出时间窗口，重置计数器
                resetFailureCounter();
                lastTime = currentTime;
            }
            
            // 更新最后失败时间
            CacheUtil.put(LAST_FAILURE_TIME_KEY, String.valueOf(currentTime));
            
            // 从Redis获取当前失败计数
            String countStr = CacheUtil.get(FAILURE_COUNT_KEY);
            int currentCount = countStr != null ? Integer.parseInt(countStr) : 0;
            
            // 增加失败计数
            currentCount++;
            CacheUtil.put(FAILURE_COUNT_KEY, String.valueOf(currentCount));
            
            logger.warn("接口访问失败，当前失败次数: {}/{}, 错误: {}", currentCount, MAX_FAILURES, e.getMessage());
            
            // 检查是否达到阈值
            if (currentCount >= MAX_FAILURES) {
                // 触发网络异常警报
                triggerNetworkAlert();
                // 重置计数器，避免重复告警
                resetFailureCounter();
            }
        } catch (Exception ex) {
            logger.error("处理失败情况时发生错误: " + ex.getMessage(), ex);
        }
    }
    
    /**
     * 重置失败计数器
     */
    private void resetFailureCounter() {
        try {
            CacheUtil.put(FAILURE_COUNT_KEY, "0");
            CacheUtil.put(LAST_FAILURE_TIME_KEY, "0");
        } catch (Exception e) {
            logger.error("重置失败计数器时发生错误: " + e.getMessage(), e);
        }
    }
    
    /**
     * 触发网络异常警报
     */
    private void triggerNetworkAlert() {
        try {
            logger.error("检测到网络异常！在{}分钟内连续{}次访问接口失败", TIME_WINDOW_MINUTES, MAX_FAILURES);
            SmsMsgUtil.sendMsg( String.format("接口网络异常: 在%d分钟内连续%d次访问接口失败，请检查网络连接和服务器状态", 
                    TIME_WINDOW_MINUTES, MAX_FAILURES),false);
            // 尝试通过备用通道发送告警
            try {
            } catch (Exception e) {
                logger.error("通过备用通道发送告警失败: {}", e.getMessage());
            }
            
        } catch (Exception e) {
            logger.error("触发网络异常警报失败: " + e.getMessage(), e);
        }
    }
    
   
}