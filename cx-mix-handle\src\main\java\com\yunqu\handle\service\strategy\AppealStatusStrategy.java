package com.yunqu.handle.service.strategy;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import com.alibaba.fastjson.JSONObject;


public class AppealStatusStrategy implements OutInterfaceStrategy{


    private static Map<String ,List<String>> appealMap = new HashMap<>();


	static {
		appealMap.put("01", Arrays.asList("00","01","21","02","22")); //当地申诉受理机构审核中
		appealMap.put("02", Arrays.asList("04","24","05","25")); //当地申诉受理机构办理中
		appealMap.put("03", Arrays.asList("06_01","12"));//属地不予受理
		appealMap.put("04", Arrays.asList("06_04")); //用户自行撤诉
		appealMap.put("06", Arrays.asList("06_02","06_03"));
		appealMap.put("13", Arrays.asList("13")); //召回
	}

    @Override
    public JSONObject execute(JSONObject params, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
                JSONObject responseJson = new JSONObject();
                String id = params.getString("id");
                JSONObject appeal = getAppealStatusAndResult(id);
                if (appeal == null) {
                    return createResult(false, "申诉不存在", null);
                }
                String serviceStatus = appeal.getString("SERVICE_STATUS");
                String appealResult = appeal.getString("APPEAL_RESULT");
                String status = StringUtils.isBlank(appealResult) ? serviceStatus : (serviceStatus + "_" + appealResult);
                logger.info("申诉状态：{}",status);
                appealMap.entrySet().forEach(entry -> {
					if (entry.getValue().contains(status)) {
						responseJson.put("status", entry.getKey());
					}
				});
                return createResult(true, "申诉状态信息", responseJson);
    }

    
    @Override
    public void confirm(JSONObject params, HttpServletRequest request, HttpServletResponse response) throws Exception {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'confirm'");
    }

    
    private JSONObject getAppealStatusAndResult (String id) throws SQLException {
        EasySQL sql = new EasySQL();
        sql.append("select t1.SERVICE_STATUS,t1.APPEAL_RESULT from " + getTableName("c_box_appeal_order t1"));
        sql.append(" where 1=1 ");
        sql.append(id," and M_ID =? ",false);
        return getQuery().queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
    }
    
    
}
