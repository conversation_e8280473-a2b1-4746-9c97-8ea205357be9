<!DOCTYPE html>
<html>

<head>
  <title>告知短信查询</title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
  <!-- 基础的 css js 资源 -->
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.4">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.3">
  <style>
    .formBody.el-form {
      padding: 24px;
      height: calc(100% - 72px);
      overflow: auto;
    }

    .footer {
      height: 72px;
      box-sizing: border-box;
      border-top: 1px solid #E8E8E8;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 0 24px;
    }

    .yq-table-page .yq-card .search-form {
      grid-gap: 16px 0;
      grid-template-columns: repeat(6, 1fr);
    }

    .vue-box .el-button+.el-button {
      margin: 0;
    }
  </style>
</head>

<body class="yq-page-full vue-box">
  <div id="system-reg-list" class="flex yq-table-page" v-loading="loading" element-loading-text="加载中..." v-cloak>
    <div class="yq-card">
      <div class="card-header">
        <div class="head-title">告知短信查询</div>
      </div>
      <div class="card-content">
        <el-form class="search-form" ref="searchForm" :model="searchForm" :rules="rules" size="small">
          <el-form-item label="用户号码" prop="phone" style="width: 260px;" label-width="80px">
            <el-input v-model="searchForm.phone" placeholder="请输入" clearable @clear="handleClearPhone" @input="handleChangePhone"></el-input>
          </el-form-item>
          <el-form-item label="订购时间" prop="orderTime" label-width="80px">
            <el-date-picker v-model="searchForm.orderTime" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
              placeholder="请选择" clearable style="width: 100%"></el-date-picker>
          </el-form-item>
          <el-form-item label="省份" prop="province" label-width="50px">
            <el-select v-model="searchForm.province" @change="handleProvinceChange" placeholder="请选择" clearable
              :disabled="groupType === '01' || groupType === '03'">
              <el-option v-for="item in provinceMap" :key="item.PINYIN" :label="item.PROVINCE_NAME"
                :value="item.PROVINCE_NAME"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="地市" prop="city" label-width="50px">
            <el-select v-model="searchForm.city" placeholder="请选择" clearable>
              <el-option v-for="item in cityMap" :key="item.CODE" :label="item.NAME" :value="item.NAME"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="运营商" prop="operator" label-width="50px">
            <el-select v-model=" searchForm.operator" placeholder="请选择" clearable
              :disabled="groupType === '01' || groupType === '02'">
              <el-option v-for="item in entMap" :key="item.CODE" :label="item.NAME" :value="item.NAME"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label-width="0px" style="grid-column: 6; justify-self: flex-end;">
            <el-button type="primary" icon="el-icon-search" @click="getList">搜索</el-button>
            <el-button type="primary" plain icon="el-icon-refresh" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="yq-table">
          <p v-if="payload" style="margin-bottom: 16px;">
            在 <span style="font-weight: bold;">{{payload.orderTime}}</span> 到 <span
              style="font-weight: bold;">{{payload.orderTimejs}}</span> 内，用户
            <span style="font-weight: bold;">{{payload.phone}}</span> 共有 <span
              style="font-weight: bold;">{{tableData.length}}</span> 条资费订单：
          </p>
          <el-table ref="table" :data="tableData" height="100%" stripe border fit style="width: 100%">
            <el-table-column label="序号" type="index" width="80">
            </el-table-column>
            <el-table-column label="用户号码" prop="phone" min-width="180" sortable>
              <template slot-scope="scope">
                {{ isValidPhone(scope.row.phone) ? getMaskedPhone(scope.row.phone) : scope.row.phone }}
              </template>
            </el-table-column>
            <el-table-column v-if="groupType === '04'" label="订购时间" prop="orderTime" min-width="180" sortable>
            </el-table-column>
            <el-table-column label="订购省份" prop="provinceName" min-width="100" sortable>
            </el-table-column>
            <el-table-column label="地市" prop="cityName" min-width="100" sortable>
            </el-table-column>
            <el-table-column v-if="groupType === '04'" label="运营商" prop="operatorName" min-width="100" sortable>
            </el-table-column>
            <el-table-column v-if="groupType === '04'" label="资费名称" prop="messagePackage" min-width="200" show-overflow-tooltip sortable>
            </el-table-column>
            <el-table-column label="订购信息" prop="originalContent" min-width="200" show-overflow-tooltip sortable>
            </el-table-column>
            <el-empty slot="empty" description="暂无信息"></el-empty>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</body>

<script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
<script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
<script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
<script src="/cc-base/static/js/my_i18n.js?v=202111"></script>
<script src="/cc-base/static/js/i18n.js?v=1"></script>
<script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.1"></script>
<script>
  var appPage = new Vue({
    el: '#system-reg-list',
    data: {
      loading: false,
      groupType: '',
      searchForm: {
        province: '',
        city: '',
        operator: '',
        phone: '',  // 存储原始手机号
        orderTime: '',
      },
      payload: null,
      tableData: [],
      rules: {
        province: [{ required: true, message: '请选择省份', trigger: 'change' }],
        // city: [{ required: true, message: '请选择地市', trigger: 'change' }],
        // operator: [{ required: true, message: '请选择运营商', trigger: 'change' }],
        phone: [{ required: true, message: '请输入用户号码', trigger: 'blur' }],
        orderTime: [{ required: true, message: '请选择订购时间', trigger: 'change' }],
      },
      provinceMap: [],
      cityMap: [],
      entMap: [
        { 'CODE': '电信', 'NAME': '电信' },
        { 'CODE': '移动', 'NAME': '移动' },
        { 'CODE': '联通', 'NAME': '联通' },
        { 'CODE': '广电', 'NAME': '广电' }
      ],
    },
    methods: {
      handleReset: function () {
        this.$refs['searchForm'].resetFields()
        this.$set(this.searchForm, 'orderTime', this.initDate())
        // this.getList()
      },
      getList() {
        this.$refs['searchForm'].validate(valid => {
          if (!valid) {
            return false
          }

          const payload = {
            ...this.searchForm,
            orderTime: this.searchForm.orderTime + ' 00:00:00',
            orderTimejs: this.searchForm.orderTime + ' 23:59:59',
          }          

          this.loading = true
          return yq.remoteCall('/cx-mix-tariff/webcall?action=order.orderInfo', payload)
            .then(res => {
              this.payload = payload
              this.tableData = res.data || []
            })
            .finally(() => this.loading = false)
        })
      },
      getDict() {
        /*yq.remoteCall('/cx-mix-tariff/webcall?action=common.ents').then(res => {
          this.entMap = res.data
        })*/
        return yq.remoteCall("/cx-mix-tariff/webcall?action=order.provinceNameDict").then((res) => {
          this.provinceMap = res.data || []
          this.provinceMap.push({
            PROVINCE_NAME: "集团",
            PINYIN: "JT",
          })
        })
      },
      handleProvinceChange(val) {
        this.searchForm.city = ''
        this.getCityMap()
      },
      getCityMap() {
        if (!this.searchForm.province) {
          return false
        }
        let find = this.provinceMap.find(item => item.PROVINCE_NAME === this.searchForm.province)
        if (!find) return false
        const payload = {
          province: find.PINYIN,
        }
        yq.remoteCall('/cx-mix-tariff/webcall?action=order.areaDictByProvinceCode', payload).then((res) => {
          this.cityMap = res.data || []
        })
      },
      initDate() {
        const date = new Date();
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();
        const startDate = new Date(year, month - 1, day, 8).toISOString().slice(0, 10)
        const endDate = new Date(year, month - 1, day, 8).toISOString().slice(0, 10)
        return startDate;
      },
      //获取当前是什么账号类型
      getMessage() {
        yq.remoteCall(
          "/cx-mix-tariff/webcall?action=common.queryUserGroupType",
          {},
          (res) => {
            if (res.state == 1) {
              let provinceCode = res.data.provinceCode
              this.provinceCode = provinceCode;
              this.groupType = res.data.groupType; //账号类型：01-省企业，02-集团，03-省管局，04-工信部,
              if (provinceCode) {
                let find = this.provinceMap.find(i => i.PROVINCE_CODE === provinceCode)
                this.searchForm.province = find && find.PROVINCE_NAME
                this.handleProvinceChange()
              }
              this.searchForm.operator = res.data.entName
            }
          }
        );
      },

      handleChangePhone(phone) {
        this.searchForm.phone = this.getMaskedPhone(phone)
      },

      // 判断是否为有效手机号
      isValidPhone(phone) {
        return phone && /^1[3456789]\d{9}$/.test(phone);
      },
      
      // 获取脱敏手机号
      getMaskedPhone(phone) {
        if (!this.isValidPhone(phone)) {
          return phone;
        }
        return phone.substring(0, 3) + '****' + phone.substring(7);
      },
      
      handleClearPhone() {
        this.searchForm.phone = '';
      },
    },
    mounted() {
      this.getDict().then(res => {
        this.getMessage()
      })
      this.$set(this.searchForm, 'orderTime', this.initDate())
      // this.getList()
    },
  });
</script>

</html>