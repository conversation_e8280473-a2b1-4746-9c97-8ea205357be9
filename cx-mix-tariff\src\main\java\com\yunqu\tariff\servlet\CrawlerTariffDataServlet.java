package com.yunqu.tariff.servlet;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.UserUtil;
import com.yq.busi.common.util.mq.MQBrokerUtil;
import com.yunqu.tariff.base.AppBaseServlet;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.mapper.TariffCrawlRecordRowMapper;
import com.yunqu.tariff.model.TariffCrawlRecord;
import com.yunqu.tariff.util.ExcelExportUtil;
import jodd.util.StringUtil;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;

import javax.servlet.annotation.WebServlet;
import java.sql.SQLException;
import java.util.Date;

/**
 * <p>
 * 资费数据爬取记录导出
 * </p>
 *
 * @ClassName CrawlerTariffDataServlet
 * <AUTHOR> Copy This Tag)
 * @Description TODO 描述文件用途
 * @Since create in 2025/5/27 16:40
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
@WebServlet("/servlet/crawlerTariffData")
public class CrawlerTariffDataServlet extends AppBaseServlet {

    private static final long serialVersionUID = -7771862910161171775L;

    /**
     * 导出资费公示数据
     */
    public void actionForExportTariffData() {
        try {
            JSONObject param = new JSONObject();
            param.put("versionNo", getPara("versionNo"));
            param.put("entCode", getPara("entCode"));
            param.put("crawlerType", getPara("crawlerType"));
            param.put("provinceCode", getPara("provinceCode"));
            param.put("tariffNo", getPara("tariffNo"));
            param.put("tariffName", getPara("tariffName"));

            if (StringUtil.isBlank(param.getString("versionNo"))) {
                CommonLogger.getLogger().error("查询资费公示数据版本号不能为空!");
                Render.renderJson(getRequest(), getResponse(), EasyResult.fail("查询资费公示数据版本号不能为空"));
                return;
            }

            // 获取当前用户信息
            UserModel user = UserUtil.getUser(getRequest());
            String serialId = IdUtil.getSnowflakeNextIdStr();

            // 生成文件名
            String fileName = "资费公示数据_" + param.getString("versionNo");
            if (StringUtil.isNotBlank(param.getString("entCode"))) {
                fileName += "_" + param.getString("entCode");
            }
            if (StringUtil.isNotBlank(param.getString("crawlerType"))) {
                fileName += "_" + param.getString("crawlerType");
            }
            fileName += "_" + EasyDate.getCurrentDateString("yyyyMMdd") + "_" + user.getUserAcc() + "_" + serialId + ".xlsx";

            // 创建导出任务记录
            JSONObject json = new JSONObject();
            json.put("ID", serialId);
            json.put("TASK_CODE", "CrawlerTariffDataExportHandler");
            json.put("TASK_NAME", "资费公示数据导出");
            json.put("CREATOR", user.getUserName());
            json.put("CREATE_ACCT", user.getUserAcc());
            json.put("CREATE_TIME", EasyDate.getCurrentTimeStampString());
            json.put("PARAMS", param.toString());
            json.put("STATUS", 1);
            json.put("FILE_NAME", fileName);

            // 保存任务记录
            EasyRecord record = new EasyRecord(getTableName("xty_tariff_export_task"), "ID");
            record.setColumns(json);
            getQuery().save(record);

            // 发送MQ消息，触发异步导出任务
            json.put("PARAMS", param);
            JSONObject params = new JSONObject();
            params.put("serialId", serialId);
            params.put("command", "CrawlerTariffDataExportHandler");
            params.put("taskObj", json);
            MQBrokerUtil.sendMsg(Constants.TARIFF_NOTIFY_EXPORT_BROKER, params.toString());

            Render.renderJson(getRequest(), getResponse(), EasyResult.ok("创建导出任务成功"));
        } catch (Exception e) {
            CommonLogger.getLogger().error("创建导出任务失败: " + e.getMessage(), e);
            Render.renderJson(getRequest(), getResponse(), EasyResult.fail("创建导出任务失败: " + e.getMessage()));
        }
    }


    /**
     * 导出资费公示数据 (旧版本，同步导出)
     */
    public void actionForExportTariffDataForOld() {
        String versionNo = getPara("versionNo");
        String entCode = getPara("entCode");
        String crawlerType = getPara("crawlerType");
        String provinceCode = getPara("provinceCode");
        String tariffNo = getPara("tariffNo");
        String tariffName = getPara("tariffName");

        if (StringUtil.isBlank(versionNo)) {
            CommonLogger.getLogger().error("查询资费公示数据版本号不能为空!");
            Render.renderJson(getRequest(), getResponse(), EasyResult.fail("查询资费公示数据版本号不能为空"));
            return;
        }

        // 构建基础查询SQL
        EasySQL baseSQL = new EasySQL("select `id`, `task_id`, `province_code`, `province_name`, `ent_code`, `ent_name`, `name`, `tariff_no`, `fees`, `fees_unit`, `exceed_fees`, `other_fees`, `call`, `data`, `data_unit`, `sms`, `orient_traffic`, " +
                "`orient_traffic_unit`, `iptv`, `bandwidth`, `rights`, `other_content`, `online_day`, `offline_day`, `tariff_attr`, `applicable_people`, `valid_period`, `channel`, " +
                "`duration`, `unsubscribe`, `others`, `date_id`, `version_no`, `crawler_type`, `create_time`, `update_time`, `classic_type_one`, `classic_type_two`, " +
                "`tariff_type`, `public_lib_id` from " + getTableName("xty_tariff_crawl_record t") + " where 1=1");

        baseSQL.append(versionNo, "and t.version_no=?");
        baseSQL.append(provinceCode, "and t.province_code=?");
        baseSQL.appendRLike(tariffNo, "and t.tariff_no like ?");
        baseSQL.appendRLike(tariffName, "and t.name like ?");
        baseSQL.append(entCode, "and t.ent_code=?");
        baseSQL.append(crawlerType, "and t.crawler_type=?");

        baseSQL.append("order by t.tariff_province_code asc, t.ent_code asc, t.crawler_type desc");

        // 生成文件名
        String fileName = "资费公示数据_" + versionNo;
        if (StringUtil.isNotBlank(entCode)) {
            fileName += "_" + entCode;
        }
        if (StringUtil.isNotBlank(crawlerType)) {
            fileName += "_" + crawlerType;
        }

        // 使用分页查询导出数据
        ExcelExportUtil.exportExcelWithPaging(
                getResponse(),
                fileName,
                "资费公示数据",
                TariffCrawlRecord.class,
                pageNo -> {
                    try {
                        EasyQuery readQuery = QueryFactory.getTariffQuery();
                        readQuery.setMaxRow(20000);
                        return readQuery.queryForList(baseSQL.getSQL(), baseSQL.getParams(), pageNo, 20000, new TariffCrawlRecordRowMapper());
                    } catch (SQLException e) {
                        throw new RuntimeException(e);
                    }
                }
        );
    }

    /**
     * 重新爬取资费公示数据
     */
    public EasyResult actionForRecrawler() {
        try {
            String taskId = getPara("taskId");
            if (StringUtil.isBlank(taskId)) {
                return EasyResult.fail("任务ID不能为空");
            }

            EasyQuery query = QueryFactory.getTariffQuery();

            // 删除相关的爬取记录
            EasyRecord easyRecord = new EasyRecord(getTableName("xty_tariff_crawl_record"), "task_id");
            easyRecord.setPrimaryValues(taskId);
            query.deleteById(easyRecord);

            // 更新任务状态为待执行
            EasyRecord task = new EasyRecord(getTableName("xty_crawler_task"), "id");
            task.setPrimaryValues(taskId);
            task.set("status", 0);
            task.set("update_time", new Date());
            query.update(task);

            getLogger().info("重新爬取任务已提交，任务ID: " + taskId);
            return EasyResult.ok("重新爬取任务已提交");
        } catch (Exception e) {
            getLogger().error("重新爬取失败: " + e.getMessage(), e);
            return EasyResult.fail("重新爬取失败: " + e.getMessage());
        }
    }


}
