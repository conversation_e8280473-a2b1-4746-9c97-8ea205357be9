package com.yunqu.tariff.handler.search;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.enums.EntEnum;
import com.yunqu.tariff.enums.TariffCheckRuleEnum;
import com.yunqu.tariff.factory.EsQueryAggsBuilder;
import com.yunqu.tariff.factory.EsQueryFactory;
import com.yunqu.tariff.handler.SearchHandler;
import com.yunqu.xty.commonex.kit.ElasticsearchKit;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.yunqu.tariff.base.Constants.AREA_CODE_ALL;
import static com.yunqu.tariff.base.Constants.XTY_TARIFF_BAK_INFO_STAT_INDEX;

/**
 * 报送资费内容情况整体统计
 */
public class TariffReportWholeStatSearchHandler implements SearchHandler<JSONObject> {

    /**
     * 全部（省份/行业）
     */
    private static final String ALL = "999999";

    private JSONObject param;

    public TariffReportWholeStatSearchHandler(JSONObject param) {
        this.param = param;
    }

    /**
     * 公众 + 在售 + 非港澳台 资费
     *
     * @return
     */
    private static JSONObject buildPublicOnSaleDomesticQuery() {
        return new JSONObject()
                .fluentPut("bool", new JSONObject()
                        .fluentPut("must", Arrays.asList(
                                new JSONObject().fluentPut("term", new JSONObject().fluentPut("classic_type_one.keyword", "1")),
                                new JSONObject().fluentPut("term", new JSONObject().fluentPut("tariff_state.keyword", "1"))
                        ))
                        .fluentPut("must_not", Arrays.asList(
                                new JSONObject().fluentPut("term", new JSONObject().fluentPut("classic_type_two.keyword", "4"))
                        ))
                );
    }

    /**
     * 解析聚合结果为接口响应结构
     *
     * @param esResponse ES 返回的 aggregations 结果（JSONObject）
     * @return 标准化响应结构
     */
    public static JSONObject parseDistributionTotalResult(JSONObject esResponse) {
//        logger.info("ProvTariffWholeStatSearchHandler 查询结果 => {}", esResponse);
        JSONObject data = new JSONObject();
        JSONArray statistics = new JSONArray();

        JSONObject aggregations = esResponse.getJSONObject("aggregations");
        if (aggregations == null) {
            logger.error("聚合结果中未找到 aggregations 字段");
            data.put("statistics", statistics);
            data.put("summary", new JSONObject().fluentPut("totalRecords", 0));
            return data;
        }

        // 1. 解析：省份 + 企业 数据
        List<JSONObject> provinceBuckets = getBuckets(aggregations, "group_by_province");
        for (JSONObject provinceBucket : provinceBuckets) {
            String provinceCode = provinceBucket.getString("key");
            String provinceName = getProvinceName(provinceBucket);

            // 1.1 解析该省下各企业数据
            List<JSONObject> entBuckets = getBuckets(provinceBucket, "group_by_ent");
            for (JSONObject entBucket : entBuckets) {
                String entCode = entBucket.getString("key");
                String entName = EntEnum.getEntName(entCode);

                JSONObject stat = buildStatItem(provinceCode, provinceName, entCode, entName, entBucket);
                statistics.add(stat);
            }

            // 1.2 解析该省“全行业”数据
            JSONObject provinceWholeIndustry = provinceBucket.getJSONObject("province_whole_industry");
            if (provinceWholeIndustry != null) {
                JSONObject stat = buildStatItem(
                        provinceCode,
                        provinceName,
                        "999999",  // 全行业
                        "全行业",
                        provinceWholeIndustry
                );
                statistics.add(stat);
            }
        }

        // 2. 解析：全国 + 企业 数据
        List<JSONObject> nationalByEntBuckets = getBuckets(aggregations, "national_by_ent");
        for (JSONObject entBucket : nationalByEntBuckets) {
            String entCode = entBucket.getString("key");
            String entName = EntEnum.getEntName(entCode);

            JSONObject stat = buildStatItem(
                    "999999",          // 全国
                    "全国",
                    entCode,
                    entName,
                    entBucket
            );
            statistics.add(stat);
        }

        // 3. 解析：全国“全行业”数据
        JSONObject nationalWholeIndustry = aggregations.getJSONObject("national_whole_industry");
        if (nationalWholeIndustry != null) {
            JSONObject stat = buildStatItem(
                    "999999",
                    "全国",
                    "999999",
                    "全行业",
                    nationalWholeIndustry
            );
            statistics.add(stat);
        }
        data.put("statistics", statistics);
        data.put("summary", new JSONObject().fluentPut("totalRecords", statistics.size()));
        return data;
    }

    // 提取指标的通用方法
    private static long getMetricValue(JSONObject parent, String metricKey) {
        if (parent == null || !parent.containsKey(metricKey)) {
            return 0L;
        }
        JSONObject metricObj = parent.getJSONObject(metricKey);
        if (metricObj == null) return 0L;

        JSONObject unique = metricObj.getJSONObject("uniqueTariff");
        return unique != null ? unique.getLongValue("value") : metricObj.getLongValue("doc_count");
    }

    // 构建单条统计记录
    private static JSONObject buildStatItem(String provinceCode, String provinceName,
                                            String entCode, String entName,
                                            JSONObject metricsContainer) {
        JSONObject stat = new JSONObject();

        // 提取行头信息
        stat.put("provinceCode", provinceCode);
        stat.put("provinceName", provinceName);
        stat.put("entCode", entCode);
        stat.put("entName", entName);

        stat.put("total", getMetricValue(metricsContainer, "total_tariffs"));

        // 提取指标
        Arrays.stream(TariffCheckRuleEnum.values()).forEach(rule -> {
            stat.put(rule.getRuleNumber(), getMetricValue(metricsContainer, rule.getFieldName() + "_count"));
        });

        return stat;
    }

    // 从聚合结果中提取 buckets
    private static List<JSONObject> getBuckets(JSONObject parent, String aggName) {
        if (parent == null) return new ArrayList<>();
        JSONObject agg = parent.getJSONObject(aggName);
        if (agg == null) return new ArrayList<>();
        JSONArray buckets = agg.getJSONArray("buckets");
        if (buckets == null) return new ArrayList<>();
        return buckets.toJavaList(JSONObject.class);
    }

    private static JSONObject getTopName(String field) {
        return new JSONObject()
                .fluentPut("top_hits", new JSONObject()
                        .fluentPut("size", 1)
                        .fluentPut("_source", new JSONObject()
                                .fluentPut("includes", new String[]{field})));
    }

    private static String getProvinceName(JSONObject provinceBucket) {
        JSONObject hits = provinceBucket.getJSONObject("province_name")
                .getJSONObject("hits");
        if (hits == null) {
            return null;
        }
        JSONArray hitArray = hits.getJSONArray("hits");
        if (hitArray != null && !hitArray.isEmpty()) {
            return hitArray.getJSONObject(0)
                    .getJSONObject("_source")
                    .getString("provinceName");
        }
        return null;
    }

    /**
     * 构建联合去重的 scripted_metric 聚合
     *
     * @param fields 需要用于联合去重的字段名数组（如 ["tariff_no.keyword", "name.keyword"]）
     *               必须不为 null 且长度 > 0 (由调用方保证)
     * @return 表示去重聚合的 JSONObject
     */
    private static JSONObject buildDedupAggregation(String... fields) {
        JSONObject scriptedMetric = new JSONObject();

        // 初始化：创建 HashSet
        scriptedMetric.put("init_script", "state.dedupSet = new HashSet();");

        // 映射脚本：检查字段存在、非空、且值不为 null
        StringBuilder mapScript = new StringBuilder("if (");

        // 字段存在且非空且值不为 null
        String fieldConditions = Arrays.stream(fields)
                .map(field -> String.format(
                        "doc.containsKey('%s') && !doc['%s'].empty && doc['%s'].value != null",
                        field, field, field))
                .collect(Collectors.joining(" && "));
        mapScript.append(fieldConditions);

        mapScript.append(") { ");

        // 拼接 key，使用 String.valueOf 防止 null
        String keyConcat = Arrays.stream(fields)
                .map(field -> String.format("String.valueOf(doc['%s'].value)", field))
                .collect(Collectors.joining(" + '_' + "));
        mapScript.append(String.format("String key = %s; state.dedupSet.add(key); }", keyConcat));

        scriptedMetric.put("map_script", mapScript.toString());

        // ✅ combine_script：直接返回 Set，无需转换
        scriptedMetric.put("combine_script", "return state.dedupSet;");

        // reduce_script：合并所有分片的集合，返回去重总数
        scriptedMetric.put("reduce_script", "def result = new HashSet(); for (s in states) { result.addAll(s); } return result.size();");

        // 封装
        JSONObject dedupAgg = new JSONObject();
        dedupAgg.put("scripted_metric", scriptedMetric);
        return dedupAgg;
    }

    /**
     * 构建所有指标聚合的公共方法（每次返回新对象）
     *
     * @return 包含所有指标（total_tariffs, public_tariffs, ...）的聚合定义
     */
    private static JSONObject buildMetricsAggregations(String... uniqueFields) {
        JSONObject aggs = new JSONObject();

        EsQueryAggsBuilder aggsBuilder = EsQueryAggsBuilder.getInstance();

        if (uniqueFields != null && uniqueFields.length > 0) {
            Arrays.stream(TariffCheckRuleEnum.values()).forEach(rule ->
                    aggs.put(rule.getFieldName() + "_count",
                            aggsBuilder.aggCardinalityFilterTerm(rule.getFieldName() + ".keyword", 1,
                                    new JSONObject().fluentPut("uniqueTariff", buildDedupAggregation(uniqueFields)))));
        } else {
            Arrays.stream(TariffCheckRuleEnum.values()).forEach(rule ->
                    aggs.put(rule.getFieldName() + "_count",
                            aggsBuilder.aggCardinalityFilterTerm(rule.getFieldName() + ".keyword", 1, null)));
        }

        // 资费总数，去重
        aggs.put("total_tariffs", aggsBuilder.aggCardinalityFilterMatchAll("uniqueTariff", buildDedupAggregation("tariffNo.keyword")));
        return aggs;
    }

    private JSONObject buildDistributionTotalQueryParams() {
        // 1. 获取指标聚合（每次调用都是新对象）
        JSONObject metricsAggs = buildMetricsAggregations();

        // 2. 构建：按运营商聚合（省份内各企业）
        JSONObject groupByEnt = EsQueryAggsBuilder.getInstance()
                .aggCardinalityTerms("entCode.keyword", 10, metricsAggs);

        // 3. 构建“省份全行业”聚合（使用 filter + aggs 包装）
        JSONObject provinceWholeIndustry = new JSONObject()
                .fluentPut("filter", new JSONObject().fluentPut("match_all", new JSONObject())) // 合法聚合类型
                .fluentPut("aggs", buildMetricsAggregations()); // 内部放指标

        // 4. 构建：按省份聚合
        JSONObject groupByProvince = new JSONObject()
                .fluentPut("terms", new JSONObject()
                        .fluentPut("field", "provinceCode.keyword")
                        .fluentPut("size", 100))
                .fluentPut("aggs", new JSONObject()
                        .fluentPut("province_name", getTopName("provinceName"))
                        .fluentPut("group_by_ent", groupByEnt)  // 省份 + 运营商
                        .fluentPut("province_whole_industry", provinceWholeIndustry) // 省份全行业
                );

        // 5. 构建：全国各企业维度
        JSONObject nationalByEnt = EsQueryAggsBuilder.getInstance()
                .aggCardinalityTerms("entCode.keyword", 10, buildMetricsAggregations("tariffNo.keyword"));

        // 6. 构建“全国全行业”聚合
        JSONObject nationalWholeIndustry = new JSONObject()
                .fluentPut("filter", new JSONObject().fluentPut("match_all", new JSONObject()))
                .fluentPut("aggs", buildMetricsAggregations("tariffNo.keyword"));

        // 7. 组合根聚合
        JSONObject rootAggregations = new JSONObject();
        rootAggregations.put("group_by_province", groupByProvince);  // 按省份 + 运营商
        rootAggregations.put("national_by_ent", nationalByEnt);      // 全国各企业
        rootAggregations.put("national_whole_industry", nationalWholeIndustry);  // 全国全行业

        // 8. 构建查询
        EsQueryFactory.EsQueryParamBuilder builder = EsQueryFactory.queryParamBuilder()
                .term("versionNos.keyword", param.getString("version")) // 版本号
                .terms("entCode.keyword", param.getString("entCode"))
                .terms("provinceCode.keyword",
                        StringUtils.contains(param.getString("provinceCode"), ALL) ?
                                null : param.getString("provinceCode"))  // 省份代码(如果包含全部则不设置)
                .terms("areaCodes.keyword",
                        StringUtils.contains(param.getString("areaCode"), ALL) ?
                                null : param.getString("areaCode"))  // 省份代码(如果包含全部则不设置)
                .terms("type1.keyword", param.getString("type1"))
                .terms("isPublic.keyword", param.getString("isPublic"))
                .terms("isTelecom.keyword", param.getString("isTelecom"))
                .terms("status.keyword", param.getString("status"))
                .aggs(rootAggregations).size(0);

        //  特殊参数处理
        //  处理适用地区参数
        if (StringUtils.isNotBlank(param.getString("areaCode"))) {
            List<String> areaCodes = Arrays.asList(param.getString("areaCode").split(","));
            builder.boolShouldOr(
                    new JSONObject().fluentPut("terms", new JSONObject().fluentPut("areaCodes", areaCodes)),
                    new JSONObject().fluentPut("term", new JSONObject().fluentPut("areaCodes", AREA_CODE_ALL)));
        }
        // 处理reporter参数
        String reporter = param.getString("reporter");
        if (StringUtils.equalsAny(reporter, EntEnum.getAllEnt())) {
            builder.term("entCode.keyword", reporter);
        } else {
            builder.wildcard("reporter.keyword", reporter);
        }
        JSONObject requestParam = builder.buildRequestParam();
        logger.info("ProvTariffWholeStatSearchHandler 查询参数 => {}", requestParam);
        return requestParam;
    }

    @Override
    public JSONObject search() {
        //1:构建ES查询条件
        JSONObject queryParams = buildDistributionTotalQueryParams();
        //2:执行ES查询
        JSONObject esResult = ElasticsearchKit.search(XTY_TARIFF_BAK_INFO_STAT_INDEX, queryParams);
        //3:解析查询结果
        JSONObject result = parseDistributionTotalResult(esResult);
        return result;
    }
}
