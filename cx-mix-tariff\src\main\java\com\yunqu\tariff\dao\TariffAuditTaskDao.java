package com.yunqu.tariff.dao;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.base.AppDaoContext;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import java.util.List;

/**
 * 稽核任务数据访问层
 *
 * <AUTHOR>
 * @date 2024-12-21
 */
@WebObject(name = "tariffAuditTask")
public class TariffAuditTaskDao extends AppDaoContext {

    /**
     * 查询任务列表
     */
    @WebControl(name = "list", type = Types.LIST)
    public JSONObject list() {
        try {
            // 从param中获取查询参数
            String taskName = param.getString("taskName");
            String provinceCode = param.getString("provinceCode");
            String entCode = param.getString("entCode");
            Integer status = param.getInteger("status");
            String createTimeStart = param.getString("createTimeStart");
            String createTimeEnd = param.getString("createTimeEnd");

            // 构建查询SQL
            EasySQL sql = new EasySQL();
            sql.append("SELECT ID, TASK_NAME, REAL_ZIP_FILE_NAME, PROVINCE_CODE, PROVINCE_NAME, ENT_CODE, ENT_NAME, SALES_FILE_NAME, ORDERS_FILE_NAME, PRODUCT_ID_COLUMN, PRODUCT_NAME_COLUMN, " +
                    "PRODUCT_CODE_COLUMN, ZIP_FILE_SIZE, STATUS, LOCAL_PROCESS_STATUS, SFTP_UPLOAD_STATUS, REMOTE_PROCESS_STATUS, TOTAL_COUNT, UNPUBLIC_COUNT, UNREPORT_COUNT, " +
                    "UNPUBLIC_FILE_NAME, UNREPORT_FILE_NAME, PROCESS_START_TIME, PROCESS_END_TIME, ERROR_MESSAGE, SFTP_REMOTE_FILE_NAME, CREATE_TIME, CREATE_ACC, " +
                    "CREATE_NAME, UPDATE_TIME, ENT_ID, BUSI_ORDER_ID FROM " + getTableName(Constants.AUDIT_TASK_TABLE) + " WHERE 1=1");

            // 添加查询条件
            sql.appendLike(taskName, " AND TASK_NAME LIKE ?");
            sql.append(provinceCode, " AND PROVINCE_CODE = ?");
            sql.append(entCode, " AND ENT_CODE = ?");
            sql.append(status, " AND STATUS = ?");
            sql.append(createTimeStart, " AND CREATE_TIME >= ?");
            sql.append(createTimeEnd, " AND CREATE_TIME <= ?");

            // 添加企业和业务订单过滤
            sql.append(getEntId(), " AND ENT_ID = ?");
            sql.append(getBusiOrderId(), " AND BUSI_ORDER_ID = ?");

            // 排序
            sql.append(" ORDER BY CREATE_TIME DESC");

            return queryForPageList(sql.getSQL(), sql.getParams());

        } catch (Exception e) {
            this.error("查询稽核任务列表失败", e);
            return getJsonResult(null);
        }
    }

    /**
     * 根据ID查询任务详情
     */
    @WebControl(name = "getById", type = Types.RECORD)
    public JSONObject getById() {
        try {
            String taskId = param.getString("taskId");
            String sql = "SELECT * FROM " + getTableName(Constants.AUDIT_TASK_TABLE) + " WHERE ID = ? AND ENT_ID = ? AND BUSI_ORDER_ID = ?";
            return queryForRecord(sql, new Object[]{taskId, getEntId(), getBusiOrderId()}, new JSONMapperImpl());

        } catch (Exception e) {
            this.error("查询任务详情失败", e);
            return getJsonResult(null);
        }
    }

    /**
     * 根据状态查询任务列表（用于定时任务扫描）
     */
    @WebControl(name = "getByStatus", type = Types.RECORD)
    public JSONObject getByStatus() {
        try {
            Integer status = (Integer) this.getMethodParam(0);
            Integer limit = (Integer) this.getMethodParam(1);

            if (status == null) {
                this.error("状态参数不能为空", null);
                return null;
            }

            EasySQL sql = new EasySQL();
            sql.append(status, "SELECT * FROM " + getTableName(Constants.AUDIT_TASK_TABLE) + " WHERE STATUS = ?");
            sql.append(" ORDER BY CREATE_TIME ASC");

            if (limit != null && limit > 0) {
                sql.append(limit, " LIMIT ?");
            }

            List<JSONObject> list = getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

            JSONObject result = new JSONObject();
            result.put("list", list);
            result.put("count", list.size());

            return result;

        } catch (Exception e) {
            this.error("根据状态查询任务失败", e);
            return null;
        }
    }

    /**
     * 更新任务状态
     */
    @WebControl(name = "updateStatus", type = Types.RECORD)
    public JSONObject updateStatus() {
        try {
            String taskId = (String) this.getMethodParam(0);
            Integer status = (Integer) this.getMethodParam(1);
            String errorMessage = (String) this.getMethodParam(2);

            if (StringUtils.isBlank(taskId) || status == null) {
                this.error("任务ID和状态不能为空", null);
                return null;
            }

            EasySQL sql = new EasySQL();
            sql.append(status, "UPDATE " + getTableName(Constants.AUDIT_TASK_TABLE) + " SET STATUS = ?, UPDATE_TIME = NOW()");

            if (StringUtils.notBlank(errorMessage)) {
                sql.append(errorMessage, ", ERROR_MESSAGE = ?");
            }

            sql.append(taskId, " WHERE ID = ?");

            int result = getQuery().executeUpdate(sql.getSQL(), sql.getParams());

            JSONObject response = new JSONObject();
            response.put("success", result > 0);
            response.put("affectedRows", result);

            return response;

        } catch (Exception e) {
            this.error("更新任务状态失败", e);
            return null;
        }
    }

    /**
     * 更新处理状态
     */
    @WebControl(name = "updateProcessStatus", type = Types.RECORD)
    public JSONObject updateProcessStatus() {
        try {
            String taskId = (String) this.getMethodParam(0);
            String statusType = (String) this.getMethodParam(1); // local, sftp, remote
            Integer status = (Integer) this.getMethodParam(2);

            if (StringUtils.isBlank(taskId) || StringUtils.isBlank(statusType) || status == null) {
                this.error("参数不能为空", null);
                return null;
            }

            String columnName;
            switch (statusType.toLowerCase()) {
                case "local":
                    columnName = "LOCAL_PROCESS_STATUS";
                    break;
                case "sftp":
                    columnName = "SFTP_UPLOAD_STATUS";
                    break;
                case "remote":
                    columnName = "REMOTE_PROCESS_STATUS";
                    break;
                default:
                    this.error("无效的状态类型: " + statusType, null);
                    return null;
            }

            String sql = "UPDATE " + getTableName(Constants.AUDIT_TASK_TABLE) +
                    " SET " + columnName + " = ?, UPDATE_TIME = NOW() WHERE ID = ?";

            int result = getQuery().executeUpdate(sql, new Object[]{status, taskId});

            JSONObject response = new JSONObject();
            response.put("success", result > 0);
            response.put("affectedRows", result);

            return response;

        } catch (Exception e) {
            this.error("更新处理状态失败", e);
            return null;
        }
    }

    /**
     * 更新统计信息
     */
    @WebControl(name = "updateStatistics", type = Types.RECORD)
    public JSONObject updateStatistics() {
        try {
            String taskId = (String) this.getMethodParam(0);
            Integer totalCount = (Integer) this.getMethodParam(1);
            Integer unpublicCount = (Integer) this.getMethodParam(2);
            Integer unreportCount = (Integer) this.getMethodParam(3);
            String unpublicFilePath = (String) this.getMethodParam(4);
            String unreportFilePath = (String) this.getMethodParam(5);

            if (StringUtils.isBlank(taskId)) {
                this.error("任务ID不能为空", null);
                return null;
            }

            EasySQL sql = new EasySQL();
            sql.append("UPDATE " + getTableName(Constants.AUDIT_TASK_TABLE) + " SET UPDATE_TIME = NOW()");

            if (totalCount != null) {
                sql.append(totalCount, ", TOTAL_COUNT = ?");
            }
            if (unpublicCount != null) {
                sql.append(unpublicCount, ", UNPUBLIC_COUNT = ?");
            }
            if (unreportCount != null) {
                sql.append(unreportCount, ", UNREPORT_COUNT = ?");
            }
            if (StringUtils.notBlank(unpublicFilePath)) {
                sql.append(unpublicFilePath, ", UNPUBLIC_FILE_PATH = ?");
            }
            if (StringUtils.notBlank(unreportFilePath)) {
                sql.append(unreportFilePath, ", UNREPORT_FILE_PATH = ?");
            }

            sql.append(taskId, " WHERE ID = ?");

            int result = getQuery().executeUpdate(sql.getSQL(), sql.getParams());

            JSONObject response = new JSONObject();
            response.put("success", result > 0);
            response.put("affectedRows", result);

            return response;

        } catch (Exception e) {
            this.error("更新统计信息失败", e);
            return null;
        }
    }

    /**
     * 更新处理时间
     */
    @WebControl(name = "updateProcessTime", type = Types.RECORD)
    public JSONObject updateProcessTime() {
        try {
            String taskId = (String) this.getMethodParam(0);
            String startTime = (String) this.getMethodParam(1);
            String endTime = (String) this.getMethodParam(2);

            if (StringUtils.isBlank(taskId)) {
                this.error("任务ID不能为空", null);
                return null;
            }

            EasySQL sql = new EasySQL();
            sql.append("UPDATE " + getTableName(Constants.AUDIT_TASK_TABLE) + " SET UPDATE_TIME = NOW()");

            if (StringUtils.notBlank(startTime)) {
                sql.append(startTime, ", PROCESS_START_TIME = ?");
            }
            if (StringUtils.notBlank(endTime)) {
                sql.append(endTime, ", PROCESS_END_TIME = ?");
            }

            sql.append(taskId, " WHERE ID = ?");

            int result = getQuery().executeUpdate(sql.getSQL(), sql.getParams());

            JSONObject response = new JSONObject();
            response.put("success", result > 0);
            response.put("affectedRows", result);

            return response;

        } catch (Exception e) {
            this.error("更新处理时间失败", e);
            return null;
        }
    }

    /**
     * 更新文件路径信息
     */
    @WebControl(name = "updateFilePaths", type = Types.RECORD)
    public JSONObject updateFilePaths() {
        try {
            String taskId = (String) this.getMethodParam(0);
            String zipFilePath = (String) this.getMethodParam(1);
            String extractPath = (String) this.getMethodParam(2);
            String sftpRemotePath = (String) this.getMethodParam(3);

            if (StringUtils.isBlank(taskId)) {
                this.error("任务ID不能为空", null);
                return null;
            }

            EasySQL sql = new EasySQL();
            sql.append("UPDATE " + getTableName(Constants.AUDIT_TASK_TABLE) + " SET UPDATE_TIME = NOW()");

            if (StringUtils.notBlank(zipFilePath)) {
                sql.append(zipFilePath, ", ZIP_FILE_PATH = ?");
            }
            if (StringUtils.notBlank(extractPath)) {
                sql.append(extractPath, ", EXTRACT_PATH = ?");
            }
            if (StringUtils.notBlank(sftpRemotePath)) {
                sql.append(sftpRemotePath, ", SFTP_REMOTE_PATH = ?");
            }

            sql.append(taskId, " WHERE ID = ?");

            int result = getQuery().executeUpdate(sql.getSQL(), sql.getParams());

            JSONObject response = new JSONObject();
            response.put("success", result > 0);
            response.put("affectedRows", result);

            return response;

        } catch (Exception e) {
            this.error("更新文件路径失败", e);
            return null;
        }
    }


    @Override
    protected void setQuery(EasyQuery query) {
        super.setQuery(QueryFactory.getTariffQuery());
    }

    @Override
    protected EasyQuery getQuery() {
        return QueryFactory.getTariffQuery();
    }
}
