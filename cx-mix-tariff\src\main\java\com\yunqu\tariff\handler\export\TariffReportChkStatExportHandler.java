package com.yunqu.tariff.handler.export;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.handler.SearchHandlerFactory;
import com.yunqu.tariff.utils.ExcelUtil;
import com.yunqu.tariff.utils.excel.CellStyleUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.MapRowMapperImpl;

import java.io.File;
import java.io.OutputStream;
import java.nio.file.Files;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class TariffReportChkStatExportHandler extends BaseExportHandler {

    // Excel表头定义
    private List<String> EXCEL_HEADERS;
    // Excel字段定义
    private List<String> EXCEL_FIELDS;


    @Override
    protected String excuete(JSONObject taskObject) {
        try {
            logger.info("开始执行导出任务:{}", taskObject);
            JSONObject param = taskObject.getJSONObject("PARAMS");
            String taskCode = taskObject.getString("TASK_CODE");
            return excuetExport(param, taskObject.getString("FILE_NAME"), taskCode);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    protected String getFileName(String userAcct) {
        return "";
    }

    /**
     * 导出资费报表的主要方法
     *
     * @param param    导出参数
     * @param fileName 文件名
     * @param taskCode 任务代码，用于区分不同类型的导出
     * @throws Exception 如果导出过程中发生异常
     */
    public String excuetExport(JSONObject param, String fileName, String taskCode) throws Exception {
        long startTime = System.currentTimeMillis();
        logger.info("开始导出检查结果统计报表... taskCode={}", taskCode);
        File file = initFileObj(fileName); // 初始化导出文件对象
        if (CollectionUtils.isEmpty(EXCEL_HEADERS)) {
            initExcelHeaderField();
        }
        try (OutputStream os = Files.newOutputStream(file.toPath())) {
            ExcelWriter excelWriter = null;
            try {
                excelWriter = EasyExcel.write(os)
                        .head(ExcelUtil.formatHeader(EXCEL_HEADERS, "检查结果统计"))
                        .registerWriteHandler(CellStyleUtil.getHorizontalCellStyleStrategy())
                        .build();
                WriteSheet sheet = EasyExcel.writerSheet("检查结果统计").build();

                // 流式查询并写入活跃数据
                exportDataByStatus(param, excelWriter, sheet);
            } finally {
                // 确保ExcelWriter正确关闭
                if (excelWriter != null) {
                    excelWriter.finish();
                }
            }
            logger.info("检查结果统计报表导出完成,耗时:{}ms", System.currentTimeMillis() - startTime);
            return file.getPath();
        } catch (Exception e) {
            logger.error("检查结果统计报表异常", e);
            throw new Exception("导出失败:" + e.getMessage());
        }
    }

    /**
     * 流式导出数据
     */
    private void exportDataByStatus(JSONObject param, ExcelWriter excelWriter, WriteSheet writeSheet) throws Exception {

//        int pageIndex = 1;
//        int total = 0;

//        while (true) {
//            param.put("pageIndex", String.valueOf(pageIndex));

        // 根据状态获取不同的查询处理器
        List<JSONObject> search = SearchHandlerFactory.getTariffReportChkStatSearchHandler(param).search();
        TypeReference<List<Map<String, String>>> typeReference = new TypeReference<List<Map<String, String>>>() {
        };
        List<Map<String, String>> records = JSON.parseObject(JSON.toJSONString(search), typeReference);
//            if (records == null || records.isEmpty()) {
//                break;
//            }

        // 转换并写入数据
        List<List<Object>> rows = convertToExcelRows(records);
        excelWriter.write(rows, writeSheet);

//            total += records.size();
//            pageIndex++;

        // 每1000条记录输出一次日志
//            if (total % 1000 == 0) {
//                logger.info("已处理{}条记录", total);
//            }
//        }
        logger.info("数据导出完成,共{}条记录", rows.size());
    }

    /**
     * 转换数据为Excel行格式
     */
    private List<List<Object>> convertToExcelRows(List<Map<String, String>> records) {
        List<List<Object>> rows = new ArrayList<>(records.size());
        if (CollectionUtils.isEmpty(EXCEL_FIELDS)) {
            initExcelHeaderField();
        }
        for (Map<String, String> record : records) {
            List<Object> row = new ArrayList<>();
            // 按字段顺序添加数据
            EXCEL_FIELDS.forEach(field -> row.add(record.get(field)));
            rows.add(row);
        }
        return rows;
    }

    private void initExcelHeaderField() {
        EasySQL sqlBuilder = new EasySQL();
        sqlBuilder.append("SELECT RULE_NO, RULE_DESC FROM ").append(Constants.getBusiSchema()).append(".xty_tariff_check_rule order by RULE_NO");

        // 执行查询
        EasyQuery query = QueryFactory.getWriteQuery();
        List<Map<String, String>> rules = new ArrayList<>();
        try {
            rules = query.queryForList(sqlBuilder.getSQL(), sqlBuilder.getParams(), new MapRowMapperImpl());
        } catch (SQLException e) {
            logger.error("未找到规则配置");
            throw new RuntimeException(e);
        }
        if (rules == null || rules.isEmpty()) {
            logger.error("未找到规则配置");
            throw new RuntimeException("未找到规则配置");
        }
        LinkedHashMap<String, String> rulesMap = CollectionUtils.emptyIfNull(rules).stream().collect(
                Collectors.toMap(it -> it.get("RULE_NO"), it -> it.get("RULE_DESC"),
                        (a, b) -> a, LinkedHashMap::new));

        List<String> headers = new ArrayList<>();
        List<String> fields = new ArrayList<>();
        headers.add("公示版本");
        fields.add("version");
        headers.add("资费总数");
        fields.add("total");
        rulesMap.forEach((k, v) -> {
            fields.add(k);
            headers.add(v);
        });
        this.EXCEL_FIELDS = fields;
        this.EXCEL_HEADERS = headers;
        logger.info("初始化检查结果统计报表字段成功, EXCEL_FIELDS => " + JSON.toJSONString(EXCEL_FIELDS) +
                ", EXCEL_HEADERS => " + JSON.toJSONString(EXCEL_HEADERS));
    }
}
