package com.yunqu.handle.servlet;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.handle.base.CommonLogger;
import com.yunqu.handle.base.Constants;
import com.yunqu.handle.service.OutInterfaceService;
import com.yunqu.handle.service.strategy.AppealStatusStrategy;
import com.yunqu.handle.service.strategy.CancelAppealStrategy;
import com.yunqu.handle.service.strategy.MediateStrategy;
import com.yunqu.handle.service.strategy.OutInterfaceStrategy;

import org.easitline.common.utils.string.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 对外接口服务
 * 支持：
 * 1. 拉取工单（获取c_box_appeal未同步的数据）
 * 2. 文件获取（根据业务busiId获取c_cf_attachment中的记录并返回文件流）
 */
@WebServlet("/out/interface")
public class OutInterfaceServlet  extends HttpServlet {

    private static Logger logger = LoggerFactory.getLogger(CommonLogger.getLogger("interface").getName());
    
    private static final long serialVersionUID = 1L;
    private static final Map<String, OutInterfaceStrategy> strategyMap = new HashMap<>();
    
    static {
        // 注册策略
        strategyMap.put("cancel", new CancelAppealStrategy());
        strategyMap.put("mediate", new MediateStrategy());
        strategyMap.put("appealStatus", new AppealStatusStrategy());
    }

    public void doPost(HttpServletRequest req, HttpServletResponse resp) {
        // 获取请求参数
        JSONObject requestData = getRequestBodyData(req);
        String serialId = requestData.getString("serialId");
        try {
            logger.info("[OutInterfaceServlet] requestData:{}", JSONObject.toJSONString(requestData));
            // 验证签名
            if (!verifySignature(requestData)) {
                logger.error("签名验证失败");
                writeResponse(serialId,resp,createResult(false,"签名验证失败"));
                return;
            }
            // 获取动作
            String command = requestData.getString("command");
            if (StringUtils.isBlank(command)) {
                logger.error("动作不能为空");
                writeResponse(serialId,resp,createResult(false,"动作不能为空"));
                return;
            }
            // 根据动作选择策略
            OutInterfaceStrategy strategy = strategyMap.get(command);
            if (strategy == null) {
                logger.error("不支持的动作: " + command);
                writeResponse(serialId,resp,createResult(false,"不支持的动作: " + command));
                return;
            }
            // 执行策略
            OutInterfaceService service = new OutInterfaceService(strategy);
            JSONObject result = service.execute(requestData, req,resp);
            
            logger.info ("[OutInterfaceServlet] command:{} result:{}", command, JSONObject.toJSONString(result));
            if (!resp.isCommitted()) {
                writeResponse(serialId,resp,result);
            }
        } catch (Exception e) {
            logger.error("处理请求失败: " + e.getMessage(), e);
            try {
                writeResponse(serialId,resp,createResult(false,"处理请求失败: " + e.getMessage()));
            } catch (Exception ex) {
                logger.error("返回错误信息失败: " + ex.getMessage(), ex);
            }
        }
    }
    
    /**
     * 验证请求签名
     * @param requestData 请求数据
     * @return 验证结果
     */
    private boolean verifySignature(JSONObject requestData) {
        try {
            String signature = requestData.getString("signature");
            // 验证签名参数
            if (StringUtils.isBlank(signature)) {
                return false;
            }
            // 获取密钥
            String secretKey = Constants.getSysSecretKey();
            if (StringUtils.isBlank(secretKey)) {
                logger.error("系统密钥未配置");
                return false;
            }
            // 计算签名
            String calculatedSignature = calculateSignature(requestData, secretKey);
            // 比较签名
            return signature.equalsIgnoreCase(calculatedSignature);
        } catch (Exception e) {
            logger.error("验证签名失败: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 计算签名
     * @param requestData 请求数据
     * @param secretKey 密钥
     * @return 签名值
     */
    private String calculateSignature(JSONObject requestData, String secretKey) {
        try {
            // 使用TreeMap按参数名称降序排列
            TreeMap<String, Object> sortedParams = new TreeMap<>((a, b) -> b.compareTo(a));
            
            // 添加所有参数（除了signature）
            for (String key : requestData.keySet()) {
                if (!"signature".equals(key)) {
                    Object value = requestData.get(key);
                    if (value != null) {
                        sortedParams.put(key.toUpperCase(), value.toString());
                    }
                }
            }
            
            // 拼接参数名和值
            StringBuilder signStr = new StringBuilder();
            for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
                signStr.append(entry.getKey()).append(entry.getValue());
            }
            // 拼接密钥
            signStr.append(secretKey);
            logger.info("签名字符串: {}", signStr.toString());
            // 计算SHA256
            return encryptBySHA256(signStr.toString());
        } catch (Exception e) {
            logger.error("计算签名失败: " + e.getMessage(), e);
            return "";
        }
    }
    
    /**
     * SHA256加密
     * @param text 待加密文本
     * @return 加密结果
     */
    private String encryptBySHA256(String text) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            md.update(text.getBytes("UTF-8"));
            byte[] digest = md.digest();
            
            // 转换为16进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            logger.error("SHA256加密失败: " + e.getMessage(), e);
            return "";
        }
    }
    
    /**
     * 写入响应
     * @param result 响应结果
     * @throws Exception 异常
     */
    private void writeResponse(String serialId,HttpServletResponse response,JSONObject result) throws Exception {
        result.put("serialId", serialId);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(result.toJSONString());
    }
    protected String getRequestBodyDataStr(HttpServletRequest request) throws Exception {

        BufferedReader br = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));
        String line = null;
        StringBuilder sb = new StringBuilder();
        while ((line = br.readLine()) != null) {
            sb.append(line);
        }
        br.close();
        String paramStr = sb.toString();
        logger.info("请求参数: {}", paramStr);
        return paramStr;
    }

    protected JSONObject getRequestBodyData(HttpServletRequest request)  {
        JSONObject data = null;
        try {
            data = JSONObject.parseObject(getRequestBodyDataStr(request));
        } catch (Exception e) {
            logger.error("解析请求参数失败: " + e.getMessage(), e);
        }
        if (data == null) {
            data = new JSONObject();
        }
        return data;
    }

    private JSONObject createResult(boolean success, String message) {
        JSONObject result = new JSONObject();
        result.put("success", success);
        result.put("message", message);
        return result;
    }
}
