package com.yunqu.tariff.inf.tariff;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.util.ZipUtil;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.enums.EntEnum;
import com.yunqu.tariff.handler.export.TariffRecordExportHandler;
import com.yunqu.tariff.thread.ThreadPoolManager;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 报送库资费按报送主体导出
 */
public class TariffReportServiceExecutor implements TariffServiceExecutor {

    private static final Logger logger = LoggerFactory.getLogger(CommonLogger.getJobLogger().getName());

    /**
     * 导出报送资费字段
     */
    private static final List<String> SOURCE_FIELDS = Arrays.asList("REPORT_NO", "SEQ_NO", "REPORTER_NAME", "TYPE1", "TYPE2",
            "IS_TELECOM", "NAME", "FEES", "FEES_UNIT", "EXTRA_FEES", "OTHER_FEES", "CALL_NUM", "DATA_NUM", "DATA_UNIT",
            "SMS_NUM", "ORIENT_TRAFFIC", "ORIENT_TRAFFIC_UNIT", "IPTV", "BANDWIDTH", "RIGHTS", "OTHER_CONTENT",
            "TARIFF_ATTR", "APPLICABLE_PEOPLE", "AREA_DESC", "VALID_PERIOD", "CHANNEL", "DURATION", "UNSUBSCRIBE",
            "RESPONSIBILITY", "ONLINE_DAY", "OFFLINE_DAY", "OTHERS", "CREATE_TIME", "CREATE_ACC");

    private static final String PUSH_FILE_URL = Constants.XTY_PUBLIC_HOST + "/cx-mix-public/tariff/report/file";


    private String schema;

    public JSONObject execute(String serviceCode, JSONObject param) {
        try {
            String entId = Constants.getEntId();
            schema = SchemaService.findSchemaByEntId(entId);
            String date = param.getString("date");
            String provinceCode = param.getString("provinceCode");
            String tariffProvinceCode = param.getString("tariffProvinceCode");
            String entCode = param.getString("entCode");
            if (StringUtils.isBlank(date)) {
                EasyCalendar easyCalendar = EasyCalendar.newInstance();
                easyCalendar.add(Calendar.DAY_OF_MONTH, -1);
                date = easyCalendar.getDateString("");
            }
            String finalDate = date;
            logger.info("报送库资费导出开始，导出日期--->{},provinceCode={},entCode={}", finalDate, provinceCode, entCode);
            EasyQuery query = getQuery();
            EasySQL provinceSql = new EasySQL("select * from " + Constants.getBusiSchema() + ".xty_tariff_province where 1=1 ");
            provinceSql.append("and TARIFF_PROVINCE_CODE <> '' and TARIFF_PROVINCE_CODE is not null ");
            if (StringUtils.isNotBlank(provinceCode)) {
                provinceSql.append(provinceCode, "and PROVINCE_CODE = ?");
            }
            if (StringUtils.isNotBlank(tariffProvinceCode)) {
                provinceSql.append(tariffProvinceCode, "and TARIFF_PROVINCE_CODE = ?");
            }
            List<JSONObject> jsonObjects = query.queryForList(provinceSql.getSQL(), provinceSql.getParams(), new JSONMapperImpl());
            jsonObjects.forEach(o -> analyzeProvinceData(o, finalDate, entCode));
            return EasyResult.ok();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return EasyResult.error(999, "系统执行异常：" + e.getMessage());
        }
    }

    /**
     * 处理省数据
     *
     * @param provinceData
     * @param date
     * @param entCode
     */
    private void analyzeProvinceData(JSONObject provinceData, String date, String entCode) {
        String provinceCode = provinceData.getString("PROVINCE_CODE");
        String tariffProvinceCode = provinceData.getString("TARIFF_PROVINCE_CODE");
        String[] arr = StringUtils.isBlank(entCode) ? EntEnum.getAllEnt() : new String[]{entCode};

        // 异步处理各企业的数据
        Arrays.stream(arr).parallel().forEach(entType -> {
            CompletableFuture<String> entTypeDataFuture = CompletableFuture.supplyAsync(() ->
                            new TariffRecordExportHandler().actionForExport(
                                    new JSONObject()
                                            .fluentPut("fields", StringUtils.join(SOURCE_FIELDS, ","))
                                            .fluentPut("reporter", tariffProvinceCode + entType),
                                    tariffProvinceCode + "_" + entType + "_" + date + ".xlsx")
                    , ThreadPoolManager.getExecutorService());
            String filePath = null;
            try {
                filePath = entTypeDataFuture.get();
            } catch (Exception e) {
                throw new RuntimeException("报送省份【" + provinceCode + "】，企业" + Arrays.toString(arr) + "时，数据获取异常：" + e.getMessage(), e);
            }
            if (StringUtils.isNotBlank(filePath)) {
                String result = handleSingleFile(filePath, tariffProvinceCode, entType, date);
                if (StringUtils.isNotBlank(result)) {
                    logger.info("[{}{}]数据处理完成，结果：{}", tariffProvinceCode, entType, result);
                } else {
                    logger.error("[{}{}]数据处理失败，结果：{}", tariffProvinceCode, entType, result);
                }
            } else {
                logger.info("[{}{}]无需上传数据", tariffProvinceCode, entType);
            }
        });
    }

    private String handleSingleFile(String filePath, String tariffProvinceCode, String entType, String date) {
        // 压缩当前文件到同名zip中
        File source = new File(filePath);
        File zipFile = new File(source.getParent(), source.getName().replaceFirst("\\.[^.]+$", "") + ".zip");
        try (FileOutputStream os = new FileOutputStream(zipFile)) {
            ZipUtil.toZip(os, Collections.singletonList(filePath));
            source.delete();
            os.flush();
        } catch (IOException e) {
            logger.error("文件压缩失败, file={}, msg={}", filePath, e.getMessage(), e);
            return null;
        }

        Map<String, Object> formData = new HashMap<>();
        formData.put("file", zipFile);
        formData.put("date", date);

        logger.info("报送库资费导出 开始推送文件到SFTP服务器: url={}, filePath={}, date={}", PUSH_FILE_URL, zipFile.getAbsolutePath(), date);
        String body = HttpUtil.createPost(PUSH_FILE_URL)
                .form(formData)
                .execute().body();
        logger.info("推送文件到SFTP服务器完成: url={}, date={}, result={}", PUSH_FILE_URL, date, body);

        JSONObject result = JSONObject.parseObject(body);
        int state = result.getIntValue("state");
        if (state == 1) {
            return result.getString("data");
        } else {
            return null;
        }
    }
}
