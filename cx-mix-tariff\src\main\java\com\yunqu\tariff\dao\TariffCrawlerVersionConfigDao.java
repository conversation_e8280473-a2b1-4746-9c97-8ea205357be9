package com.yunqu.tariff.dao;



import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.base.AppDaoContext;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.QueryFactory;
import jodd.util.StringUtil;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;



import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Calendar;

/**
 * 爬虫版本生成配置DAO
 *
 * @ClassName TariffCrawlerVersionConfigDao
 * <AUTHOR> Copy This Tag)
 * @Description 爬虫版本生成配置相关数据访问层
 * @Since create in 2025/1/28 15:00
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
@WebObject(name = "tariffCrawlerVersionConfig")
public class TariffCrawlerVersionConfigDao extends AppDaoContext {

    /**
     * 获取配置列表（分页）
     * @return 配置列表分页数据
     */
    @WebControl(name = "getConfigList", type = Types.PAGE)
    public JSONObject getConfigList() {
        try {
            EasySQL sql = new EasySQL("SELECT * FROM " + getTableName("xty_crawler_version_config") + " WHERE 1=1");

            // 添加查询条件
            String configName = param.getString("configName");
            String configType = param.getString("configType");
            String isEnabled = param.getString("isEnabled");

            if (StringUtil.isNotBlank(configName)) {
                sql.append("%" + configName + "%", " AND CONFIG_NAME LIKE ?");
            }
            if (StringUtil.isNotBlank(configType)) {
                sql.append(configType, " AND CONFIG_TYPE = ?");
            }
            if (StringUtil.isNotBlank(isEnabled)) {
                sql.append(isEnabled, " AND IS_ENABLED = ?");
            }

            sql.append(" ORDER BY PRIORITY_ORDER ASC, CREATE_TIME DESC");

            return queryForPageList(sql.getSQL(), sql.getParams());

        } catch (Exception e) {
            CommonLogger.getLogger().error("获取配置列表失败: " + e.getMessage(), e);
            return EasyResult.fail("获取配置列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取配置详情
     * @return 配置详情信息
     */
    @WebControl(name = "getConfigDetail", type = Types.RECORD)
    public JSONObject getConfigDetail() {
        try {
            String configId = param.getString("ID");

            if (StringUtil.isBlank(configId)) {
                return EasyResult.fail("配置ID不能为空");
            }

            EasySQL sql = new EasySQL("SELECT * FROM " + getTableName("xty_crawler_version_config"));
            sql.append(configId, " WHERE ID = ?");

            JSONObject result = queryForRecord(sql.getSQL(), sql.getParams());
            if (result == null) {
                return EasyResult.fail("配置信息不存在");
            }

            return EasyResult.ok(result);

        } catch (Exception e) {
            CommonLogger.getLogger().error("获取配置详情失败: " + e.getMessage(), e);
            return EasyResult.fail("获取配置详情失败: " + e.getMessage());
        }
    }



    /**
     * 检查当日是否需要生成版本任务
     * @return 检查结果
     */
    @WebControl(name = "checkTodayVersionGenerate", type = Types.RECORD)
    public JSONObject checkTodayVersionGenerate() {
        try {
            String checkDate = param.getString("checkDate");
            if (StringUtil.isBlank(checkDate)) {
                // 如果没有指定日期，使用当前日期
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                checkDate = sdf.format(new Date());
            }

            // 解析检查日期，获取当月的日期（几号）
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date date = sdf.parse(checkDate);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH); // 获取当月的第几天


            // 查询启用的配置，匹配当前日期
            EasySQL sql = new EasySQL("SELECT " +
                "ID, CONFIG_NAME, RULE_VALUE, PRIORITY_ORDER " +
                "FROM " + getTableName("xty_crawler_version_config") +
                " WHERE IS_ENABLED = 'Y' AND RULE_VALUE = ?");

            // 添加有效期过滤和当前日期匹配
            sql.append(String.valueOf(dayOfMonth), " AND (EFFECTIVE_START_DATE IS NULL OR EFFECTIVE_START_DATE <= ?)");
            sql.append(checkDate, " AND (EFFECTIVE_END_DATE IS NULL OR EFFECTIVE_END_DATE >= ?)");
            sql.append(checkDate, " ORDER BY PRIORITY_ORDER ASC");

            JSONObject configListResult = queryForList(sql.getSQL(), sql.getParams());

            List<JSONObject> matchedConfigs = new ArrayList<>();
            if (configListResult != null && configListResult.containsKey("data")) {
                Object dataObj = configListResult.get("data");
                if (dataObj instanceof List) {
                    matchedConfigs = (List<JSONObject>) dataObj;
                }
            }

            boolean needGenerate = !matchedConfigs.isEmpty();

            JSONObject finalResult = new JSONObject();
            finalResult.put("checkDate", checkDate);
            finalResult.put("dayOfMonth", dayOfMonth);
            finalResult.put("needGenerate", needGenerate);
            finalResult.put("matchedConfigs", matchedConfigs);
            finalResult.put("totalConfigs", matchedConfigs.size());
            finalResult.put("matchedCount", matchedConfigs.size());

            return EasyResult.ok(finalResult);

        } catch (Exception e) {
            CommonLogger.getLogger().error("检查当日版本生成失败: " + e.getMessage(), e);
            return EasyResult.fail("检查当日版本生成失败: " + e.getMessage());
        }
    }

    /**
     * 检查日期配置冲突
     * @return 冲突检查结果
     */
    @WebControl(name = "checkDateConflict", type = Types.RECORD)
    public JSONObject checkDateConflict() {
        try {
            String ruleValue = param.getString("ruleValue");
            String excludeConfigId = param.getString("excludeConfigId"); // 排除的配置ID（用于更新时）

            if (StringUtil.isBlank(ruleValue)) {
                return EasyResult.fail("执行日期不能为空");
            }

            // 查询现有的启用配置，检查是否有相同的执行日期
            EasySQL sql = new EasySQL("SELECT ID, CONFIG_NAME, RULE_VALUE " +
                "FROM " + getTableName("xty_crawler_version_config") +
                " WHERE IS_ENABLED = 'Y' AND RULE_VALUE = ?");
            sql.append(ruleValue);

            if (StringUtil.isNotBlank(excludeConfigId)) {
                sql.append(excludeConfigId, " AND ID != ?");
            }

            JSONObject existingConfigsResult = queryForList(sql.getSQL(), sql.getParams());
            List<JSONObject> existingConfigs = new ArrayList<>();
            if (existingConfigsResult != null && existingConfigsResult.containsKey("data")) {
                Object dataObj = existingConfigsResult.get("data");
                if (dataObj instanceof List) {
                    existingConfigs = (List<JSONObject>) dataObj;
                }
            }

            // 检查冲突
            List<JSONObject> conflicts = new ArrayList<>();
            for (JSONObject existingConfig : existingConfigs) {
                JSONObject conflict = new JSONObject();
                conflict.put("configId", existingConfig.getString("ID"));
                conflict.put("configName", existingConfig.getString("CONFIG_NAME"));
                conflict.put("conflictDay", ruleValue);
                conflicts.add(conflict);
            }

            JSONObject result = new JSONObject();
            result.put("hasConflict", !conflicts.isEmpty());
            result.put("conflicts", conflicts);
            result.put("conflictCount", conflicts.size());
            result.put("checkedDay", ruleValue);

            return EasyResult.ok(result);

        } catch (Exception e) {
            CommonLogger.getLogger().error("检查日期冲突失败: " + e.getMessage(), e);
            return EasyResult.fail("检查日期冲突失败: " + e.getMessage());
        }
    }



    /**
     * 获取规则类型列表
     * @return 规则类型列表
     */
    @WebControl(name = "getRuleTypeList", type = Types.DICT)
    public JSONObject getRuleTypeList() {
        try {
            EasySQL sql = new EasySQL("SELECT " +
                "RULE_TYPE AS VALUE, RULE_TYPE_NAME AS TEXT, RULE_DESCRIPTION " +
                "FROM " + getTableName("xty_crawler_version_config_rule_desc") +
                " ORDER BY RULE_TYPE");

            return queryForList(sql.getSQL(), sql.getParams());

        } catch (Exception e) {
            CommonLogger.getLogger().error("获取规则类型列表失败: " + e.getMessage(), e);
            return EasyResult.fail("获取规则类型列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取版本生成执行历史
     * @return 执行历史数据
     */
    @WebControl(name = "getExecuteHistory", type = Types.LIST)
    public JSONObject getExecuteHistory() {
        try {
            String configId = param.getString("configId");

            EasySQL sql = new EasySQL("SELECT " +
                "l.ID, l.CONFIG_ID, l.EXECUTE_DATE, l.EXECUTE_TIME, l.EXECUTE_RESULT, " +
                "l.EXECUTE_MESSAGE, l.CREATE_TIME, c.CONFIG_NAME " +
                "FROM " + getTableName("xty_crawler_version_generate_log") + " l " +
                "LEFT JOIN " + getTableName("xty_crawler_version_config") + " c " +
                "ON l.CONFIG_ID = c.ID WHERE 1=1");

            if (StringUtil.isNotBlank(configId)) {
                sql.append(configId, " AND l.CONFIG_ID = ?");
            }

            sql.append(" ORDER BY l.EXECUTE_DATE DESC, l.EXECUTE_TIME DESC");

            return queryForList(sql.getSQL(), sql.getParams());

        } catch (Exception e) {
            CommonLogger.getLogger().error("获取执行历史失败: " + e.getMessage(), e);
            return EasyResult.fail("获取执行历史失败: " + e.getMessage());
        }
    }


    @Override
    protected EasyQuery getQuery() {
        return QueryFactory.getTariffQuery();
    }
}
