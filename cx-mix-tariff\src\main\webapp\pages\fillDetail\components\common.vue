<template>
  <div id="tariff-common" class="yq-card" v-auth:[permissions]="'cx-xty-tariff-edit;cx-xty-tariff-delete;cx-xty-tariff-export;cx-xty-tariff-exportPdf;cx-xty-tariff-relate'">
    <!-- 企业（电信、移动、联通、广电） -->
    <div class="card-header" v-if="!allent && !allentCheck">
      <el-tabs v-model="activeName" class="yq-card" @tab-click="handleTab" style="flex: 1;">
        <el-tab-pane label="" name="qb">
          <span slot="label" title="资费属性为“全国”+“省内”+“本地”的资费">
            全部
          </span>
        </el-tab-pane>
        <el-tab-pane label="" name="qg">
          <span slot="label" title="资费属性为“全国”的资费"> 全国 </span>
        </el-tab-pane>
        <el-tab-pane label="" name="sn">
          <span slot="label" title="资费属性为“省内”+“本地”的资费"> 省内 </span>
        </el-tab-pane>
      </el-tabs>
      <div class="export">
        <columns :columns="tableConfig" query-id="allOrderList" @handlehide="handlehide"></columns>
        <el-button type="primary" plain size="small" @click="handleExport" v-if="permissions['cx-xty-tariff-export']">
          <i class="el-icon-download"></i>导出
        </el-button>
      </div>
    </div>
    <!-- 全行业（检查） -->
    <div class="card-header" v-else-if="allentCheck">
      <div class="head-title" style="flex: 1;">
        {{ getI18nValue("报送字段检查（全行业）") }}
      </div>
      <div class="export">
        <columns :columns="tableConfig" query-id="allOrderList" @handlehide="handlehide"></columns>
        <el-button type="primary" plain size="small" @click="handleExport" v-if="permissions['cx-xty-tariff-export']">
          <i class="el-icon-download"></i>导出
        </el-button>
      </div>
    </div>
    <!-- 全行业 -->
    <div class="card-header" style="justify-content: space-between" v-else>
      <div class="head-title">{{ getI18nValue("全行业") }}</div>
      <div class="export">
        <el-button type="primary" plain size="small" @click="handleDelete('all')"
          v-if="permissions['cx-xty-tariff-delete']">
          <i class="el-icon-delete"></i>批量删除
        </el-button>
        <columns :columns="tableConfig" query-id="allOrderList" @handlehide="handlehide"></columns>
        <el-button type="primary" plain size="small" @click="handleExport" v-if="permissions['cx-xty-tariff-export']">
          <i class="el-icon-download"></i>导出
        </el-button>
        <el-button type="primary" plain size="small" @click="handleExportPDF" v-if="permissions['cx-xty-tariff-exportPdf']" style="margin-left: 0">
          <i class="el-icon-download"></i>导出PDF
        </el-button>
      </div>
    </div>

    <div class="card-content">
      <div class="search-box">
        <senior-search :show.sync="moreSearch">
          <el-form class="search-form grid-5" :inline="false" :model="searchForm" ref="searchForm" size="small"
            label-width="70px">
            <el-form-item :label="getI18nValue('方案编号')" prop="reportNo">
              <el-input v-model="searchForm.reportNo" :placeholder="getI18nValue('请输入')" clearable></el-input>
            </el-form-item>
            <el-form-item :label="getI18nValue('资费名称')" prop="name">
              <el-input v-model="searchForm.name" :placeholder="getI18nValue('请输入')" clearable></el-input>
            </el-form-item>
            <el-form-item :label="getI18nValue('报送主体')" prop="reporter">
              <div class="baseflex">
                <el-cascader v-model="searchForm.label_1" :options="provinceAndGroup" :props="provinceCascaderProps" :show-all-levels="false" placeholder="请选择" clearable collapse-tags style="margin-right: 2px"></el-cascader>
                <el-select v-model="searchForm.label_2" placeholder="请选择" filterable clearable style="margin-right: 2px"
                  :disabled="!!ent">
                  <el-option v-for="(label, value) in XTY_REPORTER_ENT" :key="value" :label="label"
                    :value="value"></el-option>
                </el-select>
                <!-- <el-select v-model="searchForm.label_3" placeholder="请选择" filterable clearable @change="handleReporter">
                  <el-option v-for="(item, index) in reporter_list" :key="index" :label="item.DEPT_NAME"
                    :value="item.DEPT_INDEX"></el-option>
                </el-select> -->
              </div>
            </el-form-item>
            <el-form-item :label="getI18nValue('序列号')">
              <el-input v-model="searchForm.seq_no" :placeholder="getI18nValue('请输入')" clearable></el-input>
            </el-form-item>

            <template v-if="moreSearch">
              <el-form-item label="一级分类" prop="TYPE1">
                <el-select v-model="searchForm.TYPE1" placeholder="请选择" filterable clearable multiple collapse-tags>
                  <el-option v-for="(label, value) in XTY_TARIFF_ONE_TYPE" :key="value" :label="label"
                    :value="value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="二级分类" prop="TYPE2">
                <el-select v-model="searchForm.TYPE2" placeholder="请选择" filterable clearable multiple collapse-tags>
                  <el-option v-for="(label, value) in XTY_TARIFF_TWO_TYPE" :key="value" :label="label"
                    :value="value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="getI18nValue('是否通信类')" prop="is_telecom" label-width="80px">
                <el-select v-model="searchForm.is_telecom" placeholder="请选择" multiple collapse-tags filterable clearable>
                  <el-option v-for="(label, value) in XTY_TARIFF_TELECOM" :key="value" :label="label"
                    :value="value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="getI18nValue('资费标准')">
                <div style="display: flex">
                  <el-input v-model="searchForm.feesBegin" :placeholder="getI18nValue('请输入')" clearable
                    @input="handleInput(1)"></el-input>
                  <div>~</div>
                  <el-input v-model="searchForm.feesEnd" :placeholder="getI18nValue('请输入')" clearable
                    @input="handleInput(2)"></el-input>
                </div>
              </el-form-item>
              <el-form-item :label="getI18nValue('资费单位')" prop="fees_unit">
                <el-input v-model="searchForm.fees_unit" :placeholder="getI18nValue('请输入')" clearable></el-input>
              </el-form-item>
              <el-form-item :label="getI18nValue('超出资费')">
                <el-input v-model="searchForm.extra_fees" :placeholder="getI18nValue('请输入')" clearable></el-input>
              </el-form-item>
              <el-form-item :label="getI18nValue('其他费用')">
                <el-input v-model="searchForm.other_fees" :placeholder="getI18nValue('请输入')" clearable></el-input>
              </el-form-item>
              <el-form-item label="资费属性" prop="tariff_attr">
                <el-select v-model="searchForm.tariff_attr" placeholder="请选择" filterable clearable multiple collapse-tags
                  :disabled="tabIndex == '1' ? true : false">
                  <el-option v-for="(label, value) in XTY_TARIFF_ATTRIBUTES" :key="value" :label="label" :value="value"
                    :disabled="tabIndex == '2' ? (label == '全国' ? true : false) : false"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="资费状态" prop="status">
                <el-select v-model="searchForm.status" placeholder="请选择" filterable clearable multiple collapse-tags>
                  <el-option v-for="(label, value) in XTY_TARIFF_STATUS" :key="value" :label="label"
                    :value="value" :disabled="value === '5'"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="getI18nValue('其他服务内容')" label-width="98px">
                <el-input v-model="searchForm.otherContent" :placeholder="getI18nValue('请输入')" clearable></el-input>
              </el-form-item>
              <el-form-item :label="getI18nValue('适用范围')">
                <el-input v-model="searchForm.applicablePeople" :placeholder="getI18nValue('请输入')" clearable></el-input>
              </el-form-item>
              <el-form-item label="适用地区" prop="provinceCode">
                <el-cascader v-model="tree" :options="options" :props="props" clearable collapse-tags placeholder="请选择"
                  @change="handleCasader" style="width: 100%" size="small">
                </el-cascader>
              </el-form-item>
              <el-form-item :label="getI18nValue('有效期限')">
                <el-input v-model="searchForm.valid_period" :placeholder="getI18nValue('请输入')" clearable></el-input>
              </el-form-item>
              <el-form-item :label="getI18nValue('销售渠道')" prop="channel">
                <el-input v-model="searchForm.channel" :placeholder="getI18nValue('请输入')" clearable></el-input>
              </el-form-item>
              <el-form-item :label="getI18nValue('在网要求')">
                <el-input v-model="searchForm.duration" :placeholder="getI18nValue('请输入')" clearable></el-input>
              </el-form-item>
              <el-form-item :label="getI18nValue('退订方式')">
                <el-input v-model="searchForm.unsubscribe" :placeholder="getI18nValue('请输入')" clearable></el-input>
              </el-form-item>
              <el-form-item :label="getI18nValue('违约责任')">
                <el-input v-model="searchForm.responsibility" :placeholder="getI18nValue('请输入')" clearable></el-input>
              </el-form-item>
              <el-form-item :label="getI18nValue('其他事项')">
                <el-input v-model="searchForm.others" :placeholder="getI18nValue('请输入')" clearable></el-input>
              </el-form-item>
              <el-form-item :label="getI18nValue('上线日期开始时间')" prop="onlineDayStart" label-width="120px">
                <el-date-picker v-model="searchForm.onlineDayStart" type="date" format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd" :placeholder="getI18nValue('请选择开始时间')" clearable
                  :append-to-body="false" popper-class="left-popper-220">
                </el-date-picker>
              </el-form-item>
              <el-form-item :label="getI18nValue('上线日期结束时间')" prop="onlineDayEnd" label-width="120px">
                <el-date-picker v-model="searchForm.onlineDayEnd" type="date" format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd" :placeholder="getI18nValue('请选择结束时间')" clearable
                  :append-to-body="false">
                </el-date-picker>
              </el-form-item>
              <el-form-item :label="getI18nValue('下线日期开始时间')" prop="offlineDayStart" label-width="120px">
                <el-date-picker v-model="searchForm.offlineDayStart" type="date" format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd" :placeholder="getI18nValue('请选择开始时间')" clearable
                  :append-to-body="false">
                </el-date-picker>
              </el-form-item>
              <el-form-item :label="getI18nValue('下线日期结束时间')" prop="offlineDayEnd" label-width="120px">
                <el-date-picker v-model="searchForm.offlineDayEnd" type="date" format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd" :placeholder="getI18nValue('请选择结束时间')" clearable
                  :append-to-body="false">
                </el-date-picker>
              </el-form-item>
              <el-form-item :label="getI18nValue('创建时间')">
                <el-date-picker v-model="searchForm.createTime" type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss" 
                  :default-time="['00:00:00', '23:59:59']" start-placeholder="开始时间" end-placeholder="结束时间" clearable
                  :unlink-panels="true" :append-to-body="false" popper-class="left-popper-430">
                </el-date-picker>
              </el-form-item>
              <el-form-item :label="getI18nValue('别名')">
                <el-input v-model="searchForm.tariff_another_name" :placeholder="getI18nValue('请输入')" clearable></el-input>
              </el-form-item>
              <el-form-item label="版本号">
                <el-select v-model="searchForm.versionNum" placeholder="请选择" clearable>
                  <el-option label="未更改" value="0"></el-option>
                  <el-option label="已更改" value="1"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="getI18nValue('修改时间')">
                <el-date-picker v-model="searchForm.updateTime" type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss" 
                  :default-time="['00:00:00', '23:59:59']" start-placeholder="开始时间" end-placeholder="结束时间" clearable
                  :unlink-panels="true" :append-to-body="false" popper-class="left-popper-220">
                </el-date-picker>
              </el-form-item>
              <el-form-item :label="getI18nValue('统计日期')" v-if="sourceChannel == 'total02'">
                <el-date-picker v-model="searchForm.statTime" type="daterange" format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd" start-placeholder="开始时间" end-placeholder="结束时间" clearable
                  :unlink-panels="true" :append-to-body="false">
                </el-date-picker>
              </el-form-item>
              <el-form-item :label="getI18nValue('是否公示')" v-if="allentCheck">
                <el-select v-model="searchForm.isPublic" clearable>
                  <el-option v-for="(value, key) in is_public_list" :key="key" :label="value" :value="key"></el-option>
                </el-select>
              </el-form-item>
              <template v-if="allentCheck">
              <el-form-item :label="getI18nValue('运营商')">
                <el-select
                  v-model="searchForm.ent"
                  placeholder="请选择"
                  filterable
                  multiple
                  collapse-tags
                  clearable
                >
                  <el-option
                    v-for="(label, value) in XTY_REPORTER_ENT"
                    :key="value"
                    :label="label"
                    :value="value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="公示版本" prop="publicVersions" label-width="90px">
                                <el-select v-model="searchForm.publicVersions" placeholder="请选择版本" clearable>
                                  <el-option v-for="(label, value) in versionOptions" :key="value" :label="label.VERSION_NO"
                                    :value="label.VERSION_NO"></el-option>
                                </el-select>
                              </el-form-item>
                <el-form-item label="字段检查结果" label-width="100px">
                  <el-select v-model="searchForm.fieldCheckNo" placeholder="请选择" collapse-tags multiple clearable>
                    <el-option
                      v-for="(val, key) in fieldCheckResultDict"
                      :key="key"
                      :label="val"
                      :value="key"
                    ></el-option>
                  </el-select>
                </el-form-item>


                 <el-form-item :label="getI18nValue('报送字段检查时间')" label-width="120px">
                   <el-date-picker v-model="searchForm.fieldCheckTime" type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss"
                                  :default-time="['00:00:00', '23:59:59']" start-placeholder="开始时间" end-placeholder="结束时间" clearable
                                  :unlink-panels="true" :append-to-body="false" popper-class="left-popper-430">
                    </el-date-picker>
                 </el-form-item>


              </template>
            </template>
            <el-form-item class="btns" label-width="0px">
              <el-button type="primary" plain icon="el-icon-refresh" @click="handleReset">重置</el-button>
              <el-button type="primary" icon="el-icon-search" @click="getList(1)">搜索</el-button>
              <el-button type="primary" plain size="small" @click.stop="moreSearch = !moreSearch">
                <img src="/easitline-cdn/vue-yq/static/imgs/filter.png" alt="" />高级搜索
              </el-button>
            </el-form-item>
          </el-form>
        </senior-search>
      </div>
      <div class="yq-table">
        <el-table ref="table" :data="tableData.data" style="width: 100%" height="100%" v-loading="tableData.loading"
          stripe border>
          <el-table-column label="" type="selection" width="60px" fixed
            v-if="(allent || allentCheck) && (permissions['cx-xty-tariff-delete'] || permissions['cx-xty-tariff-export'])"></el-table-column>
          <template v-for="(label, value) in tableConfig">
            <el-table-column :key="value" :prop="value" :label="label" :min-width="value == 'REPORT_NO' ? 200 : (value == 'AREA_DESC' || value == 'NAME') ? 400 : 180
              " :show-overflow-tooltip="value == 'AREA_DESC' ? false : true"
              :fixed="value == 'REPORT_NO' ? true : false" v-if="columns.indexOf(value) != -1">
              <template slot-scope="scope">
                <template v-if="value == 'IS_PUBLIC'">
                  <span>{{ is_public_list[scope.row[value]] }}</span>
                </template>
                <template v-else-if="value == 'STATUS'">
                  <el-tag size="small" v-if="scope.row.STATUS == '1'">{{
                    XTY_TARIFF_STATUS[scope.row.STATUS]
                  }}</el-tag>
                  <el-tag type="danger" size="small" v-else-if="scope.row.STATUS == '2'">{{
                    XTY_TARIFF_STATUS[scope.row.STATUS] }}</el-tag>
                  <el-tag type="warning" size="small" v-else-if="scope.row.STATUS == '3'">{{
                    XTY_TARIFF_STATUS[scope.row.STATUS] }}</el-tag>
                  <el-tag size="small" type="info" v-else>{{
                    XTY_TARIFF_STATUS[scope.row.STATUS]
                  }}</el-tag>
                </template>
                <template v-else-if="value == 'TARIFF_ATTR'">
                  <span>{{ XTY_TARIFF_ATTRIBUTES[scope.row[value]] }}</span>
                </template>
                <template v-else-if="value == 'IS_TELECOM'">
                  <span>{{ XTY_TARIFF_TELECOM[scope.row[value]] }}</span>
                </template>
                <template v-else-if="value == 'AREA_DESC'">
                  <el-popover placement="top-start" trigger="hover">
                    <div class="popover">
                      <div v-for="(item, index) in scope.row.AREA_TREE" :key="index" class="mg-b8" style="display: flex">
                        <div class="bold">{{ item.label }}：</div>
                        <div class="children">
                          <span v-for="(c, cindex) in item.children" :key="cindex" class="mr-r8">
                            {{ c.label }}
                          </span>
                        </div>
                      </div>
                    </div>
                    <span slot="reference" style="cursor: pointer">{{
                      scope.row[value]
                    }}</span>
                  </el-popover>
                </template>
                <template v-else-if="value == 'OFFLINE_DAY'">
                  <span>{{ scope.row[value] === '49991208' ? '' : scope.row[value] }}</span>
                </template>
                <template v-else-if="value == 'PUBLIC_VERSIONS'">
                  <span>{{ scope.row[value] && scope.row[value].join()  }}</span>
                </template>
                <template v-else>
                  <span>{{ scope.row[value] }}</span>
                </template>
              </template>
            </el-table-column>
          </template>
          <!-- <el-table-column :label="getI18nValue('报送主体')" width="180">
              <template slot-scope="scope">
                {{scope.row.REPORTER_NAME ? scope.row.REPORTER_NAME:scope.row.REPORTER}}
              </template>
            </el-table-column>
          <el-table-column
            prop="REPORT_OBJ"
            :label="getI18nValue('备案者')"
            width="150"
          >
          </el-table-column>
          <el-table-column
            prop="ENT_NAME"
            :label="getI18nValue('运营商')"
            width="120"
          >
          </el-table-column>
          <el-table-column
            prop="PROVINCE_NAME"
            :label="getI18nValue('适用省份')"
            width="300"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="TYPE1"
            :label="getI18nValue('一级分类')"
            width="150"
          >
          </el-table-column>
          <el-table-column
            prop="TYPE2"
            :label="getI18nValue('二级分类')"
            width="200"
          >
          </el-table-column>
          <el-table-column
            prop="NAME"
            :label="getI18nValue('资费名称')"
            min-width="180"
          >
            <template slot-scope="scope">
              {{ scope.row.NAME }}
            </template>
          </el-table-column>
          <el-table-column
            prop="FEES"
            :label="getI18nValue('资费标准')"
            min-width="100"
          >
          </el-table-column>
          <el-table-column
            prop="FEES_UNIT"
            :label="getI18nValue('资费单位')"
            min-width="100"
          >
          </el-table-column>
          <el-table-column
            prop="APPLICABLE_PEOPLE"
            :label="getI18nValue('适用范围')"
            width="150"
          >
          </el-table-column>
          <el-table-column
            prop="AREA_NAME"
            :label="getI18nValue('适用地区')"
            width="300"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="CHANNEL"
            :label="getI18nValue('销售渠道')"
            width="120"
          >
          </el-table-column>
          <el-table-column
            prop="DURATION"
            :label="getI18nValue('在网要求')"
            width="250"
            show-overflow-tooltip
          >
           <el-table-column
            prop="UNSUBSCRIBE"
            :label="getI18nValue('退订方式')"
            width="250"
            show-overflow-tooltip
          >
           <el-table-column
            prop="UNSUBSCRIBE"
            :label="getI18nValue('退订方式')"
            width="250"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="ONLINE_DAY"
            :label="getI18nValue('上线日期')"
            width="120"
          >
          </el-table-column>
          <el-table-column
            prop="STATUS"
            :label="getI18nValue('备案状态')"
            width="120"
          >
            <template slot-scope="scope">
              {{ XTY_TARIFF_STATUS[scope.row.STATUS] }}
            </template>
          </el-table-column> -->

          <el-table-column prop="opt" :label="getI18nValue('操作')"
            :width="180" fixed="right">
            <template slot-scope="scope">
              <template v-if="allentCheck">
                <el-link class="custlink" @click="handleEdit(scope.row, 'detail')">{{ getI18nValue("详情") }}</el-link>
                <el-link class="custlink" @click="handleOpenCheckDialog(scope.row)">{{ getI18nValue("修正")}}</el-link>
                 <el-link class="custlink" @click="handleHistory(scope.row)">{{
                                  getI18nValue("历史")
                                }}</el-link>
                <!-- <el-link class="custlink" @click="handleCheckAgain(scope.row)">{{ getI18nValue("重新核查")}}</el-link> -->
              </template>
              <template v-else>
                <template v-if="allent && scope.row.STATUS != 2">
                  <el-link v-if="permissions['cx-xty-tariff-edit']" class="custlink" @click="handleEdit(scope.row, 'edit')">{{ getI18nValue("修改") }}</el-link>
                  <el-link v-if="permissions['cx-xty-tariff-delete']&&scope.row.VERSION_NUM !== 'V0'" class="custlink" type="danger" @click="handleDelete('single', scope.row)">{{ getI18nValue("删除") }}</el-link>
                </template>
                <el-link class="custlink" @click="handleEdit(scope.row, 'detail')">{{ getI18nValue("详情") }}</el-link>
                <el-link class="custlink" @click="handleHistory(scope.row)">{{
                  getI18nValue("历史")
                }}</el-link>
                <el-link class="custlink" @click="handleReleate(scope.row)"
                  v-if="allent && permissions['cx-xty-tariff-relate'] && scope.row.STATUS !== '3'">{{
                    getI18nValue("关联")
                  }}</el-link>
              </template>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination background @current-change="onPageChange" @size-change="onPageSizeChange"
          :current-page="tableData.pageIndex" :page-size="tableData.pageSize" :page-sizes="[15, 30, 50, 100]"
          layout="total, prev, pager, next, jumper,sizes" :total="tableData.totalRow">
        </el-pagination>
      </div>
    </div>

    <el-drawer :title="getI18nValue('资费信息')" :visible.sync="configDrawer" :size="1200" :wrapper-closable="false">
      <div class="drawer-content">
        <tariff-detail :config-form="configForm" :model="allentCheck ? 'allEntCheck' : ''" :allent-check="allentCheck"></tariff-detail>
      </div>
      <div class="drawer-footer">
        <el-button type="primary" plain @click="handleExportPDF" v-if="permissions['cx-xty-tariff-exportPdf']">{{
          getI18nValue("导出")
        }}</el-button>
        <el-button type="primary" plain @click="configDrawer = false">{{
          getI18nValue("取消")
        }}</el-button>
        <!-- <el-button type="primary" :loading="isAdding">{{getI18nValue('确认')}}</el-button> -->
      </div>
    </el-drawer>

    <!-- <el-drawer :visible.sync="configDrawer_edit" :title="title" size="80%" :wrapper-closable="false" @close="closeDrawer"> -->
    <drawer-edit :configform="configForm_edit" title="修改" :areatree="configForm_areaTree" :oneclass="XTY_TARIFF_ONE_TYPE"
      :twoclass="XTY_TARIFF_TWO_TYPE" :publiclist="is_public_list" :statuslist="XTY_TARIFF_STATUS"
      :tarifflist="XTY_TARIFF_ATTRIBUTES" :telecomlist="XTY_TARIFF_TELECOM" :options="options_2" :provincelist="province_list"
      :visible.sync="configDrawer_edit" @getlist="getList"></drawer-edit>
    <!-- </el-drawer> -->
    <time-line :drawer.sync="historyDrawer" :list="list"></time-line>
    <releate :dialog.sync="dialogVisible" @update="getList(1)" :current-row="currentRow" :reid="reID" :tariff_name="tariff_name" :ent_name="ent_name"
      :province_list="province_list" :abled="isAbled"></releate>

    <check-again-dialog
      ref="checkAgainDialogRef"
      :dict="fieldCheckResultDict"
      :dialog.sync="checkAgainDialogVisible"
      :values="checkAgainDialogData"
      @update="getList()"
    ></check-again-dialog>
  </div>
</template>
<script>
module.exports = {
  components: {
    "time-line": httpVueLoader("./timeline.vue"),
    columns: httpVueLoader("./columns.vue"),
    "drawer-edit": httpVueLoader("./drawerEdit.vue"),
    "releate": httpVueLoader("./releate.vue"),
    'tariff-detail': httpVueLoader('./tariffDetail.vue'),
    'check-again-dialog': httpVueLoader('./checkAgainDialog.vue'),
  },
  props: {
    allent: {
      type: Boolean,
      default: false,
    },
    allentCheck: {
      type: Boolean,
      default: false,
    },
    ent: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      sourceChannel: '',
      activeName: "qb",
      tabIndex: "all",
      moreSearch: false,
      configDrawer: false, // 资费详情
      historyDrawer: false, // 历史详情
      configDrawer_edit: false, //资费修改
      searchForm: {
        reportObj: "",
        provinceCode: "",
        areaCode: "",
        name: "",
        reportNo: "",
        TYPE1: "",
        TYPE2: "",
        type: "",
        status: "",
        tariff_attr: "", //资费属性
        is_public: "", //公示状态
        feesBegin: "",
        feesEnd: "",
        fees_unit: "", //资费单位
        onlineDayStart: "",
        onlineDayEnd: "",
        offlineDayStart: "",
        offlineDayEnd: "",
        duration: "", //在网要求
        valid_period: "", //有效期限
        ent: "",
        //applicablePeople: "",
        channel: "",
        label_1: [],
        label_2: "",
        label_3: "",
        reporter: "",
        statTime: "",
        seq_no:"",
        tariff_another_name:"",
        versionNum: '',
        updateTime: '',
        createTime: '',
        applicablePeople: '',
        others: '',
        unsubscribe: '',
        responsibility: '',
        otherContent: '',
        fieldCheckNo: '',
        fieldCheckTime: '',
        is_telecom: '',
        extra_fees: '',
        other_fees: '',
        isPublic: '',
        publicVersions: '',
      },
      configForm: {
        SEQ_NO: "",
        REPORT_NO: "",
        REPORT_OBJ: "",
        APPLICABLE_AREA: "",
        REPORTER: "",
        REPORTER_NAME: "",
        IS_PUBLIC: "",
        REASON_NO_PUBLIC: "",
        TYPE1: "",
        TYPE2: "",
        NAME: "",
        UNSUBSCRIBE: "",
        FEES: "",
        FEES_UNIT: "",
        CALL_NUM: "",
        DATA_NUM: "",
        DATA_UNIT: "",
        SMS_NUM: "",
        INTERNATIONAL_CALL: "",
        INTERNATIONAL_ROAMING_DATA: "",
        INTERNATIONAL_SMS: "",
        ORIENT_TRAFFIC: "",
        ORIENT_TRAFFIC_UNIT: "",
        IPTV: "",
        BANDWIDTH: "",
        RIGHTS: "",
        PLAN: "",
        OTHER_CONTENT: "",
        APPLICABLE_PEOPLE: "",
        VALID_PERIOD: "",
        CHANNEL: "",
        DURATION: "",
        RESPONSIBILITY: "",
        RESTRICTIONS: "",
        STATUS: "",
        TARIFF_ATTR: "",
        TARIFF_ANOTHER_NAME: '',
        STATUS_2: "",
        ONLINE_DAY: "",
        OFFLINE_DAY: "",
        UPDATE_TIME: "",
        OTHERS: "",
        CLEAR_CAUSE: "",
        AGENT_RELEASE: "",
        DEL_ACC: "",
        DEL_TIME: "",
        REASON: "",
        CREATE_ACC: "",
        CREATE_TIME: "",
        AREA_SELECT_TYPE: "",
        APPLICABLE_PROVINCE: "",
        areaTree: "",
        AREA_DESC: "",
        VERSION_NUM: '',
        VERSION_NO: '',
        PROVINCE_NAME: '',
        FIELD_CHECK_RESULT: '',
        FIELD_CHECK_TIME: '',
        IS_TELECOM: '',
        EXTRA_FEES: '',
        OTHER_FEES: ''
      },
      isPublic: "公示",
      NoPublic: "不公示",
      is_public_list: {
        Y: "公示",
        N: "未公示",
      },
      isAdding: false,
      rules: [{ required: true, message: getI18nValue("必填") }],
      company: {
        1: "电信",
        2: "联通",
        3: "移动",
        4: "广电",
        5: "其它运营商",
      },
      reportObj_b: [],
      XTY_TARIFF_ONE_TYPE: {},
      XTY_TARIFF_TWO_TYPE: {},
      XTY_TARIFF_STATUS: {},
      XTY_TARIFF_ATTRIBUTES: "", //资费属性
      XTY_TARIFF_TELECOM: "", // 是否通信类
      channelOptions: "",
      applicable_area_list: {},
      XTY_TARIFF_ENT: {},
      XTY_REPORTER_ENT: {},
      tableData: {
        pageIndex: 1,
        pageSize: 15,
        totalRow: 0,
        loading: false,
        data: [],
      },
      list: [],
      columns: [], // 显示列
      tableConfig: [],
      reporter_list: [],
      props: { multiple: true },
      options: [],
      options_2: [],
      tree: [],
      provinceCode: "",
      groupType: "", //账号类型：01-省企业，02-集团，03-省管局，04-工信部,
      area_list: {},
      areaSelectType_list: {
        1: "全国",
        2: "指定省市",
        3: "排除省市",
      },
      province_list: [],
      isAbled: false,
      configForm_edit: {},
      configForm_areaTree: [],
      ids: '',
      permissions: {},
      dialogVisible: false,
      reID: "",
      tariff_name: "",
      ent_name: "",
      sourceChannel:"",
      currentRow: null, // 暂存当前行数据
      provinceAndGroup: [],
      provinceCascaderProps: {
        value: 'id',
        label: 'name',
        children: 'children',
        emitPath: false,
        multiple: true,
      },
      configurID: '',//当前详情页的id，用于内部导出

      checkAgainDialogVisible: false,
      checkAgainDialogData: {},

      fieldCheckResultDict: [], // 报送字段检查字典
      versionOptions: [],
    };
  },

  methods: {
    // 列表增删改查 分页查询
    onPageChange: function (page) {
      this.tableData.pageIndex = page;
      this.getList();
    },
    onPageSizeChange: function (size) {
      this.tableData.pageSize = size;
      this.getList();
    },
    handleTab(item) {
      this.tableData.pageIndex = 1;
      this.searchForm.tariff_attr = "";
      switch (item.index) {
        case "0":
          this.tabIndex = "all";
          break;
        case "1":
          this.tabIndex = "1";
          this.searchForm.tariff_attr = ["1"];
          break;
        case "2":
          this.tabIndex = "2";
          break;
        default:
          break;
      }

      this.getList();
    },
    handleInput(num) {
      if (num == "1") {
        // 使用正则表达式判断输入是否为正整数
        if (!/^\d+$/.test(this.searchForm.feesBegin)) {
          this.searchForm.feesBegin = this.searchForm.feesBegin.replace(
            /\D/g,
            ""
          );
        }
      }
      if (num == "2") {
        if (!/^\d+$/.test(this.searchForm.feesEnd)) {
          this.searchForm.feesEnd = this.searchForm.feesEnd.replace(/\D/g, "");
        }
      }
    },
    //查询列表
    getList(page) {
      if(this.groupType == '01' && this.tree.length === 0){
        this.$message.warning('请选择适用地区')
        return
      }
      this.currentRow = null
      this.tableData.loading = true;
      this.tableData.pageIndex = page || this.tableData.pageIndex;
      const data = JSON.parse(JSON.stringify(this.searchForm));
      data.pageType = "3";
      data.pageIndex = this.tableData.pageIndex;
      data.pageSize = this.tableData.pageSize;
      data.ent = this.searchForm.ent 
        ? (Array.isArray(this.searchForm.ent) 
          ? this.searchForm.ent.join() 
          : this.searchForm.ent)
        : this.ent;
      data.type = this.tabIndex;
      data.type1 = this.searchForm.TYPE1
        ? this.searchForm.TYPE1.length > 0
          ? this.searchForm.TYPE1.join(",")
          : ""
        : "";
      data.type2 = this.searchForm.TYPE2
        ? this.searchForm.TYPE2.length > 0
          ? this.searchForm.TYPE2.join(",")
          : ""
        : "";
      data.status = this.searchForm.status
        ? this.searchForm.status.length > 0
          ? this.searchForm.status.join(",")
          : ""
        : "";

      data.tariff_attr = this.searchForm.tariff_attr
        ? this.searchForm.tariff_attr.length > 0
          ? this.searchForm.tariff_attr.join(",")
          : ""
        : "";

      // data.onlineDay = this.searchForm.onlineDay
      //   ? this.searchForm.onlineDay[0] + "~" + this.searchForm.onlineDay[1]
      //   : "";
      // data.offlineDay = this.searchForm.offlineDay
      //   ? this.searchForm.offlineDay[0] + "~" + this.searchForm.offlineDay[1]
      //   : "";
      data.updateTime = this.searchForm.updateTime
        ? this.searchForm.updateTime[0] + "~" + this.searchForm.updateTime[1]
        : "";
      data.createTime = this.searchForm.createTime
        ? this.searchForm.createTime[0] + "~" + this.searchForm.createTime[1]
        : "";

       data.fieldCheckTime = this.searchForm.fieldCheckTime
        ? this.searchForm.fieldCheckTime[0] + "~" + this.searchForm.fieldCheckTime[1]
        : "";

      // 跳转过来的需要传的参数
      data.sourceChannel = yq.q("sourceChannel")
        ? decodeURIComponent(yq.q("sourceChannel"))
        : "";
      data.channelDateType = yq.q("channelDateType")
        ? decodeURIComponent(yq.q("channelDateType"))
        : "";
      data.channelDate = yq.q("channelDate")
        ? decodeURIComponent(yq.q("channelDate"))
        : "";
      data.totalStatus = yq.q("totalStatus")
        ? decodeURIComponent(yq.q("totalStatus"))
        : "";
      // data.stType = yq.q("stType") ? decodeURIComponent(yq.q("stType")) : "";
      data.stime = yq.q("stime") ? decodeURIComponent(yq.q("stime")) : "";
      data.etime = yq.q("etime") ? decodeURIComponent(yq.q("etime")) : "";

      if (data.sourceChannel == 'total02') {
        data.statTime = this.searchForm.statTime
          ? this.searchForm.statTime[0] + "~" + this.searchForm.statTime[1]
          : "";
      }

      if(Array.isArray(data.label_1) && data.label_1.length > 0) {
        data.reporter = data.label_1.map(i=>this.findProvince(i, 'id')).filter(Boolean).map(i=>i.tariffProvinceCode+data.label_2).join(',')
      } else {
        data.reporter = data.label_2
      }
      this.provinceCode && (data.provinceId = this.provinceCode)

      data.fieldCheckNo = (data.fieldCheckNo || []).join(',')

      delete data.label_1;
      delete data.label_2;
      delete data.label_3;
      delete data.TYPE1;
      delete data.TYPE2;
      
      yq.remoteCall(
        "/cx-mix-tariff/webcall?action=tariffRecord.tariffRecordListNew",
        data,
        (res) => {
          if (res.state == 1) {
            res.data.forEach((el) => {
              // const array = el.PROVINCE_NAME.split(",");
              // const uniqueArr = [...new Set(array)];
              // el.PROVINCE_NAME = uniqueArr.join(",");
              el.TYPE1 = this.XTY_TARIFF_ONE_TYPE[el.TYPE1];
              el.TYPE2 = this.XTY_TARIFF_TWO_TYPE[el.TYPE2];
              el.is_telecom = this.XTY_TARIFF_TELECOM[el.is_telecom]
              this.reportObj_b.forEach((val) => {
                if (el.REPORT_OBJ == val.PINYIN) {
                  el.REPORT_OBJ = val.PROVINCE_NAME;
                }
              });
            });

            this.tableData.data = res.data;
            console.log(this.tableData.data);
            this.tableData.totalRow = res.totalRow;
            this.$refs.table.doLayout();
            this.tableData.loading = false;
          } else {
            this.$message.error(res.msg);
            this.tableData.loading = false;
          }
        }
      );
    },

    handleReset: function () {
      for (var key in this.searchForm) {
        this.searchForm[key] = "";
      }
      // this.searchForm.label_1 = this.groupType == "01" ? this.provinceCode : "";
      this.ent && (this.searchForm.label_2 = this.ent);

      if(this.groupType === '01' || this.groupType === '03') {
        // 省企业重置适用地区为默认值
        this.setTreeByRole()
        this.handleCasader()
      } else {
        // 其他角色重置为清空
        this.tree = [];
      }
      
      if (this.activeName == "qg") {
        this.searchForm.tariff_attr = ["1"];
      }
    },
    handleEdit(item, label) {
      console.log(item);
      if (label == "detail") {
        this.configDrawer = true;
        this.configurID = item.ID;
      } else {
        this.configDrawer_edit = true;
      }
      const data = {
        id: item.ID,
      };
      yq.remoteCall(
        "/cx-mix-tariff/webcall?action=tariffRecord.feeFillingInfo",
        data,
        (res) => {
          // 49991208 表示永久
          res.data.OFFLINE_DAY === '49991208' && (res.data.OFFLINE_DAY = '')
          if (
            res.data.AREA_SELECT_TYPE == "2" ||
            res.data.AREA_SELECT_TYPE == ""
          ) {
            this.configForm_areaTree = res.areaTree;
          }
          this.configForm_edit = res.data;
          this.configForm_edit.action_type = 'M'
          this.configForm_edit.isMagUp = 'Y'

          for (var key in this.configForm) {
            this.configForm[key] = res.data[key];
          }
          if(res.data.REPORTER_NAME) {
            this.configForm['REPORTER_NAME'] = `${res.data.REPORTER_NAME}（${res.data.REPORTER}）`
          }
          this.configForm.IS_PUBLIC = this.is_public_list[res.data.IS_PUBLIC]
          this.configForm.AREA_SELECT_TYPE =
            this.areaSelectType_list[res.data.AREA_SELECT_TYPE];
          this.configForm.APPLICABLE_AREA = res.data.APPLICABLE_AREA.split(",");
          this.reportObj_b.forEach((val) => {
            if (this.configForm.REPORT_OBJ == val.PINYIN) {
              this.configForm.REPORT_OBJ = val.PROVINCE_NAME;
            }
          });
          this.configForm.TYPE1 = item.TYPE1;
          this.configForm.TYPE2 = item.TYPE2;
          this.configForm.STATUS_2 = item.STATUS;
          this.configForm.STATUS =
            this.XTY_TARIFF_STATUS[this.configForm.STATUS];
          this.configForm.TARIFF_ATTR =
            this.XTY_TARIFF_ATTRIBUTES[this.configForm.TARIFF_ATTR];
          this.configForm.areaTree = res.areaTree;
          this.configForm.IS_TELECOM = this.XTY_TARIFF_TELECOM[item.IS_TELECOM]

          console.log("configForm_edit", this.configForm_edit);
        }
      );
    },
    // 删除
    handleDelete(label, item) {
      if (label == "all") {
        var ids = this.$refs["table"].selection.map((item) => item.ID);
        var versionList = this.$refs["table"].selection.map((item) => item.VERSION_NUM);
        if (ids.length == 0) {
          this.$message.error(getI18nValue("请选择要删除的备案！"));
          return;
        } else {
          if (versionList.every(i => i ==='V0')) {
            this.ids = ids.join(',')
            this.handleDeleteOk(this.ids)
          } else {
            this.$message.error(getI18nValue("不能选择已修改的备案"));
          }
        }
      } else {
        this.ids = item.ID
        this.handleDeleteOk(this.ids)
      }
    },
    handleDeleteOk(ids) {
      this.$confirm("是否删除选择的备案?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const data = {
            ids: ids
          };
          yq.remoteCall(
            "/cx-mix-tariff/servlet/tariff?action=mangerDirectDelInfos",
            data,
            (res) => {
              console.log(res)
              if (res.state == 1) {
                this.$message.success(res.msg);
                this.getList(1);
              } else {
                this.$message.error(res.msg);
              }
            }
          );
        })
        .catch(function () { });

    },
    handleHistory(item) {
      const data = {
        reportKey: item.REPORT_KEY,
      };
      yq.remoteCall(
        "/cx-mix-tariff/webcall?action=tariffRecord.findHistoryList",
        data,
        (res) => {
          res.data.forEach((element, idx, arr) => {
            // 49991208 表示永久
            element.OFFLINE_DAY === '49991208' && (element.OFFLINE_DAY = '')

            if(element.OFFLINE_DAY) {
              element.OFFLINE_DAY = element.OFFLINE_DAY.replace(/(\d{4})(\d{2})(\d{2})/, '$1年$2月$3日')
            }
            if(element.ONLINE_DAY) {
              element.ONLINE_DAY = element.ONLINE_DAY.replace(/(\d{4})(\d{2})(\d{2})/, '$1年$2月$3日')
            }

            if(element.DEL_ACC) {
              element.OPTION_CONTENT = '删除' 
              element.OPERATION_ACC = element.DEL_ACC
              element.OPERATION_TIME = element.DEL_TIME
            } else {
              if(idx === arr.length - 1) {
                element.OPTION_CONTENT = '创建'
                element.OPERATION_ACC = element.CREATE_ACC
                element.OPERATION_TIME = element.CREATE_TIME
              } else {
                element.OPTION_CONTENT = '修改'
                element.OPERATION_ACC = element.UPDATE_ACC
                element.OPERATION_TIME = element.UPDATE_TIME
              }
            }
            
            element.SELECT = false;
            element.DISABLE = false;
            element.TYPE1 = this.XTY_TARIFF_ONE_TYPE[element.TYPE1];
            element.TYPE2 = this.XTY_TARIFF_TWO_TYPE[element.TYPE2];
            element.STATUS = this.XTY_TARIFF_STATUS[element.STATUS];
            // this.reportObj.forEach((item) => {
            //   if (item.PROVINCE_CODE == element.PROVINCE) {
            //     element.PROVINCE_NAME = item.PROVINCE_NAME;
            //   }
            // });
            const array = element.PROVINCE_NAME.split(",");
            const array2 = element.AREA_NAME.split(",");
            element.PROVINCE_NAME = this.handleSort([...new Set(array)]);
            element.AREA_NAME = this.handleSort([...new Set(array2)]);
            element.IS_PUBLIC = this.is_public_list[element.IS_PUBLIC];
          });
          this.list = res.data;
        }
      );

      this.historyDrawer = true;
    },
    //排序
    handleSort(arr) {
      arr.sort(function (a, b) {
        return a.localeCompare(b, "zh-Hans-CN", { sensitivity: "accent" });
      });
      return arr.join(",");
    },
    // 确认
    handleConfirm() {
      var ids = this.$refs["table"].selection.map((item) => item.OBJ_ID);
      if (ids.length == 0) {
        this.$message.error(getI18nValue("请选择要确认的数据！"));
        return;
      }

      this.$confirm(getI18nValue("是否确认选中数据？"), getI18nValue("提示"), {
        confirmButtonText: getI18nValue("确定"),
        cancelButtonText: getI18nValue("取消"),
        type: "warning",
      })
        .then(() => {
          var data = { ids: ids };
          yq.remoteCall(
            "/cc-quality/servlet/QcTaskObj?action=ReleaseAll",
            data,
            (result) => {
              if (result.state == 1) {
                this.$message.success(result.msg);
                // this.doSearch()
              } else {
                this.$message.error(result.msg);
              }
            }
          );
        })
        .catch(() => { });
    },

    getProvince() {
      yq.remoteCall(
        "/cx-mix-tariff/webcall?action=common.provinces",
        {},
        (res) => {
          this.reportObj_b = JSON.parse(JSON.stringify(res.data));
          this.reportObj_b.push({
            PROVINCE_NAME: "集团",
            PINYIN: "JT",
          });
        }
      );
    },
    getProvince2() {
      yq.remoteCall(
        "/cx-mix-tariff/webcall?action=common.provincesAndAll",
        {},
        (res) => {
          this.province_list = JSON.parse(JSON.stringify(res.data));
        }
      );
    },
    // handleProvinceCode() {
    //   let data = {
    //     provinceCode: this.searchForm.provinceCode,
    //   };
    //   yq.remoteCall("/cx-mix-tariff/webcall?action=common.areaDict", data).then(
    //     (res) => {
    //       this.applicable_area_list = res.data;
    //       this.searchForm.areaCode = "";
    //     }
    //   );
    // },

    getDict() {
      yq.daoCall(
        {
          controls: [
            "common.getDict(XTY_TARIFF_ACTION_TYPE)",
            "common.getDict(XTY_TARIFF_ONE_TYPE)",
            "common.getDict(XTY_TARIFF_TWO_TYPE)",
            "common.getDict(XTY_TARIFF_ENT)",
            "common.getDict(XTY_TARIFF_STATUS)",
            "common.getDict(XTY_TARIFF_ATTRIBUTES)",
            "common.getDict(XTY_TARIFF_TELECOM)",
          ],
          params: {},
        },
        (data) => {
          this.XTY_TARIFF_ACTION_TYPE =
            data["common.getDict(XTY_TARIFF_ACTION_TYPE)"].data;
          this.XTY_TARIFF_ONE_TYPE =
            data["common.getDict(XTY_TARIFF_ONE_TYPE)"].data;
          this.XTY_TARIFF_TWO_TYPE =
            data["common.getDict(XTY_TARIFF_TWO_TYPE)"].data;
          this.XTY_TARIFF_ENT = data["common.getDict(XTY_TARIFF_ENT)"].data;
          this.XTY_TARIFF_STATUS =
            data["common.getDict(XTY_TARIFF_STATUS)"].data;
          this.XTY_TARIFF_ATTRIBUTES =
            data["common.getDict(XTY_TARIFF_ATTRIBUTES)"].data;
            if (!yq.q("sourceChannel") && !yq.q('status')) {
              // 默认显示 “资费状态”为“在售”
               this.searchForm.status = ['1']
            }

          this.XTY_TARIFF_TELECOM = data["common.getDict(XTY_TARIFF_TELECOM)"].data
        },
        { contextPath: "cx-mix-tariff" }
      );
      yq.remoteCall("/cx-mix-tariff/webcall?action=common.ents", {}, (res) => {
        this.XTY_REPORTER_ENT = res.data;
      });
      if (this.allentCheck) {
        yq.remoteCall("/cx-mix-tariff/webcall?action=common.tariffRuleDict").then(
          (res) => {
            res.data && (this.fieldCheckResultDict = res.data)
          }
        );
      }
    },
    //获取当前是什么账号类型
    getMessage() {

      yq.remoteCall(
        "/cx-mix-tariff/webcall?action=common.queryUserGroupType",
        {},
        (res) => {
          if (res.state == 1) {
            this.provinceCode = res.data.provinceCode;
            this.groupType = res.data.groupType;

            this.getTree()
            this.getList();
          }
        }
      );
    },
    getTree() {
      // 1. 根据省份编码生成树形结构（角色默认适用地区）
      this.setTreeByRole()

      // 2. 从本地存储获取树形结构（下穿），如果有，则覆盖角色默认适用地区
      this.setTreeByStore()

      // 根据tree生成搜索条件（provinceCode, areaCode）
      this.handleCasader();
    },
    setTreeByRole() {
      if(!this.provinceCode) {
        return 
      }
      const provinceNode = this.options.find(item => item.value === this.provinceCode);
      if (provinceNode) {
        this.tree = provinceNode.children.map(child => [provinceNode.value, child.value]);
      }
    },
    setTreeByStore() {
      if(!this.sourceChannel) {
        return 
      }
      let areaTree = localStorage.getItem("areaTree")
      console.log('[areaTree]=============>>', areaTree)
      if(!areaTree) {
        return 
      }
      try {
        this.tree = JSON.parse(areaTree);
      } catch(e) {
        console.error('[解析areaTree失败]:', e);
      }
      localStorage.removeItem("areaTree");
    },
    // 获取table字段名字
    getTariffRecordFields() {
      const url = this.allentCheck ?
        "/cx-mix-tariff/webcall?action=tariffRecord.tariffRecordFieldsForCheck" :
        "/cx-mix-tariff/webcall?action=tariffRecord.tariffRecordFields"
      yq.remoteCall(url, {},(res) => {
        if (res.state == 1) {
          console.log("获取table字段名字", res);
          this.tableConfig = res.data;
          // const local = {};
          // local["allOrderList"] = [
          //   "REPORTER",
          //   "TARIFF_ATTR",
          //   "AREA_DESC",
          //   "STATUS",
          //   "IS_PUBLIC",
          //   "TYPE1",
          //   "TYPE2",
          //   "NAME",
          //   "FEES",
          //   "FEES_UNIT",
          //   "DURATION",
          //   "ONLINE_DAY",
          //   "OFFLINE_DAY",
          //   "UPDATE_ACC",
          //   "PROVINCE_NAME",
          //   "AREA_DESC",
          //   "REPORTER_NAME",
          //   "REPORT_NO",
          //   "SEQ_NO",
          //   "VALID_PERIOD",
          //   "CHANNEL",
          // ];
          // if(!localStorage.getItem("tariff-table-colums")){
          //   localStorage.setItem("tariff-table-colums", JSON.stringify(local));
          // }
        } else {
        }
      });
    },
    handlehide(data) {
      console.log("返回的data,", data);
      this.columns = data;
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      });
    },
    // 导出
    handleExport() {
      if(this.groupType == '01' && this.tree.length === 0){
        this.$message.warning('请选择适用地区')
        return
      }
      const table_columns = this.$refs["table"].store.states.columns.map(
        (item) => {
          return item.property;
        }
      );
      // this.$confirm("是否导出当前列表?", "提示", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      //   type: "warning",
      // })
      //   .then(function () {
      const data = JSON.parse(JSON.stringify(this.searchForm));
      data.pageType = "3";
      data.pageIndex = this.tableData.pageIndex;
      data.pageSize = this.tableData.pageSize;
      data.ent = this.searchForm.ent 
        ? (Array.isArray(this.searchForm.ent) 
          ? this.searchForm.ent.join() 
          : this.searchForm.ent)
        : this.ent;
      data.type = this.tabIndex;
      data.type1 = this.searchForm.TYPE1
        ? this.searchForm.TYPE1.length > 0
          ? this.searchForm.TYPE1.join(",")
          : ""
        : "";
      data.type2 = this.searchForm.TYPE2
        ? this.searchForm.TYPE2.length > 0
          ? this.searchForm.TYPE2.join(",")
          : ""
        : "";
      data.status = this.searchForm.status
        ? this.searchForm.status.length > 0
          ? this.searchForm.status.join(",")
          : ""
        : "";
      data.tariff_attr = this.searchForm.tariff_attr
        ? this.searchForm.tariff_attr.length > 0
          ? this.searchForm.tariff_attr.join(",")
          : ""
        : "";

      data.onlineDay = this.searchForm.onlineDay
        ? this.searchForm.onlineDay[0] + "~" + this.searchForm.onlineDay[1]
        : "";
      data.offlineDay = this.searchForm.offlineDay
        ? this.searchForm.offlineDay[0] + "~" + this.searchForm.offlineDay[1]
        : "";
      data.sourceChannel = yq.q("sourceChannel")
        ? decodeURIComponent(yq.q("sourceChannel"))
        : "";
      data.channelDateType = yq.q("channelDateType")
        ? decodeURIComponent(yq.q("channelDateType"))
        : "";
      data.channelDate = yq.q("channelDate")
        ? decodeURIComponent(yq.q("channelDate"))
        : "";
      data.totalStatus = yq.q("totalStatus")
        ? decodeURIComponent(yq.q("totalStatus"))
        : "";
      // data.stType = yq.q("stType") ? decodeURIComponent(yq.q("stType")) : "";
      data.stime = yq.q("stime") ? decodeURIComponent(yq.q("stime")) : "";
      data.etime = yq.q("etime") ? decodeURIComponent(yq.q("etime")) : "";
      if (this.allent || this.allentCheck) {
        (data.fields = table_columns
          .slice(1, table_columns.length - 1)
          .join(","))
      } else {
        (data.fields = table_columns
          .slice(0, table_columns.length - 1)
          .join(","))
      }
      if (data.sourceChannel == 'total02') {
        data.statTime = this.searchForm.statTime
          ? this.searchForm.statTime[0] + "~" + this.searchForm.statTime[1]
          : "";
      }

      if(Array.isArray(data.label_1) && data.label_1.length > 0) {
        data.reporter = data.label_1.map(i=>this.findProvince(i, 'id')).filter(Boolean).map(i=>i.tariffProvinceCode+data.label_2).join(',')
      } else {
        data.reporter = data.label_2
      }
      this.provinceCode && (data.provinceId = this.provinceCode)

      delete data.label_1;
      delete data.label_2;
      delete data.label_3;
      delete data.TYPE1;
      delete data.TYPE2;

      const ids = this.$refs["table"].selection.map((item) => item.ID)

      data['fieldCheckNo'] = Array.isArray(data.fieldCheckNo) ?
                              data.fieldCheckNo.join(',') :
                              data.fieldCheckNo ?
                              data.fieldCheckNo :
                              ''

      if(ids.length > 0) {
        data.ids = ids.join()

        axios({
          method: "post",
          url: "/cx-mix-tariff/servlet/tariff?action=exportById",
          responseType: "blob",
          data: {
            data,
          },
        }).then((res) => {
          const blob = new Blob([res.data]);
          const url = URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = url;
          link.download = "资费列表.xlsx";
          link.click();
        });
      } else {
        yq.remoteCall(
          "/cx-mix-tariff/servlet/tariff?action=Export",
          data,
          (res) => {
            this.$message({
              type: res.state == 1 ? 'success' : 'error',
              message: res.msg
            })
          }
        )
      }
    },
    handleExportPDF() {
      var id = this.$refs["table"].selection.map((item) => item.ID);
      if(!this.configDrawer){
      if (id.length == 0) {
        this.$message.error(getI18nValue("请选择要导出的备案！"));
        return;
      } else if(id.length > 1) {
        this.$message.error(getI18nValue("只能导出单个备案"));
        return;
      }
    }else if(this.configurID){ 
        id[0] = this.configurID;
      }

        yq.confirm('本信息不代表资费备案信息，仅供参考！', '注意', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          window.open(`/cx-mix-tariff/servlet/tariff?action=exportPdf&id=${id[0]}`)
        }).catch(() => {
          yq.message({
            message: '已取消',
            type: 'info'
          });
        });
    },
    // 获取报送主体的省份
    getProvinceAndGroup() {
      return yq.remoteCall("/cx-mix-tariff/webcall?action=common.queryTariffProvinceTree").then(res => {
        if (res.state == 1) {
          this.provinceAndGroup = res.data || [];
        }
      })
    },
    findProvince(value, prop = 'provinceCode', unique = true) {
      let find = [];
      function search(children) {
          for (let i = 0; i < children.length; i++) {
              if (children[i][prop] === value) {
                  find.push(children[i])
              }
              if (children[i].children && children[i].children.length > 0) {
                  search(children[i].children);
              }
          }
      }
      search(this.provinceAndGroup);
      return unique ? find[find.length - 1] : find;
    },
    //获取省份树结构
    handleGetProvinces() {
      yq.remoteCall(
        "/cx-mix-tariff/webcall?action=common.queryAreaTree",
        {},
        (res) => {
          this.options = res.data || [];
          this.options_2 = res.data || [];

          // getParams获取的
          if(this.searchForm.provinceCode) {
            const provinceNode = this.options.find(item => item.value === this.searchForm.provinceCode);
            if (provinceNode) {
              this.tree = provinceNode.children.map(child => [provinceNode.value, child.value]);
            }
          }
          //获取当前是什么账号类型
          this.getMessage();
        }
      );
    },
    handleCasader() {
      const label1 = [];
      const label2 = [];
      this.tree.forEach((item) => {
        label1.push(item[0]);
        label2.push(item[1]);
      });
      this.searchForm.provinceCode = [...new Set(label1)].join(",");
      this.searchForm.areaCode = [...new Set(label2)].join(",");
    },
    // 获取资费统计界面跳转过来传来的参数
    getParams() {
      let sourceChannel = yq.q("sourceChannel")

      if (!sourceChannel) {
        return 
      }

      console.log('[sourceChannel]=============>>', sourceChannel)
      this.sourceChannel = sourceChannel

      if (sourceChannel == 'tariff-report-statistics') {
        let status = decodeURIComponent(yq.q('status'))
        this.searchForm.status = status
          ? status.split(",")
          : [];
        let createTime = decodeURIComponent(yq.q('createTime', '')).replaceAll('+', ' ')
        this.searchForm.createTime = createTime
          ? createTime.split(",")
          : "";
        let label_1 = decodeURIComponent(yq.q('label_1', '')).split(',')
        this.searchForm.label_1 = label_1.map(i=>this.findProvince(i, 'tariffProvinceCode')).filter(Boolean).map(i=>i.id)
      } else {
        const keys = Object.keys(this.searchForm);
        keys.forEach((item) => {
          if (yq.q(item)) {
            this.searchForm[item] = decodeURIComponent(yq.q(item))
              ? decodeURIComponent(yq.q(item))
              : "";
          }
        });

        this.searchForm.TYPE1 = this.searchForm.TYPE1
          ? this.searchForm.TYPE1.split(",")
          : "";
        this.searchForm.TYPE2 = this.searchForm.TYPE2
          ? this.searchForm.TYPE2.split(",")
          : "";
        this.searchForm.status = this.searchForm.status
          ? this.searchForm.status.split(",")
          : "";
        if (yq.q("sourceChannel") == 'total02') {
          this.searchForm.statTime = this.searchForm.statTime
            ? this.searchForm.statTime.split(",")
            : "";
            this.searchForm.onlineDay = ""
        } else {
          this.searchForm.onlineDay = this.searchForm.onlineDay
            ? this.searchForm.onlineDay.split(",")
            : "";
        }

        this.searchForm.offlineDay = this.searchForm.offlineDay
          ? this.searchForm.offlineDay.split(",")
          : "";
        this.searchForm.tariff_attr = this.searchForm.tariff_attr
          ? this.searchForm.tariff_attr.split(",")
          : "";
        this.searchForm.fieldCheckNo = this.searchForm.fieldCheckNo
          ? this.searchForm.fieldCheckNo.split(",")
          : "";
        this.searchForm.is_telecom = this.searchForm.is_telecom
          ? this.searchForm.is_telecom.split(",")
          : "";

        this.searchForm.label_1 = this.searchForm.label_1 || []
        if(this.searchForm.label_1 === 'string') {
          this.searchForm.label_1 = this.searchForm.label_1.split(',')
        }
        this.searchForm.ent = this.searchForm.ent
          ? this.searchForm.ent.split(",")
          : "";

        console.log("获取资费统计界面跳转过来传来的参数", this.searchForm);
      }
    },
    // 关联
    handleReleate(item) {
      this.currentRow = item
      this.dialogVisible = true
      this.reID = item.ID
      this.tariff_name = item.NAME
      this.ent_name = item.ENT_NAME
      this.isAbled = item.REPORT_OBJ == '集团' ? true : false
    },
    handleOpenCheckDialog(item) {
      this.checkAgainDialogVisible = true
      this.checkAgainDialogData = item
      this.$nextTick(() => this.$refs['checkAgainDialogRef'].init())
    },
    handleCheckAgain(item) {
      yq.remoteCall(`/cx-mix-tariff/servlet/tariffRule?action=recheckTariff&tariffId=${item.ID}`).then(res => {
        if (res.state == 1) {
          this.$message.success(res.msg || '重新核查成功')
          this.getList()
        } else {
          this.$message.error(res.msg || '重新核查失败')
        }
      })
    },
    getVersionList() {
      yq.remoteCall(
        "/cx-mix-tariff/webcall?action=crawlerTariffData.getVersionList"
      ).then((res) => {
        this.versionOptions = res.data || [];
      });
    }
  },
  created() {
    this.ent && (this.searchForm.label_2 = this.ent);
  },
  mounted() {
    this.getDict();
    this.getProvinceAndGroup().then(() => {
      this.getParams();
      this.handleGetProvinces();
    })
    this.getTariffRecordFields();
    this.getProvince();
    this.getProvince2();
    this.getVersionList();
  },
};
</script>
<style scoped>
.title {
  font-size: 16px;
  font-weight: bold;
  width: 6%;
  white-space: nowrap;
}

.export span .el-button {
  padding: 7px 16px;
}

/* el-date-picker popper修正 */
#tariff-common.yq-card {
  overflow: auto;
}

#tariff-common.yq-card .card-content {
  overflow: auto;
}

#tariff-common .left-popper-220 {
  left: -220px !important;
}

#tariff-common .left-popper-430 {
  left: -430px !important;
}
</style>
