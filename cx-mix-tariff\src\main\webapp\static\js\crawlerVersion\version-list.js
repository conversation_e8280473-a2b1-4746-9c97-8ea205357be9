/**
 * 爬虫版本管理页面脚本
 */

// 版本状态枚举
const VERSION_STATUS = {
  NORMAL: 'NORMAL',
  INVALID: 'INVALID'
}

// 工具函数
const VersionUtils = {
  // 格式化数字
  formatNumber: function(num) {
    if (!num) return '0'
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  },
  
  // 格式化版本状态
  formatStatus: function(status) {
    return status === VERSION_STATUS.NORMAL ? '正常' : '已作废'
  },
  
  // 获取状态标签类型
  getStatusTagType: function(status) {
    return status === VERSION_STATUS.NORMAL ? 'success' : 'danger'
  },
  
  // 格式化时间
  formatTime: function(time) {
    if (!time) return '-'
    return time
  }
}

// API调用封装
const VersionAPI = {
  // 获取版本列表
  getVersionList: function(params, callback) {
    yq.daoCall({
      controls: ["tariffCrawlerVersion.getVersionList"],
      params: params
    }, callback, {
      contextPath: '/cx-mix-tariff'
    })
  },
  
  // 获取版本统计
  getVersionStatistics: function(callback) {
    yq.daoCall({
      controls: ["tariffCrawlerVersion.getVersionStatistics"],
      params: {}
    }, callback, {
      contextPath: '/cx-mix-tariff'
    })
  },
  
  // 获取版本详情
  getVersionDetail: function(versionId, callback) {
    yq.remoteCall("/cx-mix-tariff/servlet/crawlerVersion/getVersionInfo", {
      versionId: versionId
    }, callback)
  },
  
  // 版本作废
  invalidVersion: function(versionId, reason, callback) {
    yq.remoteCall("/cx-mix-tariff/servlet/crawlerVersion/invalidVersion", {
      versionId: versionId,
      invalidReason: reason
    }, callback)
  },
  
  // 导出版本数据
  exportVersionData: function(versionId, versionNo, callback) {
    yq.remoteCall("/cx-mix-tariff/servlet/crawlerVersion/exportVersionData", {
      versionId: versionId,
      versionNo: versionNo
    }, callback)
  },
  
  // 批量版本操作
  batchVersionOperation: function(operation, versionIds, callback) {
    yq.remoteCall("/cx-mix-tariff/servlet/crawlerVersion/batchVersionOperation", {
      operation: operation,
      versionIds: versionIds
    }, callback)
  }
}

// 页面事件处理
const VersionEvents = {
  // 初始化页面
  init: function() {
    console.log('版本管理页面初始化')
  },
  
  // 处理搜索
  handleSearch: function() {
    console.log('执行搜索')
  },
  
  // 处理重置
  handleReset: function() {
    console.log('重置搜索条件')
  },
  
  // 查看版本详情
  viewDetail: function(versionId) {
    console.log('查看版本详情:', versionId)
  },
  
  // 版本作废
  invalidVersion: function(versionId, reason) {
    console.log('版本作废:', versionId, reason)
  },
  
  // 导出版本数据
  exportData: function(versionId, versionNo) {
    console.log('导出版本数据:', versionId, versionNo)
  },
  
  // 批量操作
  batchOperation: function(operation, versionIds) {
    console.log('批量操作:', operation, versionIds)
  }
}

// 页面验证
const VersionValidation = {
  // 验证版本作废表单
  validateInvalidForm: function(form) {
    if (!form.invalidReason || form.invalidReason.trim() === '') {
      return { valid: false, message: '请输入作废原因' }
    }
    if (form.invalidReason.length > 500) {
      return { valid: false, message: '作废原因不能超过500个字符' }
    }
    return { valid: true }
  },
  
  // 验证搜索表单
  validateSearchForm: function(form) {
    // 基本验证逻辑
    return { valid: true }
  }
}

// 导出给全局使用
window.VersionUtils = VersionUtils
window.VersionAPI = VersionAPI
window.VersionEvents = VersionEvents
window.VersionValidation = VersionValidation
