<?xml version="1.0" encoding="UTF-8"?>
<config>

	<!--
	<param key="RESULT_SMS_URL"    name="短信发送结果获取地址" 	type="string" 	description="短信发送结果获取地址" index='1' value="https://api.028lk.com/Sms/Api/GetReport"/>
    -->
    <param key="XTY_ENT_ID"  name="企业ID" 	type="string" 	description="企业ID" index='9' value="17007"/>
    <param key="busi_schema"  name="资费业务库名称" 	type="string" 	description="资费业务库名称" index='10' value="ycbusi_ekf"/>
    <param key="SFTP_IP" name="SFTP服务器IP地址" type="String" description="FTP服务器IP地址" value="" index="20"></param>
    <param key="SFTP_PORT" name="SFTP服务器端口" type="String" description="FTP服务器端口" value="" index="21"></param>
    <param key="SFTP_USERNAME" name="SFTP服务器登录用户名" type="String" description="FTP服务器登录用户名" value=""  index="22"></param>
    <param key="SFTP_PASSWORD" name="SFTP服务器登录密码" type="String" description="FTP服务器登录密码" value=""  index="23"></param>
   	<param key="SFTP_BASE_PATH" name="SFTP服务器文件根目录" type="String" description="FTP服务器文件根目录，如：/market/interface" value="" index="24"></param>
    <param key="SFTP_LOCAL_BASE_PATH" name="SFTP本地文件存放路径" type="String" description="本地文件存放路径，如：/home/<USER>/interface" value="" index="25"></param>
	<param key="TRAIFF_AUDIT_LOAD_BROKER" name="资费稽核文件下载的消息队列名称" type="String" description="资费稽核文件下载的消息队列名称" value="XTY_TRAIFF_AUDIT_LOAD_BROKER" ></param>
   	<param key="TRAIFF_BAK_BROKER" name="资费备案的消息队列名称" type="String" description="资费备案的消息队列名称" value="XTY_TRAIFF_BAK_BROKER" ></param>
	<param key="TRAIFF_REQ_BAK_BROKER" name="资费请求备案的消息队列名称" type="String" description="资费请求备案的消息队列名称" value="XTY_TRAIFF_REQ_BAK_BROKER" ></param>
    <param key="TRAIFF_ORDER_QUERY_BROKER" name="资费订单查询的消息队列" type="String" description="资费订单查询的消息队列" value="TRAIFF_ORDER_QUERY_BROKER" index="31"></param>
    <param key="TRAIFF_DEL_MSG_BROKER" name="短信订购删除通知队列" type="String" description="短信订购删除通知队列" value="TRAIFF_DEL_MSG_BROKER" index="32"></param>
    <param key="TRAIFF_EXE_MESSAGE_BROKER" name="短信订购重跑队列" type="String" description="短信订购重跑队列" value="TRAIFF_EXE_MESSAGE_BROKER" index="33"></param>
    <param key="TRAIFF_EXE_AUDIT_MESSAGE_BROKER" name="短信稽核重跑队列" type="String" description="短信稽核重跑队列" value="TRAIFF_EXE_AUDIT_MESSAGE_BROKER" index="33"></param>

    <param key="ENABLE_TESTDATA_INF" name="是否启动测试数据接口" type="radio" items="{Y:开启,N:关闭}" description="是否启动测试数据接口" value="N" index="19" />
	<param key="TARIFF_EXPORT_SIZE" name="资费导出数量限制" type="String" description="资费导出数量限制" value="10000" index="20" />
	<param key="TARIFF_REAUDIT_FLAG" name="资费稽核重跑开关" type="String" description="资费稽核重跑开关" value="N" index="21" />
	<param key="FILE_BASE_PATH" name="生成文件路径" type="String" description="生成文件路径" value="/home/<USER>/tariff" index="25" />

    <param key="BAK_TARIFF_YEAR" name="资费报送下架数据年份限制" type="String"  description="资费报送下架数据年份限制" value="" index="22" />
    <param key="TASK_BTN" name="资费模块定时任务开关" type="String"  description="资费模块定时任务开关" value="false" index="28" />
    <param key="TARIFF_INFO_ANALYSIS_API_URL" name="资费信息语义分析API地址" type="String"  description="资费信息语义分析API地址" value="" index="29" />

    <param key="EMAIL_TYPE"  name="邮件类型" 	type="string" 	description="邮件类型：126/qq/163" index='30' value=""/>
    <param key="EMAIL_FROM"  name="发件人邮箱" 	type="string" 	description="发件人邮箱" index='31' value=""/>
    <param key="EMAIL_PASSWORD"  name="发件人密码" 	type="string" 	description="发件人密码" index='32' value=""/>
    <param key="EMAIL_TO"  name="收件人邮箱" 	type="string" 	description="收件人邮箱" index='33' value=""/>
    <param key="EMAIL_CC"  name="抄送人邮箱" 	type="string" 	description="抄送人邮箱" index='34' value=""/>
    <param key="THRESHOLD"  name="触发次数阈值" 	type="string" 	description="触发次数阈值：次" index='35' value="10"/>

    <!-- 稽核任务相关配置 -->
    <param key="AUDIT_TASK_TEMP_DIR" name="稽核任务临时目录" type="string" description="ZIP文件临时存储目录" value="/home/<USER>/audit/temp" index="36"/>
    <param key="AUDIT_TASK_EXPORT_DIR" name="稽核任务导出目录" type="string" description="结果文件导出目录" value="/home/<USER>/audit/export" index="37"/>
    <param key="AUDIT_TASK_UNZIP_DIR" name="稽核任务解压目录" type="string" description="ZIP文件解压目录" value="/home/<USER>/audit/unzip" index="38"/>
    <param key="AUDIT_TASK_MAX_FILE_SIZE" name="稽核任务最大文件大小" type="string" description="最大文件大小（字节），默认50MB" value="52428800" index="39"/>
    <param key="AUDIT_TASK_THREAD_POOL_SIZE" name="稽核任务线程池大小" type="string" description="处理线程池大小" value="5" index="40"/>
    <param key="SFTP_AUDIT_DIR" name="SFTP稽核目录" type="string" description="SFTP服务器稽核目录" value="/check" index="41"/>
    <param key="AUDIT_SCAN_INTERVAL" name="稽核任务扫描间隔" type="string" description="稽核任务扫描间隔（分钟）" value="1" index="42"/>
    <param key="AUDIT_RESULT_SCAN_INTERVAL" name="稽核结果扫描间隔" type="string" description="稽核结果扫描间隔（分钟）" value="2" index="43"/>
    <param key="AUDIT_SIMILARITY_THRESHOLD" name="数据对比相似度阈值" type="string" description="数据对比相似度阈值" value="0.8" index="44"/>
    <param key="AUDIT_TASK_RETENTION_DAYS" name="稽核任务保留天数" type="string" description="稽核任务保留天数" value="30" index="45"/>
    <param key="AUDIT_TASK_SCAN_ENABLED" name="是否启用稽核任务定时扫描" type="radio" items="{true:启用,false:禁用}" description="是否启用稽核任务定时扫描" value="true" index="46"/>
    <param key="AUDIT_RESULT_SCAN_ENABLED" name="是否启用稽核结果定时扫描" type="radio" items="{true:启用,false:禁用}" description="是否启用稽核结果定时扫描" value="true" index="47"/>
    <param key="XTY_PUBLIC_HOST" name="公众端host" type="string" description="公众端host" value="" index="48"/>
    <param key="LOCAL_SERVER_HOST" name="本机HOST" type="string" description="LOCAL_SERVER_HOST" value="" index="49"/>

    <param key="FORCE_REPORT_INDEX_UPDATE" name="是否强制更新报送索引" type="radio" items="{Y:是,N:否}" description="无视版本数组是否存在，强制更新报送库索引" value="N" index="50" />


</config>
