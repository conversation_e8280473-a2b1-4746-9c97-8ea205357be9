package com.yunqu.tariff.inf.tariff;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.mq.MQBrokerUtil;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.utils.LogUtil;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.cache.CacheTime;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yunqu.tariff.base.Constants.TARIFF_HIS_STATUS_2;

/**
 * <p>
 *备案资费
 * </p>
 *
 * @ClassName TariffBakupServiceExecutor
 * <AUTHOR> Copy This Tag)
 * @Description TODO 描述文件用途
 * @Since create in 7/1/24 9:56 AM
 * @Version v1.0
 * @Copyright Copyright (c) 2024
 * @Company 广州云趣信息科技有限公司
 */
public class TariffBakupServiceExecutor implements TariffServiceExecutor {

    private static final Logger logger = LoggerFactory.getLogger(CommonLogger.getJobLogger().getName());

    private static final String[] ENT_TYPE_ARR = {"1", "2", "3", "5"};

    private String schema;

    public JSONObject execute(String serviceCode, JSONObject param) {
        try {
            String entId = Constants.getEntId();
            schema = SchemaService.findSchemaByEntId(entId);
            String date = param.getString("date");
            String provinceCode = param.getString("provinceCode");
            String tariffProvinceCode = param.getString("tariffProvinceCode");
            String entCode = param.getString("entCode");
            if(StringUtils.isBlank(date)){
                EasyCalendar easyCalendar = EasyCalendar.newInstance();
                easyCalendar.add(Calendar.DAY_OF_MONTH, -1);
                date = easyCalendar.getDateString("");
            }
            String finalDate = date;
            logger.info("开始备案，备案日期--->{},provinceCode={},entCode={}", finalDate, provinceCode, entCode);
            EasyQuery query = getQuery();
            EasySQL provinceSql = new EasySQL("select * from "+Constants.getBusiSchema()+".xty_tariff_province where 1=1 ");
            provinceSql.append("and TARIFF_PROVINCE_CODE <> '' and TARIFF_PROVINCE_CODE is not null ");
            provinceSql.append(provinceCode,"and PROVINCE_CODE = ?");
            provinceSql.append(tariffProvinceCode,"and TARIFF_PROVINCE_CODE = ?");
            List<JSONObject> jsonObjects = query.queryForList(provinceSql.getSQL(), provinceSql.getParams(), new JSONMapperImpl());

//            if(StringUtils.isBlank(provinceCode)){
//                JSONObject jtObj = new JSONObject();
//                jtObj.put("PROVINCE_CODE",Constants.JT_PROVINCE_CODE);
//                jtObj.put("TARIFF_PROVINCE_CODE","JT");
//                jsonObjects.add(jtObj);
//            }
            jsonObjects.forEach(o -> analyzeProvinceData(o, finalDate,entCode));
            return EasyResult.ok();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return EasyResult.error(999, "系统执行异常："+e.getMessage());
        }
    }


    private void analyzeProvinceData(JSONObject provinceData, String date,String entCode) {
        String provinceCode = provinceData.getString("PROVINCE_CODE");
        String tariffProvinceCode = provinceData.getString("TARIFF_PROVINCE_CODE");
        String[] arr ;
        if(StringUtils.isBlank(entCode)){
            arr = ENT_TYPE_ARR;
        }else{
            arr = new String[]{entCode};
        }
        Stream.of(arr).forEach(entType -> {
            //在售
            CompletableFuture<List<JSONObject>> salesDataFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return analyzeOnsalesData(provinceCode,tariffProvinceCode, entType, date);
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
            });

            //下架  地市编码为下架资费
            CompletableFuture<List<JSONObject>> delistedDataFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return analyzeDelistedData(provinceCode,tariffProvinceCode, entType);
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
            });
//
//            //续订-未备案转续订
//            CompletableFuture<List<JSONObject>> renewedDataFuture = CompletableFuture.supplyAsync(() -> {
//                try {
//                    return analyzeRenewedData(provinceCode, entType, date);
//                } catch (SQLException e) {
//                    throw new RuntimeException(e);
//                }
//            });


            //下架-备案库两年内下架数据
            CompletableFuture<List<JSONObject>> unsalesDataFuture1 = CompletableFuture.supplyAsync(() -> {
                try {
                    return analyzeUnsalesData(provinceCode,tariffProvinceCode, entType, date);
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
            });
            //下架-续订资费方案的下架数据
            CompletableFuture<List<JSONObject>> unsalesDataFuture2 = CompletableFuture.supplyAsync(() -> {
                try {
                    return analyzeUnsales2Data(provinceCode, tariffProvinceCode, entType);
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
            });

            // 未售资费数据
            CompletableFuture<List<JSONObject>> unsalesDataFuture3 = CompletableFuture.supplyAsync(() -> {
                try {
                    return analyzeUnsales3Data(provinceCode,tariffProvinceCode, entType, date);
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
            });
            //下架-未备案资费下架数据
            /*CompletableFuture<List<JSONObject>> unsalesDataFuture3 = CompletableFuture.supplyAsync(() -> {
                try {
                    return analyzeUnsales3Data(provinceCode, entType);
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
            });*/


            List<JSONObject> bakData = new ArrayList<>();

            List<JSONObject> salesData = null;
            try {
                salesData = salesDataFuture.get();
            } catch (InterruptedException | ExecutionException e) {
                throw new RuntimeException("报送【在售】数据获取异常：" + e.getMessage(), e);
            }
            List<JSONObject> unsalesData1 = null;
            try {
                unsalesData1 = unsalesDataFuture1.get();
            } catch (InterruptedException | ExecutionException e) {
                throw new RuntimeException("报送【备案库两年内下架数据】数据获取异常：" + e.getMessage(), e);
            }
            List<JSONObject> unsalesData2 = null;
            try {
                unsalesData2 = unsalesDataFuture2.get();
            } catch (InterruptedException | ExecutionException e) {
                throw new RuntimeException("报送【续订资费方案的下架数据】数据获取异常：" + e.getMessage(), e);
            }

            List<JSONObject> unsalesData3 = null;
            try {
                unsalesData3 = delistedDataFuture.get();
            } catch (InterruptedException | ExecutionException e) {
                throw new RuntimeException("备案【地市编码为下架的数据】数据获取异常：" + e.getMessage(), e);
            }

            bakData.addAll(salesData);
            bakData.addAll(unsalesData1);
            bakData.addAll(unsalesData2);
//            bakData.addAll(unsalesData3);
            if(CollUtil.isNotEmpty(bakData)) {
                //处理数据上报
                analyzeBakDatas(bakData, tariffProvinceCode, entType, date);
            } else {
                logger.info("[{}{}]无需上传数据", tariffProvinceCode, entType);
            }
        });
    }


    /**
     * 将备案数据放置再缓存中并且发送消息队列给公众端
     */
    private void analyzeBakDatas(List<JSONObject> data, String tariffProvinceCode, String entType, String date) {
        logger.info("[{}{}]待上传的数据量: {}", tariffProvinceCode, entType, data.size());
        // 避免mq数据过大，先塞缓存，把key传递到mq去处理
        String key = "BAK_TARIFF_DATA_" + tariffProvinceCode + "_" + entType;
        CacheUtil.put(key, JSONObject.toJSONString(data), CacheTime.HALF_HOUR);
        JSONObject reqParams = new JSONObject();
        reqParams.put("redisKey", key);
        reqParams.put("date", date);
        reqParams.put("tariffProvinceCode", tariffProvinceCode);
        reqParams.put("entType", entType);
        long reqTime = System.currentTimeMillis();
        JSONObject resultObj = MQBrokerUtil.sendQueueMsgAndGetResult(Constants.getTraiffBakBroker(), reqParams, 120);
        // 上报日志
        if (resultObj != null) {
            LogUtil.addSftpOutInvokeLog(reqTime, "资费上报系统", "资费上报接口", null, reqParams.toJSONString(), resultObj.toJSONString(), resultObj.getString("code"), resultObj.getString("msg"));
        } else {
            LogUtil.addSftpOutInvokeLog(reqTime, "资费上报系统", "资费上报接口", null, reqParams.toJSONString(), null, "404", "获取不到返回结果");
        }
        logger.info("返回结果: {}", resultObj);
    }



    /**
     * 组装在售数据
     */
    private List<JSONObject> analyzeOnsalesData(String provinceCode,String tariffProvinceCode, String entType, String date) throws SQLException {
        List<JSONObject> result = new ArrayList<>();
        int pageIndex = 1;
        int pageSize = 1000;
        EasyQuery query = getQuery();
        EasySQL sql = new EasySQL();
        sql.append("select  t1.id ,t1.REPORT_NO,t1.REPORTER,t1.NAME,t1.ONLINE_DAY,t1.OFFLINE_DAY,t1.TARIFF_ANOTHER_NAME,GROUP_CONCAT(t2.AREA_CODE SEPARATOR ',')  APPLICABLE_AREA,'在售' as TYPE,");
        sql.append("t1.TYPE1,t1.TYPE2,t1.IS_TELECOM,CONCAT(t1.FEES,t1.FEES_UNIT) FEES,t1.DURATION");
        sql.append("from " + schema + ".XTY_TARIFF_RECORD t1");
        sql.append(" LEFT JOIN " + schema + ".xty_tariff_areq t2 on t1.id = t2.TARIFF_RECORD_ID ");
        sql.append("where 1=1");
        if("JT".equals(provinceCode) || Constants.JT_PROVINCE_CODE.equals(provinceCode)){
            sql.append("and ( t1.PROVINCE='' or t1.PROVINCE is null )");
        }else{
            sql.append(provinceCode, "and t1.PROVINCE=?");
        }
        sql.append(tariffProvinceCode, "and t1.REPORT_OBJ=?");
        sql.append(entType, "and t1.ENT=?");
        sql.append(date, "and  t1.ONLINE_DAY<=?");
        sql.append(date, "and (t1.OFFLINE_DAY >= ? or t1.OFFLINE_DAY is null or t1.OFFLINE_DAY='')  ");
        sql.append(Constants.TARIFF_STATUS_2, "and t1.STATUS != ?", false);
        //update by yx 2025-4-24 17:29:12 地区编码增加类型
        sql.append(Constants.TARIFF_AREA_TYPE_3, "and t2.TYPE <> ?");
        sql.append(" GROUP BY t1.id ,t1.REPORT_NO,t1.REPORTER,t1.NAME,t1.ONLINE_DAY,t1.OFFLINE_DAY,t1.TARIFF_ANOTHER_NAME ");
        logger.info("provinceCode={},组装在售数据：{},param:{}", provinceCode, sql.getSQL(), sql.getParams());
        while (true) {
            List<JSONObject> datas = query.queryForList(sql.getSQL(), sql.getParams(), pageIndex, pageSize, new JSONMapperImpl());
            if (datas == null || datas.isEmpty()) break;
            result.addAll(datas);
            pageIndex++;
        }

        // 查询昨天删除的在售数据
        pageIndex = 1;
        pageSize = 1000;
        String dateFormat = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyyMMdd")).toString();
        sql = new EasySQL();
        sql.append("select  t1.id ,t1.REPORT_NO,t1.REPORTER,t1.NAME,t1.ONLINE_DAY,t1.OFFLINE_DAY,t1.TARIFF_ANOTHER_NAME,GROUP_CONCAT(t2.AREA_CODE SEPARATOR ',')  APPLICABLE_AREA,'在售' as TYPE,");
        sql.append("t1.TYPE1,t1.TYPE2,t1.IS_TELECOM,CONCAT(t1.FEES,t1.FEES_UNIT) FEES,t1.DURATION");
        sql.append("from " + schema + ".XTY_TARIFF_RECORD_HIS t1");
        sql.append(" LEFT JOIN " + schema + ".xty_tariff_areq t2 on t1.id = t2.TARIFF_RECORD_ID ");
        sql.append("where 1=1");
        if ("JT".equals(provinceCode) || Constants.JT_PROVINCE_CODE.equals(provinceCode)) {
            sql.append("and ( t1.PROVINCE='' or t1.PROVINCE is null )");
        } else {
            sql.append(provinceCode, "and t1.PROVINCE=?");
        }
        sql.append(tariffProvinceCode, "and t1.REPORT_OBJ=?");
        sql.append(entType, "and t1.ENT=?");
        sql.append(TARIFF_HIS_STATUS_2, "and t1.STATUS=?");  // 已删除的
        // 判断 DEL_TIME 在 date 当天范围内
        sql.append(dateFormat + " 00:00:00", "and t1.DEL_TIME >= ?");
        sql.append(dateFormat + " 23:59:59", "and t1.DEL_TIME <= ?");
        sql.append(date, "and  t1.ONLINE_DAY<= ?");  // 20201201
        sql.append(date, "and (t1.OFFLINE_DAY >= ? or t1.OFFLINE_DAY is null or t1.OFFLINE_DAY='')  ");
        //update by yx 2025-4-24 17:29:12 地区编码增加类型
        sql.append(Constants.TARIFF_AREA_TYPE_3, "and t2.TYPE <> ?");
        sql.append(" GROUP BY t1.id ,t1.REPORT_NO,t1.REPORTER,t1.NAME,t1.ONLINE_DAY,t1.OFFLINE_DAY,t1.TARIFF_ANOTHER_NAME ");
        logger.info("provinceCode={},组装当天删除的在售数据：{}", provinceCode, sql.getFullSql());
        while (true) {
            List<JSONObject> datas = query.queryForList(sql.getSQL(), sql.getParams(), pageIndex, pageSize, new JSONMapperImpl());
            if (datas == null || datas.isEmpty()) break;
            result.addAll(datas);
            pageIndex++;
        }
        return result.stream().filter(o -> StringUtils.isNotBlank(o.getString("NAME"))).collect(Collectors.toList());
    }

    /**
     * 组装下架-地市编码类型为下架的数据
     */
    private List<JSONObject> analyzeDelistedData(String provinceCode,String tariffProvinceCode, String entType) throws SQLException {
        List<JSONObject> result = new ArrayList<>();
        int pageIndex = 1;
        int pageSize = 1000;
        EasyQuery query = getQuery();
        EasySQL sql = new EasySQL();
        sql.append("select  t1.id ,t1.REPORT_NO,t1.REPORTER,t1.NAME,t1.ONLINE_DAY,t1.OFFLINE_DAY,t1.TARIFF_ANOTHER_NAME,GROUP_CONCAT(t2.AREA_CODE SEPARATOR ',')  APPLICABLE_AREA,'下架' as TYPE,");
        sql.append("t1.TYPE1,t1.TYPE2,t1.IS_TELECOM,CONCAT(t1.FEES,t1.FEES_UNIT) FEES,t1.DURATION");
        sql.append("from " + schema + ".XTY_TARIFF_RECORD t1");
        sql.append(" LEFT JOIN " + schema + ".xty_tariff_areq t2 on t1.id = t2.TARIFF_RECORD_ID ");
        sql.append("where 1=1");
        if("JT".equals(provinceCode) || Constants.JT_PROVINCE_CODE.equals(provinceCode)){
            sql.append("and ( t1.PROVINCE='' or t1.PROVINCE is null )");
        }else{
            sql.append(provinceCode, "and t1.PROVINCE=?");
        }
        sql.append(tariffProvinceCode, "and t1.REPORT_OBJ=?");
        sql.append(entType, "and t1.ENT=?");
        sql.append(Constants.TARIFF_STATUS_2, "and t1.STATUS != ?", false);
        //update by yx 2025-4-24 17:29:12 地区编码增加类型
        sql.append(Constants.TARIFF_AREA_TYPE_3, "and t2.TYPE = ?",false);
        sql.append(" GROUP BY t1.id ,t1.REPORT_NO,t1.REPORTER,t1.NAME,t1.ONLINE_DAY,t1.OFFLINE_DAY,t1.TARIFF_ANOTHER_NAME ");
        logger.info("provinceCode={},组装地市编码下架数据：{},param:{}", provinceCode, sql.getSQL(), sql.getParams());
        while (true) {
            List<JSONObject> datas = query.queryForList(sql.getSQL(), sql.getParams(), pageIndex, pageSize, new JSONMapperImpl());
            if (datas == null || datas.isEmpty()) break;
            result.addAll(datas);
            pageIndex++;
        }
        return result.stream().filter(o-> StringUtils.isNotBlank(o.getString("NAME"))).collect(Collectors.toList());
    }

    /**
     * 组装下架-备案库下架两年内的数据
     */
    private List<JSONObject> analyzeUnsalesData(String provinceCode,String tariffProvinceCode, String entType,String date) throws SQLException {
        List<JSONObject> result = new ArrayList<>();
        int pageIndex = 1;
        int pageSize = 1000;
        String year = Constants.getBakTariffYear();
        EasyQuery query = getQuery();
        EasySQL sql = new EasySQL();
        sql.append("select  t1.id ,t1.REPORT_NO,t1.REPORTER,t1.NAME,t1.ONLINE_DAY,t1.OFFLINE_DAY,t1.TARIFF_ANOTHER_NAME,GROUP_CONCAT(t2.AREA_CODE SEPARATOR ',')  APPLICABLE_AREA,'下架' as TYPE,");
        sql.append("t1.TYPE1,t1.TYPE2,t1.IS_TELECOM,CONCAT(t1.FEES,t1.FEES_UNIT) FEES,t1.DURATION");
        sql.append("from " + schema + ".XTY_TARIFF_RECORD t1");
        sql.append(" LEFT JOIN " + schema + ".xty_tariff_areq t2 on t1.id = t2.TARIFF_RECORD_ID ");
        sql.append("where 1=1");
        if("JT".equals(provinceCode) || Constants.JT_PROVINCE_CODE.equals(provinceCode)){
            sql.append("and ( t1.PROVINCE='' or t1.PROVINCE is null )");
        }else{
            sql.append(provinceCode, "and t1.PROVINCE=?");
        }
        sql.append(tariffProvinceCode, "and t1.REPORT_OBJ=?");
        sql.append(entType, "and t1.ENT=?");
        // sql.append("and t1.STATUS IN ( '-1', '3' ) ");
        sql.append("and OFFLINE_DAY is not null and OFFLINE_DAY <> ''");
        //update by yx2025-3-19 3月18日当天下架的数据3月19日报送在售
        sql.append(date, "and OFFLINE_DAY<?");
        //sql.append(date, "and OFFLINE_DAY<=?");
        if(StringUtils.isNotBlank(year)){
            sql.append(date,"and STR_TO_DATE(OFFLINE_DAY, '%Y%m%d') >= DATE_SUB(?, INTERVAL "+year+" YEAR)");
        }
        sql.append("group by t1.id ,t1.REPORT_NO,t1.REPORTER,t1.NAME,t1.ONLINE_DAY,t1.OFFLINE_DAY,t1.TARIFF_ANOTHER_NAME");

        logger.info("provinceCode={}备案库下架两年内的数据下架数据查询SQL：{},param:{}", provinceCode, sql.getSQL(), sql.getParams());
        while (true) {
            List<JSONObject> datas = query.queryForList(sql.getSQL(), sql.getParams(), pageIndex, pageSize, new JSONMapperImpl());
            if (datas == null || datas.isEmpty()) break;
            result.addAll(datas);
            pageIndex++;
        }
        return result.stream().filter(o-> StringUtils.isNotBlank(o.getString("NAME"))).collect(Collectors.toList());
    }

    /**
     * 组装下架-续订资费方案下架数据
     */
    private List<JSONObject> analyzeUnsales2Data(String provinceCode,String tariffProvinceCode, String entType) throws SQLException {
        List<JSONObject> result = new ArrayList<>();
        int pageIndex = 1;
        int pageSize = 1000;
        EasyQuery query = getQuery();
        EasySQL sql = new EasySQL();
        //未备案续订已备案下架那部分的数据适用地区APPLICABLE_AREA取省份下的所有地市
        sql.append("select  t1.ID ,case t1.TARIFF_NO when '0' then '' else t1.TARIFF_NO end as REPORT_NO,'' AS REPORTER,t1.TARIFF_NAME as `NAME`,'' as ONLINE_DAY,'' AS OFFLINE_DAY,'' AS TARIFF_ANOTHER_NAME,GROUP_CONCAT( t2.TARIFF_AREA_CODE SEPARATOR ',' ) AS APPLICABLE_AREA,'下架' as TYPE,");
        sql.append(" '' as TYPE1,'' as TYPE2,'' as IS_TELECOM,'' as FEES,'' as DURATION");
        sql.append("from " + schema + ".XTY_TARIFF_PLAN_RECORD t1");
        sql.append("left join "+schema+".xty_tariff_area t2 on t1.PROVINCE_CODE=t2.PROVINCE_CODE");
        sql.append("where 1=1");
        if("JT".equals(provinceCode) || Constants.JT_PROVINCE_CODE.equals(provinceCode)){
            sql.append("and ( t1.PROVINCE_CODE='' or t1.PROVINCE_CODE is null ");
            sql.append(Constants.JT_PROVINCE_CODE,"or t1.PROVINCE_CODE = ? )");
        }else{
            sql.append(provinceCode, "and t1.PROVINCE_CODE=?");
        }
        sql.append(tariffProvinceCode,"and t1.TARIFF_PROVINCE_CODE = ?  ");
        sql.append(entType, "and t1.ENT = ?");
        sql.append("and t1.TARIFF_TYPE = '2'  ");
        //update by yx 2025-4-22 18:59:17 续订资费方案下架数据不再报送已经删除的资费方案仅报送手动触发未报送资费方案转续订的资费
        sql.append("and t1.TARIFF_NO ='0' ");
        sql.append("and NOT EXISTS (select 1 from "+schema+".XTY_TARIFF_RECORD t where t.REPORT_KEY = t1.TARIFF_NO) ");
        sql.append("group by t1.ID,t1.TARIFF_NAME,t1.TARIFF_NO,t1.PROVINCE_CODE");
        logger.info("provinceCode={}续订资费方案下架数据查询SQL：{},param:{}", provinceCode, sql.getSQL(), sql.getParams());
        while (true) {
            List<JSONObject> datas = query.queryForList(sql.getSQL(), sql.getParams(), pageIndex, pageSize, new JSONMapperImpl());
            if (datas == null || datas.isEmpty()) break;
            result.addAll(datas);
            pageIndex++;
        }
        return result.stream().filter(o-> StringUtils.isNotBlank(o.getString("NAME"))).collect(Collectors.toList());
    }

    /**
     * 组装未售资费
     */
    private List<JSONObject> analyzeUnsales3Data(String provinceCode, String tariffProvinceCode, String entType, String date) throws SQLException {
        List<JSONObject> result = new ArrayList<>();
        int pageIndex = 1;
        int pageSize = 1000;
        EasyQuery query = getQuery();
        EasySQL sql = new EasySQL();
        sql.append("select  t1.id ,t1.REPORT_NO,t1.REPORTER,t1.NAME,t1.ONLINE_DAY,t1.OFFLINE_DAY,t1.TARIFF_ANOTHER_NAME,GROUP_CONCAT(t2.AREA_CODE SEPARATOR ',')  APPLICABLE_AREA,'未售' as TYPE,");
        sql.append("t1.TYPE1,t1.TYPE2,t1.IS_TELECOM,CONCAT(t1.FEES,t1.FEES_UNIT) FEES,t1.DURATION");
        sql.append("from " + schema + ".XTY_TARIFF_RECORD t1");
        sql.append(" LEFT JOIN " + schema + ".xty_tariff_areq t2 on t1.id = t2.TARIFF_RECORD_ID ");
        sql.append("where 1=1");
        if("JT".equals(provinceCode) || Constants.JT_PROVINCE_CODE.equals(provinceCode)){
            sql.append("and ( t1.PROVINCE='' or t1.PROVINCE is null )");
        }else{
            sql.append(provinceCode, "and t1.PROVINCE=?");
        }
        sql.append(tariffProvinceCode, "and t1.REPORT_OBJ=?");
        sql.append(entType, "and t1.ENT=?");
        sql.append(date, "and  t1.ONLINE_DAY > ?");
        sql.append(Constants.TARIFF_STATUS_2, "and t1.STATUS != ?", false);
        sql.append(Constants.TARIFF_AREA_TYPE_3, "and t2.TYPE <> ?");
        sql.append(" GROUP BY t1.id ,t1.REPORT_NO,t1.REPORTER,t1.NAME,t1.ONLINE_DAY,t1.OFFLINE_DAY,t1.TARIFF_ANOTHER_NAME ");
        logger.info("provinceCode={},组装未售数据：{},param:{}", provinceCode, sql.getSQL(), sql.getParams());
        while (true) {
            List<JSONObject> datas = query.queryForList(sql.getSQL(), sql.getParams(), pageIndex, pageSize, new JSONMapperImpl());
            if (datas == null || datas.isEmpty()) break;
            result.addAll(datas);
            pageIndex++;
        }

        // 查询昨天删除的未售数据
        pageIndex = 1;
        pageSize = 1000;
        String dateFormat = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyyMMdd")).toString();
        sql = new EasySQL();
        sql.append("select  t1.id ,t1.REPORT_NO,t1.REPORTER,t1.NAME,t1.ONLINE_DAY,t1.OFFLINE_DAY,t1.TARIFF_ANOTHER_NAME,GROUP_CONCAT(t2.AREA_CODE SEPARATOR ',')  APPLICABLE_AREA,'未售' as TYPE,");
        sql.append("t1.TYPE1,t1.TYPE2,t1.IS_TELECOM,CONCAT(t1.FEES,t1.FEES_UNIT) FEES,t1.DURATION");
        sql.append("from " + schema + ".XTY_TARIFF_RECORD_HIS t1");
        sql.append(" LEFT JOIN " + schema + ".xty_tariff_areq t2 on t1.id = t2.TARIFF_RECORD_ID ");
        sql.append("where 1=1");
        if ("JT".equals(provinceCode) || Constants.JT_PROVINCE_CODE.equals(provinceCode)) {
            sql.append("and ( t1.PROVINCE='' or t1.PROVINCE is null )");
        } else {
            sql.append(provinceCode, "and t1.PROVINCE=?");
        }
        sql.append(tariffProvinceCode, "and t1.REPORT_OBJ=?");
        sql.append(entType, "and t1.ENT=?");
        sql.append(TARIFF_HIS_STATUS_2, "and t1.STATUS=?");  // 已删除的
        // 判断 DEL_TIME 在 date 当天范围内
        sql.append(dateFormat + " 00:00:00", "and t1.DEL_TIME >= ?");
        sql.append(dateFormat + " 23:59:59", "and t1.DEL_TIME <= ?");
        sql.append(date, "and  t1.ONLINE_DAY > ?");
        //update by yx 2025-4-24 17:29:12 地区编码增加类型
        sql.append(Constants.TARIFF_AREA_TYPE_3, "and t2.TYPE <> ?");
        sql.append(" GROUP BY t1.id ,t1.REPORT_NO,t1.REPORTER,t1.NAME,t1.ONLINE_DAY,t1.OFFLINE_DAY,t1.TARIFF_ANOTHER_NAME ");
        logger.info("provinceCode={},组装当天删除的未售数据：{}", provinceCode, sql.getFullSql());
        while (true) {
            List<JSONObject> datas = query.queryForList(sql.getSQL(), sql.getParams(), pageIndex, pageSize, new JSONMapperImpl());
            if (datas == null || datas.isEmpty()) break;
            result.addAll(datas);
            pageIndex++;
        }
        return result.stream().filter(o -> StringUtils.isNotBlank(o.getString("NAME"))).collect(Collectors.toList());
    }

}
