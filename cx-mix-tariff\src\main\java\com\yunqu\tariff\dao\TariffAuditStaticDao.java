package com.yunqu.tariff.dao;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.base.AppDaoContext;
import com.yunqu.tariff.handler.SearchHandlerFactory;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;

import java.util.List;

/**
 * TariffAuditStaticDao
 *
 * @ClassName TariffAuditStaticDao
 * <AUTHOR> Copy This Tag)
 * @Since create in 2024/12/24 17:12
 * @Version v1.0
 * @Copyright Copyright (c) 2024
 * @Company 广州云趣信息科技有限公司
 */
@WebObject(name = "tariffAuditStaticDao")
public class TariffAuditStaticDao extends AppDaoContext {

    /**
     * 各省未报送资费数量统计2
     *
     */
    @WebControl(name = "getProvinceNotReportCount2", type = Types.LIST)
    public JSONObject getProvinceNotReportCount2() {
        return SearchHandlerFactory.getProvinceEntAggregationSearchHandler(param).search();
    }

    /**
     * 省内未报送资费数量统计
     *
     */
    @WebControl(name = "getProvinceNotReportCount", type = Types.LIST)
    public JSONObject getProvinceNotReportCount() {
        return SearchHandlerFactory.getProvinceNotReportCountSearchHandler(param).search();
    }

    /**
     * 各省未报送资费统计
     *
     */
    @WebControl(name = "provinceUnreportCountStatistics", type = Types.LIST)
    public JSONObject provinceUnreportCountStatistics() {
        return SearchHandlerFactory.getProvinceUnreportCountStatisticsSearchHandler(param).search();
    }

    /**
     * 报送资费统计
     *
     */
    @WebControl(name = "getReportedTariffStatistics", type = Types.LIST)
    public JSONObject getReportedTariffStatistics() {
        return SearchHandlerFactory.getReportedTariffStatisticsSearchHandler(param).search();
    }


    /**
     * 查询状态变更总数
     *
     */
    @WebControl(name = "findStatusChangeTotal", type = Types.LIST)
    public JSONObject findStatusChangeTotal() {
        return SearchHandlerFactory.getFindStatusChangeTotalSearchHandler(param).search();
    }


    /**
     * 查询时间变更总数
     *
     */
    @WebControl(name = "findTimeChangeTotal2", type = Types.LIST)
    public JSONObject findTimeChangeTotal2() {
        List<JSONObject> result = SearchHandlerFactory.getFindStatusChangeTotalSecSearchHandler(param).search();
        return getJsonResult(result);
    }


    /**
     * 查询分布统计
     *
     */
    @WebControl(name = "findDistributionTotal", type = Types.LIST)
    public JSONObject findDistributionTotal() {
        List<JSONObject> result = SearchHandlerFactory.getFindDistributionTotalSearchHandler(param).search();
        //4:返回结果
        return getJsonResult(result);
    }
}
