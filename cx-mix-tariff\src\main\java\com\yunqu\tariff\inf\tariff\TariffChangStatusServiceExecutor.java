package com.yunqu.tariff.inf.tariff;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.service.TariffAuditProcessStorageService;
import com.yunqu.tariff.service.TariffCheckService;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.List;

/**
 * <p>
 *修改资费状态
 * </p>
 *
 * @ClassName TariffChangStatusServiceExecutor
 * <AUTHOR> Copy This Tag)
 * @Description TODO 描述文件用途
 * @Since create in 7/1/24 10:04 AM
 * @Version v1.0
 * @Copyright Copyright (c) 2024
 * @Company 广州云趣信息科技有限公司
 */
public class TariffChangStatusServiceExecutor implements TariffServiceExecutor {

    private static final Logger joblogger = LoggerFactory.getLogger(CommonLogger.getJobLogger().getName());

    @Override
    public JSONObject execute(String serviceCode, JSONObject param) {
        try {
            joblogger.info("[TariffJobService] -> changTariffStatus 开始进行资费状态扫描修改");
            EasyQuery query = QueryFactory.getWriteQuery();

            // 1.将下线时间为昨天的在售资费1的资费状态改为下架3
            String currDate = DateUtil.getCurrentDateStr("yyyyMMdd");
            String date = DateUtil.addDay("yyyyMMdd", currDate, -1); // 昨天
            String entId = Constants.getEntId();
            String schema = SchemaService.findSchemaByEntId(entId);
            joblogger.info("[TariffJobService] -> changTariffStatus currDate:"+currDate+",date:"+date);
            /*EasySQL sql = new EasySQL("");
            sql.append("update " + schema + ".XTY_TARIFF_RECORD" + " set STATUS = '3' ");
            sql.append(" where 1= 1 ");
            sql.append(" and  STATUS = '1' "); // 状态为在售的
            sql.append(date, " and  OFFLINE_DAY = ? "); // 下线日期为昨天的
            query.executeUpdate(sql.getSQL(), sql.getParams());

            // 2.将上线时间为今天的未售资费4的资费状态改为在售1
            date = DateUtil.addDay("yyyyMMdd", currDate, 0); //今天
            EasySQL sql2 = new EasySQL("");
            sql2.append("update " + schema + ".XTY_TARIFF_RECORD" + " set STATUS = '1' ");
            sql2.append(" where 1= 1 ");
            sql2.append(" and  STATUS = '4' "); // 状态为未售的
            sql2.append(date, " and  ONLINE_DAY = ? "); // 上架期为今天的
            query.executeUpdate(sql2.getSQL(), sql2.getParams());*/

            dealOnlineTariff(query, currDate, schema);

            dealOfflineTariff(query, date, schema);

        } catch (Exception e) {
            joblogger.error(e.getMessage(), e);
        }
        joblogger.info("[TariffJobService] -> changTariffStatus 结束资费状态修改");
        return null;
    }



    private void dealOnlineTariff(EasyQuery query, String currDate, String schema) throws SQLException {
        try{
            JSONObject esParam = new JSONObject();
            esParam.put("status", "1");
            String sql = "select ID, REPORT_NO FROM "+schema+".XTY_TARIFF_RECORD WHERE status='4' and online_day<=? ";
            joblogger.info("资费未售-在售 dealOnlineTariff sql:"+sql+",date:"+currDate);
            List<JSONObject> list = query.queryForList(sql, new Object[]{currDate}, new JSONMapperImpl());
            joblogger.info("资费未售-在售 dealOnlineTariff list size:"+list.size());

            String date;
            date = DateUtil.addDay("yyyyMMdd", currDate, 0); //今天
            EasySQL sql2 = new EasySQL("");
            sql2.append("update " + schema + ".XTY_TARIFF_RECORD" + " set STATUS = '1' ");
            sql2.append(" where 1= 1 ");
            sql2.append(" and  STATUS = '4' "); // 状态为未售的
            sql2.append(date, " and  ONLINE_DAY <= ? "); // 上架期为今天的
            query.executeUpdate(sql2.getSQL(), sql2.getParams());
            if(list!=null && !list.isEmpty()) {
                for (JSONObject jsonObject : list) {
                    String reportNo = jsonObject.getString("REPORT_NO");
                    String reportId = jsonObject.getString("ID");
                    reqUptService(Constants.XTY_TARIFF_AUDIT_INFO_INDEX, reportNo, esParam);
                    TariffAuditProcessStorageService.getInstance().updateTariffByReportNo("", reportNo, "1", null);
                }
                reCheckTariff(list);
            }

        }catch(Exception e){
            joblogger.error(e.getMessage(), e);
        }
    }

    private void dealOfflineTariff(EasyQuery query, String date, String schema) throws SQLException {
        try{
            JSONObject esParam = new JSONObject();
            esParam.put("status", "3");
            String sql = "select ID, REPORT_NO FROM "+schema+".XTY_TARIFF_RECORD WHERE status='1' and offline_day <= ?";
            joblogger.info("资费在售-下架 sql:"+sql+",date:"+date);
            List<JSONObject> list = query.queryForList(sql, new Object[]{date}, new JSONMapperImpl());
            joblogger.info("资费在售-下架 dealOnlineTariff list size:"+list.size());

            EasySQL sql2 = new EasySQL("");
            sql2.append("update " + schema + ".XTY_TARIFF_RECORD" + " set STATUS = '3' ");
            sql2.append(" where 1= 1 ");
            sql2.append(" and  STATUS = '1' "); // 状态为在售的
            sql2.append(date, " and  OFFLINE_DAY <= ? "); // 下线日期为昨天的
            query.executeUpdate(sql2.getSQL(), sql2.getParams());
            if(list!=null && !list.isEmpty()) {
                for (JSONObject jsonObject : list) {
                    String reportNo = jsonObject.getString("REPORT_NO");
                    String reportId = jsonObject.getString("ID");
                    reqUptService(Constants.XTY_TARIFF_AUDIT_INFO_INDEX, reportNo, esParam);
                    TariffAuditProcessStorageService.getInstance().updateTariffByReportNo("", reportNo, "3", null);
                }
                reCheckTariff(list);
            }
        }catch(Exception e){
            joblogger.error(e.getMessage(), e);
        }
    }



    private void reqUptService(String indexName, String primary, JSONObject param) throws ServiceException {
        JSONObject serverParam = new JSONObject();
        serverParam.put("command", "updateDoc");
        serverParam.put("indexName", Constants.XTY_TARIFF_AUDIT_INFO_INDEX);
        serverParam.put("primary", primary);
        serverParam.put("data", param);
        IService service = ServiceContext.getService("XTY_EVT_ES_ORDER_OPERATE");
        if(service!=null) {
            service.invoke(serverParam);
        }
    }

    private void reCheckTariff(List<JSONObject> list) throws SQLException {
        new Thread(() -> {
            try {
                // 等待5秒确保ES数据同步
                Thread.sleep(5000);
                TariffCheckService checkService = new TariffCheckService();
                for (JSONObject jsonObject : list) {
                    String reportNo = jsonObject.getString("REPORT_NO");
                    String reportId = jsonObject.getString("ID");
                    joblogger.info("重新执行资费字段核查, tariffNo=>{}, tariffId=>{}", reportNo, reportId);
                    checkService.checkTariffFields(null, null, true, reportId, null, 1);
                }
            } catch (Exception e) {
                joblogger.error("异步执行核查服务失败", e);
            }
        }).start();
    }
}
