package com.yunqu.tariff.dao;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.base.AppDaoContext;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.utils.BusiUtil;
import jodd.util.StringUtil;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;

/**
 * <p>
 *
 * </p>
 *
 * @ClassName CrawlerTariffDataDao
 * <AUTHOR> Copy This Tag)
 * @Description TODO 描述文件用途
 * @Since create in 2025/5/26 14:01
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
@WebObject(name = "crawlerTariffData")
public class CrawlerTariffDataDao extends AppDaoContext {

    /**
     * 获取版本列表
     * @return
     */
    @WebControl(name = "getVersionList", type = Types.LIST)
    public JSONObject getVersionList() {
        EasySQL sql = new EasySQL("select VERSION_NO from " + getTableName("xty_crawler_version") + " order by create_time desc");
        //this.setQuery(QueryFactory.getTariffQuery());
        return queryForList(sql.getSQL(), sql.getParams());
    }


    /**
     * 获取爬虫任务列表
     * @return
     */
    @WebControl(name = "getCrawlerTaskPage", type = Types.LIST)
    public JSONObject getCrawlerTaskPage() {
        EasySQL sql = new EasySQL("select t.* from " + getTableName("xty_crawler_task t") + " where 1=1");
        sql.append(param.getString("versionNo"), "and t.version_no=?");
        sql.append(param.getString("provinceName"), "and t.province_name like ?", true);
        sql.append(param.getString("operatorName"), "and t.operator_name like ?", true);
        sql.append(param.getString("status"), "and t.status=?");
        sql.append("order by t.ent_type asc, t.province_code asc");
        //this.setQuery(QueryFactory.getTariffQuery());
        return queryForList(sql.getSQL(), sql.getParams());
    }

    /**
     * 获取爬虫数据列表
     * @return
     */
    @WebControl(name = "getCrawlerTariffDataList", type = Types.PAGE)
    public JSONObject getCrawlerTariffDataList() {

        String versionNo = param.getString("VERSION_NO");
        if (StringUtil.isBlank(versionNo)) {
            CommonLogger.getLogger().error("查询资费公示数据版本号不能为空!");
            return EasyResult.fail("查询资费公示数据版本号不能为空");
        }
        // 动态获取表名
        String tableName = BusiUtil.getCrawlRecordTableName(versionNo);
        EasySQL sql = new EasySQL("select t.* from " + getTableName(tableName) + " t where 1=1");

        sql.append(versionNo, "and t.version_no=?");
        sql.append(param.getString("provinceCode"), "and t.province_code=?");
        sql.appendRLike(param.getString("tariffNo"), "and t.tariff_no like ?");
        sql.appendRLike(param.getString("tariffName"), "and t.name like ?");
        sql.append(param.getString("ENT_CODE"), "and t.ent_code=?");
        sql.append(param.getString("CRAWLER_TYPE"), "and t.crawler_type=?");
        sql.append("order by ent_code asc, crawler_type desc");
        CommonLogger.getLogger().info(sql.toFullSql());
        //this.setQuery(QueryFactory.getTariffQuery());
        return queryForPageList(sql.getSQL(), sql.getParams());
    }


    @Override
    protected EasyQuery getQuery() {
        return QueryFactory.getTariffQuery();
    }
}
